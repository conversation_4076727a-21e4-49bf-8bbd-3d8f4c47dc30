#!/bin/bash

command -v node >/dev/null 2>&1 || { echo "I require NodeJS but it's not installed.  Aborting." >&2; exit 1; }

# Project directory "nossis-tsc"
PROJECT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && cd ../../../.. && pwd )"
echo $PROJECT_DIR
# Source directories
DEV_DIR=$PROJECT_DIR"/build/web-dev/gulp"
cd ${DEV_DIR}
if [ -f "${DEV_DIR}/node_modules/.bin/gulp" ]
then
    node ./node_modules/.bin/gulp $@
else
    npm install
    node ./node_modules/.bin/gulp $@
fi



