# install npm
yum install npm

# npm configurations (add proxy settings if needed)
vi ~/.npmrc
#----------------------------------------
# proxy=http://*************:9400/
# https-proxy=http://*************:9400
#----------------------------------------

# install gulp packages (in current directory)
npm install

# run gulp
npm start

# change nginx user (if necessary from "user nginx" to "user root")
sudo vi /etc/nginx/nginx.conf

# if using root user, start nginx with command
sudo nginx

# add location to nginx
location /entities-catalog/assets/javascripts/nossis-tsc-frontend-catalog-scripts.js {
    alias {project_root}/build/frontend/gulp/build/catalog.js;
}
