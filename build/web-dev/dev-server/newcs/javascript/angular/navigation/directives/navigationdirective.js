/**
 * @ngdoc directive
 * @name modhistorynav
 *
 * @description
 * 
 * Creates and handles the menu and items of the navigation
 * between modules of the product/solution
 * 
 *
 * # Usage
 *
 * ## HTML
 * <ul modhistorynav>
 * </ul>
 * 
 * @param {service} proxy proxy service
 * @param {service} notification notification service
 */
angular.module('navigation').directive('ossModHistoryNav', ['$compile','$templateCache','proxy','notification',function($compile,$templateCache,proxy,notification) {
    var modules = [];
    return {
        restrict : "A",
        priority : 0,
        scope: true,
        terminal : false,
        templateUrl: "/newcs/javascript/angular/navigation/partials/modhistorynav.html",
        link : function(scope, element, attrs) {                                    
            /**
             * @ngdoc event
             * @name onAddHistoryModule             
             * @description event that adds a new history module item
             * @param {event} event
             * @param {string} url url of the new item
             * @param {string} name name of the new item
             * @param {string} icon icon css class of the new item             
             */                 
            scope.$on("Navigation::ossModHistoryNav::addHistoryModule",function(event,url, name, icon){                                
                if(!existsModule(name)){
                    addModule(url,name,icon);
                } else {
                    updateModule(url);   
                }
            });                  
            /**
             * @ngdoc function
             * @name addModule             
             * @description function that adds a new hisotry module item             
             * @param {string} url url of the new item
             * @param {string} name name of the new item
             * @param {string} icon icon css class of the new item             
             */                                                                           
            function addModule(url, name, icon){
                var $li = $("<li></li>");
                var $a = $("<a></a>").attr({"href":"javascript:void(0)","data-action-url":url}).text(name).addClass("modulehistory"+name);
                $a.on("click",openModule);
                var $i =$("<i></i>").addClass(icon);                             
                $a.prepend($i);
                $li.append($a);
                element.find("#modulehistorynav").append($li);
            }
             /**
             * @ngdoc function
             * @name openModule             
             * @description function that opens a item of the menu             
             * @param {event} event        
             */    
            function openModule(event){                  
              var $a = $(event.target);
                proxy.getPage($a.data("action-url"), {}, function () {
                    notification.showGlobalNotification("success","loading","loading page",5000);
                });
            }
            /**
             * @ngdoc function
             * @name updateModule             
             * @description function that updates a item of the menu             
             * @param {string} url url of the new updated item
             * @param {string} name name of the new updated item
             */    
            function updateModule(url,name){
                element.find("."+"modulehistory"+name).attr("data-action-url",url);                                        
            }
            /**
             * @ngdoc function
             * @name existsModule             
             * @description function returns if a module exists             
             * @param {string} url url of the new updated item
             * @param {string} name name of the new updated item
             */    
            function existsModule(){
                   for (var key in scope.modules) {
                        if (scope.modules.hasOwnProperty(key)) {
                         return true;   
                        }
                   }
                return false;
            }
            
        }
    };
}]);