/**
 * @license Angular v2.0.0
 * (c) 2010-2016 Google, Inc. https://angular.io/
 * License: MIT
 */
!function(global,factory){"object"==typeof exports&&"undefined"!=typeof module?factory(exports,require("@angular/core")):"function"==typeof define&&define.amd?define(["exports","@angular/core"],factory):factory((global.ng=global.ng||{},global.ng.compiler=global.ng.compiler||{}),global.ng.core)}(this,function(exports,_angular_core){"use strict";function isPresent(obj){return void 0!==obj&&null!==obj}function isBlank(obj){return void 0===obj||null===obj}function isString(obj){return"string"==typeof obj}function isStringMap(obj){return"object"==typeof obj&&null!==obj}function isStrictStringMap(obj){return isStringMap(obj)&&Object.getPrototypeOf(obj)===STRING_MAP_PROTO}function isArray(obj){return Array.isArray(obj)}function stringify(token){if("string"==typeof token)return token;if(void 0===token||null===token)return""+token;if(token.overriddenName)return token.overriddenName;if(token.name)return token.name;var res=token.toString(),newLineIndex=res.indexOf("\n");return newLineIndex===-1?res:res.substring(0,newLineIndex)}function normalizeBlank(obj){return isBlank(obj)?null:obj}function normalizeBool(obj){return!isBlank(obj)&&obj}function isJsObject(o){return null!==o&&("function"==typeof o||"object"==typeof o)}function evalExpression(sourceUrl,expr,declarations,vars){var fnBody=declarations+"\nreturn "+expr+"\n//# sourceURL="+sourceUrl,fnArgNames=[],fnArgValues=[];for(var argName in vars)fnArgNames.push(argName),fnArgValues.push(vars[argName]);return(new(Function.bind.apply(Function,[void 0].concat(fnArgNames.concat(fnBody))))).apply(void 0,fnArgValues)}function isPrimitive(obj){return!isJsObject(obj)}function escapeRegExp(s){return s.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function templateVisitAll(visitor,asts,context){void 0===context&&(context=null);var result=[];return asts.forEach(function(ast){var astResult=ast.visit(visitor,context);isPresent(astResult)&&result.push(astResult)}),result}function _flattenArray(source,target){if(isPresent(source))for(var i=0;i<source.length;i++){var item=source[i];isArray(item)?_flattenArray(item,target):target.push(item)}return target}function splitNsName(elementName){if(":"!=elementName[0])return[null,elementName];var colonIndex=elementName.indexOf(":",1);if(colonIndex==-1)throw new Error('Unsupported format "'+elementName+'" expecting ":namespace:name"');return[elementName.slice(1,colonIndex),elementName.slice(colonIndex+1)]}function getNsPrefix(fullName){return null===fullName?null:splitNsName(fullName)[0]}function mergeNsAndName(prefix,localName){return prefix?":"+prefix+":"+localName:localName}function getHtmlTagDefinition(tagName){return TAG_DEFINITIONS[tagName.toLowerCase()]||_DEFAULT_TAG_DEFINITION}function replaceVarInExpression(varName,newValue,expression){var transformer=new _ReplaceVariableTransformer(varName,newValue);return expression.visitExpression(transformer,null)}function findReadVarNames(stmts){var finder=new _VariableFinder;return finder.visitAllStatements(stmts,null),finder.varNames}function variable(name,type){return void 0===type&&(type=null),new ReadVarExpr(name,type)}function importExpr(id,typeParams){return void 0===typeParams&&(typeParams=null),new ExternalExpr(id,null,typeParams)}function importType(id,typeParams,typeModifiers){return void 0===typeParams&&(typeParams=null),void 0===typeModifiers&&(typeModifiers=null),isPresent(id)?new ExternalType(id,typeParams,typeModifiers):null}function literalArr(values,type){return void 0===type&&(type=null),new LiteralArrayExpr(values,type)}function literalMap(values,type){return void 0===type&&(type=null),new LiteralMapExpr(values,type)}function not(expr){return new NotExpr(expr)}function fn(params,body,type){return void 0===type&&(type=null),new FunctionExpr(params,body,type)}function literal(value,type){return void 0===type&&(type=null),new LiteralExpr(value,type)}function camelCaseToDashCase(input){return StringWrapper.replaceAllMapped(input,CAMEL_CASE_REGEXP,function(m){return"-"+m[1].toLowerCase()})}function splitAtColon(input,defaultValues){var colonIndex=input.indexOf(":");return colonIndex==-1?defaultValues:[input.slice(0,colonIndex).trim(),input.slice(colonIndex+1).trim()]}function sanitizeIdentifier(name){return StringWrapper.replaceAll(name,/\W/g,"_")}function visitValue(value,visitor,context){return isArray(value)?visitor.visitArray(value,context):isStrictStringMap(value)?visitor.visitStringMap(value,context):isBlank(value)||isPrimitive(value)?visitor.visitPrimitive(value,context):visitor.visitOther(value,context)}function assetUrl(pkg,path,type){return void 0===path&&(path=null),void 0===type&&(type="src"),null==path?"asset:@angular/lib/"+pkg+"/index":"asset:@angular/lib/"+pkg+"/src/"+path}function createDiTokenExpression(token){return isPresent(token.value)?literal(token.value):token.identifierIsInstance?importExpr(token.identifier).instantiate([],importType(token.identifier,[],[TypeModifier.Const])):importExpr(token.identifier)}function unimplemented(){throw new Error("unimplemented")}function createHostComponentMeta(compMeta){var template=CssSelector.parse(compMeta.selector)[0].getMatchingElementTemplate();return CompileDirectiveMetadata.create({type:new CompileTypeMetadata({reference:Object,name:compMeta.type.name+"_Host",moduleUrl:compMeta.type.moduleUrl,isHost:!0}),template:new CompileTemplateMetadata({encapsulation:_angular_core.ViewEncapsulation.None,template:template,templateUrl:"",styles:[],styleUrls:[],ngContentSelectors:[],animations:[]}),changeDetection:_angular_core.ChangeDetectionStrategy.Default,inputs:[],outputs:[],host:{},isComponent:!0,selector:"*",providers:[],viewProviders:[],queries:[],viewQueries:[]})}function removeIdentifierDuplicates(items){var map=new Map;return items.forEach(function(item){map.get(item.identifier.reference)||map.set(item.identifier.reference,item)}),MapWrapper.values(map)}function _normalizeArray(obj){return isPresent(obj)?obj:[]}function isStaticSymbol(value){return isStringMap(value)&&isPresent(value.name)&&isPresent(value.filePath)}function isWhitespace(code){return code>=$TAB&&code<=$SPACE||code==$NBSP}function isDigit(code){return $0<=code&&code<=$9}function isAsciiLetter(code){return code>=$a&&code<=$z||code>=$A&&code<=$Z}function isAsciiHexDigit(code){return code>=$a&&code<=$f||code>=$A&&code<=$F||isDigit(code)}function assertArrayOfStrings(identifier,value){if(_angular_core.isDevMode()&&!isBlank(value)){if(!isArray(value))throw new Error("Expected '"+identifier+"' to be an array of strings.");for(var i=0;i<value.length;i+=1)if(!isString(value[i]))throw new Error("Expected '"+identifier+"' to be an array of strings.")}}function assertInterpolationSymbols(identifier,value){if(isPresent(value)&&(!isArray(value)||2!=value.length))throw new Error("Expected '"+identifier+"' to be an array, [start, end].");if(_angular_core.isDevMode()&&!isBlank(value)){var start_1=value[0],end_1=value[1];INTERPOLATION_BLACKLIST_REGEXPS.forEach(function(regexp){if(regexp.test(start_1)||regexp.test(end_1))throw new Error("['"+start_1+"', '"+end_1+"'] contains unusable interpolation symbol.")})}}function newCharacterToken(index,code){return new Token(index,TokenType.Character,code,StringWrapper.fromCharCode(code))}function newIdentifierToken(index,text){return new Token(index,TokenType.Identifier,0,text)}function newKeywordToken(index,text){return new Token(index,TokenType.Keyword,0,text)}function newOperatorToken(index,text){return new Token(index,TokenType.Operator,0,text)}function newStringToken(index,text){return new Token(index,TokenType.String,0,text)}function newNumberToken(index,n){return new Token(index,TokenType.Number,n,"")}function newErrorToken(index,message){return new Token(index,TokenType.Error,0,message)}function isIdentifierStart(code){return $a<=code&&code<=$z||$A<=code&&code<=$Z||code==$_||code==$$}function isIdentifier(input){if(0==input.length)return!1;var scanner=new _Scanner(input);if(!isIdentifierStart(scanner.peek))return!1;for(scanner.advance();scanner.peek!==$EOF;){if(!isIdentifierPart(scanner.peek))return!1;scanner.advance()}return!0}function isIdentifierPart(code){return isAsciiLetter(code)||isDigit(code)||code==$_||code==$$}function isExponentStart(code){return code==$e||code==$E}function isExponentSign(code){return code==$MINUS||code==$PLUS}function isQuote(code){return code===$SQ||code===$DQ||code===$BT}function unescape(code){switch(code){case $n:return $LF;case $f:return $FF;case $r:return $CR;case $t:return $TAB;case $v:return $VTAB;default:return code}}function _createInterpolateRegExp(config){var pattern=escapeRegExp(config.start)+"([\\s\\S]*?)"+escapeRegExp(config.end);return new RegExp(pattern,"g")}function visitAll(visitor,nodes,context){void 0===context&&(context=null);var result=[];return nodes.forEach(function(ast){var astResult=ast.visit(visitor,context);astResult&&result.push(astResult)}),result}function tokenize(source,url,getTagDefinition,tokenizeExpansionForms,interpolationConfig){return void 0===tokenizeExpansionForms&&(tokenizeExpansionForms=!1),void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG),new _Tokenizer(new ParseSourceFile(source,url),getTagDefinition,tokenizeExpansionForms,interpolationConfig).tokenize()}function _unexpectedCharacterErrorMsg(charCode){var char=charCode===$EOF?"EOF":String.fromCharCode(charCode);return'Unexpected character "'+char+'"'}function _unknownEntityErrorMsg(entitySrc){return'Unknown entity "'+entitySrc+'" - use the "&#<decimal>;" or  "&#x<hex>;" syntax'}function isNotWhitespace(code){return!isWhitespace(code)||code===$EOF}function isNameEnd(code){return isWhitespace(code)||code===$GT||code===$SLASH||code===$SQ||code===$DQ||code===$EQ}function isPrefixEnd(code){return(code<$a||$z<code)&&(code<$A||$Z<code)&&(code<$0||code>$9)}function isDigitEntityEnd(code){return code==$SEMICOLON||code==$EOF||!isAsciiHexDigit(code)}function isNamedEntityEnd(code){return code==$SEMICOLON||code==$EOF||!isAsciiLetter(code)}function isExpansionFormStart(input,offset,interpolationConfig){var isInterpolationStart=!!interpolationConfig&&input.indexOf(interpolationConfig.start,offset)==offset;return input.charCodeAt(offset)==$LBRACE&&!isInterpolationStart}function isExpansionCaseStart(peek){return peek===$EQ||isAsciiLetter(peek)}function compareCharCodeCaseInsensitive(code1,code2){return toUpperCaseCharCode(code1)==toUpperCaseCharCode(code2)}function toUpperCaseCharCode(code){return code>=$a&&code<=$z?code-$a+$A:code}function mergeTextTokens(srcTokens){for(var lastDstToken,dstTokens=[],i=0;i<srcTokens.length;i++){var token=srcTokens[i];lastDstToken&&lastDstToken.type==TokenType$1.TEXT&&token.type==TokenType$1.TEXT?(lastDstToken.parts[0]+=token.parts[0],lastDstToken.sourceSpan.end=token.sourceSpan.end):(lastDstToken=token,dstTokens.push(lastDstToken))}return dstTokens}function lastOnStack(stack,element){return stack.length>0&&stack[stack.length-1]===element}/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
function digestMessage(message){return sha1(serializeNodes(message.nodes).join("")+("["+message.meaning+"]"))}function serializeNodes(nodes){return nodes.map(function(a){return a.visit(serializerVisitor,null)})}function sha1(str){var utf8=utf8Encode(str),words32=stringToWords32(utf8),len=8*utf8.length,w=new Array(80),_a=[**********,**********,**********,271733878,**********],a=_a[0],b=_a[1],c=_a[2],d=_a[3],e=_a[4];words32[len>>5]|=128<<24-len%32,words32[(len+64>>9<<4)+15]=len;for(var i=0;i<words32.length;i+=16){for(var _b=[a,b,c,d,e],h0=_b[0],h1=_b[1],h2=_b[2],h3=_b[3],h4=_b[4],j=0;j<80;j++){j<16?w[j]=words32[i+j]:w[j]=rol32(w[j-3]^w[j-8]^w[j-14]^w[j-16],1);var _c=fk(j,b,c,d),f=_c[0],k=_c[1],temp=[rol32(a,5),f,e,k,w[j]].reduce(add32);_d=[d,c,rol32(b,30),a,temp],e=_d[0],d=_d[1],c=_d[2],b=_d[3],a=_d[4]}_e=[add32(a,h0),add32(b,h1),add32(c,h2),add32(d,h3),add32(e,h4)],a=_e[0],b=_e[1],c=_e[2],d=_e[3],e=_e[4]}for(var sha1=words32ToString([a,b,c,d,e]),hex="",i=0;i<sha1.length;i++){var b_1=sha1.charCodeAt(i);hex+=(b_1>>>4&15).toString(16)+(15&b_1).toString(16)}return hex.toLowerCase();var _d,_e}function utf8Encode(str){for(var encoded="",index=0;index<str.length;index++){var codePoint=decodeSurrogatePairs(str,index);codePoint<=127?encoded+=String.fromCharCode(codePoint):codePoint<=2047?encoded+=String.fromCharCode(192|codePoint>>>6,128|63&codePoint):codePoint<=65535?encoded+=String.fromCharCode(224|codePoint>>>12,128|codePoint>>>6&63,128|63&codePoint):codePoint<=2097151&&(encoded+=String.fromCharCode(240|codePoint>>>18,128|codePoint>>>12&63,128|codePoint>>>6&63,128|63&codePoint))}return encoded}function decodeSurrogatePairs(str,index){if(index<0||index>=str.length)throw new Error("index="+index+' is out of range in "'+str+'"');var low,high=str.charCodeAt(index);return high>=55296&&high<=57343&&str.length>index+1&&(low=str.charCodeAt(index+1),low>=56320&&low<=57343)?1024*(high-55296)+low-56320+65536:high}function stringToWords32(str){for(var words32=Array(str.length>>>2),i=0;i<words32.length;i++)words32[i]=0;for(var i=0;i<str.length;i++)words32[i>>>2]|=(255&str.charCodeAt(i))<<8*(3-i&3);return words32}function words32ToString(words32){for(var str="",i=0;i<4*words32.length;i++)str+=String.fromCharCode(words32[i>>>2]>>>8*(3-i&3)&255);return str}function fk(index,b,c,d){return index<20?[b&c|~b&d,1518500249]:index<40?[b^c^d,1859775393]:index<60?[b&c|b&d|c&d,2400959708]:[b^c^d,3395469782]}function add32(a,b){var low=(65535&a)+(65535&b),high=(a>>16)+(b>>16)+(low>>16);return high<<16|65535&low}function rol32(a,count){return a<<count|a>>>32-count}function createI18nMessageFactory(interpolationConfig){var visitor=new _I18nVisitor(_expParser,interpolationConfig);return function(nodes,meaning,description){return visitor.toI18nMessage(nodes,meaning,description)}}function _extractPlaceholderName(input){return input.split(_CUSTOM_PH_EXP)[1]}function extractMessages(nodes,interpolationConfig,implicitTags,implicitAttrs){var visitor=new _Visitor(implicitTags,implicitAttrs);return visitor.extract(nodes,interpolationConfig)}function mergeTranslations(nodes,translations,interpolationConfig,implicitTags,implicitAttrs){var visitor=new _Visitor(implicitTags,implicitAttrs);return visitor.merge(nodes,translations,interpolationConfig)}function _isOpeningComment(n){return n instanceof Comment&&n.value&&n.value.startsWith("i18n")}function _isClosingComment(n){return n instanceof Comment&&n.value&&"/i18n"===n.value}function _getI18nAttr(p){return p.attrs.find(function(attr){return attr.name===_I18N_ATTR})||null}function _splitMeaningAndDesc(i18n){if(!i18n)return["",""];var pipeIndex=i18n.indexOf("|");return pipeIndex==-1?["",i18n]:[i18n.slice(0,pipeIndex),i18n.slice(pipeIndex+1)]}function getXmlTagDefinition(tagName){return _TAG_DEFINITION}/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
function extractPlaceholders(messageBundle){var messageMap=messageBundle.getMessageMap(),placeholders={};return Object.keys(messageMap).forEach(function(msgId){placeholders[msgId]=messageMap[msgId].placeholders}),placeholders}function extractPlaceholderToIds(messageBundle){var messageMap=messageBundle.getMessageMap(),placeholderToIds={};return Object.keys(messageMap).forEach(function(msgId){placeholderToIds[msgId]=messageMap[msgId].placeholderToMsgIds}),placeholderToIds}function serialize(nodes){return nodes.map(function(node){return node.visit(_visitor)}).join("")}function _escapeXml(text){return _ESCAPED_CHARS.reduce(function(text,entry){return text.replace(entry[0],entry[1])},text)}function resolveIdentifier(identifier){return new CompileIdentifierMetadata({name:identifier.name,moduleUrl:identifier.moduleUrl,reference:reflector.resolveIdentifier(identifier.name,identifier.moduleUrl,identifier.runtime)})}function identifierToken(identifier){return new CompileTokenMetadata({identifier:identifier})}function resolveIdentifierToken(identifier){return identifierToken(resolveIdentifier(identifier))}function resolveEnumIdentifier(enumType,name){var resolvedEnum=reflector.resolveEnum(enumType.reference,name);return new CompileIdentifierMetadata({name:enumType.name+"."+name,moduleUrl:enumType.moduleUrl,reference:resolvedEnum})}function expandNodes(nodes){var expander=new _Expander;return new ExpansionResult(visitAll(expander,nodes),expander.isExpanded,expander.errors)}function _expandPluralForm(ast,errors){var children=ast.cases.map(function(c){PLURAL_CASES.indexOf(c.value)!=-1||c.value.match(/^=\d+$/)||errors.push(new ExpansionError(c.valueSourceSpan,'Plural cases should be "=<number>" or one of '+PLURAL_CASES.join(", ")));var expansionResult=expandNodes(c.expression);return errors.push.apply(errors,expansionResult.errors),new Element("template",[new Attribute$1("ngPluralCase",""+c.value,c.valueSourceSpan)],expansionResult.nodes,c.sourceSpan,c.sourceSpan,c.sourceSpan)}),switchAttr=new Attribute$1("[ngPlural]",ast.switchValue,ast.switchValueSourceSpan);return new Element("ng-container",[switchAttr],children,ast.sourceSpan,ast.sourceSpan,ast.sourceSpan)}function _expandDefaultForm(ast,errors){var children=ast.cases.map(function(c){var expansionResult=expandNodes(c.expression);return errors.push.apply(errors,expansionResult.errors),new Element("template",[new Attribute$1("ngSwitchCase",""+c.value,c.valueSourceSpan)],expansionResult.nodes,c.sourceSpan,c.sourceSpan,c.sourceSpan)}),switchAttr=new Attribute$1("[ngSwitch]",ast.switchValue,ast.switchValueSourceSpan);return new Element("ng-container",[switchAttr],children,ast.sourceSpan,ast.sourceSpan,ast.sourceSpan)}function _transformProvider(provider,_a){var useExisting=_a.useExisting,useValue=_a.useValue,deps=_a.deps;return new CompileProviderMetadata({token:provider.token,useClass:provider.useClass,useExisting:useExisting,useFactory:provider.useFactory,useValue:useValue,deps:deps,multi:provider.multi})}function _transformProviderAst(provider,_a){var eager=_a.eager,providers=_a.providers;return new ProviderAst(provider.token,provider.multiProvider,provider.eager||eager,providers,provider.providerType,provider.lifecycleHooks,provider.sourceSpan)}function _normalizeProviders(providers,sourceSpan,targetErrors,targetProviders){return void 0===targetProviders&&(targetProviders=null),isBlank(targetProviders)&&(targetProviders=[]),isPresent(providers)&&providers.forEach(function(provider){if(isArray(provider))_normalizeProviders(provider,sourceSpan,targetErrors,targetProviders);else{var normalizeProvider=void 0;provider instanceof CompileProviderMetadata?normalizeProvider=provider:provider instanceof CompileTypeMetadata?normalizeProvider=new CompileProviderMetadata({token:new CompileTokenMetadata({identifier:provider}),useClass:provider}):targetErrors.push(new ProviderError("Unknown provider type "+provider,sourceSpan)),isPresent(normalizeProvider)&&targetProviders.push(normalizeProvider)}}),targetProviders}function _resolveProvidersFromDirectives(directives,sourceSpan,targetErrors){var providersByToken=new Map;directives.forEach(function(directive){var dirProvider=new CompileProviderMetadata({token:new CompileTokenMetadata({identifier:directive.type}),useClass:directive.type});_resolveProviders([dirProvider],directive.isComponent?exports.ProviderAstType.Component:exports.ProviderAstType.Directive,!0,sourceSpan,targetErrors,providersByToken)});var directivesWithComponentFirst=directives.filter(function(dir){return dir.isComponent}).concat(directives.filter(function(dir){return!dir.isComponent}));return directivesWithComponentFirst.forEach(function(directive){_resolveProviders(_normalizeProviders(directive.providers,sourceSpan,targetErrors),exports.ProviderAstType.PublicService,!1,sourceSpan,targetErrors,providersByToken),_resolveProviders(_normalizeProviders(directive.viewProviders,sourceSpan,targetErrors),exports.ProviderAstType.PrivateService,!1,sourceSpan,targetErrors,providersByToken)}),providersByToken}function _resolveProviders(providers,providerType,eager,sourceSpan,targetErrors,targetProvidersByToken){providers.forEach(function(provider){var resolvedProvider=targetProvidersByToken.get(provider.token.reference);if(isPresent(resolvedProvider)&&resolvedProvider.multiProvider!==provider.multi&&targetErrors.push(new ProviderError("Mixing multi and non multi provider is not possible for token "+resolvedProvider.token.name,sourceSpan)),isBlank(resolvedProvider)){var lifecycleHooks=provider.token.identifier&&provider.token.identifier instanceof CompileTypeMetadata?provider.token.identifier.lifecycleHooks:[];resolvedProvider=new ProviderAst(provider.token,provider.multi,eager||lifecycleHooks.length>0,[provider],providerType,lifecycleHooks,sourceSpan),targetProvidersByToken.set(provider.token.reference,resolvedProvider)}else provider.multi||ListWrapper.clear(resolvedProvider.providers),resolvedProvider.providers.push(provider)})}function _getViewQueries(component){var viewQueries=new Map;return isPresent(component.viewQueries)&&component.viewQueries.forEach(function(query){return _addQueryToTokenMap(viewQueries,query)}),component.type.diDeps.forEach(function(dep){isPresent(dep.viewQuery)&&_addQueryToTokenMap(viewQueries,dep.viewQuery)}),viewQueries}function _getContentQueries(directives){var contentQueries=new Map;return directives.forEach(function(directive){isPresent(directive.queries)&&directive.queries.forEach(function(query){return _addQueryToTokenMap(contentQueries,query)}),directive.type.diDeps.forEach(function(dep){isPresent(dep.query)&&_addQueryToTokenMap(contentQueries,dep.query)})}),contentQueries}function _addQueryToTokenMap(map,query){query.selectors.forEach(function(token){var entry=map.get(token.reference);isBlank(entry)&&(entry=[],map.set(token.reference,entry)),entry.push(query)})}function isStyleUrlResolvable(url){if(isBlank(url)||0===url.length||"/"==url[0])return!1;var schemeMatch=url.match(_urlWithSchemaRe);return null===schemeMatch||"package"==schemeMatch[1]||"asset"==schemeMatch[1]}function extractStyleUrls(resolver,baseUrl,cssText){var foundUrls=[],modifiedCssText=StringWrapper.replaceAllMapped(cssText,_cssImportRe,function(m){var url=isPresent(m[1])?m[1]:m[2];return isStyleUrlResolvable(url)?(foundUrls.push(resolver.resolve(baseUrl,url)),""):m[0]});return new StyleWithImports(modifiedCssText,foundUrls)}function preparseElement(ast){var selectAttr=null,hrefAttr=null,relAttr=null,nonBindable=!1,projectAs=null;ast.attrs.forEach(function(attr){var lcAttrName=attr.name.toLowerCase();lcAttrName==NG_CONTENT_SELECT_ATTR?selectAttr=attr.value:lcAttrName==LINK_STYLE_HREF_ATTR?hrefAttr=attr.value:lcAttrName==LINK_STYLE_REL_ATTR?relAttr=attr.value:attr.name==NG_NON_BINDABLE_ATTR?nonBindable=!0:attr.name==NG_PROJECT_AS&&attr.value.length>0&&(projectAs=attr.value)}),selectAttr=normalizeNgContentSelect(selectAttr);var nodeName=ast.name.toLowerCase(),type=PreparsedElementType.OTHER;return splitNsName(nodeName)[1]==NG_CONTENT_ELEMENT?type=PreparsedElementType.NG_CONTENT:nodeName==STYLE_ELEMENT?type=PreparsedElementType.STYLE:nodeName==SCRIPT_ELEMENT?type=PreparsedElementType.SCRIPT:nodeName==LINK_ELEMENT&&relAttr==LINK_STYLE_REL_VALUE&&(type=PreparsedElementType.STYLESHEET),new PreparsedElement(type,selectAttr,hrefAttr,nonBindable,projectAs)}function normalizeNgContentSelect(selectAttr){return null===selectAttr||0===selectAttr.length?"*":selectAttr}function splitClasses(classAttrValue){return classAttrValue.trim().split(/\s+/g)}function createElementCssSelector(elementName,matchableAttrs){var cssSelector=new CssSelector,elNameNoNs=splitNsName(elementName)[1];cssSelector.setElement(elNameNoNs);for(var i=0;i<matchableAttrs.length;i++){var attrName=matchableAttrs[i][0],attrNameNoNs=splitNsName(attrName)[1],attrValue=matchableAttrs[i][1];if(cssSelector.addAttribute(attrNameNoNs,attrValue),attrName.toLowerCase()==CLASS_ATTR){var classes=splitClasses(attrValue);classes.forEach(function(className){return cssSelector.addClassName(className)})}}return cssSelector}function unimplemented$1(){throw new Error("unimplemented")}function parseAnimationEntry(entry){var errors=[],stateStyles={},transitions=[],stateDeclarationAsts=[];entry.definitions.forEach(function(def){def instanceof CompileAnimationStateDeclarationMetadata?_parseAnimationDeclarationStates(def,errors).forEach(function(ast){stateDeclarationAsts.push(ast),stateStyles[ast.stateName]=ast.styles}):transitions.push(def)});var stateTransitionAsts=transitions.map(function(transDef){return _parseAnimationStateTransition(transDef,stateStyles,errors)}),ast=new AnimationEntryAst(entry.name,stateDeclarationAsts,stateTransitionAsts);return new ParsedAnimationResult(ast,errors)}function parseAnimationOutputName(outputName,errors){var name,values=outputName.split("."),phase="";if(values.length>1){name=values[0];var parsedPhase=values[1];switch(parsedPhase){case"start":case"done":phase=parsedPhase;break;default:errors.push(new AnimationParseError('The provided animation output phase value "'+parsedPhase+'" for "@'+name+'" is not supported (use start or done)'))}}else name=outputName,errors.push(new AnimationParseError("The animation trigger output event (@"+name+") is missing its phase value name (start or done are currently supported)"));return new AnimationOutput(name,phase,outputName)}function _parseAnimationDeclarationStates(stateMetadata,errors){var styleValues=[];stateMetadata.styles.styles.forEach(function(stylesEntry){isStringMap(stylesEntry)?styleValues.push(stylesEntry):errors.push(new AnimationParseError("State based animations cannot contain references to other states"))});var defStyles=new AnimationStylesAst(styleValues),states=stateMetadata.stateNameExpr.split(/\s*,\s*/);return states.map(function(state){return new AnimationStateDeclarationAst(state,defStyles)})}function _parseAnimationStateTransition(transitionStateMetadata,stateStyles,errors){var styles=new StylesCollection,transitionExprs=[],transitionStates=transitionStateMetadata.stateChangeExpr.split(/\s*,\s*/);transitionStates.forEach(function(expr){_parseAnimationTransitionExpr(expr,errors).forEach(function(transExpr){transitionExprs.push(transExpr)})});var entry=_normalizeAnimationEntry(transitionStateMetadata.steps),animation=_normalizeStyleSteps(entry,stateStyles,errors),animationAst=_parseTransitionAnimation(animation,0,styles,stateStyles,errors);0==errors.length&&_fillAnimationAstStartingKeyframes(animationAst,styles,errors);var stepsAst=animationAst instanceof AnimationWithStepsAst?animationAst:new AnimationSequenceAst([animationAst]);return new AnimationStateTransitionAst(transitionExprs,stepsAst)}function _parseAnimationTransitionExpr(eventStr,errors){var expressions=[],match=eventStr.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(!isPresent(match)||match.length<4)return errors.push(new AnimationParseError("the provided "+eventStr+" is not of a supported format")),expressions;var fromState=match[1],separator=match[2],toState=match[3];expressions.push(new AnimationStateTransitionExpression(fromState,toState));var isFullAnyStateExpr=fromState==ANY_STATE&&toState==ANY_STATE;return"<"!=separator[0]||isFullAnyStateExpr||expressions.push(new AnimationStateTransitionExpression(toState,fromState)),expressions}function _normalizeAnimationEntry(entry){return isArray(entry)?new CompileAnimationSequenceMetadata(entry):entry}function _normalizeStyleMetadata(entry,stateStyles,errors){var normalizedStyles=[];return entry.styles.forEach(function(styleEntry){isString(styleEntry)?ListWrapper.addAll(normalizedStyles,_resolveStylesFromState(styleEntry,stateStyles,errors)):normalizedStyles.push(styleEntry)}),normalizedStyles}function _normalizeStyleSteps(entry,stateStyles,errors){var steps=_normalizeStyleStepEntry(entry,stateStyles,errors);return entry instanceof CompileAnimationGroupMetadata?new CompileAnimationGroupMetadata(steps):new CompileAnimationSequenceMetadata(steps)}function _mergeAnimationStyles(stylesList,newItem){if(isStringMap(newItem)&&stylesList.length>0){var lastIndex=stylesList.length-1,lastItem=stylesList[lastIndex];if(isStringMap(lastItem))return void(stylesList[lastIndex]=StringMapWrapper.merge(lastItem,newItem))}stylesList.push(newItem)}function _normalizeStyleStepEntry(entry,stateStyles,errors){var steps;if(!(entry instanceof CompileAnimationWithStepsMetadata))return[entry];steps=entry.steps;var combinedStyles,newSteps=[];return steps.forEach(function(step){if(step instanceof CompileAnimationStyleMetadata)isPresent(combinedStyles)||(combinedStyles=[]),_normalizeStyleMetadata(step,stateStyles,errors).forEach(function(entry){_mergeAnimationStyles(combinedStyles,entry)});else{if(isPresent(combinedStyles)&&(newSteps.push(new CompileAnimationStyleMetadata(0,combinedStyles)),combinedStyles=null),step instanceof CompileAnimationAnimateMetadata){var animateStyleValue=step.styles;animateStyleValue instanceof CompileAnimationStyleMetadata?animateStyleValue.styles=_normalizeStyleMetadata(animateStyleValue,stateStyles,errors):animateStyleValue instanceof CompileAnimationKeyframesSequenceMetadata&&animateStyleValue.steps.forEach(function(step){step.styles=_normalizeStyleMetadata(step,stateStyles,errors)})}else if(step instanceof CompileAnimationWithStepsMetadata){var innerSteps=_normalizeStyleStepEntry(step,stateStyles,errors);step=step instanceof CompileAnimationGroupMetadata?new CompileAnimationGroupMetadata(innerSteps):new CompileAnimationSequenceMetadata(innerSteps)}newSteps.push(step)}}),isPresent(combinedStyles)&&newSteps.push(new CompileAnimationStyleMetadata(0,combinedStyles)),newSteps}function _resolveStylesFromState(stateName,stateStyles,errors){var styles=[];if(":"!=stateName[0])errors.push(new AnimationParseError('Animation states via styles must be prefixed with a ":"'));else{var normalizedStateName=stateName.substring(1),value=stateStyles[normalizedStateName];isPresent(value)?value.styles.forEach(function(stylesEntry){isStringMap(stylesEntry)&&styles.push(stylesEntry)}):errors.push(new AnimationParseError('Unable to apply styles due to missing a state: "'+normalizedStateName+'"'))}return styles}function _parseAnimationKeyframes(keyframeSequence,currentTime,collectedStyles,stateStyles,errors){var totalEntries=keyframeSequence.steps.length,totalOffsets=0;keyframeSequence.steps.forEach(function(step){return totalOffsets+=isPresent(step.offset)?1:0}),totalOffsets>0&&totalOffsets<totalEntries&&(errors.push(new AnimationParseError("Not all style() entries contain an offset for the provided keyframe()")),totalOffsets=totalEntries);var limit=totalEntries-1,margin=0==totalOffsets?1/limit:0,rawKeyframes=[],index=0,doSortKeyframes=!1,lastOffset=0;keyframeSequence.steps.forEach(function(styleMetadata){var offset=styleMetadata.offset,keyframeStyles={};styleMetadata.styles.forEach(function(entry){StringMapWrapper.forEach(entry,function(value,prop){"offset"!=prop&&(keyframeStyles[prop]=value)})}),isPresent(offset)?doSortKeyframes=doSortKeyframes||offset<lastOffset:offset=index==limit?_TERMINAL_KEYFRAME:margin*index,rawKeyframes.push([offset,keyframeStyles]),lastOffset=offset,index++}),doSortKeyframes&&ListWrapper.sort(rawKeyframes,function(a,b){return a[0]<=b[0]?-1:1});var i,firstKeyframe=rawKeyframes[0];firstKeyframe[0]!=_INITIAL_KEYFRAME&&ListWrapper.insert(rawKeyframes,0,firstKeyframe=[_INITIAL_KEYFRAME,{}]);var firstKeyframeStyles=firstKeyframe[1];limit=rawKeyframes.length-1;var lastKeyframe=rawKeyframes[limit];lastKeyframe[0]!=_TERMINAL_KEYFRAME&&(rawKeyframes.push(lastKeyframe=[_TERMINAL_KEYFRAME,{}]),limit++);var lastKeyframeStyles=lastKeyframe[1];for(i=1;i<=limit;i++){var entry=rawKeyframes[i],styles=entry[1];StringMapWrapper.forEach(styles,function(value,prop){isPresent(firstKeyframeStyles[prop])||(firstKeyframeStyles[prop]=FILL_STYLE_FLAG)})}for(i=limit-1;i>=0;i--){var entry=rawKeyframes[i],styles=entry[1];StringMapWrapper.forEach(styles,function(value,prop){isPresent(lastKeyframeStyles[prop])||(lastKeyframeStyles[prop]=value)})}return rawKeyframes.map(function(entry){return new AnimationKeyframeAst(entry[0],new AnimationStylesAst([entry[1]]))})}function _parseTransitionAnimation(entry,currentTime,collectedStyles,stateStyles,errors){var ast,playTime=0,startingTime=currentTime;if(entry instanceof CompileAnimationWithStepsMetadata){var previousStyles,maxDuration=0,steps=[],isGroup=entry instanceof CompileAnimationGroupMetadata;if(entry.steps.forEach(function(entry){var time=isGroup?startingTime:currentTime;if(entry instanceof CompileAnimationStyleMetadata)return entry.styles.forEach(function(stylesEntry){var map=stylesEntry;StringMapWrapper.forEach(map,function(value,prop){collectedStyles.insertAtTime(prop,time,value)})}),void(previousStyles=entry.styles);var innerAst=_parseTransitionAnimation(entry,time,collectedStyles,stateStyles,errors);if(isPresent(previousStyles)){if(entry instanceof CompileAnimationWithStepsMetadata){var startingStyles=new AnimationStylesAst(previousStyles);steps.push(new AnimationStepAst(startingStyles,[],0,0,""))}else{var innerStep=innerAst;ListWrapper.addAll(innerStep.startingStyles.styles,previousStyles)}previousStyles=null}var astDuration=innerAst.playTime;currentTime+=astDuration,playTime+=astDuration,maxDuration=Math$2.max(astDuration,maxDuration),steps.push(innerAst)}),isPresent(previousStyles)){var startingStyles=new AnimationStylesAst(previousStyles);steps.push(new AnimationStepAst(startingStyles,[],0,0,""))}isGroup?(ast=new AnimationGroupAst(steps),playTime=maxDuration,currentTime=startingTime+playTime):ast=new AnimationSequenceAst(steps)}else if(entry instanceof CompileAnimationAnimateMetadata){var keyframes,timings=_parseTimeExpression(entry.timings,errors),styles=entry.styles;if(styles instanceof CompileAnimationKeyframesSequenceMetadata)keyframes=_parseAnimationKeyframes(styles,currentTime,collectedStyles,stateStyles,errors);else{var styleData=styles,offset=_TERMINAL_KEYFRAME,styleAst=new AnimationStylesAst(styleData.styles),keyframe=new AnimationKeyframeAst(offset,styleAst);keyframes=[keyframe]}ast=new AnimationStepAst(new AnimationStylesAst([]),keyframes,timings.duration,timings.delay,timings.easing),playTime=timings.duration+timings.delay,currentTime+=playTime,keyframes.forEach(function(keyframe){return keyframe.styles.styles.forEach(function(entry){return StringMapWrapper.forEach(entry,function(value,prop){return collectedStyles.insertAtTime(prop,currentTime,value)})})})}else ast=new AnimationStepAst(null,[],0,0,"");return ast.playTime=playTime,ast.startTime=startingTime,ast}function _fillAnimationAstStartingKeyframes(ast,collectedStyles,errors){if(ast instanceof AnimationStepAst&&ast.keyframes.length>0){var keyframes=ast.keyframes;if(1==keyframes.length){var endKeyframe=keyframes[0],startKeyframe=_createStartKeyframeFromEndKeyframe(endKeyframe,ast.startTime,ast.playTime,collectedStyles,errors);ast.keyframes=[startKeyframe,endKeyframe]}}else ast instanceof AnimationWithStepsAst&&ast.steps.forEach(function(entry){return _fillAnimationAstStartingKeyframes(entry,collectedStyles,errors)})}function _parseTimeExpression(exp,errors){var duration,regex=/^([\.\d]+)(m?s)(?:\s+([\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?/i,delay=0,easing=null;if(isString(exp)){var matches=exp.match(regex);if(null===matches)return errors.push(new AnimationParseError('The provided timing value "'+exp+'" is invalid.')),new _AnimationTimings(0,0,null);var durationMatch=parseFloat(matches[1]),durationUnit=matches[2];"s"==durationUnit&&(durationMatch*=_ONE_SECOND),duration=Math$2.floor(durationMatch);var delayMatch=matches[3],delayUnit=matches[4];if(isPresent(delayMatch)){var delayVal=parseFloat(delayMatch);isPresent(delayUnit)&&"s"==delayUnit&&(delayVal*=_ONE_SECOND),delay=Math$2.floor(delayVal)}var easingVal=matches[5];isBlank(easingVal)||(easing=easingVal)}else duration=exp;return new _AnimationTimings(duration,delay,easing)}function _createStartKeyframeFromEndKeyframe(endKeyframe,startTime,duration,collectedStyles,errors){var values={},endTime=startTime+duration;return endKeyframe.styles.styles.forEach(function(styleData){StringMapWrapper.forEach(styleData,function(val,prop){if("offset"!=prop){var resultEntry,nextEntry,value,resultIndex=collectedStyles.indexOfAtOrBeforeTime(prop,startTime);isPresent(resultIndex)?(resultEntry=collectedStyles.getByIndex(prop,resultIndex),value=resultEntry.value,nextEntry=collectedStyles.getByIndex(prop,resultIndex+1)):value=FILL_STYLE_FLAG,isPresent(nextEntry)&&!nextEntry.matches(endTime,val)&&errors.push(new AnimationParseError('The animated CSS property "'+prop+'" unexpectedly changes between steps "'+resultEntry.time+'ms" and "'+endTime+'ms" at "'+nextEntry.time+'ms"')),values[prop]=value}})}),new AnimationKeyframeAst(_INITIAL_KEYFRAME,new AnimationStylesAst([values]))}function _compareToAnimationStateExpr(value,animationState){var emptyStateLiteral=literal(EMPTY_ANIMATION_STATE);switch(animationState){case EMPTY_ANIMATION_STATE:return value.equals(emptyStateLiteral);case ANY_STATE:return literal(!0);default:return value.equals(literal(animationState))}}function _isEndStateAnimateStep(step){if(step instanceof AnimationStepAst&&step.duration>0&&2==step.keyframes.length){var styles1=_getStylesArray(step.keyframes[0])[0],styles2=_getStylesArray(step.keyframes[1])[0];return StringMapWrapper.isEmpty(styles1)&&StringMapWrapper.isEmpty(styles2)}return!1}function _getStylesArray(obj){return obj.styles.styles}function _validateAnimationProperties(compiledAnimations,template){var visitor=new _AnimationTemplatePropertyVisitor(compiledAnimations);return templateVisitAll(visitor,template),new AnimationPropertyValidationOutput(visitor.outputs,visitor.errors)}function convertValueToOutputAst(value,type){return void 0===type&&(type=null),visitValue(value,new _ValueOutputAstTransformer,type)}function getPropertyInView(property,callingView,definedView){if(callingView===definedView)return property;for(var viewProp=THIS_EXPR,currView=callingView;currView!==definedView&&isPresent(currView.declarationElement.view);)currView=currView.declarationElement.view,viewProp=viewProp.prop("parent");if(currView!==definedView)throw new Error("Internal error: Could not calculate a property in a parent view: "+property);if(property instanceof ReadPropExpr){var readPropExpr_1=property;(definedView.fields.some(function(field){return field.name==readPropExpr_1.name})||definedView.getters.some(function(field){return field.name==readPropExpr_1.name}))&&(viewProp=viewProp.cast(definedView.classType))}return replaceVarInExpression(THIS_EXPR.name,viewProp,property)}function injectFromViewParentInjector(token,optional){var args=[createDiTokenExpression(token)];return optional&&args.push(NULL_EXPR),THIS_EXPR.prop("parentInjector").callMethod("get",args)}function getViewFactoryName(component,embeddedTemplateIndex){return"viewFactory_"+component.type.name+embeddedTemplateIndex}function createFlatArray(expressions){for(var lastNonArrayExpressions=[],result=literalArr([]),i=0;i<expressions.length;i++){var expr=expressions[i];expr.type instanceof ArrayType?(lastNonArrayExpressions.length>0&&(result=result.callMethod(BuiltinMethod.ConcatArray,[literalArr(lastNonArrayExpressions)]),lastNonArrayExpressions=[]),result=result.callMethod(BuiltinMethod.ConcatArray,[expr])):lastNonArrayExpressions.push(expr)}return lastNonArrayExpressions.length>0&&(result=result.callMethod(BuiltinMethod.ConcatArray,[literalArr(lastNonArrayExpressions)])),result}function createPureProxy(fn,argCount,pureProxyProp,view){view.fields.push(new ClassField(pureProxyProp.name,null));var pureProxyId=argCount<Identifiers.pureProxies.length?Identifiers.pureProxies[argCount]:null;if(isBlank(pureProxyId))throw new Error("Unsupported number of argument for pure functions: "+argCount);view.createMethod.addStmt(THIS_EXPR.prop(pureProxyProp.name).set(importExpr(resolveIdentifier(pureProxyId)).callFn([fn])).toStmt())}function createQueryValues(viewValues){return ListWrapper.flatten(viewValues.values.map(function(entry){return entry instanceof ViewQueryValues?mapNestedViews(entry.view.declarationElement.appElement,entry.view,createQueryValues(entry)):entry}))}function mapNestedViews(declarationAppElement,view,expressions){var adjustedExpressions=expressions.map(function(expr){return replaceVarInExpression(THIS_EXPR.name,variable("nestedView"),expr)});return declarationAppElement.callMethod("mapNestedViews",[variable(view.className),fn([new FnParam("nestedView",view.classType)],[new ReturnStatement(literalArr(adjustedExpressions))],DYNAMIC_TYPE)])}function createQueryList(query,directiveInstance,propertyName,compileView){compileView.fields.push(new ClassField(propertyName,importType(resolveIdentifier(Identifiers.QueryList),[DYNAMIC_TYPE])));var expr=THIS_EXPR.prop(propertyName);return compileView.createMethod.addStmt(THIS_EXPR.prop(propertyName).set(importExpr(resolveIdentifier(Identifiers.QueryList),[DYNAMIC_TYPE]).instantiate([])).toStmt()),expr}function addQueryToTokenMap(map,query){query.meta.selectors.forEach(function(selector){var entry=map.get(selector.reference);isBlank(entry)&&(entry=[],map.set(selector.reference,entry)),entry.push(query)})}function _enumExpression(classIdentifier,name){return importExpr(resolveEnumIdentifier(classIdentifier,name))}function createInjectInternalCondition(nodeIndex,childNodeCount,provider,providerExpr){var indexCondition;return indexCondition=childNodeCount>0?literal(nodeIndex).lowerEquals(InjectMethodVars.requestNodeIndex).and(InjectMethodVars.requestNodeIndex.lowerEquals(literal(nodeIndex+childNodeCount))):literal(nodeIndex).identical(InjectMethodVars.requestNodeIndex),new IfStmt(InjectMethodVars.token.identical(createDiTokenExpression(provider.token)).and(indexCondition),[new ReturnStatement(providerExpr)])}function createProviderProperty(propName,provider,providerValueExpressions,isMulti,isEager,compileElement){var resolvedProviderValueExpr,type,view=compileElement.view;if(isMulti?(resolvedProviderValueExpr=literalArr(providerValueExpressions),type=new ArrayType(DYNAMIC_TYPE)):(resolvedProviderValueExpr=providerValueExpressions[0],type=providerValueExpressions[0].type),isBlank(type)&&(type=DYNAMIC_TYPE),isEager)view.fields.push(new ClassField(propName,type)),view.createMethod.addStmt(THIS_EXPR.prop(propName).set(resolvedProviderValueExpr).toStmt());else{var internalField="_"+propName;view.fields.push(new ClassField(internalField,type));var getter=new CompileMethod(view);getter.resetDebugInfo(compileElement.nodeIndex,compileElement.sourceAst),getter.addStmt(new IfStmt(THIS_EXPR.prop(internalField).isBlank(),[THIS_EXPR.prop(internalField).set(resolvedProviderValueExpr).toStmt()])),getter.addStmt(new ReturnStatement(THIS_EXPR.prop(internalField))),view.getters.push(new ClassGetter(propName,getter.finish(),type))}return THIS_EXPR.prop(propName)}function _findPipeMeta(view,name){for(var pipeMeta=null,i=view.pipeMetas.length-1;i>=0;i--){var localPipeMeta=view.pipeMetas[i];if(localPipeMeta.name==name){pipeMeta=localPipeMeta;break}}if(isBlank(pipeMeta))throw new Error("Illegal state: Could not find pipe "+name+" although the parser should have detected this error!");return pipeMeta}function getViewType(component,embeddedTemplateIndex){return embeddedTemplateIndex>0?ViewType.EMBEDDED:component.type.isHost?ViewType.HOST:ViewType.COMPONENT}function convertCdExpressionToIr(nameResolver,implicitReceiver,expression,valueUnwrapper,bindingIndex){var visitor=new _AstToIrVisitor(nameResolver,implicitReceiver,valueUnwrapper,bindingIndex),irAst=expression.visit(visitor,_Mode.Expression);return new ExpressionWithWrappedValueInfo(irAst,visitor.needsValueUnwrapper,visitor.temporaryCount)}function convertCdStatementToIr(nameResolver,implicitReceiver,stmt,bindingIndex){var visitor=new _AstToIrVisitor(nameResolver,implicitReceiver,null,bindingIndex),statements=[];return flattenStatements(stmt.visit(visitor,_Mode.Statement),statements),prependTemporaryDecls(visitor.temporaryCount,bindingIndex,statements),statements}function temporaryName(bindingIndex,temporaryNumber){return"tmp_"+bindingIndex+"_"+temporaryNumber}function temporaryDeclaration(bindingIndex,temporaryNumber){return new DeclareVarStmt(temporaryName(bindingIndex,temporaryNumber),NULL_EXPR)}function prependTemporaryDecls(temporaryCount,bindingIndex,statements){for(var i=temporaryCount-1;i>=0;i--)statements.unshift(temporaryDeclaration(bindingIndex,i))}function ensureStatementMode(mode,ast){if(mode!==_Mode.Statement)throw new Error("Expected a statement, but saw "+ast)}function ensureExpressionMode(mode,ast){if(mode!==_Mode.Expression)throw new Error("Expected an expression, but saw "+ast)}function convertToStatementIfNeeded(mode,expr){return mode===_Mode.Statement?expr.toStmt():expr}function flattenStatements(arg,output){isArray(arg)?arg.forEach(function(entry){return flattenStatements(entry,output)}):output.push(arg)}function collectEventListeners(hostEvents,dirs,compileElement){var eventListeners=[];return hostEvents.forEach(function(hostEvent){compileElement.view.bindings.push(new CompileBinding(compileElement,hostEvent));var listener=CompileEventListener.getOrCreate(compileElement,hostEvent.target,hostEvent.name,eventListeners);listener.addAction(hostEvent,null,null)}),dirs.forEach(function(directiveAst){var directiveInstance=compileElement.instances.get(identifierToken(directiveAst.directive.type).reference);directiveAst.hostEvents.forEach(function(hostEvent){compileElement.view.bindings.push(new CompileBinding(compileElement,hostEvent));var listener=CompileEventListener.getOrCreate(compileElement,hostEvent.target,hostEvent.name,eventListeners);listener.addAction(hostEvent,directiveAst.directive,directiveInstance)})}),eventListeners.forEach(function(listener){return listener.finishMethod()}),eventListeners}function bindDirectiveOutputs(directiveAst,directiveInstance,eventListeners){StringMapWrapper.forEach(directiveAst.directive.outputs,function(eventName,observablePropName){eventListeners.filter(function(listener){return listener.eventName==eventName}).forEach(function(listener){listener.listenToDirective(directiveInstance,observablePropName)})})}function bindRenderOutputs(eventListeners){eventListeners.forEach(function(listener){return listener.listenToRenderer()})}function bindAnimationOutputs(eventListeners){eventListeners.forEach(function(entry){entry.listener.listenToAnimation(entry.output)})}function convertStmtIntoExpression(stmt){return stmt instanceof ExpressionStatement?stmt.expr:stmt instanceof ReturnStatement?stmt.value:null}function santitizeEventName(name){return StringWrapper.replaceAll(name,/[^a-zA-Z_]/g,"_")}function bindDirectiveDetectChangesLifecycleCallbacks(directiveAst,directiveInstance,compileElement){var view=compileElement.view,detectChangesInInputsMethod=view.detectChangesInInputsMethod,lifecycleHooks=directiveAst.directive.type.lifecycleHooks;lifecycleHooks.indexOf(LifecycleHooks.OnChanges)!==-1&&directiveAst.inputs.length>0&&detectChangesInInputsMethod.addStmt(new IfStmt(DetectChangesVars.changes.notIdentical(NULL_EXPR),[directiveInstance.callMethod("ngOnChanges",[DetectChangesVars.changes]).toStmt()])),
lifecycleHooks.indexOf(LifecycleHooks.OnInit)!==-1&&detectChangesInInputsMethod.addStmt(new IfStmt(STATE_IS_NEVER_CHECKED.and(NOT_THROW_ON_CHANGES),[directiveInstance.callMethod("ngOnInit",[]).toStmt()])),lifecycleHooks.indexOf(LifecycleHooks.DoCheck)!==-1&&detectChangesInInputsMethod.addStmt(new IfStmt(NOT_THROW_ON_CHANGES,[directiveInstance.callMethod("ngDoCheck",[]).toStmt()]))}function bindDirectiveAfterContentLifecycleCallbacks(directiveMeta,directiveInstance,compileElement){var view=compileElement.view,lifecycleHooks=directiveMeta.type.lifecycleHooks,afterContentLifecycleCallbacksMethod=view.afterContentLifecycleCallbacksMethod;afterContentLifecycleCallbacksMethod.resetDebugInfo(compileElement.nodeIndex,compileElement.sourceAst),lifecycleHooks.indexOf(LifecycleHooks.AfterContentInit)!==-1&&afterContentLifecycleCallbacksMethod.addStmt(new IfStmt(STATE_IS_NEVER_CHECKED,[directiveInstance.callMethod("ngAfterContentInit",[]).toStmt()])),lifecycleHooks.indexOf(LifecycleHooks.AfterContentChecked)!==-1&&afterContentLifecycleCallbacksMethod.addStmt(directiveInstance.callMethod("ngAfterContentChecked",[]).toStmt())}function bindDirectiveAfterViewLifecycleCallbacks(directiveMeta,directiveInstance,compileElement){var view=compileElement.view,lifecycleHooks=directiveMeta.type.lifecycleHooks,afterViewLifecycleCallbacksMethod=view.afterViewLifecycleCallbacksMethod;afterViewLifecycleCallbacksMethod.resetDebugInfo(compileElement.nodeIndex,compileElement.sourceAst),lifecycleHooks.indexOf(LifecycleHooks.AfterViewInit)!==-1&&afterViewLifecycleCallbacksMethod.addStmt(new IfStmt(STATE_IS_NEVER_CHECKED,[directiveInstance.callMethod("ngAfterViewInit",[]).toStmt()])),lifecycleHooks.indexOf(LifecycleHooks.AfterViewChecked)!==-1&&afterViewLifecycleCallbacksMethod.addStmt(directiveInstance.callMethod("ngAfterViewChecked",[]).toStmt())}function bindInjectableDestroyLifecycleCallbacks(provider,providerInstance,compileElement){var onDestroyMethod=compileElement.view.destroyMethod;onDestroyMethod.resetDebugInfo(compileElement.nodeIndex,compileElement.sourceAst),provider.lifecycleHooks.indexOf(LifecycleHooks.OnDestroy)!==-1&&onDestroyMethod.addStmt(providerInstance.callMethod("ngOnDestroy",[]).toStmt())}function bindPipeDestroyLifecycleCallbacks(pipeMeta,pipeInstance,view){var onDestroyMethod=view.destroyMethod;pipeMeta.type.lifecycleHooks.indexOf(LifecycleHooks.OnDestroy)!==-1&&onDestroyMethod.addStmt(pipeInstance.callMethod("ngOnDestroy",[]).toStmt())}function createBindFieldExpr(exprIndex){return THIS_EXPR.prop("_expr_"+exprIndex)}function createCurrValueExpr(exprIndex){return variable("currVal_"+exprIndex)}function bind(view,currValExpr,fieldExpr,parsedExpression,context,actions,method,bindingIndex){var checkExpression=convertCdExpressionToIr(view,context,parsedExpression,DetectChangesVars.valUnwrapper,bindingIndex);if(!isBlank(checkExpression.expression)){if(checkExpression.temporaryCount)for(var i=0;i<checkExpression.temporaryCount;i++)method.addStmt(temporaryDeclaration(bindingIndex,i));if(view.fields.push(new ClassField(fieldExpr.name,null,[StmtModifier.Private])),view.createMethod.addStmt(THIS_EXPR.prop(fieldExpr.name).set(importExpr(resolveIdentifier(Identifiers.UNINITIALIZED))).toStmt()),checkExpression.needsValueUnwrapper){var initValueUnwrapperStmt=DetectChangesVars.valUnwrapper.callMethod("reset",[]).toStmt();method.addStmt(initValueUnwrapperStmt)}method.addStmt(currValExpr.set(checkExpression.expression).toDeclStmt(null,[StmtModifier.Final]));var condition=importExpr(resolveIdentifier(Identifiers.checkBinding)).callFn([DetectChangesVars.throwOnChange,fieldExpr,currValExpr]);checkExpression.needsValueUnwrapper&&(condition=DetectChangesVars.valUnwrapper.prop("hasWrappedValue").or(condition)),method.addStmt(new IfStmt(condition,actions.concat([THIS_EXPR.prop(fieldExpr.name).set(currValExpr).toStmt()])))}}function bindRenderText(boundText,compileNode,view){var bindingIndex=view.bindings.length;view.bindings.push(new CompileBinding(compileNode,boundText));var currValExpr=createCurrValueExpr(bindingIndex),valueField=createBindFieldExpr(bindingIndex);view.detectChangesRenderPropertiesMethod.resetDebugInfo(compileNode.nodeIndex,boundText),bind(view,currValExpr,valueField,boundText.value,view.componentContext,[THIS_EXPR.prop("renderer").callMethod("setText",[compileNode.renderNode,currValExpr]).toStmt()],view.detectChangesRenderPropertiesMethod,bindingIndex)}function bindAndWriteToRenderer(boundProps,context,compileElement,isHostProp){var view=compileElement.view,renderNode=compileElement.renderNode;boundProps.forEach(function(boundProp){var bindingIndex=view.bindings.length;view.bindings.push(new CompileBinding(compileElement,boundProp)),view.detectChangesRenderPropertiesMethod.resetDebugInfo(compileElement.nodeIndex,boundProp);var fieldExpr=createBindFieldExpr(bindingIndex),currValExpr=createCurrValueExpr(bindingIndex),oldRenderValue=sanitizedValue(boundProp,fieldExpr),renderValue=sanitizedValue(boundProp,currValExpr),updateStmts=[],compileMethod=view.detectChangesRenderPropertiesMethod;switch(boundProp.type){case exports.PropertyBindingType.Property:view.genConfig.logBindingUpdate&&updateStmts.push(logBindingUpdateStmt(renderNode,boundProp.name,renderValue)),updateStmts.push(THIS_EXPR.prop("renderer").callMethod("setElementProperty",[renderNode,literal(boundProp.name),renderValue]).toStmt());break;case exports.PropertyBindingType.Attribute:renderValue=renderValue.isBlank().conditional(NULL_EXPR,renderValue.callMethod("toString",[])),updateStmts.push(THIS_EXPR.prop("renderer").callMethod("setElementAttribute",[renderNode,literal(boundProp.name),renderValue]).toStmt());break;case exports.PropertyBindingType.Class:updateStmts.push(THIS_EXPR.prop("renderer").callMethod("setElementClass",[renderNode,literal(boundProp.name),renderValue]).toStmt());break;case exports.PropertyBindingType.Style:var strValue=renderValue.callMethod("toString",[]);isPresent(boundProp.unit)&&(strValue=strValue.plus(literal(boundProp.unit))),renderValue=renderValue.isBlank().conditional(NULL_EXPR,strValue),updateStmts.push(THIS_EXPR.prop("renderer").callMethod("setElementStyle",[renderNode,literal(boundProp.name),renderValue]).toStmt());break;case exports.PropertyBindingType.Animation:var animationName=boundProp.name,targetViewExpr=THIS_EXPR;isHostProp&&(targetViewExpr=compileElement.appElement.prop("componentView")),compileMethod=view.animationBindingsMethod;var animationFnExpr=targetViewExpr.prop("componentType").prop("animations").key(literal(animationName)),emptyStateValue=literal(EMPTY_ANIMATION_STATE),oldRenderVar=variable("oldRenderVar");updateStmts.push(oldRenderVar.set(oldRenderValue).toDeclStmt()),updateStmts.push(new IfStmt(oldRenderVar.equals(importExpr(resolveIdentifier(Identifiers.UNINITIALIZED))),[oldRenderVar.set(emptyStateValue).toStmt()]));var newRenderVar=variable("newRenderVar");updateStmts.push(newRenderVar.set(renderValue).toDeclStmt()),updateStmts.push(new IfStmt(newRenderVar.equals(importExpr(resolveIdentifier(Identifiers.UNINITIALIZED))),[newRenderVar.set(emptyStateValue).toStmt()])),updateStmts.push(animationFnExpr.callFn([THIS_EXPR,renderNode,oldRenderVar,newRenderVar]).toStmt()),view.detachMethod.addStmt(animationFnExpr.callFn([THIS_EXPR,renderNode,oldRenderValue,emptyStateValue]).toStmt())}bind(view,currValExpr,fieldExpr,boundProp.value,context,updateStmts,compileMethod,view.bindings.length)})}function sanitizedValue(boundProp,renderValue){var enumValue;switch(boundProp.securityContext){case _angular_core.SecurityContext.NONE:return renderValue;case _angular_core.SecurityContext.HTML:enumValue="HTML";break;case _angular_core.SecurityContext.STYLE:enumValue="STYLE";break;case _angular_core.SecurityContext.SCRIPT:enumValue="SCRIPT";break;case _angular_core.SecurityContext.URL:enumValue="URL";break;case _angular_core.SecurityContext.RESOURCE_URL:enumValue="RESOURCE_URL";break;default:throw new Error("internal error, unexpected SecurityContext "+boundProp.securityContext+".")}var ctx=ViewProperties.viewUtils.prop("sanitizer"),args=[importExpr(resolveIdentifier(Identifiers.SecurityContext)).prop(enumValue),renderValue];return ctx.callMethod("sanitize",args)}function bindRenderInputs(boundProps,compileElement){bindAndWriteToRenderer(boundProps,compileElement.view.componentContext,compileElement,!1)}function bindDirectiveHostProps(directiveAst,directiveInstance,compileElement){bindAndWriteToRenderer(directiveAst.hostProperties,directiveInstance,compileElement,!0)}function bindDirectiveInputs(directiveAst,directiveInstance,compileElement){if(0!==directiveAst.inputs.length){var view=compileElement.view,detectChangesInInputsMethod=view.detectChangesInInputsMethod;detectChangesInInputsMethod.resetDebugInfo(compileElement.nodeIndex,compileElement.sourceAst);var lifecycleHooks=directiveAst.directive.type.lifecycleHooks,calcChangesMap=lifecycleHooks.indexOf(LifecycleHooks.OnChanges)!==-1,isOnPushComp=directiveAst.directive.isComponent&&!isDefaultChangeDetectionStrategy(directiveAst.directive.changeDetection);calcChangesMap&&detectChangesInInputsMethod.addStmt(DetectChangesVars.changes.set(NULL_EXPR).toStmt()),isOnPushComp&&detectChangesInInputsMethod.addStmt(DetectChangesVars.changed.set(literal(!1)).toStmt()),directiveAst.inputs.forEach(function(input){var bindingIndex=view.bindings.length;view.bindings.push(new CompileBinding(compileElement,input)),detectChangesInInputsMethod.resetDebugInfo(compileElement.nodeIndex,input);var fieldExpr=createBindFieldExpr(bindingIndex),currValExpr=createCurrValueExpr(bindingIndex),statements=[directiveInstance.prop(input.directiveName).set(currValExpr).toStmt()];calcChangesMap&&(statements.push(new IfStmt(DetectChangesVars.changes.identical(NULL_EXPR),[DetectChangesVars.changes.set(literalMap([],new MapType(importType(resolveIdentifier(Identifiers.SimpleChange))))).toStmt()])),statements.push(DetectChangesVars.changes.key(literal(input.directiveName)).set(importExpr(resolveIdentifier(Identifiers.SimpleChange)).instantiate([fieldExpr,currValExpr])).toStmt())),isOnPushComp&&statements.push(DetectChangesVars.changed.set(literal(!0)).toStmt()),view.genConfig.logBindingUpdate&&statements.push(logBindingUpdateStmt(compileElement.renderNode,input.directiveName,currValExpr)),bind(view,currValExpr,fieldExpr,input.value,view.componentContext,statements,detectChangesInInputsMethod,bindingIndex)}),isOnPushComp&&detectChangesInInputsMethod.addStmt(new IfStmt(DetectChangesVars.changed,[compileElement.appElement.prop("componentView").callMethod("markAsCheckOnce",[]).toStmt()]))}}function logBindingUpdateStmt(renderNode,propName,value){var tryStmt=THIS_EXPR.prop("renderer").callMethod("setBindingDebugInfo",[renderNode,literal("ng-reflect-"+camelCaseToDashCase(propName)),value.isBlank().conditional(NULL_EXPR,value.callMethod("toString",[]))]).toStmt(),catchStmt=THIS_EXPR.prop("renderer").callMethod("setBindingDebugInfo",[renderNode,literal("ng-reflect-"+camelCaseToDashCase(propName)),literal("[ERROR] Exception while trying to serialize the value")]).toStmt();return new TryCatchStmt([tryStmt],[catchStmt])}function bindView(view,parsedTemplate,animationOutputs){var visitor=new ViewBinderVisitor(view,animationOutputs);templateVisitAll(visitor,parsedTemplate),view.pipes.forEach(function(pipe){bindPipeDestroyLifecycleCallbacks(pipe.meta,pipe.instance,pipe.view)})}function buildView(view,template,targetDependencies){var builderVisitor=new ViewBuilderVisitor(view,targetDependencies);return templateVisitAll(builderVisitor,template,view.declarationElement.isNull()?view.declarationElement:view.declarationElement.parent),builderVisitor.nestedViewCount}function finishView(view,targetStatements){view.afterNodes(),createViewTopLevelStmts(view,targetStatements),view.nodes.forEach(function(node){node instanceof CompileElement&&node.hasEmbeddedView&&finishView(node.embeddedView,targetStatements)})}function _getOuterContainerOrSelf(node){for(var view=node.view;_isNgContainer(node.parent,view);)node=node.parent;return node}function _getOuterContainerParentOrSelf(el){for(var view=el.view;_isNgContainer(el,view);)el=el.parent;return el}function _isNgContainer(node,view){return!node.isNull()&&node.sourceAst.name===NG_CONTAINER_TAG&&node.view===view}function _mergeHtmlAndDirectiveAttrs(declaredHtmlAttrs,directives){var result={};return StringMapWrapper.forEach(declaredHtmlAttrs,function(value,key){result[key]=value}),directives.forEach(function(directiveMeta){StringMapWrapper.forEach(directiveMeta.hostAttributes,function(value,name){var prevValue=result[name];result[name]=isPresent(prevValue)?mergeAttributeValue(name,prevValue,value):value})}),mapToKeyValueArray(result)}function _readHtmlAttrs(attrs){var htmlAttrs={};return attrs.forEach(function(ast){htmlAttrs[ast.name]=ast.value}),htmlAttrs}function mergeAttributeValue(attrName,attrValue1,attrValue2){return attrName==CLASS_ATTR$1||attrName==STYLE_ATTR?attrValue1+" "+attrValue2:attrValue2}function mapToKeyValueArray(data){var entryArray=[];return StringMapWrapper.forEach(data,function(value,name){entryArray.push([name,value])}),ListWrapper.sort(entryArray,function(entry1,entry2){return StringWrapper.compare(entry1[0],entry2[0])}),entryArray}function createViewTopLevelStmts(view,targetStatements){var nodeDebugInfosVar=NULL_EXPR;view.genConfig.genDebugInfo&&(nodeDebugInfosVar=variable("nodeDebugInfos_"+view.component.type.name+view.viewIndex),targetStatements.push(nodeDebugInfosVar.set(literalArr(view.nodes.map(createStaticNodeDebugInfo),new ArrayType(new ExternalType(resolveIdentifier(Identifiers.StaticNodeDebugInfo)),[TypeModifier.Const]))).toDeclStmt(null,[StmtModifier.Final])));var renderCompTypeVar=variable("renderType_"+view.component.type.name);0===view.viewIndex&&targetStatements.push(renderCompTypeVar.set(NULL_EXPR).toDeclStmt(importType(resolveIdentifier(Identifiers.RenderComponentType))));var viewClass=createViewClass(view,renderCompTypeVar,nodeDebugInfosVar);targetStatements.push(viewClass),targetStatements.push(createViewFactory(view,viewClass,renderCompTypeVar))}function createStaticNodeDebugInfo(node){var compileElement=node instanceof CompileElement?node:null,providerTokens=[],componentToken=NULL_EXPR,varTokenEntries=[];return isPresent(compileElement)&&(providerTokens=compileElement.getProviderTokens(),isPresent(compileElement.component)&&(componentToken=createDiTokenExpression(identifierToken(compileElement.component.type))),StringMapWrapper.forEach(compileElement.referenceTokens,function(token,varName){varTokenEntries.push([varName,isPresent(token)?createDiTokenExpression(token):NULL_EXPR])})),importExpr(resolveIdentifier(Identifiers.StaticNodeDebugInfo)).instantiate([literalArr(providerTokens,new ArrayType(DYNAMIC_TYPE,[TypeModifier.Const])),componentToken,literalMap(varTokenEntries,new MapType(DYNAMIC_TYPE,[TypeModifier.Const]))],importType(resolveIdentifier(Identifiers.StaticNodeDebugInfo),null,[TypeModifier.Const]))}function createViewClass(view,renderCompTypeVar,nodeDebugInfosVar){var viewConstructorArgs=[new FnParam(ViewConstructorVars.viewUtils.name,importType(resolveIdentifier(Identifiers.ViewUtils))),new FnParam(ViewConstructorVars.parentInjector.name,importType(resolveIdentifier(Identifiers.Injector))),new FnParam(ViewConstructorVars.declarationEl.name,importType(resolveIdentifier(Identifiers.AppElement)))],superConstructorArgs=[variable(view.className),renderCompTypeVar,ViewTypeEnum.fromValue(view.viewType),ViewConstructorVars.viewUtils,ViewConstructorVars.parentInjector,ViewConstructorVars.declarationEl,ChangeDetectorStatusEnum.fromValue(getChangeDetectionMode(view))];view.genConfig.genDebugInfo&&superConstructorArgs.push(nodeDebugInfosVar);var viewConstructor=new ClassMethod(null,viewConstructorArgs,[SUPER_EXPR.callFn(superConstructorArgs).toStmt()]),viewMethods=[new ClassMethod("createInternal",[new FnParam(rootSelectorVar.name,STRING_TYPE)],generateCreateMethod(view),importType(resolveIdentifier(Identifiers.AppElement))),new ClassMethod("injectorGetInternal",[new FnParam(InjectMethodVars.token.name,DYNAMIC_TYPE),new FnParam(InjectMethodVars.requestNodeIndex.name,NUMBER_TYPE),new FnParam(InjectMethodVars.notFoundResult.name,DYNAMIC_TYPE)],addReturnValuefNotEmpty(view.injectorGetMethod.finish(),InjectMethodVars.notFoundResult),DYNAMIC_TYPE),new ClassMethod("detectChangesInternal",[new FnParam(DetectChangesVars.throwOnChange.name,BOOL_TYPE)],generateDetectChangesMethod(view)),new ClassMethod("dirtyParentQueriesInternal",[],view.dirtyParentQueriesMethod.finish()),new ClassMethod("destroyInternal",[],view.destroyMethod.finish()),new ClassMethod("detachInternal",[],view.detachMethod.finish())].concat(view.eventHandlerMethods),superClass=view.genConfig.genDebugInfo?Identifiers.DebugAppView:Identifiers.AppView,viewClass=new ClassStmt(view.className,importExpr(resolveIdentifier(superClass),[getContextType(view)]),view.fields,view.getters,viewConstructor,viewMethods.filter(function(method){return method.body.length>0}));return viewClass}function createViewFactory(view,viewClass,renderCompTypeVar){var templateUrlInfo,viewFactoryArgs=[new FnParam(ViewConstructorVars.viewUtils.name,importType(resolveIdentifier(Identifiers.ViewUtils))),new FnParam(ViewConstructorVars.parentInjector.name,importType(resolveIdentifier(Identifiers.Injector))),new FnParam(ViewConstructorVars.declarationEl.name,importType(resolveIdentifier(Identifiers.AppElement)))],initRenderCompTypeStmts=[];if(templateUrlInfo=view.component.template.templateUrl==view.component.type.moduleUrl?view.component.type.moduleUrl+" class "+view.component.type.name+" - inline template":view.component.template.templateUrl,0===view.viewIndex){var animationsExpr=literalMap(view.animations.map(function(entry){return[entry.name,entry.fnVariable]}));initRenderCompTypeStmts=[new IfStmt(renderCompTypeVar.identical(NULL_EXPR),[renderCompTypeVar.set(ViewConstructorVars.viewUtils.callMethod("createRenderComponentType",[literal(templateUrlInfo),literal(view.component.template.ngContentSelectors.length),ViewEncapsulationEnum.fromValue(view.component.template.encapsulation),view.styles,animationsExpr])).toStmt()])]}return fn(viewFactoryArgs,initRenderCompTypeStmts.concat([new ReturnStatement(variable(viewClass.name).instantiate(viewClass.constructorMethod.params.map(function(param){return variable(param.name)})))]),importType(resolveIdentifier(Identifiers.AppView),[getContextType(view)])).toDeclStmt(view.viewFactory.name,[StmtModifier.Final])}function generateCreateMethod(view){var parentRenderNodeExpr=NULL_EXPR,parentRenderNodeStmts=[];view.viewType===ViewType.COMPONENT&&(parentRenderNodeExpr=ViewProperties.renderer.callMethod("createViewRoot",[THIS_EXPR.prop("declarationAppElement").prop("nativeElement")]),parentRenderNodeStmts=[parentRenderNodeVar.set(parentRenderNodeExpr).toDeclStmt(importType(view.genConfig.renderTypes.renderNode),[StmtModifier.Final])]);var resultExpr;return resultExpr=view.viewType===ViewType.HOST?view.nodes[0].appElement:NULL_EXPR,parentRenderNodeStmts.concat(view.createMethod.finish(),[THIS_EXPR.callMethod("init",[createFlatArray(view.rootNodesOrAppElements),literalArr(view.nodes.map(function(node){return node.renderNode})),literalArr(view.disposables),literalArr(view.subscriptions)]).toStmt(),new ReturnStatement(resultExpr)])}function generateDetectChangesMethod(view){var stmts=[];if(view.animationBindingsMethod.isEmpty()&&view.detectChangesInInputsMethod.isEmpty()&&view.updateContentQueriesMethod.isEmpty()&&view.afterContentLifecycleCallbacksMethod.isEmpty()&&view.detectChangesRenderPropertiesMethod.isEmpty()&&view.updateViewQueriesMethod.isEmpty()&&view.afterViewLifecycleCallbacksMethod.isEmpty())return stmts;ListWrapper.addAll(stmts,view.animationBindingsMethod.finish()),ListWrapper.addAll(stmts,view.detectChangesInInputsMethod.finish()),stmts.push(THIS_EXPR.callMethod("detectContentChildrenChanges",[DetectChangesVars.throwOnChange]).toStmt());var afterContentStmts=view.updateContentQueriesMethod.finish().concat(view.afterContentLifecycleCallbacksMethod.finish());afterContentStmts.length>0&&stmts.push(new IfStmt(not(DetectChangesVars.throwOnChange),afterContentStmts)),ListWrapper.addAll(stmts,view.detectChangesRenderPropertiesMethod.finish()),stmts.push(THIS_EXPR.callMethod("detectViewChildrenChanges",[DetectChangesVars.throwOnChange]).toStmt());var afterViewStmts=view.updateViewQueriesMethod.finish().concat(view.afterViewLifecycleCallbacksMethod.finish());afterViewStmts.length>0&&stmts.push(new IfStmt(not(DetectChangesVars.throwOnChange),afterViewStmts));var varStmts=[],readVars=findReadVarNames(stmts);return SetWrapper.has(readVars,DetectChangesVars.changed.name)&&varStmts.push(DetectChangesVars.changed.set(literal(!0)).toDeclStmt(BOOL_TYPE)),SetWrapper.has(readVars,DetectChangesVars.changes.name)&&varStmts.push(DetectChangesVars.changes.set(NULL_EXPR).toDeclStmt(new MapType(importType(resolveIdentifier(Identifiers.SimpleChange))))),SetWrapper.has(readVars,DetectChangesVars.valUnwrapper.name)&&varStmts.push(DetectChangesVars.valUnwrapper.set(importExpr(resolveIdentifier(Identifiers.ValueUnwrapper)).instantiate([])).toDeclStmt(null,[StmtModifier.Final])),varStmts.concat(stmts)}function addReturnValuefNotEmpty(statements,value){return statements.length>0?statements.concat([new ReturnStatement(value)]):statements}function getContextType(view){return view.viewType===ViewType.COMPONENT?importType(view.component.type):DYNAMIC_TYPE}function getChangeDetectionMode(view){var mode;return mode=view.viewType===ViewType.COMPONENT?isDefaultChangeDetectionStrategy(view.component.changeDetection)?ChangeDetectorStatus.CheckAlways:ChangeDetectorStatus.CheckOnce:ChangeDetectorStatus.CheckAlways}function _resolveViewStatements(compileResult){return compileResult.dependencies.forEach(function(dep){if(dep instanceof ViewFactoryDependency){var vfd=dep;vfd.placeholder.moduleUrl=_ngfactoryModuleUrl(vfd.comp.moduleUrl)}else if(dep instanceof ComponentFactoryDependency){var cfd=dep;cfd.placeholder.name=_componentFactoryName(cfd.comp),cfd.placeholder.moduleUrl=_ngfactoryModuleUrl(cfd.comp.moduleUrl)}}),compileResult.statements}function _resolveStyleStatements(compileResult,fileSuffix){return compileResult.dependencies.forEach(function(dep){dep.valuePlaceholder.moduleUrl=_stylesModuleUrl(dep.moduleUrl,dep.isShimmed,fileSuffix)}),compileResult.statements}function _ngfactoryModuleUrl(compUrl){var urlWithSuffix=_splitTypescriptSuffix(compUrl);return urlWithSuffix[0]+".ngfactory"+urlWithSuffix[1]}function _componentFactoryName(comp){return comp.name+"NgFactory"}function _stylesModuleUrl(stylesheetUrl,shim,suffix){return shim?stylesheetUrl+".shim"+suffix:""+stylesheetUrl+suffix}function _assertComponent(meta){if(!meta.isComponent)throw new Error("Could not compile '"+meta.type.name+"' because it is not a component.")}function _splitTypescriptSuffix(path){if(/\.d\.ts$/.test(path))return[path.substring(0,path.length-5),".ts"];var lastDot=path.lastIndexOf(".");return lastDot!==-1?[path.substring(0,lastDot),path.substring(lastDot)]:[path,""]}function createOfflineCompileUrlResolver(){return new UrlResolver(_ASSET_SCHEME)}function getUrlScheme(url){var match=_split(url);return match&&match[_ComponentIndex.Scheme]||""}function _buildFromEncodedParts(opt_scheme,opt_userInfo,opt_domain,opt_port,opt_path,opt_queryData,opt_fragment){var out=[];return isPresent(opt_scheme)&&out.push(opt_scheme+":"),isPresent(opt_domain)&&(out.push("//"),isPresent(opt_userInfo)&&out.push(opt_userInfo+"@"),out.push(opt_domain),isPresent(opt_port)&&out.push(":"+opt_port)),isPresent(opt_path)&&out.push(opt_path),isPresent(opt_queryData)&&out.push("?"+opt_queryData),isPresent(opt_fragment)&&out.push("#"+opt_fragment),out.join("")}function _split(uri){return uri.match(_splitRe)}function _removeDotSegments(path){if("/"==path)return"/";for(var leadingSlash="/"==path[0]?"/":"",trailingSlash="/"===path[path.length-1]?"/":"",segments=path.split("/"),out=[],up=0,pos=0;pos<segments.length;pos++){var segment=segments[pos];switch(segment){case"":case".":break;case"..":out.length>0?out.pop():up++;break;default:out.push(segment)}}if(""==leadingSlash){for(;up-- >0;)out.unshift("..");0===out.length&&out.push(".")}return leadingSlash+out.join("/")+trailingSlash}function _joinAndCanonicalizePath(parts){var path=parts[_ComponentIndex.Path];return path=isBlank(path)?"":_removeDotSegments(path),parts[_ComponentIndex.Path]=path,_buildFromEncodedParts(parts[_ComponentIndex.Scheme],parts[_ComponentIndex.UserInfo],parts[_ComponentIndex.Domain],parts[_ComponentIndex.Port],path,parts[_ComponentIndex.QueryData],parts[_ComponentIndex.Fragment])}function _resolveUrl(base,url){var parts=_split(encodeURI(url)),baseParts=_split(base);if(isPresent(parts[_ComponentIndex.Scheme]))return _joinAndCanonicalizePath(parts);parts[_ComponentIndex.Scheme]=baseParts[_ComponentIndex.Scheme];for(var i=_ComponentIndex.Scheme;i<=_ComponentIndex.Port;i++)isBlank(parts[i])&&(parts[i]=baseParts[i]);if("/"==parts[_ComponentIndex.Path][0])return _joinAndCanonicalizePath(parts);var path=baseParts[_ComponentIndex.Path];isBlank(path)&&(path="/");var index=path.lastIndexOf("/");return path=path.substring(0,index+1)+parts[_ComponentIndex.Path],parts[_ComponentIndex.Path]=path,_joinAndCanonicalizePath(parts)}function _cloneDirectiveWithTemplate(directive,template){return new CompileDirectiveMetadata({type:directive.type,isComponent:directive.isComponent,selector:directive.selector,exportAs:directive.exportAs,changeDetection:directive.changeDetection,inputs:directive.inputs,outputs:directive.outputs,hostListeners:directive.hostListeners,hostProperties:directive.hostProperties,hostAttributes:directive.hostAttributes,providers:directive.providers,viewProviders:directive.viewProviders,queries:directive.queries,viewQueries:directive.viewQueries,entryComponents:directive.entryComponents,template:template})}function _isDirectiveMetadata(type){return type instanceof _angular_core.Directive}function hasLifecycleHook(hook,token){var lcInterface=LIFECYCLE_INTERFACES.get(hook),lcProp=LIFECYCLE_PROPS.get(hook);return reflector.hasLifecycleHook(token,lcInterface,lcProp)}function _isNgModuleMetadata(obj){return obj instanceof _angular_core.NgModule}function _isPipeMetadata(type){return type instanceof _angular_core.Pipe}function getTransitiveModules(modules,includeImports,targetModules,visitedModules){return void 0===targetModules&&(targetModules=[]),void 0===visitedModules&&(visitedModules=new Set),modules.forEach(function(ngModule){if(!visitedModules.has(ngModule.type.reference)){visitedModules.add(ngModule.type.reference);var nestedModules=includeImports?ngModule.importedModules.concat(ngModule.exportedModules):ngModule.exportedModules;getTransitiveModules(nestedModules,includeImports,targetModules,visitedModules),targetModules.push(ngModule)}}),targetModules}function flattenArray(tree,out){if(void 0===out&&(out=[]),tree)for(var i=0;i<tree.length;i++){var item=_angular_core.resolveForwardRef(tree[i]);isArray(item)?flattenArray(item,out):out.push(item)}return out}function isValidType(value){return isStaticSymbol(value)||value instanceof _angular_core.Type}function staticTypeModuleUrl(value){return isStaticSymbol(value)?value.filePath:null}function componentModuleUrl(reflector,type,cmpMetadata){if(isStaticSymbol(type))return staticTypeModuleUrl(type);if(isPresent(cmpMetadata.moduleId)){var moduleId=cmpMetadata.moduleId,scheme=getUrlScheme(moduleId);return isPresent(scheme)&&scheme.length>0?moduleId:"package:"+moduleId+MODULE_SUFFIX}return reflector.importUri(type)}function convertToCompileValue(value,targetIdentifiers){return visitValue(value,new _CompileValueConverter,targetIdentifiers)}function escapeIdentifier(input,escapeDollar,alwaysQuote){if(void 0===alwaysQuote&&(alwaysQuote=!0),isBlank(input))return null;var body=StringWrapper.replaceAllMapped(input,_SINGLE_QUOTE_ESCAPE_STRING_RE,function(match){return"$"==match[0]?escapeDollar?"\\$":"$":"\n"==match[0]?"\\n":"\r"==match[0]?"\\r":"\\"+match[0]}),requiresQuotes=alwaysQuote||!_LEGAL_IDENTIFIER_RE.test(body);return requiresQuotes?"'"+body+"'":body}function _createIndent(count){for(var res="",i=0;i<count;i++)res+="  ";return res}function debugOutputAstAsTypeScript(ast){var asts,converter=new _TsEmitterVisitor(_debugModuleUrl),ctx=EmitterVisitorContext.createRoot([]);return asts=isArray(ast)?ast:[ast],asts.forEach(function(ast){if(ast instanceof Statement)ast.visitStatement(converter,ctx);else if(ast instanceof Expression)ast.visitExpression(converter,ctx);else{if(!(ast instanceof Type$1))throw new Error("Don't know how to print debug info for "+ast);ast.visitType(converter,ctx)}}),ctx.toSource()}function interpretStatements(statements,resultVar){var stmtsWithReturn=statements.concat([new ReturnStatement(variable(resultVar))]),ctx=new _ExecutionContext(null,null,null,new Map),visitor=new StatementInterpreter,result=visitor.visitAllStatements(stmtsWithReturn,ctx);return isPresent(result)?result.value:null}function _executeFunctionStatements(varNames,varValues,statements,ctx,visitor){for(var childCtx=ctx.createChildWihtLocalVars(),i=0;i<varNames.length;i++)childCtx.vars.set(varNames[i],varValues[i]);var result=visitor.visitAllStatements(statements,childCtx);return isPresent(result)?result.value:null}function createDynamicClass(_classStmt,_ctx,_visitor){var propertyDescriptors={};_classStmt.getters.forEach(function(getter){propertyDescriptors[getter.name]={configurable:!1,get:function(){var instanceCtx=new _ExecutionContext(_ctx,this,_classStmt.name,_ctx.vars);return _executeFunctionStatements([],[],getter.body,instanceCtx,_visitor)}}}),_classStmt.methods.forEach(function(method){var paramNames=method.params.map(function(param){return param.name});propertyDescriptors[method.name]={writable:!1,configurable:!1,value:function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i-0]=arguments[_i];var instanceCtx=new _ExecutionContext(_ctx,this,_classStmt.name,_ctx.vars);return _executeFunctionStatements(paramNames,args,method.body,instanceCtx,_visitor)}}});var ctorParamNames=_classStmt.constructorMethod.params.map(function(param){return param.name}),ctor=function(){for(var _this=this,args=[],_i=0;_i<arguments.length;_i++)args[_i-0]=arguments[_i];var instanceCtx=new _ExecutionContext(_ctx,this,_classStmt.name,_ctx.vars);_classStmt.fields.forEach(function(field){_this[field.name]=void 0}),_executeFunctionStatements(ctorParamNames,args,_classStmt.constructorMethod.body,instanceCtx,_visitor)},superClass=_classStmt.parent.visitExpression(_visitor,_ctx);return ctor.prototype=Object.create(superClass.prototype,propertyDescriptors),ctor}function _declareFn(varNames,statements,ctx,visitor){return function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i-0]=arguments[_i];return _executeFunctionStatements(varNames,args,statements,ctx,visitor)}}function jitStatements(sourceUrl,statements,resultVar){var converter=new JitEmitterVisitor,ctx=EmitterVisitorContext.createRoot([resultVar]);return converter.visitAllStatements(statements,ctx),evalExpression(sourceUrl,resultVar,ctx.toSource(),converter.getArgs())}function stripComments(input){return StringWrapper.replaceAllMapped(input,_commentRe,function(_){return""})}function extractSourceMappingUrl(input){var matcher=input.match(_sourceMappingUrlRe);return matcher?matcher[0]:""}function processRules(input,ruleCallback){var inputWithEscapedBlocks=escapeBlocks(input),nextBlockIndex=0;return StringWrapper.replaceAllMapped(inputWithEscapedBlocks.escapedString,_ruleRe,function(m){var selector=m[2],content="",suffix=m[4],contentPrefix="";isPresent(m[4])&&m[4].startsWith("{"+BLOCK_PLACEHOLDER)&&(content=inputWithEscapedBlocks.blocks[nextBlockIndex++],suffix=m[4].substring(BLOCK_PLACEHOLDER.length+1),contentPrefix="{");var rule=ruleCallback(new CssRule(selector,content));return""+m[1]+rule.selector+m[3]+contentPrefix+rule.content+suffix})}function escapeBlocks(input){for(var inputParts=StringWrapper.split(input,_curlyRe),resultParts=[],escapedBlocks=[],bracketCount=0,currentBlockParts=[],partIndex=0;partIndex<inputParts.length;partIndex++){
var part=inputParts[partIndex];part==CLOSE_CURLY&&bracketCount--,bracketCount>0?currentBlockParts.push(part):(currentBlockParts.length>0&&(escapedBlocks.push(currentBlockParts.join("")),resultParts.push(BLOCK_PLACEHOLDER),currentBlockParts=[]),resultParts.push(part)),part==OPEN_CURLY&&bracketCount++}return currentBlockParts.length>0&&(escapedBlocks.push(currentBlockParts.join("")),resultParts.push(BLOCK_PLACEHOLDER)),new StringWithEscapedBlocks(resultParts.join(""),escapedBlocks)}function getStylesVarName(component){var result="styles";return component&&(result+="_"+component.type.name),result}function assertComponent(meta){if(!meta.isComponent)throw new Error("Could not compile '"+meta.type.name+"' because it is not a component.")}function registerContext(ctx,specs){for(var _i=0,specs_1=specs;_i<specs_1.length;_i++){var spec=specs_1[_i];SECURITY_SCHEMA[spec.toLowerCase()]=ctx}}function _initReflector(){reflector.reflectionCapabilities=new ReflectionCapabilities}function _mergeOptions(optionsArr){return{useDebug:_lastDefined(optionsArr.map(function(options){return options.useDebug})),useJit:_lastDefined(optionsArr.map(function(options){return options.useJit})),defaultEncapsulation:_lastDefined(optionsArr.map(function(options){return options.defaultEncapsulation})),providers:_mergeArrays(optionsArr.map(function(options){return options.providers}))}}function _lastDefined(args){for(var i=args.length-1;i>=0;i--)if(void 0!==args[i])return args[i]}function _mergeArrays(parts){var result=[];return parts.forEach(function(part){return part&&result.push.apply(result,part)}),result}/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
var globalScope;globalScope="undefined"==typeof window?"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:global:window;var global$1=globalScope;global$1.assert=function(condition){};var STRING_MAP_PROTO=Object.getPrototypeOf({}),StringWrapper=function(){function StringWrapper(){}return StringWrapper.fromCharCode=function(code){return String.fromCharCode(code)},StringWrapper.charCodeAt=function(s,index){return s.charCodeAt(index)},StringWrapper.split=function(s,regExp){return s.split(regExp)},StringWrapper.equals=function(s,s2){return s===s2},StringWrapper.stripLeft=function(s,charVal){if(s&&s.length){for(var pos=0,i=0;i<s.length&&s[i]==charVal;i++)pos++;s=s.substring(pos)}return s},StringWrapper.stripRight=function(s,charVal){if(s&&s.length){for(var pos=s.length,i=s.length-1;i>=0&&s[i]==charVal;i--)pos--;s=s.substring(0,pos)}return s},StringWrapper.replace=function(s,from,replace){return s.replace(from,replace)},StringWrapper.replaceAll=function(s,from,replace){return s.replace(from,replace)},StringWrapper.slice=function(s,from,to){return void 0===from&&(from=0),void 0===to&&(to=null),s.slice(from,null===to?void 0:to)},StringWrapper.replaceAllMapped=function(s,from,cb){return s.replace(from,function(){for(var matches=[],_i=0;_i<arguments.length;_i++)matches[_i-0]=arguments[_i];return matches.splice(-2,2),cb(matches)})},StringWrapper.contains=function(s,substr){return s.indexOf(substr)!=-1},StringWrapper.compare=function(a,b){return a<b?-1:a>b?1:0},StringWrapper}(),StringJoiner=function(){function StringJoiner(parts){void 0===parts&&(parts=[]),this.parts=parts}return StringJoiner.prototype.add=function(part){this.parts.push(part)},StringJoiner.prototype.toString=function(){return this.parts.join("")},StringJoiner}(),NumberWrapper=function(){function NumberWrapper(){}return NumberWrapper.toFixed=function(n,fractionDigits){return n.toFixed(fractionDigits)},NumberWrapper.equal=function(a,b){return a===b},NumberWrapper.parseIntAutoRadix=function(text){var result=parseInt(text);if(isNaN(result))throw new Error("Invalid integer literal when parsing "+text);return result},NumberWrapper.parseInt=function(text,radix){if(10==radix){if(/^(\-|\+)?[0-9]+$/.test(text))return parseInt(text,radix)}else if(16==radix){if(/^(\-|\+)?[0-9ABCDEFabcdef]+$/.test(text))return parseInt(text,radix)}else{var result=parseInt(text,radix);if(!isNaN(result))return result}throw new Error("Invalid integer literal when parsing "+text+" in base "+radix)},Object.defineProperty(NumberWrapper,"NaN",{get:function(){return NaN},enumerable:!0,configurable:!0}),NumberWrapper.isNumeric=function(value){return!isNaN(value-parseFloat(value))},NumberWrapper.isNaN=function(value){return isNaN(value)},NumberWrapper.isInteger=function(value){return Number.isInteger(value)},NumberWrapper}(),TextAst=function(){function TextAst(value,ngContentIndex,sourceSpan){this.value=value,this.ngContentIndex=ngContentIndex,this.sourceSpan=sourceSpan}return TextAst.prototype.visit=function(visitor,context){return visitor.visitText(this,context)},TextAst}(),BoundTextAst=function(){function BoundTextAst(value,ngContentIndex,sourceSpan){this.value=value,this.ngContentIndex=ngContentIndex,this.sourceSpan=sourceSpan}return BoundTextAst.prototype.visit=function(visitor,context){return visitor.visitBoundText(this,context)},BoundTextAst}(),AttrAst=function(){function AttrAst(name,value,sourceSpan){this.name=name,this.value=value,this.sourceSpan=sourceSpan}return AttrAst.prototype.visit=function(visitor,context){return visitor.visitAttr(this,context)},AttrAst}(),BoundElementPropertyAst=function(){function BoundElementPropertyAst(name,type,securityContext,value,unit,sourceSpan){this.name=name,this.type=type,this.securityContext=securityContext,this.value=value,this.unit=unit,this.sourceSpan=sourceSpan}return BoundElementPropertyAst.prototype.visit=function(visitor,context){return visitor.visitElementProperty(this,context)},BoundElementPropertyAst}(),BoundEventAst=function(){function BoundEventAst(name,target,handler,sourceSpan){this.name=name,this.target=target,this.handler=handler,this.sourceSpan=sourceSpan}return BoundEventAst.prototype.visit=function(visitor,context){return visitor.visitEvent(this,context)},Object.defineProperty(BoundEventAst.prototype,"fullName",{get:function(){return isPresent(this.target)?this.target+":"+this.name:this.name},enumerable:!0,configurable:!0}),BoundEventAst}(),ReferenceAst=function(){function ReferenceAst(name,value,sourceSpan){this.name=name,this.value=value,this.sourceSpan=sourceSpan}return ReferenceAst.prototype.visit=function(visitor,context){return visitor.visitReference(this,context)},ReferenceAst}(),VariableAst=function(){function VariableAst(name,value,sourceSpan){this.name=name,this.value=value,this.sourceSpan=sourceSpan}return VariableAst.prototype.visit=function(visitor,context){return visitor.visitVariable(this,context)},VariableAst}(),ElementAst=function(){function ElementAst(name,attrs,inputs,outputs,references,directives,providers,hasViewContainer,children,ngContentIndex,sourceSpan){this.name=name,this.attrs=attrs,this.inputs=inputs,this.outputs=outputs,this.references=references,this.directives=directives,this.providers=providers,this.hasViewContainer=hasViewContainer,this.children=children,this.ngContentIndex=ngContentIndex,this.sourceSpan=sourceSpan}return ElementAst.prototype.visit=function(visitor,context){return visitor.visitElement(this,context)},ElementAst}(),EmbeddedTemplateAst=function(){function EmbeddedTemplateAst(attrs,outputs,references,variables,directives,providers,hasViewContainer,children,ngContentIndex,sourceSpan){this.attrs=attrs,this.outputs=outputs,this.references=references,this.variables=variables,this.directives=directives,this.providers=providers,this.hasViewContainer=hasViewContainer,this.children=children,this.ngContentIndex=ngContentIndex,this.sourceSpan=sourceSpan}return EmbeddedTemplateAst.prototype.visit=function(visitor,context){return visitor.visitEmbeddedTemplate(this,context)},EmbeddedTemplateAst}(),BoundDirectivePropertyAst=function(){function BoundDirectivePropertyAst(directiveName,templateName,value,sourceSpan){this.directiveName=directiveName,this.templateName=templateName,this.value=value,this.sourceSpan=sourceSpan}return BoundDirectivePropertyAst.prototype.visit=function(visitor,context){return visitor.visitDirectiveProperty(this,context)},BoundDirectivePropertyAst}(),DirectiveAst=function(){function DirectiveAst(directive,inputs,hostProperties,hostEvents,sourceSpan){this.directive=directive,this.inputs=inputs,this.hostProperties=hostProperties,this.hostEvents=hostEvents,this.sourceSpan=sourceSpan}return DirectiveAst.prototype.visit=function(visitor,context){return visitor.visitDirective(this,context)},DirectiveAst}(),ProviderAst=function(){function ProviderAst(token,multiProvider,eager,providers,providerType,lifecycleHooks,sourceSpan){this.token=token,this.multiProvider=multiProvider,this.eager=eager,this.providers=providers,this.providerType=providerType,this.lifecycleHooks=lifecycleHooks,this.sourceSpan=sourceSpan}return ProviderAst.prototype.visit=function(visitor,context){return null},ProviderAst}();exports.ProviderAstType,function(ProviderAstType){ProviderAstType[ProviderAstType.PublicService=0]="PublicService",ProviderAstType[ProviderAstType.PrivateService=1]="PrivateService",ProviderAstType[ProviderAstType.Component=2]="Component",ProviderAstType[ProviderAstType.Directive=3]="Directive",ProviderAstType[ProviderAstType.Builtin=4]="Builtin"}(exports.ProviderAstType||(exports.ProviderAstType={}));var NgContentAst=function(){function NgContentAst(index,ngContentIndex,sourceSpan){this.index=index,this.ngContentIndex=ngContentIndex,this.sourceSpan=sourceSpan}return NgContentAst.prototype.visit=function(visitor,context){return visitor.visitNgContent(this,context)},NgContentAst}();exports.PropertyBindingType,function(PropertyBindingType){PropertyBindingType[PropertyBindingType.Property=0]="Property",PropertyBindingType[PropertyBindingType.Attribute=1]="Attribute",PropertyBindingType[PropertyBindingType.Class=2]="Class",PropertyBindingType[PropertyBindingType.Style=3]="Style",PropertyBindingType[PropertyBindingType.Animation=4]="Animation"}(exports.PropertyBindingType||(exports.PropertyBindingType={}));var TagContentType,Map$1=global$1.Map,Set$1=global$1.Set,createMapFromPairs=function(){try{if(1===new Map$1([[1,2]]).size)return function(pairs){return new Map$1(pairs)}}catch(e){}return function(pairs){for(var map=new Map$1,i=0;i<pairs.length;i++){var pair=pairs[i];map.set(pair[0],pair[1])}return map}}(),createMapFromMap=function(){try{if(new Map$1(new Map$1))return function(m){return new Map$1(m)}}catch(e){}return function(m){var map=new Map$1;return m.forEach(function(v,k){map.set(k,v)}),map}}(),_clearValues=function(){return(new Map$1).keys().next?function(m){for(var k,keyIterator=m.keys();!(k=keyIterator.next()).done;)m.set(k.value,null)}:function(m){m.forEach(function(v,k){m.set(k,null)})}}(),_arrayFromMap=function(){try{if((new Map$1).values().next)return function(m,getValues){return getValues?Array.from(m.values()):Array.from(m.keys())}}catch(e){}return function(m,getValues){var res=ListWrapper.createFixedSize(m.size),i=0;return m.forEach(function(v,k){res[i]=getValues?v:k,i++}),res}}(),MapWrapper=function(){function MapWrapper(){}return MapWrapper.clone=function(m){return createMapFromMap(m)},MapWrapper.createFromStringMap=function(stringMap){var result=new Map$1;for(var prop in stringMap)result.set(prop,stringMap[prop]);return result},MapWrapper.toStringMap=function(m){var r={};return m.forEach(function(v,k){return r[k]=v}),r},MapWrapper.createFromPairs=function(pairs){return createMapFromPairs(pairs)},MapWrapper.clearValues=function(m){_clearValues(m)},MapWrapper.iterable=function(m){return m},MapWrapper.keys=function(m){return _arrayFromMap(m,!1)},MapWrapper.values=function(m){return _arrayFromMap(m,!0)},MapWrapper}(),StringMapWrapper=function(){function StringMapWrapper(){}return StringMapWrapper.create=function(){return{}},StringMapWrapper.contains=function(map,key){return map.hasOwnProperty(key)},StringMapWrapper.get=function(map,key){return map.hasOwnProperty(key)?map[key]:void 0},StringMapWrapper.set=function(map,key,value){map[key]=value},StringMapWrapper.keys=function(map){return Object.keys(map)},StringMapWrapper.values=function(map){return Object.keys(map).map(function(k){return map[k]})},StringMapWrapper.isEmpty=function(map){for(var prop in map)return!1;return!0},StringMapWrapper.delete=function(map,key){delete map[key]},StringMapWrapper.forEach=function(map,callback){for(var _i=0,_a=Object.keys(map);_i<_a.length;_i++){var k=_a[_i];callback(map[k],k)}},StringMapWrapper.merge=function(m1,m2){for(var m={},_i=0,_a=Object.keys(m1);_i<_a.length;_i++){var k=_a[_i];m[k]=m1[k]}for(var _b=0,_c=Object.keys(m2);_b<_c.length;_b++){var k=_c[_b];m[k]=m2[k]}return m},StringMapWrapper.equals=function(m1,m2){var k1=Object.keys(m1),k2=Object.keys(m2);if(k1.length!=k2.length)return!1;for(var i=0;i<k1.length;i++){var key=k1[i];if(m1[key]!==m2[key])return!1}return!0},StringMapWrapper}(),ListWrapper=function(){function ListWrapper(){}return ListWrapper.createFixedSize=function(size){return new Array(size)},ListWrapper.createGrowableSize=function(size){return new Array(size)},ListWrapper.clone=function(array){return array.slice(0)},ListWrapper.forEachWithIndex=function(array,fn){for(var i=0;i<array.length;i++)fn(array[i],i)},ListWrapper.first=function(array){return array?array[0]:null},ListWrapper.last=function(array){return array&&0!=array.length?array[array.length-1]:null},ListWrapper.indexOf=function(array,value,startIndex){return void 0===startIndex&&(startIndex=0),array.indexOf(value,startIndex)},ListWrapper.contains=function(list,el){return list.indexOf(el)!==-1},ListWrapper.reversed=function(array){var a=ListWrapper.clone(array);return a.reverse()},ListWrapper.concat=function(a,b){return a.concat(b)},ListWrapper.insert=function(list,index,value){list.splice(index,0,value)},ListWrapper.removeAt=function(list,index){var res=list[index];return list.splice(index,1),res},ListWrapper.removeAll=function(list,items){for(var i=0;i<items.length;++i){var index=list.indexOf(items[i]);list.splice(index,1)}},ListWrapper.remove=function(list,el){var index=list.indexOf(el);return index>-1&&(list.splice(index,1),!0)},ListWrapper.clear=function(list){list.length=0},ListWrapper.isEmpty=function(list){return 0==list.length},ListWrapper.fill=function(list,value,start,end){void 0===start&&(start=0),void 0===end&&(end=null),list.fill(value,start,null===end?list.length:end)},ListWrapper.equals=function(a,b){if(a.length!=b.length)return!1;for(var i=0;i<a.length;++i)if(a[i]!==b[i])return!1;return!0},ListWrapper.slice=function(l,from,to){return void 0===from&&(from=0),void 0===to&&(to=null),l.slice(from,null===to?void 0:to)},ListWrapper.splice=function(l,from,length){return l.splice(from,length)},ListWrapper.sort=function(l,compareFn){isPresent(compareFn)?l.sort(compareFn):l.sort()},ListWrapper.toString=function(l){return l.toString()},ListWrapper.toJSON=function(l){return JSON.stringify(l)},ListWrapper.maximum=function(list,predicate){if(0==list.length)return null;for(var solution=null,maxValue=-(1/0),index=0;index<list.length;index++){var candidate=list[index];if(!isBlank(candidate)){var candidateValue=predicate(candidate);candidateValue>maxValue&&(solution=candidate,maxValue=candidateValue)}}return solution},ListWrapper.flatten=function(list){var target=[];return _flattenArray(list,target),target},ListWrapper.addAll=function(list,source){for(var i=0;i<source.length;i++)list.push(source[i])},ListWrapper}(),createSetFromList=function(){var test=new Set$1([1,2,3]);return 3===test.size?function(lst){return new Set$1(lst)}:function(lst){var res=new Set$1(lst);if(res.size!==lst.length)for(var i=0;i<lst.length;i++)res.add(lst[i]);return res}}(),SetWrapper=function(){function SetWrapper(){}return SetWrapper.createFromList=function(lst){return createSetFromList(lst)},SetWrapper.has=function(s,key){return s.has(key)},SetWrapper.delete=function(m,k){m.delete(k)},SetWrapper}();!function(TagContentType){TagContentType[TagContentType.RAW_TEXT=0]="RAW_TEXT",TagContentType[TagContentType.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",TagContentType[TagContentType.PARSABLE_DATA=2]="PARSABLE_DATA"}(TagContentType||(TagContentType={}));var TypeModifier,NAMED_ENTITIES={Aacute:"Á",aacute:"á",Acirc:"Â",acirc:"â",acute:"´",AElig:"Æ",aelig:"æ",Agrave:"À",agrave:"à",alefsym:"ℵ",Alpha:"Α",alpha:"α",amp:"&",and:"∧",ang:"∠",apos:"'",Aring:"Å",aring:"å",asymp:"≈",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",bdquo:"„",Beta:"Β",beta:"β",brvbar:"¦",bull:"•",cap:"∩",Ccedil:"Ç",ccedil:"ç",cedil:"¸",cent:"¢",Chi:"Χ",chi:"χ",circ:"ˆ",clubs:"♣",cong:"≅",copy:"©",crarr:"↵",cup:"∪",curren:"¤",dagger:"†",Dagger:"‡",darr:"↓",dArr:"⇓",deg:"°",Delta:"Δ",delta:"δ",diams:"♦",divide:"÷",Eacute:"É",eacute:"é",Ecirc:"Ê",ecirc:"ê",Egrave:"È",egrave:"è",empty:"∅",emsp:" ",ensp:" ",Epsilon:"Ε",epsilon:"ε",equiv:"≡",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",exist:"∃",fnof:"ƒ",forall:"∀",frac12:"½",frac14:"¼",frac34:"¾",frasl:"⁄",Gamma:"Γ",gamma:"γ",ge:"≥",gt:">",harr:"↔",hArr:"⇔",hearts:"♥",hellip:"…",Iacute:"Í",iacute:"í",Icirc:"Î",icirc:"î",iexcl:"¡",Igrave:"Ì",igrave:"ì",image:"ℑ",infin:"∞",int:"∫",Iota:"Ι",iota:"ι",iquest:"¿",isin:"∈",Iuml:"Ï",iuml:"ï",Kappa:"Κ",kappa:"κ",Lambda:"Λ",lambda:"λ",lang:"⟨",laquo:"«",larr:"←",lArr:"⇐",lceil:"⌈",ldquo:"“",le:"≤",lfloor:"⌊",lowast:"∗",loz:"◊",lrm:"‎",lsaquo:"‹",lsquo:"‘",lt:"<",macr:"¯",mdash:"—",micro:"µ",middot:"·",minus:"−",Mu:"Μ",mu:"μ",nabla:"∇",nbsp:" ",ndash:"–",ne:"≠",ni:"∋",not:"¬",notin:"∉",nsub:"⊄",Ntilde:"Ñ",ntilde:"ñ",Nu:"Ν",nu:"ν",Oacute:"Ó",oacute:"ó",Ocirc:"Ô",ocirc:"ô",OElig:"Œ",oelig:"œ",Ograve:"Ò",ograve:"ò",oline:"‾",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",oplus:"⊕",or:"∨",ordf:"ª",ordm:"º",Oslash:"Ø",oslash:"ø",Otilde:"Õ",otilde:"õ",otimes:"⊗",Ouml:"Ö",ouml:"ö",para:"¶",permil:"‰",perp:"⊥",Phi:"Φ",phi:"φ",Pi:"Π",pi:"π",piv:"ϖ",plusmn:"±",pound:"£",prime:"′",Prime:"″",prod:"∏",prop:"∝",Psi:"Ψ",psi:"ψ",quot:'"',radic:"√",rang:"⟩",raquo:"»",rarr:"→",rArr:"⇒",rceil:"⌉",rdquo:"”",real:"ℜ",reg:"®",rfloor:"⌋",Rho:"Ρ",rho:"ρ",rlm:"‏",rsaquo:"›",rsquo:"’",sbquo:"‚",Scaron:"Š",scaron:"š",sdot:"⋅",sect:"§",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sim:"∼",spades:"♠",sub:"⊂",sube:"⊆",sum:"∑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supe:"⊇",szlig:"ß",Tau:"Τ",tau:"τ",there4:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thinsp:" ",THORN:"Þ",thorn:"þ",tilde:"˜",times:"×",trade:"™",Uacute:"Ú",uacute:"ú",uarr:"↑",uArr:"⇑",Ucirc:"Û",ucirc:"û",Ugrave:"Ù",ugrave:"ù",uml:"¨",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",Uuml:"Ü",uuml:"ü",weierp:"℘",Xi:"Ξ",xi:"ξ",Yacute:"Ý",yacute:"ý",yen:"¥",yuml:"ÿ",Yuml:"Ÿ",Zeta:"Ζ",zeta:"ζ",zwj:"‍",zwnj:"‌"},HtmlTagDefinition=function(){function HtmlTagDefinition(_a){var _this=this,_b=void 0===_a?{}:_a,closedByChildren=_b.closedByChildren,requiredParents=_b.requiredParents,implicitNamespacePrefix=_b.implicitNamespacePrefix,_c=_b.contentType,contentType=void 0===_c?TagContentType.PARSABLE_DATA:_c,_d=_b.closedByParent,closedByParent=void 0!==_d&&_d,_e=_b.isVoid,isVoid=void 0!==_e&&_e,_f=_b.ignoreFirstLf,ignoreFirstLf=void 0!==_f&&_f;this.closedByChildren={},this.closedByParent=!1,this.canSelfClose=!1,closedByChildren&&closedByChildren.length>0&&closedByChildren.forEach(function(tagName){return _this.closedByChildren[tagName]=!0}),this.isVoid=isVoid,this.closedByParent=closedByParent||isVoid,requiredParents&&requiredParents.length>0&&(this.requiredParents={},this.parentToAdd=requiredParents[0],requiredParents.forEach(function(tagName){return _this.requiredParents[tagName]=!0})),this.implicitNamespacePrefix=implicitNamespacePrefix,this.contentType=contentType,this.ignoreFirstLf=ignoreFirstLf}return HtmlTagDefinition.prototype.requireExtraParent=function(currentParent){if(!this.requiredParents)return!1;if(!currentParent)return!0;var lcParent=currentParent.toLowerCase();return 1!=this.requiredParents[lcParent]&&"template"!=lcParent},HtmlTagDefinition.prototype.isClosedByChild=function(name){return this.isVoid||name.toLowerCase()in this.closedByChildren},HtmlTagDefinition}(),TAG_DEFINITIONS={base:new HtmlTagDefinition({isVoid:!0}),meta:new HtmlTagDefinition({isVoid:!0}),area:new HtmlTagDefinition({isVoid:!0}),embed:new HtmlTagDefinition({isVoid:!0}),link:new HtmlTagDefinition({isVoid:!0}),img:new HtmlTagDefinition({isVoid:!0}),input:new HtmlTagDefinition({isVoid:!0}),param:new HtmlTagDefinition({isVoid:!0}),hr:new HtmlTagDefinition({isVoid:!0}),br:new HtmlTagDefinition({isVoid:!0}),source:new HtmlTagDefinition({isVoid:!0}),track:new HtmlTagDefinition({isVoid:!0}),wbr:new HtmlTagDefinition({isVoid:!0}),p:new HtmlTagDefinition({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new HtmlTagDefinition({closedByChildren:["tbody","tfoot"]}),tbody:new HtmlTagDefinition({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new HtmlTagDefinition({closedByChildren:["tbody"],closedByParent:!0}),tr:new HtmlTagDefinition({closedByChildren:["tr"],requiredParents:["tbody","tfoot","thead"],closedByParent:!0}),td:new HtmlTagDefinition({closedByChildren:["td","th"],closedByParent:!0}),th:new HtmlTagDefinition({closedByChildren:["td","th"],closedByParent:!0}),col:new HtmlTagDefinition({requiredParents:["colgroup"],isVoid:!0}),svg:new HtmlTagDefinition({implicitNamespacePrefix:"svg"}),math:new HtmlTagDefinition({implicitNamespacePrefix:"math"}),li:new HtmlTagDefinition({closedByChildren:["li"],closedByParent:!0}),dt:new HtmlTagDefinition({closedByChildren:["dt","dd"]}),dd:new HtmlTagDefinition({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new HtmlTagDefinition({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new HtmlTagDefinition({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new HtmlTagDefinition({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new HtmlTagDefinition({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new HtmlTagDefinition({closedByChildren:["optgroup"],closedByParent:!0}),option:new HtmlTagDefinition({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new HtmlTagDefinition({ignoreFirstLf:!0}),listing:new HtmlTagDefinition({ignoreFirstLf:!0}),style:new HtmlTagDefinition({contentType:TagContentType.RAW_TEXT}),script:new HtmlTagDefinition({contentType:TagContentType.RAW_TEXT}),title:new HtmlTagDefinition({contentType:TagContentType.ESCAPABLE_RAW_TEXT}),textarea:new HtmlTagDefinition({contentType:TagContentType.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})},_DEFAULT_TAG_DEFINITION=new HtmlTagDefinition,_EMPTY_ATTR_VALUE="",_SELECTOR_REGEXP=new RegExp("(\\:not\\()|([-\\w]+)|(?:\\.([-\\w]+))|(?:\\[([-\\w*]+)(?:=([^\\]]*))?\\])|(\\))|(\\s*,\\s*)","g"),CssSelector=function(){function CssSelector(){this.element=null,this.classNames=[],this.attrs=[],this.notSelectors=[]}return CssSelector.parse=function(selector){var match,results=[],_addResult=function(res,cssSel){cssSel.notSelectors.length>0&&isBlank(cssSel.element)&&ListWrapper.isEmpty(cssSel.classNames)&&ListWrapper.isEmpty(cssSel.attrs)&&(cssSel.element="*"),res.push(cssSel)},cssSelector=new CssSelector,current=cssSelector,inNot=!1;for(_SELECTOR_REGEXP.lastIndex=0;isPresent(match=_SELECTOR_REGEXP.exec(selector));){if(isPresent(match[1])){if(inNot)throw new Error("Nesting :not is not allowed in a selector");inNot=!0,current=new CssSelector,cssSelector.notSelectors.push(current)}if(isPresent(match[2])&&current.setElement(match[2]),isPresent(match[3])&&current.addClassName(match[3]),isPresent(match[4])&&current.addAttribute(match[4],match[5]),isPresent(match[6])&&(inNot=!1,current=cssSelector),isPresent(match[7])){if(inNot)throw new Error("Multiple selectors in :not are not supported");_addResult(results,cssSelector),cssSelector=current=new CssSelector}}return _addResult(results,cssSelector),results},CssSelector.prototype.isElementSelector=function(){return this.hasElementSelector()&&0==this.classNames.length&&0==this.attrs.length&&0===this.notSelectors.length},CssSelector.prototype.hasElementSelector=function(){return!!this.element},CssSelector.prototype.setElement=function(element){void 0===element&&(element=null),this.element=element},CssSelector.prototype.getMatchingElementTemplate=function(){for(var tagName=this.element||"div",classAttr=this.classNames.length>0?' class="'+this.classNames.join(" ")+'"':"",attrs="",i=0;i<this.attrs.length;i+=2){var attrName=this.attrs[i],attrValue=""!==this.attrs[i+1]?'="'+this.attrs[i+1]+'"':"";attrs+=" "+attrName+attrValue}return getHtmlTagDefinition(tagName).isVoid?"<"+tagName+classAttr+attrs+"/>":"<"+tagName+classAttr+attrs+"></"+tagName+">"},CssSelector.prototype.addAttribute=function(name,value){void 0===value&&(value=_EMPTY_ATTR_VALUE),this.attrs.push(name),value=isPresent(value)?value.toLowerCase():_EMPTY_ATTR_VALUE,this.attrs.push(value)},CssSelector.prototype.addClassName=function(name){this.classNames.push(name.toLowerCase())},CssSelector.prototype.toString=function(){var res="";if(isPresent(this.element)&&(res+=this.element),isPresent(this.classNames))for(var i=0;i<this.classNames.length;i++)res+="."+this.classNames[i];if(isPresent(this.attrs))for(var i=0;i<this.attrs.length;){var attrName=this.attrs[i++],attrValue=this.attrs[i++];res+="["+attrName,attrValue.length>0&&(res+="="+attrValue),res+="]"}return this.notSelectors.forEach(function(notSelector){return res+=":not("+notSelector+")"}),res},CssSelector}(),SelectorMatcher=function(){function SelectorMatcher(){this._elementMap=new Map,this._elementPartialMap=new Map,this._classMap=new Map,this._classPartialMap=new Map,this._attrValueMap=new Map,this._attrValuePartialMap=new Map,this._listContexts=[]}return SelectorMatcher.createNotMatcher=function(notSelectors){var notMatcher=new SelectorMatcher;return notMatcher.addSelectables(notSelectors,null),notMatcher},SelectorMatcher.prototype.addSelectables=function(cssSelectors,callbackCtxt){var listContext=null;cssSelectors.length>1&&(listContext=new SelectorListContext(cssSelectors),this._listContexts.push(listContext));for(var i=0;i<cssSelectors.length;i++)this._addSelectable(cssSelectors[i],callbackCtxt,listContext)},SelectorMatcher.prototype._addSelectable=function(cssSelector,callbackCtxt,listContext){var matcher=this,element=cssSelector.element,classNames=cssSelector.classNames,attrs=cssSelector.attrs,selectable=new SelectorContext(cssSelector,callbackCtxt,listContext);if(isPresent(element)){var isTerminal=0===attrs.length&&0===classNames.length;isTerminal?this._addTerminal(matcher._elementMap,element,selectable):matcher=this._addPartial(matcher._elementPartialMap,element)}if(isPresent(classNames))for(var index=0;index<classNames.length;index++){var isTerminal=0===attrs.length&&index===classNames.length-1,className=classNames[index];isTerminal?this._addTerminal(matcher._classMap,className,selectable):matcher=this._addPartial(matcher._classPartialMap,className)}if(isPresent(attrs))for(var index=0;index<attrs.length;){var isTerminal=index===attrs.length-2,attrName=attrs[index++],attrValue=attrs[index++];if(isTerminal){var terminalMap=matcher._attrValueMap,terminalValuesMap=terminalMap.get(attrName);isBlank(terminalValuesMap)&&(terminalValuesMap=new Map,terminalMap.set(attrName,terminalValuesMap)),this._addTerminal(terminalValuesMap,attrValue,selectable)}else{var parttialMap=matcher._attrValuePartialMap,partialValuesMap=parttialMap.get(attrName);isBlank(partialValuesMap)&&(partialValuesMap=new Map,parttialMap.set(attrName,partialValuesMap)),matcher=this._addPartial(partialValuesMap,attrValue)}}},SelectorMatcher.prototype._addTerminal=function(map,name,selectable){var terminalList=map.get(name);isBlank(terminalList)&&(terminalList=[],map.set(name,terminalList)),terminalList.push(selectable)},SelectorMatcher.prototype._addPartial=function(map,name){var matcher=map.get(name);return isBlank(matcher)&&(matcher=new SelectorMatcher,map.set(name,matcher)),matcher},SelectorMatcher.prototype.match=function(cssSelector,matchedCallback){for(var result=!1,element=cssSelector.element,classNames=cssSelector.classNames,attrs=cssSelector.attrs,i=0;i<this._listContexts.length;i++)this._listContexts[i].alreadyMatched=!1;if(result=this._matchTerminal(this._elementMap,element,cssSelector,matchedCallback)||result,result=this._matchPartial(this._elementPartialMap,element,cssSelector,matchedCallback)||result,isPresent(classNames))for(var index=0;index<classNames.length;index++){var className=classNames[index];result=this._matchTerminal(this._classMap,className,cssSelector,matchedCallback)||result,result=this._matchPartial(this._classPartialMap,className,cssSelector,matchedCallback)||result}if(isPresent(attrs))for(var index=0;index<attrs.length;){var attrName=attrs[index++],attrValue=attrs[index++],terminalValuesMap=this._attrValueMap.get(attrName);StringWrapper.equals(attrValue,_EMPTY_ATTR_VALUE)||(result=this._matchTerminal(terminalValuesMap,_EMPTY_ATTR_VALUE,cssSelector,matchedCallback)||result),result=this._matchTerminal(terminalValuesMap,attrValue,cssSelector,matchedCallback)||result;var partialValuesMap=this._attrValuePartialMap.get(attrName);StringWrapper.equals(attrValue,_EMPTY_ATTR_VALUE)||(result=this._matchPartial(partialValuesMap,_EMPTY_ATTR_VALUE,cssSelector,matchedCallback)||result),result=this._matchPartial(partialValuesMap,attrValue,cssSelector,matchedCallback)||result}return result},SelectorMatcher.prototype._matchTerminal=function(map,name,cssSelector,matchedCallback){if(isBlank(map)||isBlank(name))return!1;var selectables=map.get(name),starSelectables=map.get("*");if(isPresent(starSelectables)&&(selectables=selectables.concat(starSelectables)),isBlank(selectables))return!1;for(var selectable,result=!1,index=0;index<selectables.length;index++)selectable=selectables[index],result=selectable.finalize(cssSelector,matchedCallback)||result;return result},SelectorMatcher.prototype._matchPartial=function(map,name,cssSelector,matchedCallback){if(isBlank(map)||isBlank(name))return!1;var nestedSelector=map.get(name);return!isBlank(nestedSelector)&&nestedSelector.match(cssSelector,matchedCallback)},SelectorMatcher}(),SelectorListContext=function(){function SelectorListContext(selectors){this.selectors=selectors,this.alreadyMatched=!1}return SelectorListContext}(),SelectorContext=function(){function SelectorContext(selector,cbContext,listContext){this.selector=selector,this.cbContext=cbContext,this.listContext=listContext,this.notSelectors=selector.notSelectors}return SelectorContext.prototype.finalize=function(cssSelector,callback){var result=!0;if(this.notSelectors.length>0&&(isBlank(this.listContext)||!this.listContext.alreadyMatched)){var notMatcher=SelectorMatcher.createNotMatcher(this.notSelectors);result=!notMatcher.match(cssSelector,null)}return result&&isPresent(callback)&&(isBlank(this.listContext)||!this.listContext.alreadyMatched)&&(isPresent(this.listContext)&&(this.listContext.alreadyMatched=!0),callback(this.selector,this.cbContext)),result},SelectorContext}(),__extends$2=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)};!function(TypeModifier){TypeModifier[TypeModifier.Const=0]="Const"}(TypeModifier||(TypeModifier={}));var BuiltinTypeName,Type$1=function(){function Type(modifiers){void 0===modifiers&&(modifiers=null),this.modifiers=modifiers,isBlank(modifiers)&&(this.modifiers=[])}return Type.prototype.hasModifier=function(modifier){return this.modifiers.indexOf(modifier)!==-1},Type}();!function(BuiltinTypeName){BuiltinTypeName[BuiltinTypeName.Dynamic=0]="Dynamic",BuiltinTypeName[BuiltinTypeName.Bool=1]="Bool",BuiltinTypeName[BuiltinTypeName.String=2]="String",BuiltinTypeName[BuiltinTypeName.Int=3]="Int",BuiltinTypeName[BuiltinTypeName.Number=4]="Number",BuiltinTypeName[BuiltinTypeName.Function=5]="Function"}(BuiltinTypeName||(BuiltinTypeName={}));var BinaryOperator,BuiltinType=function(_super){function BuiltinType(name,modifiers){void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.name=name}return __extends$2(BuiltinType,_super),BuiltinType.prototype.visitType=function(visitor,context){return visitor.visitBuiltintType(this,context)},BuiltinType}(Type$1),ExternalType=function(_super){function ExternalType(value,typeParams,modifiers){void 0===typeParams&&(typeParams=null),void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.value=value,this.typeParams=typeParams}return __extends$2(ExternalType,_super),ExternalType.prototype.visitType=function(visitor,context){return visitor.visitExternalType(this,context)},ExternalType}(Type$1),ArrayType=function(_super){function ArrayType(of,modifiers){void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.of=of}return __extends$2(ArrayType,_super),ArrayType.prototype.visitType=function(visitor,context){return visitor.visitArrayType(this,context)},ArrayType}(Type$1),MapType=function(_super){function MapType(valueType,modifiers){void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.valueType=valueType}return __extends$2(MapType,_super),MapType.prototype.visitType=function(visitor,context){return visitor.visitMapType(this,context)},MapType}(Type$1),DYNAMIC_TYPE=new BuiltinType(BuiltinTypeName.Dynamic),BOOL_TYPE=new BuiltinType(BuiltinTypeName.Bool),NUMBER_TYPE=(new BuiltinType(BuiltinTypeName.Int),new BuiltinType(BuiltinTypeName.Number)),STRING_TYPE=new BuiltinType(BuiltinTypeName.String),FUNCTION_TYPE=new BuiltinType(BuiltinTypeName.Function);!function(BinaryOperator){BinaryOperator[BinaryOperator.Equals=0]="Equals",BinaryOperator[BinaryOperator.NotEquals=1]="NotEquals",BinaryOperator[BinaryOperator.Identical=2]="Identical",
BinaryOperator[BinaryOperator.NotIdentical=3]="NotIdentical",BinaryOperator[BinaryOperator.Minus=4]="Minus",BinaryOperator[BinaryOperator.Plus=5]="Plus",BinaryOperator[BinaryOperator.Divide=6]="Divide",BinaryOperator[BinaryOperator.Multiply=7]="Multiply",BinaryOperator[BinaryOperator.Modulo=8]="Modulo",BinaryOperator[BinaryOperator.And=9]="And",BinaryOperator[BinaryOperator.Or=10]="Or",BinaryOperator[BinaryOperator.Lower=11]="Lower",BinaryOperator[BinaryOperator.LowerEquals=12]="LowerEquals",BinaryOperator[BinaryOperator.Bigger=13]="Bigger",BinaryOperator[BinaryOperator.BiggerEquals=14]="BiggerEquals"}(BinaryOperator||(BinaryOperator={}));var BuiltinVar,Expression=function(){function Expression(type){this.type=type}return Expression.prototype.prop=function(name){return new ReadPropExpr(this,name)},Expression.prototype.key=function(index,type){return void 0===type&&(type=null),new ReadKeyExpr(this,index,type)},Expression.prototype.callMethod=function(name,params){return new InvokeMethodExpr(this,name,params)},Expression.prototype.callFn=function(params){return new InvokeFunctionExpr(this,params)},Expression.prototype.instantiate=function(params,type){return void 0===type&&(type=null),new InstantiateExpr(this,params,type)},Expression.prototype.conditional=function(trueCase,falseCase){return void 0===falseCase&&(falseCase=null),new ConditionalExpr(this,trueCase,falseCase)},Expression.prototype.equals=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Equals,this,rhs)},Expression.prototype.notEquals=function(rhs){return new BinaryOperatorExpr(BinaryOperator.NotEquals,this,rhs)},Expression.prototype.identical=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Identical,this,rhs)},Expression.prototype.notIdentical=function(rhs){return new BinaryOperatorExpr(BinaryOperator.NotIdentical,this,rhs)},Expression.prototype.minus=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Minus,this,rhs)},Expression.prototype.plus=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Plus,this,rhs)},Expression.prototype.divide=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Divide,this,rhs)},Expression.prototype.multiply=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Multiply,this,rhs)},Expression.prototype.modulo=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Modulo,this,rhs)},Expression.prototype.and=function(rhs){return new BinaryOperatorExpr(BinaryOperator.And,this,rhs)},Expression.prototype.or=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Or,this,rhs)},Expression.prototype.lower=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Lower,this,rhs)},Expression.prototype.lowerEquals=function(rhs){return new BinaryOperatorExpr(BinaryOperator.LowerEquals,this,rhs)},Expression.prototype.bigger=function(rhs){return new BinaryOperatorExpr(BinaryOperator.Bigger,this,rhs)},Expression.prototype.biggerEquals=function(rhs){return new BinaryOperatorExpr(BinaryOperator.BiggerEquals,this,rhs)},Expression.prototype.isBlank=function(){return this.equals(NULL_EXPR)},Expression.prototype.cast=function(type){return new CastExpr(this,type)},Expression.prototype.toStmt=function(){return new ExpressionStatement(this)},Expression}();!function(BuiltinVar){BuiltinVar[BuiltinVar.This=0]="This",BuiltinVar[BuiltinVar.Super=1]="Super",BuiltinVar[BuiltinVar.CatchError=2]="CatchError",BuiltinVar[BuiltinVar.CatchStack=3]="CatchStack"}(BuiltinVar||(BuiltinVar={}));var BuiltinMethod,ReadVarExpr=function(_super){function ReadVarExpr(name,type){void 0===type&&(type=null),_super.call(this,type),isString(name)?(this.name=name,this.builtin=null):(this.name=null,this.builtin=name)}return __extends$2(ReadVarExpr,_super),ReadVarExpr.prototype.visitExpression=function(visitor,context){return visitor.visitReadVarExpr(this,context)},ReadVarExpr.prototype.set=function(value){return new WriteVarExpr(this.name,value)},ReadVarExpr}(Expression),WriteVarExpr=function(_super){function WriteVarExpr(name,value,type){void 0===type&&(type=null),_super.call(this,isPresent(type)?type:value.type),this.name=name,this.value=value}return __extends$2(WriteVarExpr,_super),WriteVarExpr.prototype.visitExpression=function(visitor,context){return visitor.visitWriteVarExpr(this,context)},WriteVarExpr.prototype.toDeclStmt=function(type,modifiers){return void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),new DeclareVarStmt(this.name,this.value,type,modifiers)},WriteVarExpr}(Expression),WriteKeyExpr=function(_super){function WriteKeyExpr(receiver,index,value,type){void 0===type&&(type=null),_super.call(this,isPresent(type)?type:value.type),this.receiver=receiver,this.index=index,this.value=value}return __extends$2(WriteKeyExpr,_super),WriteKeyExpr.prototype.visitExpression=function(visitor,context){return visitor.visitWriteKeyExpr(this,context)},WriteKeyExpr}(Expression),WritePropExpr=function(_super){function WritePropExpr(receiver,name,value,type){void 0===type&&(type=null),_super.call(this,isPresent(type)?type:value.type),this.receiver=receiver,this.name=name,this.value=value}return __extends$2(WritePropExpr,_super),WritePropExpr.prototype.visitExpression=function(visitor,context){return visitor.visitWritePropExpr(this,context)},WritePropExpr}(Expression);!function(BuiltinMethod){BuiltinMethod[BuiltinMethod.ConcatArray=0]="ConcatArray",BuiltinMethod[BuiltinMethod.SubscribeObservable=1]="SubscribeObservable",BuiltinMethod[BuiltinMethod.Bind=2]="Bind"}(BuiltinMethod||(BuiltinMethod={}));var StmtModifier,InvokeMethodExpr=function(_super){function InvokeMethodExpr(receiver,method,args,type){void 0===type&&(type=null),_super.call(this,type),this.receiver=receiver,this.args=args,isString(method)?(this.name=method,this.builtin=null):(this.name=null,this.builtin=method)}return __extends$2(InvokeMethodExpr,_super),InvokeMethodExpr.prototype.visitExpression=function(visitor,context){return visitor.visitInvokeMethodExpr(this,context)},InvokeMethodExpr}(Expression),InvokeFunctionExpr=function(_super){function InvokeFunctionExpr(fn,args,type){void 0===type&&(type=null),_super.call(this,type),this.fn=fn,this.args=args}return __extends$2(InvokeFunctionExpr,_super),InvokeFunctionExpr.prototype.visitExpression=function(visitor,context){return visitor.visitInvokeFunctionExpr(this,context)},InvokeFunctionExpr}(Expression),InstantiateExpr=function(_super){function InstantiateExpr(classExpr,args,type){_super.call(this,type),this.classExpr=classExpr,this.args=args}return __extends$2(InstantiateExpr,_super),InstantiateExpr.prototype.visitExpression=function(visitor,context){return visitor.visitInstantiateExpr(this,context)},InstantiateExpr}(Expression),LiteralExpr=function(_super){function LiteralExpr(value,type){void 0===type&&(type=null),_super.call(this,type),this.value=value}return __extends$2(LiteralExpr,_super),LiteralExpr.prototype.visitExpression=function(visitor,context){return visitor.visitLiteralExpr(this,context)},LiteralExpr}(Expression),ExternalExpr=function(_super){function ExternalExpr(value,type,typeParams){void 0===type&&(type=null),void 0===typeParams&&(typeParams=null),_super.call(this,type),this.value=value,this.typeParams=typeParams}return __extends$2(ExternalExpr,_super),ExternalExpr.prototype.visitExpression=function(visitor,context){return visitor.visitExternalExpr(this,context)},ExternalExpr}(Expression),ConditionalExpr=function(_super){function ConditionalExpr(condition,trueCase,falseCase,type){void 0===falseCase&&(falseCase=null),void 0===type&&(type=null),_super.call(this,isPresent(type)?type:trueCase.type),this.condition=condition,this.falseCase=falseCase,this.trueCase=trueCase}return __extends$2(ConditionalExpr,_super),ConditionalExpr.prototype.visitExpression=function(visitor,context){return visitor.visitConditionalExpr(this,context)},ConditionalExpr}(Expression),NotExpr=function(_super){function NotExpr(condition){_super.call(this,BOOL_TYPE),this.condition=condition}return __extends$2(NotExpr,_super),NotExpr.prototype.visitExpression=function(visitor,context){return visitor.visitNotExpr(this,context)},NotExpr}(Expression),CastExpr=function(_super){function CastExpr(value,type){_super.call(this,type),this.value=value}return __extends$2(CastExpr,_super),CastExpr.prototype.visitExpression=function(visitor,context){return visitor.visitCastExpr(this,context)},CastExpr}(Expression),FnParam=function(){function FnParam(name,type){void 0===type&&(type=null),this.name=name,this.type=type}return FnParam}(),FunctionExpr=function(_super){function FunctionExpr(params,statements,type){void 0===type&&(type=null),_super.call(this,type),this.params=params,this.statements=statements}return __extends$2(FunctionExpr,_super),FunctionExpr.prototype.visitExpression=function(visitor,context){return visitor.visitFunctionExpr(this,context)},FunctionExpr.prototype.toDeclStmt=function(name,modifiers){return void 0===modifiers&&(modifiers=null),new DeclareFunctionStmt(name,this.params,this.statements,this.type,modifiers)},FunctionExpr}(Expression),BinaryOperatorExpr=function(_super){function BinaryOperatorExpr(operator,lhs,rhs,type){void 0===type&&(type=null),_super.call(this,isPresent(type)?type:lhs.type),this.operator=operator,this.rhs=rhs,this.lhs=lhs}return __extends$2(BinaryOperatorExpr,_super),BinaryOperatorExpr.prototype.visitExpression=function(visitor,context){return visitor.visitBinaryOperatorExpr(this,context)},BinaryOperatorExpr}(Expression),ReadPropExpr=function(_super){function ReadPropExpr(receiver,name,type){void 0===type&&(type=null),_super.call(this,type),this.receiver=receiver,this.name=name}return __extends$2(ReadPropExpr,_super),ReadPropExpr.prototype.visitExpression=function(visitor,context){return visitor.visitReadPropExpr(this,context)},ReadPropExpr.prototype.set=function(value){return new WritePropExpr(this.receiver,this.name,value)},ReadPropExpr}(Expression),ReadKeyExpr=function(_super){function ReadKeyExpr(receiver,index,type){void 0===type&&(type=null),_super.call(this,type),this.receiver=receiver,this.index=index}return __extends$2(ReadKeyExpr,_super),ReadKeyExpr.prototype.visitExpression=function(visitor,context){return visitor.visitReadKeyExpr(this,context)},ReadKeyExpr.prototype.set=function(value){return new WriteKeyExpr(this.receiver,this.index,value)},ReadKeyExpr}(Expression),LiteralArrayExpr=function(_super){function LiteralArrayExpr(entries,type){void 0===type&&(type=null),_super.call(this,type),this.entries=entries}return __extends$2(LiteralArrayExpr,_super),LiteralArrayExpr.prototype.visitExpression=function(visitor,context){return visitor.visitLiteralArrayExpr(this,context)},LiteralArrayExpr}(Expression),LiteralMapExpr=function(_super){function LiteralMapExpr(entries,type){void 0===type&&(type=null),_super.call(this,type),this.entries=entries,this.valueType=null,isPresent(type)&&(this.valueType=type.valueType)}return __extends$2(LiteralMapExpr,_super),LiteralMapExpr.prototype.visitExpression=function(visitor,context){return visitor.visitLiteralMapExpr(this,context)},LiteralMapExpr}(Expression),THIS_EXPR=new ReadVarExpr(BuiltinVar.This),SUPER_EXPR=new ReadVarExpr(BuiltinVar.Super),NULL_EXPR=(new ReadVarExpr(BuiltinVar.CatchError),new ReadVarExpr(BuiltinVar.CatchStack),new LiteralExpr(null,null));!function(StmtModifier){StmtModifier[StmtModifier.Final=0]="Final",StmtModifier[StmtModifier.Private=1]="Private"}(StmtModifier||(StmtModifier={}));var TokenType,Statement=function(){function Statement(modifiers){void 0===modifiers&&(modifiers=null),this.modifiers=modifiers,isBlank(modifiers)&&(this.modifiers=[])}return Statement.prototype.hasModifier=function(modifier){return this.modifiers.indexOf(modifier)!==-1},Statement}(),DeclareVarStmt=function(_super){function DeclareVarStmt(name,value,type,modifiers){void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.name=name,this.value=value,this.type=isPresent(type)?type:value.type}return __extends$2(DeclareVarStmt,_super),DeclareVarStmt.prototype.visitStatement=function(visitor,context){return visitor.visitDeclareVarStmt(this,context)},DeclareVarStmt}(Statement),DeclareFunctionStmt=function(_super){function DeclareFunctionStmt(name,params,statements,type,modifiers){void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.name=name,this.params=params,this.statements=statements,this.type=type}return __extends$2(DeclareFunctionStmt,_super),DeclareFunctionStmt.prototype.visitStatement=function(visitor,context){return visitor.visitDeclareFunctionStmt(this,context)},DeclareFunctionStmt}(Statement),ExpressionStatement=function(_super){function ExpressionStatement(expr){_super.call(this),this.expr=expr}return __extends$2(ExpressionStatement,_super),ExpressionStatement.prototype.visitStatement=function(visitor,context){return visitor.visitExpressionStmt(this,context)},ExpressionStatement}(Statement),ReturnStatement=function(_super){function ReturnStatement(value){_super.call(this),this.value=value}return __extends$2(ReturnStatement,_super),ReturnStatement.prototype.visitStatement=function(visitor,context){return visitor.visitReturnStmt(this,context)},ReturnStatement}(Statement),AbstractClassPart=function(){function AbstractClassPart(type,modifiers){void 0===type&&(type=null),this.type=type,this.modifiers=modifiers,isBlank(modifiers)&&(this.modifiers=[])}return AbstractClassPart.prototype.hasModifier=function(modifier){return this.modifiers.indexOf(modifier)!==-1},AbstractClassPart}(),ClassField=function(_super){function ClassField(name,type,modifiers){void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),_super.call(this,type,modifiers),this.name=name}return __extends$2(ClassField,_super),ClassField}(AbstractClassPart),ClassMethod=function(_super){function ClassMethod(name,params,body,type,modifiers){void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),_super.call(this,type,modifiers),this.name=name,this.params=params,this.body=body}return __extends$2(ClassMethod,_super),ClassMethod}(AbstractClassPart),ClassGetter=function(_super){function ClassGetter(name,body,type,modifiers){void 0===type&&(type=null),void 0===modifiers&&(modifiers=null),_super.call(this,type,modifiers),this.name=name,this.body=body}return __extends$2(ClassGetter,_super),ClassGetter}(AbstractClassPart),ClassStmt=function(_super){function ClassStmt(name,parent,fields,getters,constructorMethod,methods,modifiers){void 0===modifiers&&(modifiers=null),_super.call(this,modifiers),this.name=name,this.parent=parent,this.fields=fields,this.getters=getters,this.constructorMethod=constructorMethod,this.methods=methods}return __extends$2(ClassStmt,_super),ClassStmt.prototype.visitStatement=function(visitor,context){return visitor.visitDeclareClassStmt(this,context)},ClassStmt}(Statement),IfStmt=function(_super){function IfStmt(condition,trueCase,falseCase){void 0===falseCase&&(falseCase=[]),_super.call(this),this.condition=condition,this.trueCase=trueCase,this.falseCase=falseCase}return __extends$2(IfStmt,_super),IfStmt.prototype.visitStatement=function(visitor,context){return visitor.visitIfStmt(this,context)},IfStmt}(Statement),TryCatchStmt=(function(_super){function CommentStmt(comment){_super.call(this),this.comment=comment}return __extends$2(CommentStmt,_super),CommentStmt.prototype.visitStatement=function(visitor,context){return visitor.visitCommentStmt(this,context)},CommentStmt}(Statement),function(_super){function TryCatchStmt(bodyStmts,catchStmts){_super.call(this),this.bodyStmts=bodyStmts,this.catchStmts=catchStmts}return __extends$2(TryCatchStmt,_super),TryCatchStmt.prototype.visitStatement=function(visitor,context){return visitor.visitTryCatchStmt(this,context)},TryCatchStmt}(Statement)),ThrowStmt=function(_super){function ThrowStmt(error){_super.call(this),this.error=error}return __extends$2(ThrowStmt,_super),ThrowStmt.prototype.visitStatement=function(visitor,context){return visitor.visitThrowStmt(this,context)},ThrowStmt}(Statement),ExpressionTransformer=function(){function ExpressionTransformer(){}return ExpressionTransformer.prototype.visitReadVarExpr=function(ast,context){return ast},ExpressionTransformer.prototype.visitWriteVarExpr=function(expr,context){return new WriteVarExpr(expr.name,expr.value.visitExpression(this,context))},ExpressionTransformer.prototype.visitWriteKeyExpr=function(expr,context){return new WriteKeyExpr(expr.receiver.visitExpression(this,context),expr.index.visitExpression(this,context),expr.value.visitExpression(this,context))},ExpressionTransformer.prototype.visitWritePropExpr=function(expr,context){return new WritePropExpr(expr.receiver.visitExpression(this,context),expr.name,expr.value.visitExpression(this,context))},ExpressionTransformer.prototype.visitInvokeMethodExpr=function(ast,context){var method=isPresent(ast.builtin)?ast.builtin:ast.name;return new InvokeMethodExpr(ast.receiver.visitExpression(this,context),method,this.visitAllExpressions(ast.args,context),ast.type)},ExpressionTransformer.prototype.visitInvokeFunctionExpr=function(ast,context){return new InvokeFunctionExpr(ast.fn.visitExpression(this,context),this.visitAllExpressions(ast.args,context),ast.type)},ExpressionTransformer.prototype.visitInstantiateExpr=function(ast,context){return new InstantiateExpr(ast.classExpr.visitExpression(this,context),this.visitAllExpressions(ast.args,context),ast.type)},ExpressionTransformer.prototype.visitLiteralExpr=function(ast,context){return ast},ExpressionTransformer.prototype.visitExternalExpr=function(ast,context){return ast},ExpressionTransformer.prototype.visitConditionalExpr=function(ast,context){return new ConditionalExpr(ast.condition.visitExpression(this,context),ast.trueCase.visitExpression(this,context),ast.falseCase.visitExpression(this,context))},ExpressionTransformer.prototype.visitNotExpr=function(ast,context){return new NotExpr(ast.condition.visitExpression(this,context))},ExpressionTransformer.prototype.visitCastExpr=function(ast,context){return new CastExpr(ast.value.visitExpression(this,context),context)},ExpressionTransformer.prototype.visitFunctionExpr=function(ast,context){return ast},ExpressionTransformer.prototype.visitBinaryOperatorExpr=function(ast,context){return new BinaryOperatorExpr(ast.operator,ast.lhs.visitExpression(this,context),ast.rhs.visitExpression(this,context),ast.type)},ExpressionTransformer.prototype.visitReadPropExpr=function(ast,context){return new ReadPropExpr(ast.receiver.visitExpression(this,context),ast.name,ast.type)},ExpressionTransformer.prototype.visitReadKeyExpr=function(ast,context){return new ReadKeyExpr(ast.receiver.visitExpression(this,context),ast.index.visitExpression(this,context),ast.type)},ExpressionTransformer.prototype.visitLiteralArrayExpr=function(ast,context){return new LiteralArrayExpr(this.visitAllExpressions(ast.entries,context))},ExpressionTransformer.prototype.visitLiteralMapExpr=function(ast,context){var _this=this;return new LiteralMapExpr(ast.entries.map(function(entry){return[entry[0],entry[1].visitExpression(_this,context)]}))},ExpressionTransformer.prototype.visitAllExpressions=function(exprs,context){var _this=this;return exprs.map(function(expr){return expr.visitExpression(_this,context)})},ExpressionTransformer.prototype.visitDeclareVarStmt=function(stmt,context){return new DeclareVarStmt(stmt.name,stmt.value.visitExpression(this,context),stmt.type,stmt.modifiers)},ExpressionTransformer.prototype.visitDeclareFunctionStmt=function(stmt,context){return stmt},ExpressionTransformer.prototype.visitExpressionStmt=function(stmt,context){return new ExpressionStatement(stmt.expr.visitExpression(this,context))},ExpressionTransformer.prototype.visitReturnStmt=function(stmt,context){return new ReturnStatement(stmt.value.visitExpression(this,context))},ExpressionTransformer.prototype.visitDeclareClassStmt=function(stmt,context){return stmt},ExpressionTransformer.prototype.visitIfStmt=function(stmt,context){return new IfStmt(stmt.condition.visitExpression(this,context),this.visitAllStatements(stmt.trueCase,context),this.visitAllStatements(stmt.falseCase,context))},ExpressionTransformer.prototype.visitTryCatchStmt=function(stmt,context){return new TryCatchStmt(this.visitAllStatements(stmt.bodyStmts,context),this.visitAllStatements(stmt.catchStmts,context))},ExpressionTransformer.prototype.visitThrowStmt=function(stmt,context){return new ThrowStmt(stmt.error.visitExpression(this,context))},ExpressionTransformer.prototype.visitCommentStmt=function(stmt,context){return stmt},ExpressionTransformer.prototype.visitAllStatements=function(stmts,context){var _this=this;return stmts.map(function(stmt){return stmt.visitStatement(_this,context)})},ExpressionTransformer}(),RecursiveExpressionVisitor=function(){function RecursiveExpressionVisitor(){}return RecursiveExpressionVisitor.prototype.visitReadVarExpr=function(ast,context){return ast},RecursiveExpressionVisitor.prototype.visitWriteVarExpr=function(expr,context){return expr.value.visitExpression(this,context),expr},RecursiveExpressionVisitor.prototype.visitWriteKeyExpr=function(expr,context){return expr.receiver.visitExpression(this,context),expr.index.visitExpression(this,context),expr.value.visitExpression(this,context),expr},RecursiveExpressionVisitor.prototype.visitWritePropExpr=function(expr,context){return expr.receiver.visitExpression(this,context),expr.value.visitExpression(this,context),expr},RecursiveExpressionVisitor.prototype.visitInvokeMethodExpr=function(ast,context){return ast.receiver.visitExpression(this,context),this.visitAllExpressions(ast.args,context),ast},RecursiveExpressionVisitor.prototype.visitInvokeFunctionExpr=function(ast,context){return ast.fn.visitExpression(this,context),this.visitAllExpressions(ast.args,context),ast},RecursiveExpressionVisitor.prototype.visitInstantiateExpr=function(ast,context){return ast.classExpr.visitExpression(this,context),this.visitAllExpressions(ast.args,context),ast},RecursiveExpressionVisitor.prototype.visitLiteralExpr=function(ast,context){return ast},RecursiveExpressionVisitor.prototype.visitExternalExpr=function(ast,context){return ast},RecursiveExpressionVisitor.prototype.visitConditionalExpr=function(ast,context){return ast.condition.visitExpression(this,context),ast.trueCase.visitExpression(this,context),ast.falseCase.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitNotExpr=function(ast,context){return ast.condition.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitCastExpr=function(ast,context){return ast.value.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitFunctionExpr=function(ast,context){return ast},RecursiveExpressionVisitor.prototype.visitBinaryOperatorExpr=function(ast,context){return ast.lhs.visitExpression(this,context),ast.rhs.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitReadPropExpr=function(ast,context){return ast.receiver.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitReadKeyExpr=function(ast,context){return ast.receiver.visitExpression(this,context),ast.index.visitExpression(this,context),ast},RecursiveExpressionVisitor.prototype.visitLiteralArrayExpr=function(ast,context){return this.visitAllExpressions(ast.entries,context),ast},RecursiveExpressionVisitor.prototype.visitLiteralMapExpr=function(ast,context){var _this=this;return ast.entries.forEach(function(entry){return entry[1].visitExpression(_this,context)}),ast},RecursiveExpressionVisitor.prototype.visitAllExpressions=function(exprs,context){var _this=this;exprs.forEach(function(expr){return expr.visitExpression(_this,context)})},RecursiveExpressionVisitor.prototype.visitDeclareVarStmt=function(stmt,context){return stmt.value.visitExpression(this,context),stmt},RecursiveExpressionVisitor.prototype.visitDeclareFunctionStmt=function(stmt,context){return stmt},RecursiveExpressionVisitor.prototype.visitExpressionStmt=function(stmt,context){return stmt.expr.visitExpression(this,context),stmt},RecursiveExpressionVisitor.prototype.visitReturnStmt=function(stmt,context){return stmt.value.visitExpression(this,context),stmt},RecursiveExpressionVisitor.prototype.visitDeclareClassStmt=function(stmt,context){return stmt},RecursiveExpressionVisitor.prototype.visitIfStmt=function(stmt,context){return stmt.condition.visitExpression(this,context),this.visitAllStatements(stmt.trueCase,context),this.visitAllStatements(stmt.falseCase,context),stmt},RecursiveExpressionVisitor.prototype.visitTryCatchStmt=function(stmt,context){return this.visitAllStatements(stmt.bodyStmts,context),this.visitAllStatements(stmt.catchStmts,context),stmt},RecursiveExpressionVisitor.prototype.visitThrowStmt=function(stmt,context){return stmt.error.visitExpression(this,context),stmt},RecursiveExpressionVisitor.prototype.visitCommentStmt=function(stmt,context){return stmt},RecursiveExpressionVisitor.prototype.visitAllStatements=function(stmts,context){var _this=this;stmts.forEach(function(stmt){return stmt.visitStatement(_this,context)})},RecursiveExpressionVisitor}(),_ReplaceVariableTransformer=function(_super){function _ReplaceVariableTransformer(_varName,_newValue){_super.call(this),this._varName=_varName,this._newValue=_newValue}return __extends$2(_ReplaceVariableTransformer,_super),_ReplaceVariableTransformer.prototype.visitReadVarExpr=function(ast,context){return ast.name==this._varName?this._newValue:ast},_ReplaceVariableTransformer}(ExpressionTransformer),_VariableFinder=function(_super){function _VariableFinder(){_super.apply(this,arguments),this.varNames=new Set}return __extends$2(_VariableFinder,_super),_VariableFinder.prototype.visitReadVarExpr=function(ast,context){return this.varNames.add(ast.name),null},_VariableFinder}(RecursiveExpressionVisitor),MODULE_SUFFIX="",CAMEL_CASE_REGEXP=/([A-Z])/g,ValueTransformer=function(){function ValueTransformer(){}return ValueTransformer.prototype.visitArray=function(arr,context){var _this=this;return arr.map(function(value){return visitValue(value,_this,context)})},ValueTransformer.prototype.visitStringMap=function(map,context){var _this=this,result={};return StringMapWrapper.forEach(map,function(value,key){result[key]=visitValue(value,_this,context)}),result},ValueTransformer.prototype.visitPrimitive=function(value,context){return value},ValueTransformer.prototype.visitOther=function(value,context){return value},ValueTransformer}(),SyncAsyncResult=function(){function SyncAsyncResult(syncResult,asyncResult){void 0===asyncResult&&(asyncResult=null),this.syncResult=syncResult,this.asyncResult=asyncResult,asyncResult||(this.asyncResult=Promise.resolve(syncResult))}return SyncAsyncResult}(),__extends$1=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},HOST_REG_EXP=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/,CompileMetadataWithIdentifier=function(){function CompileMetadataWithIdentifier(){}return Object.defineProperty(CompileMetadataWithIdentifier.prototype,"identifier",{get:function(){return unimplemented()},enumerable:!0,configurable:!0}),CompileMetadataWithIdentifier}(),CompileAnimationEntryMetadata=function(){function CompileAnimationEntryMetadata(name,definitions){void 0===name&&(name=null),void 0===definitions&&(definitions=null),this.name=name,this.definitions=definitions}return CompileAnimationEntryMetadata}(),CompileAnimationStateMetadata=function(){function CompileAnimationStateMetadata(){}return CompileAnimationStateMetadata}(),CompileAnimationStateDeclarationMetadata=function(_super){function CompileAnimationStateDeclarationMetadata(stateNameExpr,styles){_super.call(this),this.stateNameExpr=stateNameExpr,this.styles=styles}return __extends$1(CompileAnimationStateDeclarationMetadata,_super),CompileAnimationStateDeclarationMetadata}(CompileAnimationStateMetadata),CompileAnimationStateTransitionMetadata=function(_super){function CompileAnimationStateTransitionMetadata(stateChangeExpr,steps){_super.call(this),this.stateChangeExpr=stateChangeExpr,this.steps=steps}return __extends$1(CompileAnimationStateTransitionMetadata,_super),CompileAnimationStateTransitionMetadata}(CompileAnimationStateMetadata),CompileAnimationMetadata=function(){function CompileAnimationMetadata(){}return CompileAnimationMetadata}(),CompileAnimationKeyframesSequenceMetadata=function(_super){function CompileAnimationKeyframesSequenceMetadata(steps){void 0===steps&&(steps=[]),_super.call(this),this.steps=steps}return __extends$1(CompileAnimationKeyframesSequenceMetadata,_super),CompileAnimationKeyframesSequenceMetadata}(CompileAnimationMetadata),CompileAnimationStyleMetadata=function(_super){function CompileAnimationStyleMetadata(offset,styles){void 0===styles&&(styles=null),_super.call(this),this.offset=offset,this.styles=styles}return __extends$1(CompileAnimationStyleMetadata,_super),CompileAnimationStyleMetadata}(CompileAnimationMetadata),CompileAnimationAnimateMetadata=function(_super){function CompileAnimationAnimateMetadata(timings,styles){void 0===timings&&(timings=0),void 0===styles&&(styles=null),_super.call(this),this.timings=timings,this.styles=styles}return __extends$1(CompileAnimationAnimateMetadata,_super),CompileAnimationAnimateMetadata}(CompileAnimationMetadata),CompileAnimationWithStepsMetadata=function(_super){function CompileAnimationWithStepsMetadata(steps){void 0===steps&&(steps=null),_super.call(this),this.steps=steps}return __extends$1(CompileAnimationWithStepsMetadata,_super),CompileAnimationWithStepsMetadata}(CompileAnimationMetadata),CompileAnimationSequenceMetadata=function(_super){function CompileAnimationSequenceMetadata(steps){void 0===steps&&(steps=null),_super.call(this,steps)}return __extends$1(CompileAnimationSequenceMetadata,_super),CompileAnimationSequenceMetadata}(CompileAnimationWithStepsMetadata),CompileAnimationGroupMetadata=function(_super){function CompileAnimationGroupMetadata(steps){void 0===steps&&(steps=null),_super.call(this,steps)}return __extends$1(CompileAnimationGroupMetadata,_super),CompileAnimationGroupMetadata}(CompileAnimationWithStepsMetadata),CompileIdentifierMetadata=function(){function CompileIdentifierMetadata(_a){var _b=void 0===_a?{}:_a,reference=_b.reference,name=_b.name,moduleUrl=_b.moduleUrl,prefix=_b.prefix,value=_b.value;this.reference=reference,this.name=name,this.prefix=prefix,this.moduleUrl=moduleUrl,this.value=value}return Object.defineProperty(CompileIdentifierMetadata.prototype,"identifier",{get:function(){return this},enumerable:!0,configurable:!0}),CompileIdentifierMetadata}(),CompileDiDependencyMetadata=function(){function CompileDiDependencyMetadata(_a){var _b=void 0===_a?{}:_a,isAttribute=_b.isAttribute,isSelf=_b.isSelf,isHost=_b.isHost,isSkipSelf=_b.isSkipSelf,isOptional=_b.isOptional,isValue=_b.isValue,query=_b.query,viewQuery=_b.viewQuery,token=_b.token,value=_b.value;this.isAttribute=normalizeBool(isAttribute),this.isSelf=normalizeBool(isSelf),this.isHost=normalizeBool(isHost),this.isSkipSelf=normalizeBool(isSkipSelf),this.isOptional=normalizeBool(isOptional),this.isValue=normalizeBool(isValue),this.query=query,this.viewQuery=viewQuery,this.token=token,this.value=value}return CompileDiDependencyMetadata}(),CompileProviderMetadata=function(){function CompileProviderMetadata(_a){var token=_a.token,useClass=_a.useClass,useValue=_a.useValue,useExisting=_a.useExisting,useFactory=_a.useFactory,deps=_a.deps,multi=_a.multi;this.token=token,this.useClass=useClass,this.useValue=useValue,this.useExisting=useExisting,this.useFactory=useFactory,this.deps=normalizeBlank(deps),this.multi=normalizeBool(multi)}return CompileProviderMetadata}(),CompileFactoryMetadata=function(_super){function CompileFactoryMetadata(_a){var reference=_a.reference,name=_a.name,moduleUrl=_a.moduleUrl,prefix=_a.prefix,diDeps=_a.diDeps,value=_a.value;_super.call(this,{reference:reference,name:name,prefix:prefix,moduleUrl:moduleUrl,value:value}),this.diDeps=_normalizeArray(diDeps);
}return __extends$1(CompileFactoryMetadata,_super),CompileFactoryMetadata}(CompileIdentifierMetadata),CompileTokenMetadata=function(){function CompileTokenMetadata(_a){var value=_a.value,identifier=_a.identifier,identifierIsInstance=_a.identifierIsInstance;this.value=value,this.identifier=identifier,this.identifierIsInstance=normalizeBool(identifierIsInstance)}return Object.defineProperty(CompileTokenMetadata.prototype,"reference",{get:function(){return isPresent(this.identifier)?this.identifier.reference:this.value},enumerable:!0,configurable:!0}),Object.defineProperty(CompileTokenMetadata.prototype,"name",{get:function(){return isPresent(this.value)?sanitizeIdentifier(this.value):this.identifier.name},enumerable:!0,configurable:!0}),CompileTokenMetadata}(),CompileTypeMetadata=function(_super){function CompileTypeMetadata(_a){var _b=void 0===_a?{}:_a,reference=_b.reference,name=_b.name,moduleUrl=_b.moduleUrl,prefix=_b.prefix,isHost=_b.isHost,value=_b.value,diDeps=_b.diDeps,lifecycleHooks=_b.lifecycleHooks;_super.call(this,{reference:reference,name:name,moduleUrl:moduleUrl,prefix:prefix,value:value}),this.isHost=normalizeBool(isHost),this.diDeps=_normalizeArray(diDeps),this.lifecycleHooks=_normalizeArray(lifecycleHooks)}return __extends$1(CompileTypeMetadata,_super),CompileTypeMetadata}(CompileIdentifierMetadata),CompileQueryMetadata=function(){function CompileQueryMetadata(_a){var _b=void 0===_a?{}:_a,selectors=_b.selectors,descendants=_b.descendants,first=_b.first,propertyName=_b.propertyName,read=_b.read;this.selectors=selectors,this.descendants=normalizeBool(descendants),this.first=normalizeBool(first),this.propertyName=propertyName,this.read=read}return CompileQueryMetadata}(),CompileStylesheetMetadata=function(){function CompileStylesheetMetadata(_a){var _b=void 0===_a?{}:_a,moduleUrl=_b.moduleUrl,styles=_b.styles,styleUrls=_b.styleUrls;this.moduleUrl=moduleUrl,this.styles=_normalizeArray(styles),this.styleUrls=_normalizeArray(styleUrls)}return CompileStylesheetMetadata}(),CompileTemplateMetadata=function(){function CompileTemplateMetadata(_a){var _b=void 0===_a?{}:_a,encapsulation=_b.encapsulation,template=_b.template,templateUrl=_b.templateUrl,styles=_b.styles,styleUrls=_b.styleUrls,externalStylesheets=_b.externalStylesheets,animations=_b.animations,ngContentSelectors=_b.ngContentSelectors,interpolation=_b.interpolation;if(this.encapsulation=encapsulation,this.template=template,this.templateUrl=templateUrl,this.styles=_normalizeArray(styles),this.styleUrls=_normalizeArray(styleUrls),this.externalStylesheets=_normalizeArray(externalStylesheets),this.animations=isPresent(animations)?ListWrapper.flatten(animations):[],this.ngContentSelectors=isPresent(ngContentSelectors)?ngContentSelectors:[],isPresent(interpolation)&&2!=interpolation.length)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=interpolation}return CompileTemplateMetadata}(),CompileDirectiveMetadata=function(){function CompileDirectiveMetadata(_a){var _b=void 0===_a?{}:_a,type=_b.type,isComponent=_b.isComponent,selector=_b.selector,exportAs=_b.exportAs,changeDetection=_b.changeDetection,inputs=_b.inputs,outputs=_b.outputs,hostListeners=_b.hostListeners,hostProperties=_b.hostProperties,hostAttributes=_b.hostAttributes,providers=_b.providers,viewProviders=_b.viewProviders,queries=_b.queries,viewQueries=_b.viewQueries,entryComponents=_b.entryComponents,template=_b.template;this.type=type,this.isComponent=isComponent,this.selector=selector,this.exportAs=exportAs,this.changeDetection=changeDetection,this.inputs=inputs,this.outputs=outputs,this.hostListeners=hostListeners,this.hostProperties=hostProperties,this.hostAttributes=hostAttributes,this.providers=_normalizeArray(providers),this.viewProviders=_normalizeArray(viewProviders),this.queries=_normalizeArray(queries),this.viewQueries=_normalizeArray(viewQueries),this.entryComponents=_normalizeArray(entryComponents),this.template=template}return CompileDirectiveMetadata.create=function(_a){var _b=void 0===_a?{}:_a,type=_b.type,isComponent=_b.isComponent,selector=_b.selector,exportAs=_b.exportAs,changeDetection=_b.changeDetection,inputs=_b.inputs,outputs=_b.outputs,host=_b.host,providers=_b.providers,viewProviders=_b.viewProviders,queries=_b.queries,viewQueries=_b.viewQueries,entryComponents=_b.entryComponents,template=_b.template,hostListeners={},hostProperties={},hostAttributes={};isPresent(host)&&StringMapWrapper.forEach(host,function(value,key){var matches=key.match(HOST_REG_EXP);null===matches?hostAttributes[key]=value:isPresent(matches[1])?hostProperties[matches[1]]=value:isPresent(matches[2])&&(hostListeners[matches[2]]=value)});var inputsMap={};isPresent(inputs)&&inputs.forEach(function(bindConfig){var parts=splitAtColon(bindConfig,[bindConfig,bindConfig]);inputsMap[parts[0]]=parts[1]});var outputsMap={};return isPresent(outputs)&&outputs.forEach(function(bindConfig){var parts=splitAtColon(bindConfig,[bindConfig,bindConfig]);outputsMap[parts[0]]=parts[1]}),new CompileDirectiveMetadata({type:type,isComponent:normalizeBool(isComponent),selector:selector,exportAs:exportAs,changeDetection:changeDetection,inputs:inputsMap,outputs:outputsMap,hostListeners:hostListeners,hostProperties:hostProperties,hostAttributes:hostAttributes,providers:providers,viewProviders:viewProviders,queries:queries,viewQueries:viewQueries,entryComponents:entryComponents,template:template})},Object.defineProperty(CompileDirectiveMetadata.prototype,"identifier",{get:function(){return this.type},enumerable:!0,configurable:!0}),CompileDirectiveMetadata}(),CompilePipeMetadata=function(){function CompilePipeMetadata(_a){var _b=void 0===_a?{}:_a,type=_b.type,name=_b.name,pure=_b.pure;this.type=type,this.name=name,this.pure=normalizeBool(pure)}return Object.defineProperty(CompilePipeMetadata.prototype,"identifier",{get:function(){return this.type},enumerable:!0,configurable:!0}),CompilePipeMetadata}(),CompileNgModuleMetadata=function(){function CompileNgModuleMetadata(_a){var _b=void 0===_a?{}:_a,type=_b.type,providers=_b.providers,declaredDirectives=_b.declaredDirectives,exportedDirectives=_b.exportedDirectives,declaredPipes=_b.declaredPipes,exportedPipes=_b.exportedPipes,entryComponents=_b.entryComponents,bootstrapComponents=_b.bootstrapComponents,importedModules=_b.importedModules,exportedModules=_b.exportedModules,schemas=_b.schemas,transitiveModule=_b.transitiveModule,id=_b.id;this.type=type,this.declaredDirectives=_normalizeArray(declaredDirectives),this.exportedDirectives=_normalizeArray(exportedDirectives),this.declaredPipes=_normalizeArray(declaredPipes),this.exportedPipes=_normalizeArray(exportedPipes),this.providers=_normalizeArray(providers),this.entryComponents=_normalizeArray(entryComponents),this.bootstrapComponents=_normalizeArray(bootstrapComponents),this.importedModules=_normalizeArray(importedModules),this.exportedModules=_normalizeArray(exportedModules),this.schemas=_normalizeArray(schemas),this.id=id,this.transitiveModule=transitiveModule}return Object.defineProperty(CompileNgModuleMetadata.prototype,"identifier",{get:function(){return this.type},enumerable:!0,configurable:!0}),CompileNgModuleMetadata}(),TransitiveCompileNgModuleMetadata=function(){function TransitiveCompileNgModuleMetadata(modules,providers,entryComponents,directives,pipes){var _this=this;this.modules=modules,this.providers=providers,this.entryComponents=entryComponents,this.directives=directives,this.pipes=pipes,this.directivesSet=new Set,this.pipesSet=new Set,directives.forEach(function(dir){return _this.directivesSet.add(dir.type.reference)}),pipes.forEach(function(pipe){return _this.pipesSet.add(pipe.type.reference)})}return TransitiveCompileNgModuleMetadata}(),ProviderMeta=function(){function ProviderMeta(token,_a){var useClass=_a.useClass,useValue=_a.useValue,useExisting=_a.useExisting,useFactory=_a.useFactory,deps=_a.deps,multi=_a.multi;this.token=token,this.useClass=useClass,this.useValue=useValue,this.useExisting=useExisting,this.useFactory=useFactory,this.dependencies=deps,this.multi=!!multi}return ProviderMeta}(),__extends$3=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},ParserError=function(){function ParserError(message,input,errLocation,ctxLocation){this.input=input,this.errLocation=errLocation,this.ctxLocation=ctxLocation,this.message="Parser Error: "+message+" "+errLocation+" ["+input+"] in "+ctxLocation}return ParserError}(),ParseSpan=function(){function ParseSpan(start,end){this.start=start,this.end=end}return ParseSpan}(),AST=function(){function AST(span){this.span=span}return AST.prototype.visit=function(visitor,context){return void 0===context&&(context=null),null},AST.prototype.toString=function(){return"AST"},AST}(),Quote=function(_super){function Quote(span,prefix,uninterpretedExpression,location){_super.call(this,span),this.prefix=prefix,this.uninterpretedExpression=uninterpretedExpression,this.location=location}return __extends$3(Quote,_super),Quote.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitQuote(this,context)},Quote.prototype.toString=function(){return"Quote"},Quote}(AST),EmptyExpr=function(_super){function EmptyExpr(){_super.apply(this,arguments)}return __extends$3(EmptyExpr,_super),EmptyExpr.prototype.visit=function(visitor,context){void 0===context&&(context=null)},EmptyExpr}(AST),ImplicitReceiver=function(_super){function ImplicitReceiver(){_super.apply(this,arguments)}return __extends$3(ImplicitReceiver,_super),ImplicitReceiver.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitImplicitReceiver(this,context)},ImplicitReceiver}(AST),Chain=function(_super){function Chain(span,expressions){_super.call(this,span),this.expressions=expressions}return __extends$3(Chain,_super),Chain.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitChain(this,context)},Chain}(AST),Conditional=function(_super){function Conditional(span,condition,trueExp,falseExp){_super.call(this,span),this.condition=condition,this.trueExp=trueExp,this.falseExp=falseExp}return __extends$3(Conditional,_super),Conditional.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitConditional(this,context)},Conditional}(AST),PropertyRead=function(_super){function PropertyRead(span,receiver,name){_super.call(this,span),this.receiver=receiver,this.name=name}return __extends$3(PropertyRead,_super),PropertyRead.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitPropertyRead(this,context)},PropertyRead}(AST),PropertyWrite=function(_super){function PropertyWrite(span,receiver,name,value){_super.call(this,span),this.receiver=receiver,this.name=name,this.value=value}return __extends$3(PropertyWrite,_super),PropertyWrite.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitPropertyWrite(this,context)},PropertyWrite}(AST),SafePropertyRead=function(_super){function SafePropertyRead(span,receiver,name){_super.call(this,span),this.receiver=receiver,this.name=name}return __extends$3(SafePropertyRead,_super),SafePropertyRead.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitSafePropertyRead(this,context)},SafePropertyRead}(AST),KeyedRead=function(_super){function KeyedRead(span,obj,key){_super.call(this,span),this.obj=obj,this.key=key}return __extends$3(KeyedRead,_super),KeyedRead.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitKeyedRead(this,context)},KeyedRead}(AST),KeyedWrite=function(_super){function KeyedWrite(span,obj,key,value){_super.call(this,span),this.obj=obj,this.key=key,this.value=value}return __extends$3(KeyedWrite,_super),KeyedWrite.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitKeyedWrite(this,context)},KeyedWrite}(AST),BindingPipe=function(_super){function BindingPipe(span,exp,name,args){_super.call(this,span),this.exp=exp,this.name=name,this.args=args}return __extends$3(BindingPipe,_super),BindingPipe.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitPipe(this,context)},BindingPipe}(AST),LiteralPrimitive=function(_super){function LiteralPrimitive(span,value){_super.call(this,span),this.value=value}return __extends$3(LiteralPrimitive,_super),LiteralPrimitive.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitLiteralPrimitive(this,context)},LiteralPrimitive}(AST),LiteralArray=function(_super){function LiteralArray(span,expressions){_super.call(this,span),this.expressions=expressions}return __extends$3(LiteralArray,_super),LiteralArray.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitLiteralArray(this,context)},LiteralArray}(AST),LiteralMap=function(_super){function LiteralMap(span,keys,values){_super.call(this,span),this.keys=keys,this.values=values}return __extends$3(LiteralMap,_super),LiteralMap.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitLiteralMap(this,context)},LiteralMap}(AST),Interpolation=function(_super){function Interpolation(span,strings,expressions){_super.call(this,span),this.strings=strings,this.expressions=expressions}return __extends$3(Interpolation,_super),Interpolation.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitInterpolation(this,context)},Interpolation}(AST),Binary=function(_super){function Binary(span,operation,left,right){_super.call(this,span),this.operation=operation,this.left=left,this.right=right}return __extends$3(Binary,_super),Binary.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitBinary(this,context)},Binary}(AST),PrefixNot=function(_super){function PrefixNot(span,expression){_super.call(this,span),this.expression=expression}return __extends$3(PrefixNot,_super),PrefixNot.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitPrefixNot(this,context)},PrefixNot}(AST),MethodCall=function(_super){function MethodCall(span,receiver,name,args){_super.call(this,span),this.receiver=receiver,this.name=name,this.args=args}return __extends$3(MethodCall,_super),MethodCall.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitMethodCall(this,context)},MethodCall}(AST),SafeMethodCall=function(_super){function SafeMethodCall(span,receiver,name,args){_super.call(this,span),this.receiver=receiver,this.name=name,this.args=args}return __extends$3(SafeMethodCall,_super),SafeMethodCall.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitSafeMethodCall(this,context)},SafeMethodCall}(AST),FunctionCall=function(_super){function FunctionCall(span,target,args){_super.call(this,span),this.target=target,this.args=args}return __extends$3(FunctionCall,_super),FunctionCall.prototype.visit=function(visitor,context){return void 0===context&&(context=null),visitor.visitFunctionCall(this,context)},FunctionCall}(AST),ASTWithSource=function(_super){function ASTWithSource(ast,source,location,errors){_super.call(this,new ParseSpan(0,isBlank(source)?0:source.length)),this.ast=ast,this.source=source,this.location=location,this.errors=errors}return __extends$3(ASTWithSource,_super),ASTWithSource.prototype.visit=function(visitor,context){return void 0===context&&(context=null),this.ast.visit(visitor,context)},ASTWithSource.prototype.toString=function(){return this.source+" in "+this.location},ASTWithSource}(AST),TemplateBinding=function(){function TemplateBinding(key,keyIsVar,name,expression){this.key=key,this.keyIsVar=keyIsVar,this.name=name,this.expression=expression}return TemplateBinding}(),RecursiveAstVisitor=function(){function RecursiveAstVisitor(){}return RecursiveAstVisitor.prototype.visitBinary=function(ast,context){return ast.left.visit(this),ast.right.visit(this),null},RecursiveAstVisitor.prototype.visitChain=function(ast,context){return this.visitAll(ast.expressions,context)},RecursiveAstVisitor.prototype.visitConditional=function(ast,context){return ast.condition.visit(this),ast.trueExp.visit(this),ast.falseExp.visit(this),null},RecursiveAstVisitor.prototype.visitPipe=function(ast,context){return ast.exp.visit(this),this.visitAll(ast.args,context),null},RecursiveAstVisitor.prototype.visitFunctionCall=function(ast,context){return ast.target.visit(this),this.visitAll(ast.args,context),null},RecursiveAstVisitor.prototype.visitImplicitReceiver=function(ast,context){return null},RecursiveAstVisitor.prototype.visitInterpolation=function(ast,context){return this.visitAll(ast.expressions,context)},RecursiveAstVisitor.prototype.visitKeyedRead=function(ast,context){return ast.obj.visit(this),ast.key.visit(this),null},RecursiveAstVisitor.prototype.visitKeyedWrite=function(ast,context){return ast.obj.visit(this),ast.key.visit(this),ast.value.visit(this),null},RecursiveAstVisitor.prototype.visitLiteralArray=function(ast,context){return this.visitAll(ast.expressions,context)},RecursiveAstVisitor.prototype.visitLiteralMap=function(ast,context){return this.visitAll(ast.values,context)},RecursiveAstVisitor.prototype.visitLiteralPrimitive=function(ast,context){return null},RecursiveAstVisitor.prototype.visitMethodCall=function(ast,context){return ast.receiver.visit(this),this.visitAll(ast.args,context)},RecursiveAstVisitor.prototype.visitPrefixNot=function(ast,context){return ast.expression.visit(this),null},RecursiveAstVisitor.prototype.visitPropertyRead=function(ast,context){return ast.receiver.visit(this),null},RecursiveAstVisitor.prototype.visitPropertyWrite=function(ast,context){return ast.receiver.visit(this),ast.value.visit(this),null},RecursiveAstVisitor.prototype.visitSafePropertyRead=function(ast,context){return ast.receiver.visit(this),null},RecursiveAstVisitor.prototype.visitSafeMethodCall=function(ast,context){return ast.receiver.visit(this),this.visitAll(ast.args,context)},RecursiveAstVisitor.prototype.visitAll=function(asts,context){var _this=this;return asts.forEach(function(ast){return ast.visit(_this,context)}),null},RecursiveAstVisitor.prototype.visitQuote=function(ast,context){return null},RecursiveAstVisitor}(),$EOF=0,$TAB=9,$LF=10,$VTAB=11,$FF=12,$CR=13,$SPACE=32,$BANG=33,$DQ=34,$HASH=35,$$=36,$PERCENT=37,$AMPERSAND=38,$SQ=39,$LPAREN=40,$RPAREN=41,$STAR=42,$PLUS=43,$COMMA=44,$MINUS=45,$PERIOD=46,$SLASH=47,$COLON=58,$SEMICOLON=59,$LT=60,$EQ=61,$GT=62,$QUESTION=63,$0=48,$9=57,$A=65,$E=69,$F=70,$X=88,$Z=90,$LBRACKET=91,$BACKSLASH=92,$RBRACKET=93,$CARET=94,$_=95,$a=97,$e=101,$f=102,$n=110,$r=114,$t=116,$u=117,$v=118,$x=120,$z=122,$LBRACE=123,$BAR=124,$RBRACE=125,$NBSP=160,$BT=96,INTERPOLATION_BLACKLIST_REGEXPS=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//],InterpolationConfig=function(){function InterpolationConfig(start,end){this.start=start,this.end=end}return InterpolationConfig.fromArray=function(markers){return markers?(assertInterpolationSymbols("interpolation",markers),new InterpolationConfig(markers[0],markers[1])):DEFAULT_INTERPOLATION_CONFIG},InterpolationConfig}(),DEFAULT_INTERPOLATION_CONFIG=new InterpolationConfig("{{","}}");!function(TokenType){TokenType[TokenType.Character=0]="Character",TokenType[TokenType.Identifier=1]="Identifier",TokenType[TokenType.Keyword=2]="Keyword",TokenType[TokenType.String=3]="String",TokenType[TokenType.Operator=4]="Operator",TokenType[TokenType.Number=5]="Number",TokenType[TokenType.Error=6]="Error"}(TokenType||(TokenType={}));var ParseErrorLevel,KEYWORDS=["var","let","null","undefined","true","false","if","else","this"],Lexer=function(){function Lexer(){}return Lexer.prototype.tokenize=function(text){for(var scanner=new _Scanner(text),tokens=[],token=scanner.scanToken();null!=token;)tokens.push(token),token=scanner.scanToken();return tokens},Lexer.decorators=[{type:_angular_core.Injectable}],Lexer.ctorParameters=[],Lexer}(),Token=function(){function Token(index,type,numValue,strValue){this.index=index,this.type=type,this.numValue=numValue,this.strValue=strValue}return Token.prototype.isCharacter=function(code){return this.type==TokenType.Character&&this.numValue==code},Token.prototype.isNumber=function(){return this.type==TokenType.Number},Token.prototype.isString=function(){return this.type==TokenType.String},Token.prototype.isOperator=function(operater){return this.type==TokenType.Operator&&this.strValue==operater},Token.prototype.isIdentifier=function(){return this.type==TokenType.Identifier},Token.prototype.isKeyword=function(){return this.type==TokenType.Keyword},Token.prototype.isKeywordLet=function(){return this.type==TokenType.Keyword&&"let"==this.strValue},Token.prototype.isKeywordNull=function(){return this.type==TokenType.Keyword&&"null"==this.strValue},Token.prototype.isKeywordUndefined=function(){return this.type==TokenType.Keyword&&"undefined"==this.strValue},Token.prototype.isKeywordTrue=function(){return this.type==TokenType.Keyword&&"true"==this.strValue},Token.prototype.isKeywordFalse=function(){return this.type==TokenType.Keyword&&"false"==this.strValue},Token.prototype.isKeywordThis=function(){return this.type==TokenType.Keyword&&"this"==this.strValue},Token.prototype.isError=function(){return this.type==TokenType.Error},Token.prototype.toNumber=function(){return this.type==TokenType.Number?this.numValue:-1},Token.prototype.toString=function(){switch(this.type){case TokenType.Character:case TokenType.Identifier:case TokenType.Keyword:case TokenType.Operator:case TokenType.String:case TokenType.Error:return this.strValue;case TokenType.Number:return this.numValue.toString();default:return null}},Token}(),EOF=new Token((-1),TokenType.Character,0,""),_Scanner=function(){function _Scanner(input){this.input=input,this.peek=0,this.index=-1,this.length=input.length,this.advance()}return _Scanner.prototype.advance=function(){this.peek=++this.index>=this.length?$EOF:StringWrapper.charCodeAt(this.input,this.index)},_Scanner.prototype.scanToken=function(){for(var input=this.input,length=this.length,peek=this.peek,index=this.index;peek<=$SPACE;){if(++index>=length){peek=$EOF;break}peek=StringWrapper.charCodeAt(input,index)}if(this.peek=peek,this.index=index,index>=length)return null;if(isIdentifierStart(peek))return this.scanIdentifier();if(isDigit(peek))return this.scanNumber(index);var start=index;switch(peek){case $PERIOD:return this.advance(),isDigit(this.peek)?this.scanNumber(start):newCharacterToken(start,$PERIOD);case $LPAREN:case $RPAREN:case $LBRACE:case $RBRACE:case $LBRACKET:case $RBRACKET:case $COMMA:case $COLON:case $SEMICOLON:return this.scanCharacter(start,peek);case $SQ:case $DQ:return this.scanString();case $HASH:case $PLUS:case $MINUS:case $STAR:case $SLASH:case $PERCENT:case $CARET:return this.scanOperator(start,StringWrapper.fromCharCode(peek));case $QUESTION:return this.scanComplexOperator(start,"?",$PERIOD,".");case $LT:case $GT:return this.scanComplexOperator(start,StringWrapper.fromCharCode(peek),$EQ,"=");case $BANG:case $EQ:return this.scanComplexOperator(start,StringWrapper.fromCharCode(peek),$EQ,"=",$EQ,"=");case $AMPERSAND:return this.scanComplexOperator(start,"&",$AMPERSAND,"&");case $BAR:return this.scanComplexOperator(start,"|",$BAR,"|");case $NBSP:for(;isWhitespace(this.peek);)this.advance();return this.scanToken()}return this.advance(),this.error("Unexpected character ["+StringWrapper.fromCharCode(peek)+"]",0)},_Scanner.prototype.scanCharacter=function(start,code){return this.advance(),newCharacterToken(start,code)},_Scanner.prototype.scanOperator=function(start,str){return this.advance(),newOperatorToken(start,str)},_Scanner.prototype.scanComplexOperator=function(start,one,twoCode,two,threeCode,three){this.advance();var str=one;return this.peek==twoCode&&(this.advance(),str+=two),isPresent(threeCode)&&this.peek==threeCode&&(this.advance(),str+=three),newOperatorToken(start,str)},_Scanner.prototype.scanIdentifier=function(){var start=this.index;for(this.advance();isIdentifierPart(this.peek);)this.advance();var str=this.input.substring(start,this.index);return KEYWORDS.indexOf(str)>-1?newKeywordToken(start,str):newIdentifierToken(start,str)},_Scanner.prototype.scanNumber=function(start){var simple=this.index===start;for(this.advance();;){if(isDigit(this.peek));else if(this.peek==$PERIOD)simple=!1;else{if(!isExponentStart(this.peek))break;if(this.advance(),isExponentSign(this.peek)&&this.advance(),!isDigit(this.peek))return this.error("Invalid exponent",-1);simple=!1}this.advance()}var str=this.input.substring(start,this.index),value=simple?NumberWrapper.parseIntAutoRadix(str):parseFloat(str);return newNumberToken(start,value)},_Scanner.prototype.scanString=function(){var start=this.index,quote=this.peek;this.advance();for(var buffer,marker=this.index,input=this.input;this.peek!=quote;)if(this.peek==$BACKSLASH){null==buffer&&(buffer=new StringJoiner),buffer.add(input.substring(marker,this.index)),this.advance();var unescapedCode;if(this.peek==$u){var hex=input.substring(this.index+1,this.index+5);try{unescapedCode=NumberWrapper.parseInt(hex,16)}catch(e){return this.error("Invalid unicode escape [\\u"+hex+"]",0)}for(var i=0;i<5;i++)this.advance()}else unescapedCode=unescape(this.peek),this.advance();buffer.add(StringWrapper.fromCharCode(unescapedCode)),marker=this.index}else{if(this.peek==$EOF)return this.error("Unterminated quote",0);this.advance()}var last=input.substring(marker,this.index);this.advance();var unescaped=last;return null!=buffer&&(buffer.add(last),unescaped=buffer.toString()),newStringToken(start,unescaped)},_Scanner.prototype.error=function(message,offset){var position=this.index+offset;return newErrorToken(position,"Lexer Error: "+message+" at column "+position+" in expression ["+this.input+"]")},_Scanner}(),SplitInterpolation=function(){function SplitInterpolation(strings,expressions){this.strings=strings,this.expressions=expressions}return SplitInterpolation}(),TemplateBindingParseResult=function(){function TemplateBindingParseResult(templateBindings,warnings,errors){this.templateBindings=templateBindings,this.warnings=warnings,this.errors=errors}return TemplateBindingParseResult}(),Parser=function(){function Parser(_lexer){this._lexer=_lexer,this.errors=[]}return Parser.prototype.parseAction=function(input,location,interpolationConfig){void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG),this._checkNoInterpolation(input,location,interpolationConfig);var tokens=this._lexer.tokenize(this._stripComments(input)),ast=new _ParseAST(input,location,tokens,(!0),this.errors).parseChain();return new ASTWithSource(ast,input,location,this.errors)},Parser.prototype.parseBinding=function(input,location,interpolationConfig){void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var ast=this._parseBindingAst(input,location,interpolationConfig);return new ASTWithSource(ast,input,location,this.errors)},Parser.prototype.parseSimpleBinding=function(input,location,interpolationConfig){void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var ast=this._parseBindingAst(input,location,interpolationConfig);return SimpleExpressionChecker.check(ast)||this._reportError("Host binding expression can only contain field access and constants",input,location),new ASTWithSource(ast,input,location,this.errors)},Parser.prototype._reportError=function(message,input,errLocation,ctxLocation){this.errors.push(new ParserError(message,input,errLocation,ctxLocation))},Parser.prototype._parseBindingAst=function(input,location,interpolationConfig){var quote=this._parseQuote(input,location);if(isPresent(quote))return quote;this._checkNoInterpolation(input,location,interpolationConfig);var tokens=this._lexer.tokenize(this._stripComments(input));return new _ParseAST(input,location,tokens,(!1),this.errors).parseChain()},Parser.prototype._parseQuote=function(input,location){if(isBlank(input))return null;var prefixSeparatorIndex=input.indexOf(":");if(prefixSeparatorIndex==-1)return null;var prefix=input.substring(0,prefixSeparatorIndex).trim();if(!isIdentifier(prefix))return null;var uninterpretedExpression=input.substring(prefixSeparatorIndex+1);return new Quote(new ParseSpan(0,input.length),prefix,uninterpretedExpression,location)},Parser.prototype.parseTemplateBindings=function(input,location){var tokens=this._lexer.tokenize(input);return new _ParseAST(input,location,tokens,(!1),this.errors).parseTemplateBindings()},Parser.prototype.parseInterpolation=function(input,location,interpolationConfig){void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var split=this.splitInterpolation(input,location,interpolationConfig);if(null==split)return null;for(var expressions=[],i=0;i<split.expressions.length;++i){var tokens=this._lexer.tokenize(this._stripComments(split.expressions[i])),ast=new _ParseAST(input,location,tokens,(!1),this.errors).parseChain();expressions.push(ast)}return new ASTWithSource(new Interpolation(new ParseSpan(0,isBlank(input)?0:input.length),split.strings,expressions),input,location,this.errors)},Parser.prototype.splitInterpolation=function(input,location,interpolationConfig){void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var regexp=_createInterpolateRegExp(interpolationConfig),parts=StringWrapper.split(input,regexp);if(parts.length<=1)return null;for(var strings=[],expressions=[],i=0;i<parts.length;i++){var part=parts[i];i%2===0?strings.push(part):part.trim().length>0?expressions.push(part):this._reportError("Blank expressions are not allowed in interpolated strings",input,"at column "+this._findInterpolationErrorColumn(parts,i,interpolationConfig)+" in",location)}return new SplitInterpolation(strings,expressions)},Parser.prototype.wrapLiteralPrimitive=function(input,location){return new ASTWithSource(new LiteralPrimitive(new ParseSpan(0,isBlank(input)?0:input.length),input),input,location,this.errors)},Parser.prototype._stripComments=function(input){var i=this._commentStart(input);return isPresent(i)?input.substring(0,i).trim():input},Parser.prototype._commentStart=function(input){for(var outerQuote=null,i=0;i<input.length-1;i++){var char=StringWrapper.charCodeAt(input,i),nextChar=StringWrapper.charCodeAt(input,i+1);if(char===$SLASH&&nextChar==$SLASH&&isBlank(outerQuote))return i;outerQuote===char?outerQuote=null:isBlank(outerQuote)&&isQuote(char)&&(outerQuote=char)}return null},Parser.prototype._checkNoInterpolation=function(input,location,interpolationConfig){var regexp=_createInterpolateRegExp(interpolationConfig),parts=StringWrapper.split(input,regexp);parts.length>1&&this._reportError("Got interpolation ("+interpolationConfig.start+interpolationConfig.end+") where expression was expected",input,"at column "+this._findInterpolationErrorColumn(parts,1,interpolationConfig)+" in",location)},Parser.prototype._findInterpolationErrorColumn=function(parts,partInErrIdx,interpolationConfig){for(var errLocation="",j=0;j<partInErrIdx;j++)errLocation+=j%2===0?parts[j]:""+interpolationConfig.start+parts[j]+interpolationConfig.end;return errLocation.length},Parser.decorators=[{type:_angular_core.Injectable}],Parser.ctorParameters=[{type:Lexer}],Parser}(),_ParseAST=function(){function _ParseAST(input,location,tokens,parseAction,errors){this.input=input,this.location=location,this.tokens=tokens,this.parseAction=parseAction,this.errors=errors,this.rparensExpected=0,this.rbracketsExpected=0,this.rbracesExpected=0,this.index=0}return _ParseAST.prototype.peek=function(offset){var i=this.index+offset;return i<this.tokens.length?this.tokens[i]:EOF},Object.defineProperty(_ParseAST.prototype,"next",{get:function(){return this.peek(0)},enumerable:!0,configurable:!0
}),Object.defineProperty(_ParseAST.prototype,"inputIndex",{get:function(){return this.index<this.tokens.length?this.next.index:this.input.length},enumerable:!0,configurable:!0}),_ParseAST.prototype.span=function(start){return new ParseSpan(start,this.inputIndex)},_ParseAST.prototype.advance=function(){this.index++},_ParseAST.prototype.optionalCharacter=function(code){return!!this.next.isCharacter(code)&&(this.advance(),!0)},_ParseAST.prototype.peekKeywordLet=function(){return this.next.isKeywordLet()},_ParseAST.prototype.expectCharacter=function(code){this.optionalCharacter(code)||this.error("Missing expected "+StringWrapper.fromCharCode(code))},_ParseAST.prototype.optionalOperator=function(op){return!!this.next.isOperator(op)&&(this.advance(),!0)},_ParseAST.prototype.expectOperator=function(operator){this.optionalOperator(operator)||this.error("Missing expected operator "+operator)},_ParseAST.prototype.expectIdentifierOrKeyword=function(){var n=this.next;return n.isIdentifier()||n.isKeyword()?(this.advance(),n.toString()):(this.error("Unexpected token "+n+", expected identifier or keyword"),"")},_ParseAST.prototype.expectIdentifierOrKeywordOrString=function(){var n=this.next;return n.isIdentifier()||n.isKeyword()||n.isString()?(this.advance(),n.toString()):(this.error("Unexpected token "+n+", expected identifier, keyword, or string"),"")},_ParseAST.prototype.parseChain=function(){for(var exprs=[],start=this.inputIndex;this.index<this.tokens.length;){var expr=this.parsePipe();if(exprs.push(expr),this.optionalCharacter($SEMICOLON))for(this.parseAction||this.error("Binding expression cannot contain chained expression");this.optionalCharacter($SEMICOLON););else this.index<this.tokens.length&&this.error("Unexpected token '"+this.next+"'")}return 0==exprs.length?new EmptyExpr(this.span(start)):1==exprs.length?exprs[0]:new Chain(this.span(start),exprs)},_ParseAST.prototype.parsePipe=function(){var result=this.parseExpression();if(this.optionalOperator("|")){this.parseAction&&this.error("Cannot have a pipe in an action expression");do{for(var name=this.expectIdentifierOrKeyword(),args=[];this.optionalCharacter($COLON);)args.push(this.parseExpression());result=new BindingPipe(this.span(result.span.start),result,name,args)}while(this.optionalOperator("|"))}return result},_ParseAST.prototype.parseExpression=function(){return this.parseConditional()},_ParseAST.prototype.parseConditional=function(){var start=this.inputIndex,result=this.parseLogicalOr();if(this.optionalOperator("?")){var yes=this.parsePipe(),no=void 0;if(this.optionalCharacter($COLON))no=this.parsePipe();else{var end=this.inputIndex,expression=this.input.substring(start,end);this.error("Conditional expression "+expression+" requires all 3 expressions"),no=new EmptyExpr(this.span(start))}return new Conditional(this.span(start),result,yes,no)}return result},_ParseAST.prototype.parseLogicalOr=function(){for(var result=this.parseLogicalAnd();this.optionalOperator("||");){var right=this.parseLogicalAnd();result=new Binary(this.span(result.span.start),"||",result,right)}return result},_ParseAST.prototype.parseLogicalAnd=function(){for(var result=this.parseEquality();this.optionalOperator("&&");){var right=this.parseEquality();result=new Binary(this.span(result.span.start),"&&",result,right)}return result},_ParseAST.prototype.parseEquality=function(){for(var result=this.parseRelational();this.next.type==TokenType.Operator;){var operator=this.next.strValue;switch(operator){case"==":case"===":case"!=":case"!==":this.advance();var right=this.parseRelational();result=new Binary(this.span(result.span.start),operator,result,right);continue}break}return result},_ParseAST.prototype.parseRelational=function(){for(var result=this.parseAdditive();this.next.type==TokenType.Operator;){var operator=this.next.strValue;switch(operator){case"<":case">":case"<=":case">=":this.advance();var right=this.parseAdditive();result=new Binary(this.span(result.span.start),operator,result,right);continue}break}return result},_ParseAST.prototype.parseAdditive=function(){for(var result=this.parseMultiplicative();this.next.type==TokenType.Operator;){var operator=this.next.strValue;switch(operator){case"+":case"-":this.advance();var right=this.parseMultiplicative();result=new Binary(this.span(result.span.start),operator,result,right);continue}break}return result},_ParseAST.prototype.parseMultiplicative=function(){for(var result=this.parsePrefix();this.next.type==TokenType.Operator;){var operator=this.next.strValue;switch(operator){case"*":case"%":case"/":this.advance();var right=this.parsePrefix();result=new Binary(this.span(result.span.start),operator,result,right);continue}break}return result},_ParseAST.prototype.parsePrefix=function(){if(this.next.type==TokenType.Operator){var start=this.inputIndex,operator=this.next.strValue,result=void 0;switch(operator){case"+":return this.advance(),this.parsePrefix();case"-":return this.advance(),result=this.parsePrefix(),new Binary(this.span(start),operator,new LiteralPrimitive(new ParseSpan(start,start),0),result);case"!":return this.advance(),result=this.parsePrefix(),new PrefixNot(this.span(start),result)}}return this.parseCallChain()},_ParseAST.prototype.parseCallChain=function(){for(var result=this.parsePrimary();;)if(this.optionalCharacter($PERIOD))result=this.parseAccessMemberOrMethodCall(result,!1);else if(this.optionalOperator("?."))result=this.parseAccessMemberOrMethodCall(result,!0);else if(this.optionalCharacter($LBRACKET)){this.rbracketsExpected++;var key=this.parsePipe();if(this.rbracketsExpected--,this.expectCharacter($RBRACKET),this.optionalOperator("=")){var value=this.parseConditional();result=new KeyedWrite(this.span(result.span.start),result,key,value)}else result=new KeyedRead(this.span(result.span.start),result,key)}else{if(!this.optionalCharacter($LPAREN))return result;this.rparensExpected++;var args=this.parseCallArguments();this.rparensExpected--,this.expectCharacter($RPAREN),result=new FunctionCall(this.span(result.span.start),result,args)}},_ParseAST.prototype.parsePrimary=function(){var start=this.inputIndex;if(this.optionalCharacter($LPAREN)){this.rparensExpected++;var result=this.parsePipe();return this.rparensExpected--,this.expectCharacter($RPAREN),result}if(this.next.isKeywordNull())return this.advance(),new LiteralPrimitive(this.span(start),null);if(this.next.isKeywordUndefined())return this.advance(),new LiteralPrimitive(this.span(start),(void 0));if(this.next.isKeywordTrue())return this.advance(),new LiteralPrimitive(this.span(start),(!0));if(this.next.isKeywordFalse())return this.advance(),new LiteralPrimitive(this.span(start),(!1));if(this.next.isKeywordThis())return this.advance(),new ImplicitReceiver(this.span(start));if(this.optionalCharacter($LBRACKET)){this.rbracketsExpected++;var elements=this.parseExpressionList($RBRACKET);return this.rbracketsExpected--,this.expectCharacter($RBRACKET),new LiteralArray(this.span(start),elements)}if(this.next.isCharacter($LBRACE))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMemberOrMethodCall(new ImplicitReceiver(this.span(start)),!1);if(this.next.isNumber()){var value=this.next.toNumber();return this.advance(),new LiteralPrimitive(this.span(start),value)}if(this.next.isString()){var literalValue=this.next.toString();return this.advance(),new LiteralPrimitive(this.span(start),literalValue)}return this.index>=this.tokens.length?(this.error("Unexpected end of expression: "+this.input),new EmptyExpr(this.span(start))):(this.error("Unexpected token "+this.next),new EmptyExpr(this.span(start)))},_ParseAST.prototype.parseExpressionList=function(terminator){var result=[];if(!this.next.isCharacter(terminator))do result.push(this.parsePipe());while(this.optionalCharacter($COMMA));return result},_ParseAST.prototype.parseLiteralMap=function(){var keys=[],values=[],start=this.inputIndex;if(this.expectCharacter($LBRACE),!this.optionalCharacter($RBRACE)){this.rbracesExpected++;do{var key=this.expectIdentifierOrKeywordOrString();keys.push(key),this.expectCharacter($COLON),values.push(this.parsePipe())}while(this.optionalCharacter($COMMA));this.rbracesExpected--,this.expectCharacter($RBRACE)}return new LiteralMap(this.span(start),keys,values)},_ParseAST.prototype.parseAccessMemberOrMethodCall=function(receiver,isSafe){void 0===isSafe&&(isSafe=!1);var start=receiver.span.start,id=this.expectIdentifierOrKeyword();if(this.optionalCharacter($LPAREN)){this.rparensExpected++;var args=this.parseCallArguments();this.expectCharacter($RPAREN),this.rparensExpected--;var span=this.span(start);return isSafe?new SafeMethodCall(span,receiver,id,args):new MethodCall(span,receiver,id,args)}if(isSafe)return this.optionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),new EmptyExpr(this.span(start))):new SafePropertyRead(this.span(start),receiver,id);if(this.optionalOperator("=")){if(!this.parseAction)return this.error("Bindings cannot contain assignments"),new EmptyExpr(this.span(start));var value=this.parseConditional();return new PropertyWrite(this.span(start),receiver,id,value)}return new PropertyRead(this.span(start),receiver,id)},_ParseAST.prototype.parseCallArguments=function(){if(this.next.isCharacter($RPAREN))return[];var positionals=[];do positionals.push(this.parsePipe());while(this.optionalCharacter($COMMA));return positionals},_ParseAST.prototype.expectTemplateBindingKey=function(){var result="",operatorFound=!1;do result+=this.expectIdentifierOrKeywordOrString(),operatorFound=this.optionalOperator("-"),operatorFound&&(result+="-");while(operatorFound);return result.toString()},_ParseAST.prototype.parseTemplateBindings=function(){for(var bindings=[],prefix=null,warnings=[];this.index<this.tokens.length;){var keyIsVar=this.peekKeywordLet();keyIsVar&&this.advance();var key=this.expectTemplateBindingKey();keyIsVar||(null==prefix?prefix=key:key=prefix+key[0].toUpperCase()+key.substring(1)),this.optionalCharacter($COLON);var name=null,expression=null;if(keyIsVar)name=this.optionalOperator("=")?this.expectTemplateBindingKey():"$implicit";else if(this.next!==EOF&&!this.peekKeywordLet()){var start=this.inputIndex,ast=this.parsePipe(),source=this.input.substring(start,this.inputIndex);expression=new ASTWithSource(ast,source,this.location,this.errors)}bindings.push(new TemplateBinding(key,keyIsVar,name,expression)),this.optionalCharacter($SEMICOLON)||this.optionalCharacter($COMMA)}return new TemplateBindingParseResult(bindings,warnings,this.errors)},_ParseAST.prototype.error=function(message,index){void 0===index&&(index=null),this.errors.push(new ParserError(message,this.input,this.locationText(index),this.location)),this.skip()},_ParseAST.prototype.locationText=function(index){return void 0===index&&(index=null),isBlank(index)&&(index=this.index),index<this.tokens.length?"at column "+(this.tokens[index].index+1)+" in":"at the end of the expression"},_ParseAST.prototype.skip=function(){for(var n=this.next;this.index<this.tokens.length&&!n.isCharacter($SEMICOLON)&&(this.rparensExpected<=0||!n.isCharacter($RPAREN))&&(this.rbracesExpected<=0||!n.isCharacter($RBRACE))&&(this.rbracketsExpected<=0||!n.isCharacter($RBRACKET));)this.next.isError()&&this.errors.push(new ParserError(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),n=this.next},_ParseAST}(),SimpleExpressionChecker=function(){function SimpleExpressionChecker(){this.simple=!0}return SimpleExpressionChecker.check=function(ast){var s=new SimpleExpressionChecker;return ast.visit(s),s.simple},SimpleExpressionChecker.prototype.visitImplicitReceiver=function(ast,context){},SimpleExpressionChecker.prototype.visitInterpolation=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitLiteralPrimitive=function(ast,context){},SimpleExpressionChecker.prototype.visitPropertyRead=function(ast,context){},SimpleExpressionChecker.prototype.visitPropertyWrite=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitSafePropertyRead=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitMethodCall=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitSafeMethodCall=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitFunctionCall=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitLiteralArray=function(ast,context){this.visitAll(ast.expressions)},SimpleExpressionChecker.prototype.visitLiteralMap=function(ast,context){this.visitAll(ast.values)},SimpleExpressionChecker.prototype.visitBinary=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitPrefixNot=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitConditional=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitPipe=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitKeyedRead=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitKeyedWrite=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitAll=function(asts){var _this=this;return asts.map(function(node){return node.visit(_this)})},SimpleExpressionChecker.prototype.visitChain=function(ast,context){this.simple=!1},SimpleExpressionChecker.prototype.visitQuote=function(ast,context){this.simple=!1},SimpleExpressionChecker}(),ParseLocation=function(){function ParseLocation(file,offset,line,col){this.file=file,this.offset=offset,this.line=line,this.col=col}return ParseLocation.prototype.toString=function(){return isPresent(this.offset)?this.file.url+"@"+this.line+":"+this.col:this.file.url},ParseLocation}(),ParseSourceFile=function(){function ParseSourceFile(content,url){this.content=content,this.url=url}return ParseSourceFile}(),ParseSourceSpan=function(){function ParseSourceSpan(start,end,details){void 0===details&&(details=null),this.start=start,this.end=end,this.details=details}return ParseSourceSpan.prototype.toString=function(){return this.start.file.content.substring(this.start.offset,this.end.offset)},ParseSourceSpan}();!function(ParseErrorLevel){ParseErrorLevel[ParseErrorLevel.WARNING=0]="WARNING",ParseErrorLevel[ParseErrorLevel.FATAL=1]="FATAL"}(ParseErrorLevel||(ParseErrorLevel={}));var TokenType$1,ParseError=function(){function ParseError(span,msg,level){void 0===level&&(level=ParseErrorLevel.FATAL),this.span=span,this.msg=msg,this.level=level}return ParseError.prototype.toString=function(){var source=this.span.start.file.content,ctxStart=this.span.start.offset,contextStr="",details="";if(isPresent(ctxStart)){ctxStart>source.length-1&&(ctxStart=source.length-1);for(var ctxEnd=ctxStart,ctxLen=0,ctxLines=0;ctxLen<100&&ctxStart>0&&(ctxStart--,ctxLen++,"\n"!=source[ctxStart]||3!=++ctxLines););for(ctxLen=0,ctxLines=0;ctxLen<100&&ctxEnd<source.length-1&&(ctxEnd++,ctxLen++,"\n"!=source[ctxEnd]||3!=++ctxLines););var context=source.substring(ctxStart,this.span.start.offset)+"[ERROR ->]"+source.substring(this.span.start.offset,ctxEnd+1);contextStr=' ("'+context+'")'}return this.span.details&&(details=", "+this.span.details),""+this.msg+contextStr+": "+this.span.start+details},ParseError}(),Text=function(){function Text(value,sourceSpan){this.value=value,this.sourceSpan=sourceSpan}return Text.prototype.visit=function(visitor,context){return visitor.visitText(this,context)},Text}(),Expansion=function(){function Expansion(switchValue,type,cases,sourceSpan,switchValueSourceSpan){this.switchValue=switchValue,this.type=type,this.cases=cases,this.sourceSpan=sourceSpan,this.switchValueSourceSpan=switchValueSourceSpan}return Expansion.prototype.visit=function(visitor,context){return visitor.visitExpansion(this,context)},Expansion}(),ExpansionCase=function(){function ExpansionCase(value,expression,sourceSpan,valueSourceSpan,expSourceSpan){this.value=value,this.expression=expression,this.sourceSpan=sourceSpan,this.valueSourceSpan=valueSourceSpan,this.expSourceSpan=expSourceSpan}return ExpansionCase.prototype.visit=function(visitor,context){return visitor.visitExpansionCase(this,context)},ExpansionCase}(),Attribute$1=function(){function Attribute(name,value,sourceSpan){this.name=name,this.value=value,this.sourceSpan=sourceSpan}return Attribute.prototype.visit=function(visitor,context){return visitor.visitAttribute(this,context)},Attribute}(),Element=function(){function Element(name,attrs,children,sourceSpan,startSourceSpan,endSourceSpan){this.name=name,this.attrs=attrs,this.children=children,this.sourceSpan=sourceSpan,this.startSourceSpan=startSourceSpan,this.endSourceSpan=endSourceSpan}return Element.prototype.visit=function(visitor,context){return visitor.visitElement(this,context)},Element}(),Comment=function(){function Comment(value,sourceSpan){this.value=value,this.sourceSpan=sourceSpan}return Comment.prototype.visit=function(visitor,context){return visitor.visitComment(this,context)},Comment}(),__extends$5=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)};!function(TokenType){TokenType[TokenType.TAG_OPEN_START=0]="TAG_OPEN_START",TokenType[TokenType.TAG_OPEN_END=1]="TAG_OPEN_END",TokenType[TokenType.TAG_OPEN_END_VOID=2]="TAG_OPEN_END_VOID",TokenType[TokenType.TAG_CLOSE=3]="TAG_CLOSE",TokenType[TokenType.TEXT=4]="TEXT",TokenType[TokenType.ESCAPABLE_RAW_TEXT=5]="ESCAPABLE_RAW_TEXT",TokenType[TokenType.RAW_TEXT=6]="RAW_TEXT",TokenType[TokenType.COMMENT_START=7]="COMMENT_START",TokenType[TokenType.COMMENT_END=8]="COMMENT_END",TokenType[TokenType.CDATA_START=9]="CDATA_START",TokenType[TokenType.CDATA_END=10]="CDATA_END",TokenType[TokenType.ATTR_NAME=11]="ATTR_NAME",TokenType[TokenType.ATTR_VALUE=12]="ATTR_VALUE",TokenType[TokenType.DOC_TYPE=13]="DOC_TYPE",TokenType[TokenType.EXPANSION_FORM_START=14]="EXPANSION_FORM_START",TokenType[TokenType.EXPANSION_CASE_VALUE=15]="EXPANSION_CASE_VALUE",TokenType[TokenType.EXPANSION_CASE_EXP_START=16]="EXPANSION_CASE_EXP_START",TokenType[TokenType.EXPANSION_CASE_EXP_END=17]="EXPANSION_CASE_EXP_END",TokenType[TokenType.EXPANSION_FORM_END=18]="EXPANSION_FORM_END",TokenType[TokenType.EOF=19]="EOF"}(TokenType$1||(TokenType$1={}));var _VisitorMode,Token$1=function(){function Token(type,parts,sourceSpan){this.type=type,this.parts=parts,this.sourceSpan=sourceSpan}return Token}(),TokenError=function(_super){function TokenError(errorMsg,tokenType,span){_super.call(this,span,errorMsg),this.tokenType=tokenType}return __extends$5(TokenError,_super),TokenError}(ParseError),TokenizeResult=function(){function TokenizeResult(tokens,errors){this.tokens=tokens,this.errors=errors}return TokenizeResult}(),_CR_OR_CRLF_REGEXP=/\r\n?/g,_ControlFlowError=function(){function _ControlFlowError(error){this.error=error}return _ControlFlowError}(),_Tokenizer=function(){function _Tokenizer(_file,_getTagDefinition,_tokenizeIcu,_interpolationConfig){void 0===_interpolationConfig&&(_interpolationConfig=DEFAULT_INTERPOLATION_CONFIG),this._file=_file,this._getTagDefinition=_getTagDefinition,this._tokenizeIcu=_tokenizeIcu,this._interpolationConfig=_interpolationConfig,this._peek=-1,this._nextPeek=-1,this._index=-1,this._line=0,this._column=-1,this._expansionCaseStack=[],this._inInterpolation=!1,this.tokens=[],this.errors=[],this._input=_file.content,this._length=_file.content.length,this._advance()}return _Tokenizer.prototype._processCarriageReturns=function(content){return content.replace(_CR_OR_CRLF_REGEXP,"\n")},_Tokenizer.prototype.tokenize=function(){for(;this._peek!==$EOF;){var start=this._getLocation();try{this._attemptCharCode($LT)?this._attemptCharCode($BANG)?this._attemptCharCode($LBRACKET)?this._consumeCdata(start):this._attemptCharCode($MINUS)?this._consumeComment(start):this._consumeDocType(start):this._attemptCharCode($SLASH)?this._consumeTagClose(start):this._consumeTagOpen(start):this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeText()}catch(e){if(!(e instanceof _ControlFlowError))throw e;this.errors.push(e.error)}}return this._beginToken(TokenType$1.EOF),this._endToken([]),new TokenizeResult(mergeTextTokens(this.tokens),this.errors)},_Tokenizer.prototype._tokenizeExpansionForm=function(){if(isExpansionFormStart(this._input,this._index,this._interpolationConfig))return this._consumeExpansionFormStart(),!0;if(isExpansionCaseStart(this._peek)&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;if(this._peek===$RBRACE){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1},_Tokenizer.prototype._getLocation=function(){return new ParseLocation(this._file,this._index,this._line,this._column)},_Tokenizer.prototype._getSpan=function(start,end){return void 0===start&&(start=this._getLocation()),void 0===end&&(end=this._getLocation()),new ParseSourceSpan(start,end)},_Tokenizer.prototype._beginToken=function(type,start){void 0===start&&(start=this._getLocation()),this._currentTokenStart=start,this._currentTokenType=type},_Tokenizer.prototype._endToken=function(parts,end){void 0===end&&(end=this._getLocation());var token=new Token$1(this._currentTokenType,parts,new ParseSourceSpan(this._currentTokenStart,end));return this.tokens.push(token),this._currentTokenStart=null,this._currentTokenType=null,token},_Tokenizer.prototype._createError=function(msg,span){this._isInExpansionForm()&&(msg+=' (Do you have an unescaped "{" in your template? Use "{{ \'{\' }}") to escape it.)');var error=new TokenError(msg,this._currentTokenType,span);return this._currentTokenStart=null,this._currentTokenType=null,new _ControlFlowError(error)},_Tokenizer.prototype._advance=function(){if(this._index>=this._length)throw this._createError(_unexpectedCharacterErrorMsg($EOF),this._getSpan());this._peek===$LF?(this._line++,this._column=0):this._peek!==$LF&&this._peek!==$CR&&this._column++,this._index++,this._peek=this._index>=this._length?$EOF:this._input.charCodeAt(this._index),this._nextPeek=this._index+1>=this._length?$EOF:this._input.charCodeAt(this._index+1)},_Tokenizer.prototype._attemptCharCode=function(charCode){return this._peek===charCode&&(this._advance(),!0)},_Tokenizer.prototype._attemptCharCodeCaseInsensitive=function(charCode){return!!compareCharCodeCaseInsensitive(this._peek,charCode)&&(this._advance(),!0)},_Tokenizer.prototype._requireCharCode=function(charCode){var location=this._getLocation();if(!this._attemptCharCode(charCode))throw this._createError(_unexpectedCharacterErrorMsg(this._peek),this._getSpan(location,location))},_Tokenizer.prototype._attemptStr=function(chars){var len=chars.length;if(this._index+len>this._length)return!1;for(var initialPosition=this._savePosition(),i=0;i<len;i++)if(!this._attemptCharCode(chars.charCodeAt(i)))return this._restorePosition(initialPosition),!1;return!0},_Tokenizer.prototype._attemptStrCaseInsensitive=function(chars){for(var i=0;i<chars.length;i++)if(!this._attemptCharCodeCaseInsensitive(chars.charCodeAt(i)))return!1;return!0},_Tokenizer.prototype._requireStr=function(chars){var location=this._getLocation();if(!this._attemptStr(chars))throw this._createError(_unexpectedCharacterErrorMsg(this._peek),this._getSpan(location))},_Tokenizer.prototype._attemptCharCodeUntilFn=function(predicate){for(;!predicate(this._peek);)this._advance()},_Tokenizer.prototype._requireCharCodeUntilFn=function(predicate,len){var start=this._getLocation();if(this._attemptCharCodeUntilFn(predicate),this._index-start.offset<len)throw this._createError(_unexpectedCharacterErrorMsg(this._peek),this._getSpan(start,start))},_Tokenizer.prototype._attemptUntilChar=function(char){for(;this._peek!==char;)this._advance()},_Tokenizer.prototype._readChar=function(decodeEntities){if(decodeEntities&&this._peek===$AMPERSAND)return this._decodeEntity();var index=this._index;return this._advance(),this._input[index]},_Tokenizer.prototype._decodeEntity=function(){var start=this._getLocation();if(this._advance(),!this._attemptCharCode($HASH)){var startPosition=this._savePosition();if(this._attemptCharCodeUntilFn(isNamedEntityEnd),this._peek!=$SEMICOLON)return this._restorePosition(startPosition),"&";this._advance();var name_1=this._input.substring(start.offset+1,this._index-1),char=NAMED_ENTITIES[name_1];if(!char)throw this._createError(_unknownEntityErrorMsg(name_1),this._getSpan(start));return char}var isHex=this._attemptCharCode($x)||this._attemptCharCode($X),numberStart=this._getLocation().offset;if(this._attemptCharCodeUntilFn(isDigitEntityEnd),this._peek!=$SEMICOLON)throw this._createError(_unexpectedCharacterErrorMsg(this._peek),this._getSpan());this._advance();var strNum=this._input.substring(numberStart,this._index-1);try{var charCode=parseInt(strNum,isHex?16:10);return String.fromCharCode(charCode)}catch(e){var entity=this._input.substring(start.offset+1,this._index-1);throw this._createError(_unknownEntityErrorMsg(entity),this._getSpan(start))}},_Tokenizer.prototype._consumeRawText=function(decodeEntities,firstCharOfEnd,attemptEndRest){var tagCloseStart,textStart=this._getLocation();this._beginToken(decodeEntities?TokenType$1.ESCAPABLE_RAW_TEXT:TokenType$1.RAW_TEXT,textStart);for(var parts=[];;){if(tagCloseStart=this._getLocation(),this._attemptCharCode(firstCharOfEnd)&&attemptEndRest())break;for(this._index>tagCloseStart.offset&&parts.push(this._input.substring(tagCloseStart.offset,this._index));this._peek!==firstCharOfEnd;)parts.push(this._readChar(decodeEntities))}return this._endToken([this._processCarriageReturns(parts.join(""))],tagCloseStart)},_Tokenizer.prototype._consumeComment=function(start){var _this=this;this._beginToken(TokenType$1.COMMENT_START,start),this._requireCharCode($MINUS),this._endToken([]);var textToken=this._consumeRawText(!1,$MINUS,function(){return _this._attemptStr("->")});this._beginToken(TokenType$1.COMMENT_END,textToken.sourceSpan.end),this._endToken([])},_Tokenizer.prototype._consumeCdata=function(start){var _this=this;this._beginToken(TokenType$1.CDATA_START,start),this._requireStr("CDATA["),this._endToken([]);var textToken=this._consumeRawText(!1,$RBRACKET,function(){return _this._attemptStr("]>")});this._beginToken(TokenType$1.CDATA_END,textToken.sourceSpan.end),this._endToken([])},_Tokenizer.prototype._consumeDocType=function(start){this._beginToken(TokenType$1.DOC_TYPE,start),this._attemptUntilChar($GT),this._advance(),this._endToken([this._input.substring(start.offset+2,this._index-1)])},_Tokenizer.prototype._consumePrefixAndName=function(){for(var nameOrPrefixStart=this._index,prefix=null;this._peek!==$COLON&&!isPrefixEnd(this._peek);)this._advance();var nameStart;this._peek===$COLON?(this._advance(),prefix=this._input.substring(nameOrPrefixStart,this._index-1),nameStart=this._index):nameStart=nameOrPrefixStart,this._requireCharCodeUntilFn(isNameEnd,this._index===nameStart?1:0);var name=this._input.substring(nameStart,this._index);return[prefix,name]},_Tokenizer.prototype._consumeTagOpen=function(start){var tagName,lowercaseTagName,savedPos=this._savePosition();try{if(!isAsciiLetter(this._peek))throw this._createError(_unexpectedCharacterErrorMsg(this._peek),this._getSpan());var nameStart=this._index;for(this._consumeTagOpenStart(start),tagName=this._input.substring(nameStart,this._index),lowercaseTagName=tagName.toLowerCase(),this._attemptCharCodeUntilFn(isNotWhitespace);this._peek!==$SLASH&&this._peek!==$GT;)this._consumeAttributeName(),this._attemptCharCodeUntilFn(isNotWhitespace),this._attemptCharCode($EQ)&&(this._attemptCharCodeUntilFn(isNotWhitespace),this._consumeAttributeValue()),this._attemptCharCodeUntilFn(isNotWhitespace);this._consumeTagOpenEnd()}catch(e){if(e instanceof _ControlFlowError)return this._restorePosition(savedPos),this._beginToken(TokenType$1.TEXT,start),void this._endToken(["<"]);throw e}var contentTokenType=this._getTagDefinition(tagName).contentType;contentTokenType===TagContentType.RAW_TEXT?this._consumeRawTextWithTagClose(lowercaseTagName,!1):contentTokenType===TagContentType.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(lowercaseTagName,!0)},_Tokenizer.prototype._consumeRawTextWithTagClose=function(lowercaseTagName,decodeEntities){var _this=this,textToken=this._consumeRawText(decodeEntities,$LT,function(){return!!_this._attemptCharCode($SLASH)&&(_this._attemptCharCodeUntilFn(isNotWhitespace),!!_this._attemptStrCaseInsensitive(lowercaseTagName)&&(_this._attemptCharCodeUntilFn(isNotWhitespace),_this._attemptCharCode($GT)))});this._beginToken(TokenType$1.TAG_CLOSE,textToken.sourceSpan.end),this._endToken([null,lowercaseTagName])},_Tokenizer.prototype._consumeTagOpenStart=function(start){this._beginToken(TokenType$1.TAG_OPEN_START,start);var parts=this._consumePrefixAndName();this._endToken(parts)},_Tokenizer.prototype._consumeAttributeName=function(){this._beginToken(TokenType$1.ATTR_NAME);var prefixAndName=this._consumePrefixAndName();this._endToken(prefixAndName)},_Tokenizer.prototype._consumeAttributeValue=function(){this._beginToken(TokenType$1.ATTR_VALUE);var value;if(this._peek===$SQ||this._peek===$DQ){var quoteChar=this._peek;this._advance();for(var parts=[];this._peek!==quoteChar;)parts.push(this._readChar(!0));value=parts.join(""),this._advance()}else{var valueStart=this._index;this._requireCharCodeUntilFn(isNameEnd,1),value=this._input.substring(valueStart,this._index)}this._endToken([this._processCarriageReturns(value)])},_Tokenizer.prototype._consumeTagOpenEnd=function(){var tokenType=this._attemptCharCode($SLASH)?TokenType$1.TAG_OPEN_END_VOID:TokenType$1.TAG_OPEN_END;this._beginToken(tokenType),this._requireCharCode($GT),this._endToken([])},_Tokenizer.prototype._consumeTagClose=function(start){this._beginToken(TokenType$1.TAG_CLOSE,start),this._attemptCharCodeUntilFn(isNotWhitespace);var prefixAndName=this._consumePrefixAndName();this._attemptCharCodeUntilFn(isNotWhitespace),this._requireCharCode($GT),this._endToken(prefixAndName)},_Tokenizer.prototype._consumeExpansionFormStart=function(){this._beginToken(TokenType$1.EXPANSION_FORM_START,this._getLocation()),this._requireCharCode($LBRACE),this._endToken([]),this._expansionCaseStack.push(TokenType$1.EXPANSION_FORM_START),this._beginToken(TokenType$1.RAW_TEXT,this._getLocation());var condition=this._readUntil($COMMA);this._endToken([condition],this._getLocation()),this._requireCharCode($COMMA),this._attemptCharCodeUntilFn(isNotWhitespace),this._beginToken(TokenType$1.RAW_TEXT,this._getLocation());var type=this._readUntil($COMMA);this._endToken([type],this._getLocation()),this._requireCharCode($COMMA),this._attemptCharCodeUntilFn(isNotWhitespace)},_Tokenizer.prototype._consumeExpansionCaseStart=function(){this._beginToken(TokenType$1.EXPANSION_CASE_VALUE,this._getLocation());var value=this._readUntil($LBRACE).trim();this._endToken([value],this._getLocation()),this._attemptCharCodeUntilFn(isNotWhitespace),this._beginToken(TokenType$1.EXPANSION_CASE_EXP_START,this._getLocation()),this._requireCharCode($LBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(isNotWhitespace),this._expansionCaseStack.push(TokenType$1.EXPANSION_CASE_EXP_START)},_Tokenizer.prototype._consumeExpansionCaseEnd=function(){this._beginToken(TokenType$1.EXPANSION_CASE_EXP_END,this._getLocation()),this._requireCharCode($RBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(isNotWhitespace),this._expansionCaseStack.pop()},_Tokenizer.prototype._consumeExpansionFormEnd=function(){this._beginToken(TokenType$1.EXPANSION_FORM_END,this._getLocation()),this._requireCharCode($RBRACE),this._endToken([]),this._expansionCaseStack.pop()},_Tokenizer.prototype._consumeText=function(){var start=this._getLocation();this._beginToken(TokenType$1.TEXT,start);var parts=[];do this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(parts.push(this._interpolationConfig.start),this._inInterpolation=!0):this._interpolationConfig&&this._attemptStr(this._interpolationConfig.end)&&this._inInterpolation?(parts.push(this._interpolationConfig.end),
this._inInterpolation=!1):parts.push(this._readChar(!0));while(!this._isTextEnd());this._endToken([this._processCarriageReturns(parts.join(""))])},_Tokenizer.prototype._isTextEnd=function(){if(this._peek===$LT||this._peek===$EOF)return!0;if(this._tokenizeIcu&&!this._inInterpolation){if(isExpansionFormStart(this._input,this._index,this._interpolationConfig))return!0;if(this._peek===$RBRACE&&this._isInExpansionCase())return!0}return!1},_Tokenizer.prototype._savePosition=function(){return[this._peek,this._index,this._column,this._line,this.tokens.length]},_Tokenizer.prototype._readUntil=function(char){var start=this._index;return this._attemptUntilChar(char),this._input.substring(start,this._index)},_Tokenizer.prototype._restorePosition=function(position){this._peek=position[0],this._index=position[1],this._column=position[2],this._line=position[3];var nbTokens=position[4];nbTokens<this.tokens.length&&(this.tokens=this.tokens.slice(0,nbTokens))},_Tokenizer.prototype._isInExpansionCase=function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===TokenType$1.EXPANSION_CASE_EXP_START},_Tokenizer.prototype._isInExpansionForm=function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===TokenType$1.EXPANSION_FORM_START},_Tokenizer}(),__extends$4=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},TreeError=function(_super){function TreeError(elementName,span,msg){_super.call(this,span,msg),this.elementName=elementName}return __extends$4(TreeError,_super),TreeError.create=function(elementName,span,msg){return new TreeError(elementName,span,msg)},TreeError}(ParseError),ParseTreeResult=function(){function ParseTreeResult(rootNodes,errors){this.rootNodes=rootNodes,this.errors=errors}return ParseTreeResult}(),Parser$1=function(){function Parser(getTagDefinition){this.getTagDefinition=getTagDefinition}return Parser.prototype.parse=function(source,url,parseExpansionForms,interpolationConfig){void 0===parseExpansionForms&&(parseExpansionForms=!1),void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var tokensAndErrors=tokenize(source,url,this.getTagDefinition,parseExpansionForms,interpolationConfig),treeAndErrors=new _TreeBuilder(tokensAndErrors.tokens,this.getTagDefinition).build();return new ParseTreeResult(treeAndErrors.rootNodes,tokensAndErrors.errors.concat(treeAndErrors.errors))},Parser}(),_TreeBuilder=function(){function _TreeBuilder(tokens,getTagDefinition){this.tokens=tokens,this.getTagDefinition=getTagDefinition,this._index=-1,this._rootNodes=[],this._errors=[],this._elementStack=[],this._advance()}return _TreeBuilder.prototype.build=function(){for(;this._peek.type!==TokenType$1.EOF;)this._peek.type===TokenType$1.TAG_OPEN_START?this._consumeStartTag(this._advance()):this._peek.type===TokenType$1.TAG_CLOSE?this._consumeEndTag(this._advance()):this._peek.type===TokenType$1.CDATA_START?(this._closeVoidElement(),this._consumeCdata(this._advance())):this._peek.type===TokenType$1.COMMENT_START?(this._closeVoidElement(),this._consumeComment(this._advance())):this._peek.type===TokenType$1.TEXT||this._peek.type===TokenType$1.RAW_TEXT||this._peek.type===TokenType$1.ESCAPABLE_RAW_TEXT?(this._closeVoidElement(),this._consumeText(this._advance())):this._peek.type===TokenType$1.EXPANSION_FORM_START?this._consumeExpansion(this._advance()):this._advance();return new ParseTreeResult(this._rootNodes,this._errors)},_TreeBuilder.prototype._advance=function(){var prev=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],prev},_TreeBuilder.prototype._advanceIf=function(type){return this._peek.type===type?this._advance():null},_TreeBuilder.prototype._consumeCdata=function(startToken){this._consumeText(this._advance()),this._advanceIf(TokenType$1.CDATA_END)},_TreeBuilder.prototype._consumeComment=function(token){var text=this._advanceIf(TokenType$1.RAW_TEXT);this._advanceIf(TokenType$1.COMMENT_END);var value=isPresent(text)?text.parts[0].trim():null;this._addToParent(new Comment(value,token.sourceSpan))},_TreeBuilder.prototype._consumeExpansion=function(token){for(var switchValue=this._advance(),type=this._advance(),cases=[];this._peek.type===TokenType$1.EXPANSION_CASE_VALUE;){var expCase=this._parseExpansionCase();if(isBlank(expCase))return;cases.push(expCase)}if(this._peek.type!==TokenType$1.EXPANSION_FORM_END)return void this._errors.push(TreeError.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."));var sourceSpan=new ParseSourceSpan(token.sourceSpan.start,this._peek.sourceSpan.end);this._addToParent(new Expansion(switchValue.parts[0],type.parts[0],cases,sourceSpan,switchValue.sourceSpan)),this._advance()},_TreeBuilder.prototype._parseExpansionCase=function(){var value=this._advance();if(this._peek.type!==TokenType$1.EXPANSION_CASE_EXP_START)return this._errors.push(TreeError.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;var start=this._advance(),exp=this._collectExpansionExpTokens(start);if(isBlank(exp))return null;var end=this._advance();exp.push(new Token$1(TokenType$1.EOF,[],end.sourceSpan));var parsedExp=new _TreeBuilder(exp,this.getTagDefinition).build();if(parsedExp.errors.length>0)return this._errors=this._errors.concat(parsedExp.errors),null;var sourceSpan=new ParseSourceSpan(value.sourceSpan.start,end.sourceSpan.end),expSourceSpan=new ParseSourceSpan(start.sourceSpan.start,end.sourceSpan.end);return new ExpansionCase(value.parts[0],parsedExp.rootNodes,sourceSpan,value.sourceSpan,expSourceSpan)},_TreeBuilder.prototype._collectExpansionExpTokens=function(start){for(var exp=[],expansionFormStack=[TokenType$1.EXPANSION_CASE_EXP_START];;){if(this._peek.type!==TokenType$1.EXPANSION_FORM_START&&this._peek.type!==TokenType$1.EXPANSION_CASE_EXP_START||expansionFormStack.push(this._peek.type),this._peek.type===TokenType$1.EXPANSION_CASE_EXP_END){if(!lastOnStack(expansionFormStack,TokenType$1.EXPANSION_CASE_EXP_START))return this._errors.push(TreeError.create(null,start.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(expansionFormStack.pop(),0==expansionFormStack.length)return exp}if(this._peek.type===TokenType$1.EXPANSION_FORM_END){if(!lastOnStack(expansionFormStack,TokenType$1.EXPANSION_FORM_START))return this._errors.push(TreeError.create(null,start.sourceSpan,"Invalid ICU message. Missing '}'.")),null;expansionFormStack.pop()}if(this._peek.type===TokenType$1.EOF)return this._errors.push(TreeError.create(null,start.sourceSpan,"Invalid ICU message. Missing '}'.")),null;exp.push(this._advance())}},_TreeBuilder.prototype._consumeText=function(token){var text=token.parts[0];if(text.length>0&&"\n"==text[0]){var parent_1=this._getParentElement();isPresent(parent_1)&&0==parent_1.children.length&&this.getTagDefinition(parent_1.name).ignoreFirstLf&&(text=text.substring(1))}text.length>0&&this._addToParent(new Text(text,token.sourceSpan))},_TreeBuilder.prototype._closeVoidElement=function(){if(this._elementStack.length>0){var el=ListWrapper.last(this._elementStack);this.getTagDefinition(el.name).isVoid&&this._elementStack.pop()}},_TreeBuilder.prototype._consumeStartTag=function(startTagToken){for(var prefix=startTagToken.parts[0],name=startTagToken.parts[1],attrs=[];this._peek.type===TokenType$1.ATTR_NAME;)attrs.push(this._consumeAttr(this._advance()));var fullName=this._getElementFullName(prefix,name,this._getParentElement()),selfClosing=!1;if(this._peek.type===TokenType$1.TAG_OPEN_END_VOID){this._advance(),selfClosing=!0;var tagDef=this.getTagDefinition(fullName);tagDef.canSelfClose||null!==getNsPrefix(fullName)||tagDef.isVoid||this._errors.push(TreeError.create(fullName,startTagToken.sourceSpan,'Only void and foreign elements can be self closed "'+startTagToken.parts[1]+'"'))}else this._peek.type===TokenType$1.TAG_OPEN_END&&(this._advance(),selfClosing=!1);var end=this._peek.sourceSpan.start,span=new ParseSourceSpan(startTagToken.sourceSpan.start,end),el=new Element(fullName,attrs,[],span,span,null);this._pushElement(el),selfClosing&&(this._popElement(fullName),el.endSourceSpan=span)},_TreeBuilder.prototype._pushElement=function(el){if(this._elementStack.length>0){var parentEl=ListWrapper.last(this._elementStack);this.getTagDefinition(parentEl.name).isClosedByChild(el.name)&&this._elementStack.pop()}var tagDef=this.getTagDefinition(el.name),_a=this._getParentElementSkippingContainers(),parent=_a.parent,container=_a.container;if(isPresent(parent)&&tagDef.requireExtraParent(parent.name)){var newParent=new Element(tagDef.parentToAdd,[],[],el.sourceSpan,el.startSourceSpan,el.endSourceSpan);this._insertBeforeContainer(parent,container,newParent)}this._addToParent(el),this._elementStack.push(el)},_TreeBuilder.prototype._consumeEndTag=function(endTagToken){var fullName=this._getElementFullName(endTagToken.parts[0],endTagToken.parts[1],this._getParentElement());this._getParentElement()&&(this._getParentElement().endSourceSpan=endTagToken.sourceSpan),this.getTagDefinition(fullName).isVoid?this._errors.push(TreeError.create(fullName,endTagToken.sourceSpan,'Void elements do not have end tags "'+endTagToken.parts[1]+'"')):this._popElement(fullName)||this._errors.push(TreeError.create(fullName,endTagToken.sourceSpan,'Unexpected closing tag "'+endTagToken.parts[1]+'"'))},_TreeBuilder.prototype._popElement=function(fullName){for(var stackIndex=this._elementStack.length-1;stackIndex>=0;stackIndex--){var el=this._elementStack[stackIndex];if(el.name==fullName)return ListWrapper.splice(this._elementStack,stackIndex,this._elementStack.length-stackIndex),!0;if(!this.getTagDefinition(el.name).closedByParent)return!1}return!1},_TreeBuilder.prototype._consumeAttr=function(attrName){var fullName=mergeNsAndName(attrName.parts[0],attrName.parts[1]),end=attrName.sourceSpan.end,value="";if(this._peek.type===TokenType$1.ATTR_VALUE){var valueToken=this._advance();value=valueToken.parts[0],end=valueToken.sourceSpan.end}return new Attribute$1(fullName,value,new ParseSourceSpan(attrName.sourceSpan.start,end))},_TreeBuilder.prototype._getParentElement=function(){return this._elementStack.length>0?ListWrapper.last(this._elementStack):null},_TreeBuilder.prototype._getParentElementSkippingContainers=function(){for(var container=null,i=this._elementStack.length-1;i>=0;i--){if("ng-container"!==this._elementStack[i].name)return{parent:this._elementStack[i],container:container};container=this._elementStack[i]}return{parent:ListWrapper.last(this._elementStack),container:container}},_TreeBuilder.prototype._addToParent=function(node){var parent=this._getParentElement();isPresent(parent)?parent.children.push(node):this._rootNodes.push(node)},_TreeBuilder.prototype._insertBeforeContainer=function(parent,container,node){if(container){if(parent){var index=parent.children.indexOf(container);parent.children[index]=node}else this._rootNodes.push(node);node.children.push(container),this._elementStack.splice(this._elementStack.indexOf(container),0,node)}else this._addToParent(node),this._elementStack.push(node)},_TreeBuilder.prototype._getElementFullName=function(prefix,localName,parentElement){return isBlank(prefix)&&(prefix=this.getTagDefinition(localName).implicitNamespacePrefix,isBlank(prefix)&&isPresent(parentElement)&&(prefix=getNsPrefix(parentElement.name))),mergeNsAndName(prefix,localName)},_TreeBuilder}(),_SerializerVisitor=function(){function _SerializerVisitor(){}return _SerializerVisitor.prototype.visitText=function(text,context){return text.value},_SerializerVisitor.prototype.visitContainer=function(container,context){var _this=this;return"["+container.children.map(function(child){return child.visit(_this)}).join(", ")+"]"},_SerializerVisitor.prototype.visitIcu=function(icu,context){var _this=this,strCases=Object.keys(icu.cases).map(function(k){return k+" {"+icu.cases[k].visit(_this)+"}"});return"{"+icu.expression+", "+icu.type+", "+strCases.join(", ")+"}"},_SerializerVisitor.prototype.visitTagPlaceholder=function(ph,context){var _this=this;return ph.isVoid?'<ph tag name="'+ph.startName+'"/>':'<ph tag name="'+ph.startName+'">'+ph.children.map(function(child){return child.visit(_this)}).join(", ")+'</ph name="'+ph.closeName+'">'},_SerializerVisitor.prototype.visitPlaceholder=function(ph,context){return'<ph name="'+ph.name+'">'+ph.value+"</ph>"},_SerializerVisitor.prototype.visitIcuPlaceholder=function(ph,context){return'<ph icu name="'+ph.name+'">'+ph.value.visit(this)+"</ph>"},_SerializerVisitor}(),serializerVisitor=new _SerializerVisitor,Message=function(){function Message(nodes,placeholders,placeholderToMsgIds,meaning,description){this.nodes=nodes,this.placeholders=placeholders,this.placeholderToMsgIds=placeholderToMsgIds,this.meaning=meaning,this.description=description}return Message}(),Text$1=function(){function Text(value,sourceSpan){this.value=value,this.sourceSpan=sourceSpan}return Text.prototype.visit=function(visitor,context){return visitor.visitText(this,context)},Text}(),Container=function(){function Container(children,sourceSpan){this.children=children,this.sourceSpan=sourceSpan}return Container.prototype.visit=function(visitor,context){return visitor.visitContainer(this,context)},Container}(),Icu=function(){function Icu(expression,type,cases,sourceSpan){this.expression=expression,this.type=type,this.cases=cases,this.sourceSpan=sourceSpan}return Icu.prototype.visit=function(visitor,context){return visitor.visitIcu(this,context)},Icu}(),TagPlaceholder=function(){function TagPlaceholder(tag,attrs,startName,closeName,children,isVoid,sourceSpan){this.tag=tag,this.attrs=attrs,this.startName=startName,this.closeName=closeName,this.children=children,this.isVoid=isVoid,this.sourceSpan=sourceSpan}return TagPlaceholder.prototype.visit=function(visitor,context){return visitor.visitTagPlaceholder(this,context)},TagPlaceholder}(),Placeholder=function(){function Placeholder(value,name,sourceSpan){void 0===name&&(name=""),this.value=value,this.name=name,this.sourceSpan=sourceSpan}return Placeholder.prototype.visit=function(visitor,context){return visitor.visitPlaceholder(this,context)},Placeholder}(),IcuPlaceholder=function(){function IcuPlaceholder(value,name,sourceSpan){void 0===name&&(name=""),this.value=value,this.name=name,this.sourceSpan=sourceSpan}return IcuPlaceholder.prototype.visit=function(visitor,context){return visitor.visitIcuPlaceholder(this,context)},IcuPlaceholder}(),TAG_TO_PLACEHOLDER_NAMES={A:"LINK",B:"BOLD_TEXT",BR:"LINE_BREAK",EM:"EMPHASISED_TEXT",H1:"HEADING_LEVEL1",H2:"HEADING_LEVEL2",H3:"HEADING_LEVEL3",H4:"HEADING_LEVEL4",H5:"HEADING_LEVEL5",H6:"HEADING_LEVEL6",HR:"HORIZONTAL_RULE",I:"ITALIC_TEXT",LI:"LIST_ITEM",LINK:"MEDIA_LINK",OL:"ORDERED_LIST",P:"PARAGRAPH",Q:"QUOTATION",S:"STRIKETHROUGH_TEXT",SMALL:"SMALL_TEXT",SUB:"SUBSTRIPT",SUP:"SUPERSCRIPT",TBODY:"TABLE_BODY",TD:"TABLE_CELL",TFOOT:"TABLE_FOOTER",TH:"TABLE_HEADER_CELL",THEAD:"TABLE_HEADER",TR:"TABLE_ROW",TT:"MONOSPACED_TEXT",U:"UNDERLINED_TEXT",UL:"UNORDERED_LIST"},PlaceholderRegistry=function(){function PlaceholderRegistry(){this._placeHolderNameCounts={},this._signatureToName={}}return PlaceholderRegistry.prototype.getStartTagPlaceholderName=function(tag,attrs,isVoid){var signature=this._hashTag(tag,attrs,isVoid);if(this._signatureToName[signature])return this._signatureToName[signature];var upperTag=tag.toUpperCase(),baseName=TAG_TO_PLACEHOLDER_NAMES[upperTag]||"TAG_"+upperTag,name=this._generateUniqueName(isVoid?baseName:"START_"+baseName);return this._signatureToName[signature]=name,name},PlaceholderRegistry.prototype.getCloseTagPlaceholderName=function(tag){var signature=this._hashClosingTag(tag);if(this._signatureToName[signature])return this._signatureToName[signature];var upperTag=tag.toUpperCase(),baseName=TAG_TO_PLACEHOLDER_NAMES[upperTag]||"TAG_"+upperTag,name=this._generateUniqueName("CLOSE_"+baseName);return this._signatureToName[signature]=name,name},PlaceholderRegistry.prototype.getPlaceholderName=function(name,content){var upperName=name.toUpperCase(),signature="PH: "+upperName+"="+content;if(this._signatureToName[signature])return this._signatureToName[signature];var uniqueName=this._generateUniqueName(upperName);return this._signatureToName[signature]=uniqueName,uniqueName},PlaceholderRegistry.prototype._hashTag=function(tag,attrs,isVoid){var start="<"+tag,strAttrs=Object.keys(attrs).sort().map(function(name){return" "+name+"="+attrs[name]}).join(""),end=isVoid?"/>":"></"+tag+">";return start+strAttrs+end},PlaceholderRegistry.prototype._hashClosingTag=function(tag){return this._hashTag("/"+tag,{},!1)},PlaceholderRegistry.prototype._generateUniqueName=function(base){var name=base,next=this._placeHolderNameCounts[name];return next?(name+="_"+next,next++):next=1,this._placeHolderNameCounts[base]=next,name},PlaceholderRegistry}(),_expParser=new Parser(new Lexer),_I18nVisitor=function(){function _I18nVisitor(_expressionParser,_interpolationConfig){this._expressionParser=_expressionParser,this._interpolationConfig=_interpolationConfig}return _I18nVisitor.prototype.toI18nMessage=function(nodes,meaning,description){this._isIcu=1==nodes.length&&nodes[0]instanceof Expansion,this._icuDepth=0,this._placeholderRegistry=new PlaceholderRegistry,this._placeholderToContent={},this._placeholderToIds={};var i18nodes=visitAll(this,nodes,{});return new Message(i18nodes,this._placeholderToContent,this._placeholderToIds,meaning,description)},_I18nVisitor.prototype.visitElement=function(el,context){var children=visitAll(this,el.children),attrs={};el.attrs.forEach(function(attr){attrs[attr.name]=attr.value});var isVoid=getHtmlTagDefinition(el.name).isVoid,startPhName=this._placeholderRegistry.getStartTagPlaceholderName(el.name,attrs,isVoid);this._placeholderToContent[startPhName]=el.sourceSpan.toString();var closePhName="";return isVoid||(closePhName=this._placeholderRegistry.getCloseTagPlaceholderName(el.name),this._placeholderToContent[closePhName]="</"+el.name+">"),new TagPlaceholder(el.name,attrs,startPhName,closePhName,children,isVoid,el.sourceSpan)},_I18nVisitor.prototype.visitAttribute=function(attribute,context){return this._visitTextWithInterpolation(attribute.value,attribute.sourceSpan)},_I18nVisitor.prototype.visitText=function(text,context){return this._visitTextWithInterpolation(text.value,text.sourceSpan)},_I18nVisitor.prototype.visitComment=function(comment,context){return null},_I18nVisitor.prototype.visitExpansion=function(icu,context){var _this=this;this._icuDepth++;var i18nIcuCases={},i18nIcu=new Icu(icu.switchValue,icu.type,i18nIcuCases,icu.sourceSpan);if(icu.cases.forEach(function(caze){i18nIcuCases[caze.value]=new Container(caze.expression.map(function(node){return node.visit(_this,{})}),caze.expSourceSpan)}),this._icuDepth--,this._isIcu||this._icuDepth>0)return i18nIcu;var phName=this._placeholderRegistry.getPlaceholderName("ICU",icu.sourceSpan.toString()),visitor=new _I18nVisitor(this._expressionParser,this._interpolationConfig);return this._placeholderToIds[phName]=digestMessage(visitor.toI18nMessage([icu],"","")),new IcuPlaceholder(i18nIcu,phName,icu.sourceSpan)},_I18nVisitor.prototype.visitExpansionCase=function(icuCase,context){throw new Error("Unreachable code")},_I18nVisitor.prototype._visitTextWithInterpolation=function(text,sourceSpan){var splitInterpolation=this._expressionParser.splitInterpolation(text,sourceSpan.start.toString(),this._interpolationConfig);if(!splitInterpolation)return new Text$1(text,sourceSpan);for(var nodes=[],container=new Container(nodes,sourceSpan),_a=this._interpolationConfig,sDelimiter=_a.start,eDelimiter=_a.end,i=0;i<splitInterpolation.strings.length-1;i++){var expression=splitInterpolation.expressions[i],baseName=_extractPlaceholderName(expression)||"INTERPOLATION",phName=this._placeholderRegistry.getPlaceholderName(baseName,expression);splitInterpolation.strings[i].length&&nodes.push(new Text$1(splitInterpolation.strings[i],sourceSpan)),nodes.push(new Placeholder(expression,phName,sourceSpan)),this._placeholderToContent[phName]=sDelimiter+expression+eDelimiter}var lastStringIdx=splitInterpolation.strings.length-1;return splitInterpolation.strings[lastStringIdx].length&&nodes.push(new Text$1(splitInterpolation.strings[lastStringIdx],sourceSpan)),container},_I18nVisitor}(),_CUSTOM_PH_EXP=/\/\/[\s\S]*i18n[\s\S]*\([\s\S]*ph[\s\S]*=[\s\S]*"([\s\S]*?)"[\s\S]*\)/g,__extends$6=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},I18nError=function(_super){function I18nError(span,msg){_super.call(this,span,msg)}return __extends$6(I18nError,_super),I18nError}(ParseError),_I18N_ATTR="i18n",_I18N_ATTR_PREFIX="i18n-",_I18N_COMMENT_PREFIX_REGEXP=/^i18n:?/,ExtractionResult=function(){function ExtractionResult(messages,errors){this.messages=messages,this.errors=errors}return ExtractionResult}();!function(_VisitorMode){_VisitorMode[_VisitorMode.Extract=0]="Extract",_VisitorMode[_VisitorMode.Merge=1]="Merge"}(_VisitorMode||(_VisitorMode={}));var PreparsedElementType,_Visitor=function(){function _Visitor(_implicitTags,_implicitAttrs){this._implicitTags=_implicitTags,this._implicitAttrs=_implicitAttrs}return _Visitor.prototype.extract=function(nodes,interpolationConfig){var _this=this;return this._init(_VisitorMode.Extract,interpolationConfig),nodes.forEach(function(node){return node.visit(_this,null)}),this._inI18nBlock&&this._reportError(nodes[nodes.length-1],"Unclosed block"),new ExtractionResult(this._messages,this._errors)},_Visitor.prototype.merge=function(nodes,translations,interpolationConfig){this._init(_VisitorMode.Merge,interpolationConfig),this._translations=translations;var wrapper=new Element("wrapper",[],nodes,null,null,null),translatedNode=wrapper.visit(this,null);return this._inI18nBlock&&this._reportError(nodes[nodes.length-1],"Unclosed block"),new ParseTreeResult(translatedNode.children,this._errors)},_Visitor.prototype.visitExpansionCase=function(icuCase,context){var expression=visitAll(this,icuCase.expression,context);if(this._mode===_VisitorMode.Merge)return new ExpansionCase(icuCase.value,expression,icuCase.sourceSpan,icuCase.valueSourceSpan,icuCase.expSourceSpan)},_Visitor.prototype.visitExpansion=function(icu,context){this._mayBeAddBlockChildren(icu);var wasInIcu=this._inIcu;this._inIcu||(this._isInTranslatableSection&&this._addMessage([icu]),this._inIcu=!0);var cases=visitAll(this,icu.cases,context);return this._mode===_VisitorMode.Merge&&(icu=new Expansion(icu.switchValue,icu.type,cases,icu.sourceSpan,icu.switchValueSourceSpan)),this._inIcu=wasInIcu,icu},_Visitor.prototype.visitComment=function(comment,context){var isOpening=_isOpeningComment(comment);if(isOpening&&this._isInTranslatableSection)return void this._reportError(comment,"Could not start a block inside a translatable section");var isClosing=_isClosingComment(comment);if(isClosing&&!this._inI18nBlock)return void this._reportError(comment,"Trying to close an unopened block");if(!this._inI18nNode&&!this._inIcu)if(this._inI18nBlock){if(isClosing){if(this._depth==this._blockStartDepth){this._closeTranslatableSection(comment,this._blockChildren),this._inI18nBlock=!1;var message=this._addMessage(this._blockChildren,this._blockMeaningAndDesc),nodes=this._translateMessage(comment,message);return visitAll(this,nodes)}return void this._reportError(comment,"I18N blocks should not cross element boundaries")}}else isOpening&&(this._inI18nBlock=!0,this._blockStartDepth=this._depth,this._blockChildren=[],this._blockMeaningAndDesc=comment.value.replace(_I18N_COMMENT_PREFIX_REGEXP,"").trim(),this._openTranslatableSection(comment))},_Visitor.prototype.visitText=function(text,context){return this._isInTranslatableSection&&this._mayBeAddBlockChildren(text),text},_Visitor.prototype.visitElement=function(el,context){var _this=this;this._mayBeAddBlockChildren(el),this._depth++;var childNodes,wasInI18nNode=this._inI18nNode,wasInImplicitNode=this._inImplicitNode,i18nAttr=_getI18nAttr(el),isImplicit=this._implicitTags.some(function(tag){return el.name===tag})&&!this._inIcu&&!this._isInTranslatableSection,isTopLevelImplicit=!wasInImplicitNode&&isImplicit;if(this._inImplicitNode=this._inImplicitNode||isImplicit,this._isInTranslatableSection||this._inIcu)(i18nAttr||isTopLevelImplicit)&&this._reportError(el,"Could not mark an element as translatable inside a translatable section"),this._mode==_VisitorMode.Extract&&visitAll(this,el.children),this._mode==_VisitorMode.Merge&&(childNodes=[],el.children.forEach(function(child){var visited=child.visit(_this,context);visited&&!_this._isInTranslatableSection&&(childNodes=childNodes.concat(visited))}));else{if(i18nAttr){this._inI18nNode=!0;var message=this._addMessage(el.children,i18nAttr.value);childNodes=this._translateMessage(el,message)}else if(isTopLevelImplicit){this._inI18nNode=!0;var message=this._addMessage(el.children);childNodes=this._translateMessage(el,message)}if(this._mode==_VisitorMode.Extract){var isTranslatable=i18nAttr||isTopLevelImplicit;isTranslatable&&this._openTranslatableSection(el),visitAll(this,el.children),isTranslatable&&this._closeTranslatableSection(el,el.children)}this._mode!==_VisitorMode.Merge||i18nAttr||isTopLevelImplicit||(childNodes=[],el.children.forEach(function(child){var visited=child.visit(_this,context);visited&&!_this._isInTranslatableSection&&(childNodes=childNodes.concat(visited))}))}if(this._visitAttributesOf(el),this._depth--,this._inI18nNode=wasInI18nNode,this._inImplicitNode=wasInImplicitNode,this._mode===_VisitorMode.Merge){var translatedAttrs=this._translateAttributes(el);return new Element(el.name,translatedAttrs,childNodes,el.sourceSpan,el.startSourceSpan,el.endSourceSpan)}},_Visitor.prototype.visitAttribute=function(attribute,context){throw new Error("unreachable code")},_Visitor.prototype._init=function(mode,interpolationConfig){this._mode=mode,this._inI18nBlock=!1,this._inI18nNode=!1,this._depth=0,this._inIcu=!1,this._msgCountAtSectionStart=void 0,this._errors=[],this._messages=[],this._inImplicitNode=!1,this._createI18nMessage=createI18nMessageFactory(interpolationConfig)},_Visitor.prototype._visitAttributesOf=function(el){var _this=this,explicitAttrNameToValue={},implicitAttrNames=this._implicitAttrs[el.name]||[];el.attrs.filter(function(attr){return attr.name.startsWith(_I18N_ATTR_PREFIX)}).forEach(function(attr){return explicitAttrNameToValue[attr.name.slice(_I18N_ATTR_PREFIX.length)]=attr.value}),el.attrs.forEach(function(attr){attr.name in explicitAttrNameToValue?_this._addMessage([attr],explicitAttrNameToValue[attr.name]):implicitAttrNames.some(function(name){return attr.name===name})&&_this._addMessage([attr])})},_Visitor.prototype._addMessage=function(ast,meaningAndDesc){if(!(0==ast.length||1==ast.length&&ast[0]instanceof Attribute$1&&!ast[0].value)){var _a=_splitMeaningAndDesc(meaningAndDesc),meaning=_a[0],description=_a[1],message=this._createI18nMessage(ast,meaning,description);return this._messages.push(message),message}},_Visitor.prototype._translateMessage=function(el,message){if(message&&this._mode===_VisitorMode.Merge){var id=digestMessage(message),nodes=this._translations.get(id);if(nodes)return nodes;this._reportError(el,'Translation unavailable for message id="'+id+'"')}return[]},_Visitor.prototype._translateAttributes=function(el){var _this=this,attributes=el.attrs,i18nAttributeMeanings={};attributes.forEach(function(attr){attr.name.startsWith(_I18N_ATTR_PREFIX)&&(i18nAttributeMeanings[attr.name.slice(_I18N_ATTR_PREFIX.length)]=_splitMeaningAndDesc(attr.value)[0])});var translatedAttributes=[];return attributes.forEach(function(attr){if(attr.name!==_I18N_ATTR&&!attr.name.startsWith(_I18N_ATTR_PREFIX))if(attr.value&&""!=attr.value&&i18nAttributeMeanings.hasOwnProperty(attr.name)){var meaning=i18nAttributeMeanings[attr.name],message=_this._createI18nMessage([attr],meaning,""),id=digestMessage(message),nodes=_this._translations.get(id);if(nodes)if(nodes[0]instanceof Text){var value=nodes[0].value;translatedAttributes.push(new Attribute$1(attr.name,value,attr.sourceSpan))}else _this._reportError(el,'Unexpected translation for attribute "'+attr.name+'" (id="'+id+'")');else _this._reportError(el,'Translation unavailable for attribute "'+attr.name+'" (id="'+id+'")')}else translatedAttributes.push(attr)}),translatedAttributes},_Visitor.prototype._mayBeAddBlockChildren=function(node){this._inI18nBlock&&!this._inIcu&&this._depth==this._blockStartDepth&&this._blockChildren.push(node)},_Visitor.prototype._openTranslatableSection=function(node){this._isInTranslatableSection?this._reportError(node,"Unexpected section start"):this._msgCountAtSectionStart=this._messages.length},Object.defineProperty(_Visitor.prototype,"_isInTranslatableSection",{get:function(){return void 0!==this._msgCountAtSectionStart},enumerable:!0,configurable:!0}),_Visitor.prototype._closeTranslatableSection=function(node,directChildren){if(!this._isInTranslatableSection)return void this._reportError(node,"Unexpected section end");var startIndex=this._msgCountAtSectionStart,significantChildren=directChildren.reduce(function(count,node){return count+(node instanceof Comment?0:1)},0);if(1==significantChildren)for(var i=this._messages.length-1;i>=startIndex;i--){var ast=this._messages[i].nodes;if(!(1==ast.length&&ast[0]instanceof Text$1)){this._messages.splice(i,1);break}}this._msgCountAtSectionStart=void 0},_Visitor.prototype._reportError=function(node,msg){this._errors.push(new I18nError(node.sourceSpan,msg))},_Visitor}(),MessageBundle=function(){function MessageBundle(_htmlParser,_implicitTags,_implicitAttrs){this._htmlParser=_htmlParser,this._implicitTags=_implicitTags,this._implicitAttrs=_implicitAttrs,this._messageMap={}}return MessageBundle.prototype.updateFromTemplate=function(html,url,interpolationConfig){var _this=this,htmlParserResult=this._htmlParser.parse(html,url,!0,interpolationConfig);if(htmlParserResult.errors.length)return htmlParserResult.errors;var i18nParserResult=extractMessages(htmlParserResult.rootNodes,interpolationConfig,this._implicitTags,this._implicitAttrs);return i18nParserResult.errors.length?i18nParserResult.errors:void i18nParserResult.messages.forEach(function(message){_this._messageMap[digestMessage(message)]=message})},MessageBundle.prototype.getMessageMap=function(){return this._messageMap},MessageBundle.prototype.write=function(serializer){return serializer.write(this._messageMap)},MessageBundle}(),XmlTagDefinition=function(){function XmlTagDefinition(){this.closedByParent=!1,this.contentType=TagContentType.PARSABLE_DATA,this.isVoid=!1,this.ignoreFirstLf=!1,this.canSelfClose=!0}return XmlTagDefinition.prototype.requireExtraParent=function(currentParent){return!1},XmlTagDefinition.prototype.isClosedByChild=function(name){return!1},XmlTagDefinition}(),_TAG_DEFINITION=new XmlTagDefinition,__extends$7=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},XmlParser=function(_super){function XmlParser(){_super.call(this,getXmlTagDefinition)}return __extends$7(XmlParser,_super),XmlParser.prototype.parse=function(source,url,parseExpansionForms){return void 0===parseExpansionForms&&(parseExpansionForms=!1),_super.prototype.parse.call(this,source,url,parseExpansionForms,null)},XmlParser}(Parser$1),_Visitor$1=function(){function _Visitor(){}return _Visitor.prototype.visitTag=function(tag){var _this=this,strAttrs=this._serializeAttributes(tag.attrs);if(0==tag.children.length)return"<"+tag.name+strAttrs+"/>";var strChildren=tag.children.map(function(node){return node.visit(_this)});return"<"+tag.name+strAttrs+">"+strChildren.join("")+"</"+tag.name+">"},_Visitor.prototype.visitText=function(text){return text.value},_Visitor.prototype.visitDeclaration=function(decl){
return"<?xml"+this._serializeAttributes(decl.attrs)+" ?>"},_Visitor.prototype._serializeAttributes=function(attrs){var strAttrs=Object.keys(attrs).map(function(name){return name+'="'+attrs[name]+'"'}).join(" ");return strAttrs.length>0?" "+strAttrs:""},_Visitor.prototype.visitDoctype=function(doctype){return"<!DOCTYPE "+doctype.rootTag+" [\n"+doctype.dtd+"\n]>"},_Visitor}(),_visitor=new _Visitor$1,Declaration=function(){function Declaration(unescapedAttrs){var _this=this;this.attrs={},Object.keys(unescapedAttrs).forEach(function(k){_this.attrs[k]=_escapeXml(unescapedAttrs[k])})}return Declaration.prototype.visit=function(visitor){return visitor.visitDeclaration(this)},Declaration}(),Doctype=function(){function Doctype(rootTag,dtd){this.rootTag=rootTag,this.dtd=dtd}return Doctype.prototype.visit=function(visitor){return visitor.visitDoctype(this)},Doctype}(),Tag=function(){function Tag(name,unescapedAttrs,children){var _this=this;void 0===unescapedAttrs&&(unescapedAttrs={}),void 0===children&&(children=[]),this.name=name,this.children=children,this.attrs={},Object.keys(unescapedAttrs).forEach(function(k){_this.attrs[k]=_escapeXml(unescapedAttrs[k])})}return Tag.prototype.visit=function(visitor){return visitor.visitTag(this)},Tag}(),Text$2=function(){function Text(unescapedValue){this.value=_escapeXml(unescapedValue)}return Text.prototype.visit=function(visitor){return visitor.visitText(this)},Text}(),_ESCAPED_CHARS=[[/&/g,"&amp;"],[/"/g,"&quot;"],[/'/g,"&apos;"],[/</g,"&lt;"],[/>/g,"&gt;"]],_VERSION="1.2",_XMLNS="urn:oasis:names:tc:xliff:document:1.2",_SOURCE_LANG="en",_PLACEHOLDER_TAG="x",_SOURCE_TAG="source",_TARGET_TAG="target",_UNIT_TAG="trans-unit",_CR=function(ws){return void 0===ws&&(ws=0),new Text$2("\n"+new Array(ws).join(" "))},Xliff=function(){function Xliff(_htmlParser,_interpolationConfig){this._htmlParser=_htmlParser,this._interpolationConfig=_interpolationConfig}return Xliff.prototype.write=function(messageMap){var visitor=new _WriteVisitor,transUnits=[];Object.keys(messageMap).forEach(function(id){var message=messageMap[id],transUnit=new Tag(_UNIT_TAG,{id:id,datatype:"html"});transUnit.children.push(_CR(8),new Tag(_SOURCE_TAG,{},visitor.serialize(message.nodes)),_CR(8),new Tag(_TARGET_TAG)),message.description&&transUnit.children.push(_CR(8),new Tag("note",{priority:"1",from:"description"},[new Text$2(message.description)])),message.meaning&&transUnit.children.push(_CR(8),new Tag("note",{priority:"1",from:"meaning"},[new Text$2(message.meaning)])),transUnit.children.push(_CR(6)),transUnits.push(_CR(6),transUnit)});var body=new Tag("body",{},transUnits.concat([_CR(4)])),file=new Tag("file",{"source-language":_SOURCE_LANG,datatype:"plaintext",original:"ng2.template"},[_CR(4),body,_CR(2)]),xliff=new Tag("xliff",{version:_VERSION,xmlns:_XMLNS},[_CR(2),file,_CR()]);return serialize([new Declaration({version:"1.0",encoding:"UTF-8"}),_CR(),xliff])},Xliff.prototype.load=function(content,url,messageBundle){var _this=this,result=(new XmlParser).parse(content,url);if(result.errors.length)throw new Error("xtb parse errors:\n"+result.errors.join("\n"));var _a=(new _LoadVisitor).parse(result.rootNodes,messageBundle),messages=_a.messages,errors=_a.errors;if(errors.length)throw new Error("xtb parse errors:\n"+errors.join("\n"));var messageMap={},parseErrors=[];if(Object.keys(messages).forEach(function(id){var res=_this._htmlParser.parse(messages[id],url,!0,_this._interpolationConfig);parseErrors.push.apply(parseErrors,res.errors),messageMap[id]=res.rootNodes}),parseErrors.length)throw new Error("xtb parse errors:\n"+parseErrors.join("\n"));return messageMap},Xliff}(),_WriteVisitor=function(){function _WriteVisitor(){}return _WriteVisitor.prototype.visitText=function(text,context){return[new Text$2(text.value)]},_WriteVisitor.prototype.visitContainer=function(container,context){var _this=this,nodes=[];return container.children.forEach(function(node){return nodes.push.apply(nodes,node.visit(_this))}),nodes},_WriteVisitor.prototype.visitIcu=function(icu,context){if(this._isInIcu)throw new Error("xliff does not support nested ICU messages");this._isInIcu=!0;var nodes=[];return this._isInIcu=!1,nodes},_WriteVisitor.prototype.visitTagPlaceholder=function(ph,context){var startTagPh=new Tag(_PLACEHOLDER_TAG,{id:ph.startName,ctype:ph.tag});if(ph.isVoid)return[startTagPh];var closeTagPh=new Tag(_PLACEHOLDER_TAG,{id:ph.closeName,ctype:ph.tag});return[startTagPh].concat(this.serialize(ph.children),[closeTagPh])},_WriteVisitor.prototype.visitPlaceholder=function(ph,context){return[new Tag(_PLACEHOLDER_TAG,{id:ph.name})]},_WriteVisitor.prototype.visitIcuPlaceholder=function(ph,context){return[new Tag(_PLACEHOLDER_TAG,{id:ph.name})]},_WriteVisitor.prototype.serialize=function(nodes){var _this=this;return this._isInIcu=!1,ListWrapper.flatten(nodes.map(function(node){return node.visit(_this)}))},_WriteVisitor}(),_LoadVisitor=function(){function _LoadVisitor(){}return _LoadVisitor.prototype.parse=function(nodes,messageBundle){var _this=this;this._messageNodes=[],this._translatedMessages={},this._msgId="",this._target=[],this._errors=[],visitAll(this,nodes,null);var messageMap=messageBundle.getMessageMap(),placeholders=extractPlaceholders(messageBundle),placeholderToIds=extractPlaceholderToIds(messageBundle);return this._messageNodes.filter(function(message){return messageMap.hasOwnProperty(message[0])}).sort(function(a,b){return 0==Object.keys(messageMap[a[0]].placeholderToMsgIds).length?-1:0==Object.keys(messageMap[b[0]].placeholderToMsgIds).length?1:0}).forEach(function(message){var id=message[0];_this._placeholders=placeholders[id]||{},_this._placeholderToIds=placeholderToIds[id]||{},_this._translatedMessages[id]=visitAll(_this,message[1]).join("")}),{messages:this._translatedMessages,errors:this._errors}},_LoadVisitor.prototype.visitElement=function(element,context){switch(element.name){case _UNIT_TAG:this._target=null;var msgId=element.attrs.find(function(attr){return"id"===attr.name});msgId?this._msgId=msgId.value:this._addError(element,"<"+_UNIT_TAG+'> misses the "id" attribute'),visitAll(this,element.children,null),null!==this._msgId&&this._messageNodes.push([this._msgId,this._target]);break;case _SOURCE_TAG:break;case _TARGET_TAG:this._target=element.children;break;case _PLACEHOLDER_TAG:var idAttr=element.attrs.find(function(attr){return"id"===attr.name});if(idAttr){var id=idAttr.value;if(this._placeholders.hasOwnProperty(id))return this._placeholders[id];if(this._placeholderToIds.hasOwnProperty(id)&&this._translatedMessages.hasOwnProperty(this._placeholderToIds[id]))return this._translatedMessages[this._placeholderToIds[id]];this._addError(element,'The placeholder "'+id+'" does not exists in the source message')}else this._addError(element,"<"+_PLACEHOLDER_TAG+'> misses the "id" attribute');break;default:visitAll(this,element.children,null)}},_LoadVisitor.prototype.visitAttribute=function(attribute,context){throw new Error("unreachable code")},_LoadVisitor.prototype.visitText=function(text,context){return text.value},_LoadVisitor.prototype.visitComment=function(comment,context){return""},_LoadVisitor.prototype.visitExpansion=function(expansion,context){throw new Error("unreachable code")},_LoadVisitor.prototype.visitExpansionCase=function(expansionCase,context){throw new Error("unreachable code")},_LoadVisitor.prototype._addError=function(node,message){this._errors.push(new I18nError(node.sourceSpan,message))},_LoadVisitor}(),_MESSAGES_TAG="messagebundle",_MESSAGE_TAG="msg",_PLACEHOLDER_TAG$1="ph",_EXEMPLE_TAG="ex",_DOCTYPE='<!ELEMENT messagebundle (msg)*>\n<!ATTLIST messagebundle class CDATA #IMPLIED>\n\n<!ELEMENT msg (#PCDATA|ph|source)*>\n<!ATTLIST msg id CDATA #IMPLIED>\n<!ATTLIST msg seq CDATA #IMPLIED>\n<!ATTLIST msg name CDATA #IMPLIED>\n<!ATTLIST msg desc CDATA #IMPLIED>\n<!ATTLIST msg meaning CDATA #IMPLIED>\n<!ATTLIST msg obsolete (obsolete) #IMPLIED>\n<!ATTLIST msg xml:space (default|preserve) "default">\n<!ATTLIST msg is_hidden CDATA #IMPLIED>\n\n<!ELEMENT source (#PCDATA)>\n\n<!ELEMENT ph (#PCDATA|ex)*>\n<!ATTLIST ph name CDATA #REQUIRED>\n\n<!ELEMENT ex (#PCDATA)>',Xmb=function(){function Xmb(){}return Xmb.prototype.write=function(messageMap){var visitor=new _Visitor$2,rootNode=new Tag(_MESSAGES_TAG);return rootNode.children.push(new Text$2("\n")),Object.keys(messageMap).forEach(function(id){var message=messageMap[id],attrs={id:id};message.description&&(attrs.desc=message.description),message.meaning&&(attrs.meaning=message.meaning),rootNode.children.push(new Text$2("  "),new Tag(_MESSAGE_TAG,attrs,visitor.serialize(message.nodes)),new Text$2("\n"))}),serialize([new Declaration({version:"1.0",encoding:"UTF-8"}),new Text$2("\n"),new Doctype(_MESSAGES_TAG,_DOCTYPE),new Text$2("\n"),rootNode])},Xmb.prototype.load=function(content,url,messageBundle){throw new Error("Unsupported")},Xmb}(),_Visitor$2=function(){function _Visitor(){}return _Visitor.prototype.visitText=function(text,context){return[new Text$2(text.value)]},_Visitor.prototype.visitContainer=function(container,context){var _this=this,nodes=[];return container.children.forEach(function(node){return nodes.push.apply(nodes,node.visit(_this))}),nodes},_Visitor.prototype.visitIcu=function(icu,context){var _this=this,nodes=[new Text$2("{"+icu.expression+", "+icu.type+", ")];return Object.keys(icu.cases).forEach(function(c){nodes.push.apply(nodes,[new Text$2(c+" {")].concat(icu.cases[c].visit(_this),[new Text$2("} ")]))}),nodes.push(new Text$2("}")),nodes},_Visitor.prototype.visitTagPlaceholder=function(ph,context){var startEx=new Tag(_EXEMPLE_TAG,{},[new Text$2("<"+ph.tag+">")]),startTagPh=new Tag(_PLACEHOLDER_TAG$1,{name:ph.startName},[startEx]);if(ph.isVoid)return[startTagPh];var closeEx=new Tag(_EXEMPLE_TAG,{},[new Text$2("</"+ph.tag+">")]),closeTagPh=new Tag(_PLACEHOLDER_TAG$1,{name:ph.closeName},[closeEx]);return[startTagPh].concat(this.serialize(ph.children),[closeTagPh])},_Visitor.prototype.visitPlaceholder=function(ph,context){return[new Tag(_PLACEHOLDER_TAG$1,{name:ph.name})]},_Visitor.prototype.visitIcuPlaceholder=function(ph,context){return[new Tag(_PLACEHOLDER_TAG$1,{name:ph.name})]},_Visitor.prototype.serialize=function(nodes){var _this=this;return ListWrapper.flatten(nodes.map(function(node){return node.visit(_this)}))},_Visitor}(),_TRANSLATIONS_TAG="translationbundle",_TRANSLATION_TAG="translation",_PLACEHOLDER_TAG$2="ph",Xtb=function(){function Xtb(_htmlParser,_interpolationConfig){this._htmlParser=_htmlParser,this._interpolationConfig=_interpolationConfig}return Xtb.prototype.write=function(messageMap){throw new Error("Unsupported")},Xtb.prototype.load=function(content,url,messageBundle){var _this=this,result=(new XmlParser).parse(content,url);if(result.errors.length)throw new Error("xtb parse errors:\n"+result.errors.join("\n"));var _a=(new _Visitor$3).parse(result.rootNodes,messageBundle),messages=_a.messages,errors=_a.errors;if(errors.length)throw new Error("xtb parse errors:\n"+errors.join("\n"));var messageMap={},parseErrors=[];if(Object.keys(messages).forEach(function(id){var res=_this._htmlParser.parse(messages[id],url,!0,_this._interpolationConfig);parseErrors.push.apply(parseErrors,res.errors),messageMap[id]=res.rootNodes}),parseErrors.length)throw new Error("xtb parse errors:\n"+parseErrors.join("\n"));return messageMap},Xtb}(),_Visitor$3=function(){function _Visitor(){}return _Visitor.prototype.parse=function(nodes,messageBundle){var _this=this;this._messageNodes=[],this._translatedMessages={},this._bundleDepth=0,this._translationDepth=0,this._errors=[],visitAll(this,nodes,null);var messageMap=messageBundle.getMessageMap(),placeholders=extractPlaceholders(messageBundle),placeholderToIds=extractPlaceholderToIds(messageBundle);return this._messageNodes.filter(function(message){return messageMap.hasOwnProperty(message[0])}).sort(function(a,b){return 0==Object.keys(messageMap[a[0]].placeholderToMsgIds).length?-1:0==Object.keys(messageMap[b[0]].placeholderToMsgIds).length?1:0}).forEach(function(message){var id=message[0];_this._placeholders=placeholders[id]||{},_this._placeholderToIds=placeholderToIds[id]||{},_this._translatedMessages[id]=visitAll(_this,message[1]).join("")}),{messages:this._translatedMessages,errors:this._errors}},_Visitor.prototype.visitElement=function(element,context){switch(element.name){case _TRANSLATIONS_TAG:this._bundleDepth++,this._bundleDepth>1&&this._addError(element,"<"+_TRANSLATIONS_TAG+"> elements can not be nested"),visitAll(this,element.children,null),this._bundleDepth--;break;case _TRANSLATION_TAG:this._translationDepth++,this._translationDepth>1&&this._addError(element,"<"+_TRANSLATION_TAG+"> elements can not be nested");var idAttr=element.attrs.find(function(attr){return"id"===attr.name});idAttr?this._messageNodes.push([idAttr.value,element.children]):this._addError(element,"<"+_TRANSLATION_TAG+'> misses the "id" attribute'),this._translationDepth--;break;case _PLACEHOLDER_TAG$2:var nameAttr=element.attrs.find(function(attr){return"name"===attr.name});if(nameAttr){var name_1=nameAttr.value;if(this._placeholders.hasOwnProperty(name_1))return this._placeholders[name_1];if(this._placeholderToIds.hasOwnProperty(name_1)&&this._translatedMessages.hasOwnProperty(this._placeholderToIds[name_1]))return this._translatedMessages[this._placeholderToIds[name_1]];this._addError(element,'The placeholder "'+name_1+'" does not exists in the source message')}else this._addError(element,"<"+_PLACEHOLDER_TAG$2+'> misses the "name" attribute');break;default:this._addError(element,"Unexpected tag")}},_Visitor.prototype.visitAttribute=function(attribute,context){throw new Error("unreachable code")},_Visitor.prototype.visitText=function(text,context){return text.value},_Visitor.prototype.visitComment=function(comment,context){return""},_Visitor.prototype.visitExpansion=function(expansion,context){var _this=this;expansion.cases.map(function(c){return c.visit(_this,null)});return"{"+expansion.switchValue+", "+expansion.type+", strCases.join(' ')}"},_Visitor.prototype.visitExpansionCase=function(expansionCase,context){return expansionCase.value+" {"+visitAll(this,expansionCase.expression,null)+"}"},_Visitor.prototype._addError=function(node,message){this._errors.push(new I18nError(node.sourceSpan,message))},_Visitor}(),TranslationBundle=function(){function TranslationBundle(_messageMap){void 0===_messageMap&&(_messageMap={}),this._messageMap=_messageMap}return TranslationBundle.load=function(content,url,messageBundle,serializer){return new TranslationBundle(serializer.load(content,url,messageBundle))},TranslationBundle.prototype.get=function(id){return this._messageMap[id]},TranslationBundle.prototype.has=function(id){return id in this._messageMap},TranslationBundle}(),I18NHtmlParser=function(){function I18NHtmlParser(_htmlParser,_translations,_translationsFormat){this._htmlParser=_htmlParser,this._translations=_translations,this._translationsFormat=_translationsFormat}return I18NHtmlParser.prototype.parse=function(source,url,parseExpansionForms,interpolationConfig){void 0===parseExpansionForms&&(parseExpansionForms=!1),void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG);var parseResult=this._htmlParser.parse(source,url,parseExpansionForms,interpolationConfig);if(!this._translations||""===this._translations)return parseResult;var messageBundle=new MessageBundle(this._htmlParser,[],{}),errors=messageBundle.updateFromTemplate(source,url,interpolationConfig);if(errors&&errors.length)return new ParseTreeResult(parseResult.rootNodes,parseResult.errors.concat(errors));var serializer=this._createSerializer(interpolationConfig),translationBundle=TranslationBundle.load(this._translations,url,messageBundle,serializer);return mergeTranslations(parseResult.rootNodes,translationBundle,interpolationConfig,[],{})},I18NHtmlParser.prototype._createSerializer=function(interpolationConfig){var format=(this._translationsFormat||"xlf").toLowerCase();switch(format){case"xmb":return new Xmb;case"xtb":return new Xtb(this._htmlParser,interpolationConfig);case"xliff":case"xlf":default:return new Xliff(this._htmlParser,interpolationConfig)}},I18NHtmlParser}(),isDefaultChangeDetectionStrategy=_angular_core.__core_private__.isDefaultChangeDetectionStrategy,ChangeDetectorStatus=_angular_core.__core_private__.ChangeDetectorStatus,LifecycleHooks=_angular_core.__core_private__.LifecycleHooks,LIFECYCLE_HOOKS_VALUES=_angular_core.__core_private__.LIFECYCLE_HOOKS_VALUES,ReflectorReader=_angular_core.__core_private__.ReflectorReader,AppElement=_angular_core.__core_private__.AppElement,CodegenComponentFactoryResolver=_angular_core.__core_private__.CodegenComponentFactoryResolver,AppView=_angular_core.__core_private__.AppView,DebugAppView=_angular_core.__core_private__.DebugAppView,NgModuleInjector=_angular_core.__core_private__.NgModuleInjector,registerModuleFactory=_angular_core.__core_private__.registerModuleFactory,ViewType=_angular_core.__core_private__.ViewType,MAX_INTERPOLATION_VALUES=_angular_core.__core_private__.MAX_INTERPOLATION_VALUES,checkBinding=_angular_core.__core_private__.checkBinding,flattenNestedViewRenderNodes=_angular_core.__core_private__.flattenNestedViewRenderNodes,interpolate=_angular_core.__core_private__.interpolate,ViewUtils=_angular_core.__core_private__.ViewUtils,DebugContext=_angular_core.__core_private__.DebugContext,StaticNodeDebugInfo=_angular_core.__core_private__.StaticNodeDebugInfo,devModeEqual=_angular_core.__core_private__.devModeEqual,UNINITIALIZED=_angular_core.__core_private__.UNINITIALIZED,ValueUnwrapper=_angular_core.__core_private__.ValueUnwrapper,TemplateRef_=_angular_core.__core_private__.TemplateRef_,EMPTY_ARRAY=_angular_core.__core_private__.EMPTY_ARRAY,EMPTY_MAP=_angular_core.__core_private__.EMPTY_MAP,pureProxy1=_angular_core.__core_private__.pureProxy1,pureProxy2=_angular_core.__core_private__.pureProxy2,pureProxy3=_angular_core.__core_private__.pureProxy3,pureProxy4=_angular_core.__core_private__.pureProxy4,pureProxy5=_angular_core.__core_private__.pureProxy5,pureProxy6=_angular_core.__core_private__.pureProxy6,pureProxy7=_angular_core.__core_private__.pureProxy7,pureProxy8=_angular_core.__core_private__.pureProxy8,pureProxy9=_angular_core.__core_private__.pureProxy9,pureProxy10=_angular_core.__core_private__.pureProxy10,castByValue=_angular_core.__core_private__.castByValue,Console=_angular_core.__core_private__.Console,reflector=_angular_core.__core_private__.reflector,Reflector=_angular_core.__core_private__.Reflector,ReflectionCapabilities=_angular_core.__core_private__.ReflectionCapabilities,NoOpAnimationPlayer=_angular_core.__core_private__.NoOpAnimationPlayer,AnimationSequencePlayer=_angular_core.__core_private__.AnimationSequencePlayer,AnimationGroupPlayer=_angular_core.__core_private__.AnimationGroupPlayer,AnimationKeyframe=_angular_core.__core_private__.AnimationKeyframe,AnimationStyles=_angular_core.__core_private__.AnimationStyles,AnimationOutput=_angular_core.__core_private__.AnimationOutput,ANY_STATE=_angular_core.__core_private__.ANY_STATE,DEFAULT_STATE=_angular_core.__core_private__.DEFAULT_STATE,EMPTY_ANIMATION_STATE=_angular_core.__core_private__.EMPTY_STATE,FILL_STYLE_FLAG=_angular_core.__core_private__.FILL_STYLE_FLAG,prepareFinalAnimationStyles=_angular_core.__core_private__.prepareFinalAnimationStyles,balanceAnimationKeyframes=_angular_core.__core_private__.balanceAnimationKeyframes,clearStyles=_angular_core.__core_private__.clearStyles,collectAndResolveStyles=_angular_core.__core_private__.collectAndResolveStyles,renderStyles=_angular_core.__core_private__.renderStyles,ComponentStillLoadingError=_angular_core.__core_private__.ComponentStillLoadingError,APP_VIEW_MODULE_URL=assetUrl("core","linker/view"),VIEW_UTILS_MODULE_URL=assetUrl("core","linker/view_utils"),CD_MODULE_URL=assetUrl("core","change_detection/change_detection"),ANIMATION_STYLE_UTIL_ASSET_URL=assetUrl("core","animation/animation_style_util"),Identifiers=function(){function Identifiers(){}return Identifiers.ANALYZE_FOR_ENTRY_COMPONENTS={name:"ANALYZE_FOR_ENTRY_COMPONENTS",moduleUrl:assetUrl("core","metadata/di"),runtime:_angular_core.ANALYZE_FOR_ENTRY_COMPONENTS},Identifiers.ViewUtils={name:"ViewUtils",moduleUrl:assetUrl("core","linker/view_utils"),runtime:ViewUtils},Identifiers.AppView={name:"AppView",moduleUrl:APP_VIEW_MODULE_URL,runtime:AppView},Identifiers.DebugAppView={name:"DebugAppView",moduleUrl:APP_VIEW_MODULE_URL,runtime:DebugAppView},Identifiers.AppElement={name:"AppElement",moduleUrl:assetUrl("core","linker/element"),runtime:AppElement},Identifiers.ElementRef={name:"ElementRef",moduleUrl:assetUrl("core","linker/element_ref"),runtime:_angular_core.ElementRef},Identifiers.ViewContainerRef={name:"ViewContainerRef",moduleUrl:assetUrl("core","linker/view_container_ref"),runtime:_angular_core.ViewContainerRef},Identifiers.ChangeDetectorRef={name:"ChangeDetectorRef",moduleUrl:assetUrl("core","change_detection/change_detector_ref"),runtime:_angular_core.ChangeDetectorRef},Identifiers.RenderComponentType={name:"RenderComponentType",moduleUrl:assetUrl("core","render/api"),runtime:_angular_core.RenderComponentType},Identifiers.QueryList={name:"QueryList",moduleUrl:assetUrl("core","linker/query_list"),runtime:_angular_core.QueryList},Identifiers.TemplateRef={name:"TemplateRef",moduleUrl:assetUrl("core","linker/template_ref"),runtime:_angular_core.TemplateRef},Identifiers.TemplateRef_={name:"TemplateRef_",moduleUrl:assetUrl("core","linker/template_ref"),runtime:TemplateRef_},Identifiers.CodegenComponentFactoryResolver={name:"CodegenComponentFactoryResolver",moduleUrl:assetUrl("core","linker/component_factory_resolver"),runtime:CodegenComponentFactoryResolver},Identifiers.ComponentFactoryResolver={name:"ComponentFactoryResolver",moduleUrl:assetUrl("core","linker/component_factory_resolver"),runtime:_angular_core.ComponentFactoryResolver},Identifiers.ComponentFactory={name:"ComponentFactory",runtime:_angular_core.ComponentFactory,moduleUrl:assetUrl("core","linker/component_factory")},Identifiers.NgModuleFactory={name:"NgModuleFactory",runtime:_angular_core.NgModuleFactory,moduleUrl:assetUrl("core","linker/ng_module_factory")},Identifiers.NgModuleInjector={name:"NgModuleInjector",runtime:NgModuleInjector,moduleUrl:assetUrl("core","linker/ng_module_factory")},Identifiers.RegisterModuleFactoryFn={name:"registerModuleFactory",runtime:registerModuleFactory,moduleUrl:assetUrl("core","linker/ng_module_factory_loader")},Identifiers.ValueUnwrapper={name:"ValueUnwrapper",moduleUrl:CD_MODULE_URL,runtime:ValueUnwrapper},Identifiers.Injector={name:"Injector",moduleUrl:assetUrl("core","di/injector"),runtime:_angular_core.Injector},Identifiers.ViewEncapsulation={name:"ViewEncapsulation",moduleUrl:assetUrl("core","metadata/view"),runtime:_angular_core.ViewEncapsulation},Identifiers.ViewType={name:"ViewType",moduleUrl:assetUrl("core","linker/view_type"),runtime:ViewType},Identifiers.ChangeDetectionStrategy={name:"ChangeDetectionStrategy",moduleUrl:CD_MODULE_URL,runtime:_angular_core.ChangeDetectionStrategy},Identifiers.StaticNodeDebugInfo={name:"StaticNodeDebugInfo",moduleUrl:assetUrl("core","linker/debug_context"),runtime:StaticNodeDebugInfo},Identifiers.DebugContext={name:"DebugContext",moduleUrl:assetUrl("core","linker/debug_context"),runtime:DebugContext},Identifiers.Renderer={name:"Renderer",moduleUrl:assetUrl("core","render/api"),runtime:_angular_core.Renderer},Identifiers.SimpleChange={name:"SimpleChange",moduleUrl:CD_MODULE_URL,runtime:_angular_core.SimpleChange},Identifiers.UNINITIALIZED={name:"UNINITIALIZED",moduleUrl:CD_MODULE_URL,runtime:UNINITIALIZED},Identifiers.ChangeDetectorStatus={name:"ChangeDetectorStatus",moduleUrl:CD_MODULE_URL,runtime:ChangeDetectorStatus},Identifiers.checkBinding={name:"checkBinding",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:checkBinding},Identifiers.flattenNestedViewRenderNodes={name:"flattenNestedViewRenderNodes",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:flattenNestedViewRenderNodes},Identifiers.devModeEqual={name:"devModeEqual",moduleUrl:CD_MODULE_URL,runtime:devModeEqual},Identifiers.interpolate={name:"interpolate",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:interpolate},Identifiers.castByValue={name:"castByValue",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:castByValue},Identifiers.EMPTY_ARRAY={name:"EMPTY_ARRAY",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:EMPTY_ARRAY},Identifiers.EMPTY_MAP={name:"EMPTY_MAP",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:EMPTY_MAP},Identifiers.pureProxies=[null,{name:"pureProxy1",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy1},{name:"pureProxy2",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy2},{name:"pureProxy3",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy3},{name:"pureProxy4",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy4},{name:"pureProxy5",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy5},{name:"pureProxy6",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy6},{name:"pureProxy7",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy7},{name:"pureProxy8",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy8},{name:"pureProxy9",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy9},{name:"pureProxy10",moduleUrl:VIEW_UTILS_MODULE_URL,runtime:pureProxy10}],Identifiers.SecurityContext={name:"SecurityContext",moduleUrl:assetUrl("core","security"),runtime:_angular_core.SecurityContext},Identifiers.AnimationKeyframe={name:"AnimationKeyframe",moduleUrl:assetUrl("core","animation/animation_keyframe"),runtime:AnimationKeyframe},Identifiers.AnimationStyles={name:"AnimationStyles",moduleUrl:assetUrl("core","animation/animation_styles"),runtime:AnimationStyles},Identifiers.NoOpAnimationPlayer={name:"NoOpAnimationPlayer",moduleUrl:assetUrl("core","animation/animation_player"),runtime:NoOpAnimationPlayer},Identifiers.AnimationGroupPlayer={name:"AnimationGroupPlayer",moduleUrl:assetUrl("core","animation/animation_group_player"),runtime:AnimationGroupPlayer},Identifiers.AnimationSequencePlayer={name:"AnimationSequencePlayer",moduleUrl:assetUrl("core","animation/animation_sequence_player"),runtime:AnimationSequencePlayer},Identifiers.prepareFinalAnimationStyles={name:"prepareFinalAnimationStyles",moduleUrl:ANIMATION_STYLE_UTIL_ASSET_URL,runtime:prepareFinalAnimationStyles},Identifiers.balanceAnimationKeyframes={name:"balanceAnimationKeyframes",moduleUrl:ANIMATION_STYLE_UTIL_ASSET_URL,runtime:balanceAnimationKeyframes},Identifiers.clearStyles={name:"clearStyles",moduleUrl:ANIMATION_STYLE_UTIL_ASSET_URL,runtime:clearStyles},Identifiers.renderStyles={name:"renderStyles",moduleUrl:ANIMATION_STYLE_UTIL_ASSET_URL,runtime:renderStyles},Identifiers.collectAndResolveStyles={name:"collectAndResolveStyles",moduleUrl:ANIMATION_STYLE_UTIL_ASSET_URL,runtime:collectAndResolveStyles},Identifiers.LOCALE_ID={name:"LOCALE_ID",moduleUrl:assetUrl("core","i18n/tokens"),runtime:_angular_core.LOCALE_ID},Identifiers.TRANSLATIONS_FORMAT={name:"TRANSLATIONS_FORMAT",moduleUrl:assetUrl("core","i18n/tokens"),runtime:_angular_core.TRANSLATIONS_FORMAT},Identifiers.AnimationOutput={name:"AnimationOutput",moduleUrl:assetUrl("core","animation/animation_output"),runtime:AnimationOutput},Identifiers}(),__extends$8=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},HtmlParser=function(_super){function HtmlParser(){_super.call(this,getHtmlTagDefinition)}return __extends$8(HtmlParser,_super),HtmlParser.prototype.parse=function(source,url,parseExpansionForms,interpolationConfig){return void 0===parseExpansionForms&&(parseExpansionForms=!1),void 0===interpolationConfig&&(interpolationConfig=DEFAULT_INTERPOLATION_CONFIG),_super.prototype.parse.call(this,source,url,parseExpansionForms,interpolationConfig)},HtmlParser.decorators=[{type:_angular_core.Injectable}],HtmlParser.ctorParameters=[],HtmlParser}(Parser$1),__extends$9=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},PLURAL_CASES=["zero","one","two","few","many","other"],ExpansionResult=function(){function ExpansionResult(nodes,expanded,errors){this.nodes=nodes,this.expanded=expanded,this.errors=errors}return ExpansionResult}(),ExpansionError=function(_super){function ExpansionError(span,errorMsg){_super.call(this,span,errorMsg)}return __extends$9(ExpansionError,_super),ExpansionError}(ParseError),_Expander=function(){function _Expander(){this.isExpanded=!1,this.errors=[]}return _Expander.prototype.visitElement=function(element,context){return new Element(element.name,element.attrs,visitAll(this,element.children),element.sourceSpan,element.startSourceSpan,element.endSourceSpan)},_Expander.prototype.visitAttribute=function(attribute,context){return attribute},_Expander.prototype.visitText=function(text,context){return text},_Expander.prototype.visitComment=function(comment,context){return comment},_Expander.prototype.visitExpansion=function(icu,context){return this.isExpanded=!0,"plural"==icu.type?_expandPluralForm(icu,this.errors):_expandDefaultForm(icu,this.errors)},_Expander.prototype.visitExpansionCase=function(icuCase,context){throw new Error("Should not be reached")},_Expander}(),__extends$10=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},ProviderError=function(_super){function ProviderError(message,span){_super.call(this,span,message)}return __extends$10(ProviderError,_super),ProviderError}(ParseError),ProviderViewContext=function(){function ProviderViewContext(component,sourceSpan){var _this=this;this.component=component,this.sourceSpan=sourceSpan,this.errors=[],this.viewQueries=_getViewQueries(component),this.viewProviders=new Map,_normalizeProviders(component.viewProviders,sourceSpan,this.errors).forEach(function(provider){isBlank(_this.viewProviders.get(provider.token.reference))&&_this.viewProviders.set(provider.token.reference,!0)})}return ProviderViewContext}(),ProviderElementContext=function(){function ProviderElementContext(_viewContext,_parent,_isViewRoot,_directiveAsts,attrs,refs,_sourceSpan){var _this=this;this._viewContext=_viewContext,this._parent=_parent,this._isViewRoot=_isViewRoot,this._directiveAsts=_directiveAsts,this._sourceSpan=_sourceSpan,this._transformedProviders=new Map,this._seenProviders=new Map,this._hasViewContainer=!1,this._attrs={},attrs.forEach(function(attrAst){return _this._attrs[attrAst.name]=attrAst.value});var directivesMeta=_directiveAsts.map(function(directiveAst){return directiveAst.directive});this._allProviders=_resolveProvidersFromDirectives(directivesMeta,_sourceSpan,_viewContext.errors),this._contentQueries=_getContentQueries(directivesMeta);var queriedTokens=new Map;MapWrapper.values(this._allProviders).forEach(function(provider){_this._addQueryReadsTo(provider.token,queriedTokens)}),refs.forEach(function(refAst){_this._addQueryReadsTo(new CompileTokenMetadata({value:refAst.name}),queriedTokens)}),isPresent(queriedTokens.get(resolveIdentifierToken(Identifiers.ViewContainerRef).reference))&&(this._hasViewContainer=!0),MapWrapper.values(this._allProviders).forEach(function(provider){var eager=provider.eager||isPresent(queriedTokens.get(provider.token.reference));eager&&_this._getOrCreateLocalProvider(provider.providerType,provider.token,!0)})}return ProviderElementContext.prototype.afterElement=function(){var _this=this;MapWrapper.values(this._allProviders).forEach(function(provider){_this._getOrCreateLocalProvider(provider.providerType,provider.token,!1)})},Object.defineProperty(ProviderElementContext.prototype,"transformProviders",{get:function(){return MapWrapper.values(this._transformedProviders)},enumerable:!0,configurable:!0}),Object.defineProperty(ProviderElementContext.prototype,"transformedDirectiveAsts",{get:function(){var sortedProviderTypes=this.transformProviders.map(function(provider){
return provider.token.identifier}),sortedDirectives=ListWrapper.clone(this._directiveAsts);return ListWrapper.sort(sortedDirectives,function(dir1,dir2){return sortedProviderTypes.indexOf(dir1.directive.type)-sortedProviderTypes.indexOf(dir2.directive.type)}),sortedDirectives},enumerable:!0,configurable:!0}),Object.defineProperty(ProviderElementContext.prototype,"transformedHasViewContainer",{get:function(){return this._hasViewContainer},enumerable:!0,configurable:!0}),ProviderElementContext.prototype._addQueryReadsTo=function(token,queryReadTokens){this._getQueriesFor(token).forEach(function(query){var queryReadToken=isPresent(query.read)?query.read:token;isBlank(queryReadTokens.get(queryReadToken.reference))&&queryReadTokens.set(queryReadToken.reference,!0)})},ProviderElementContext.prototype._getQueriesFor=function(token){for(var queries,result=[],currentEl=this,distance=0;null!==currentEl;)queries=currentEl._contentQueries.get(token.reference),isPresent(queries)&&ListWrapper.addAll(result,queries.filter(function(query){return query.descendants||distance<=1})),currentEl._directiveAsts.length>0&&distance++,currentEl=currentEl._parent;return queries=this._viewContext.viewQueries.get(token.reference),isPresent(queries)&&ListWrapper.addAll(result,queries),result},ProviderElementContext.prototype._getOrCreateLocalProvider=function(requestingProviderType,token,eager){var _this=this,resolvedProvider=this._allProviders.get(token.reference);if(isBlank(resolvedProvider)||(requestingProviderType===exports.ProviderAstType.Directive||requestingProviderType===exports.ProviderAstType.PublicService)&&resolvedProvider.providerType===exports.ProviderAstType.PrivateService||(requestingProviderType===exports.ProviderAstType.PrivateService||requestingProviderType===exports.ProviderAstType.PublicService)&&resolvedProvider.providerType===exports.ProviderAstType.Builtin)return null;var transformedProviderAst=this._transformedProviders.get(token.reference);if(isPresent(transformedProviderAst))return transformedProviderAst;if(isPresent(this._seenProviders.get(token.reference)))return this._viewContext.errors.push(new ProviderError("Cannot instantiate cyclic dependency! "+token.name,this._sourceSpan)),null;this._seenProviders.set(token.reference,!0);var transformedProviders=resolvedProvider.providers.map(function(provider){var transformedDeps,transformedUseValue=provider.useValue,transformedUseExisting=provider.useExisting;if(isPresent(provider.useExisting)){var existingDiDep=_this._getDependency(resolvedProvider.providerType,new CompileDiDependencyMetadata({token:provider.useExisting}),eager);isPresent(existingDiDep.token)?transformedUseExisting=existingDiDep.token:(transformedUseExisting=null,transformedUseValue=existingDiDep.value)}else if(isPresent(provider.useFactory)){var deps=isPresent(provider.deps)?provider.deps:provider.useFactory.diDeps;transformedDeps=deps.map(function(dep){return _this._getDependency(resolvedProvider.providerType,dep,eager)})}else if(isPresent(provider.useClass)){var deps=isPresent(provider.deps)?provider.deps:provider.useClass.diDeps;transformedDeps=deps.map(function(dep){return _this._getDependency(resolvedProvider.providerType,dep,eager)})}return _transformProvider(provider,{useExisting:transformedUseExisting,useValue:transformedUseValue,deps:transformedDeps})});return transformedProviderAst=_transformProviderAst(resolvedProvider,{eager:eager,providers:transformedProviders}),this._transformedProviders.set(token.reference,transformedProviderAst),transformedProviderAst},ProviderElementContext.prototype._getLocalDependency=function(requestingProviderType,dep,eager){if(void 0===eager&&(eager=null),dep.isAttribute){var attrValue=this._attrs[dep.token.value];return new CompileDiDependencyMetadata({isValue:!0,value:normalizeBlank(attrValue)})}if(isPresent(dep.query)||isPresent(dep.viewQuery))return dep;if(isPresent(dep.token)){if(requestingProviderType===exports.ProviderAstType.Directive||requestingProviderType===exports.ProviderAstType.Component){if(dep.token.reference===resolveIdentifierToken(Identifiers.Renderer).reference||dep.token.reference===resolveIdentifierToken(Identifiers.ElementRef).reference||dep.token.reference===resolveIdentifierToken(Identifiers.ChangeDetectorRef).reference||dep.token.reference===resolveIdentifierToken(Identifiers.TemplateRef).reference)return dep;dep.token.reference===resolveIdentifierToken(Identifiers.ViewContainerRef).reference&&(this._hasViewContainer=!0)}if(dep.token.reference===resolveIdentifierToken(Identifiers.Injector).reference)return dep;if(isPresent(this._getOrCreateLocalProvider(requestingProviderType,dep.token,eager)))return dep}return null},ProviderElementContext.prototype._getDependency=function(requestingProviderType,dep,eager){void 0===eager&&(eager=null);var currElement=this,currEager=eager,result=null;if(dep.isSkipSelf||(result=this._getLocalDependency(requestingProviderType,dep,eager)),dep.isSelf)isBlank(result)&&dep.isOptional&&(result=new CompileDiDependencyMetadata({isValue:!0,value:null}));else{for(;isBlank(result)&&isPresent(currElement._parent);){var prevElement=currElement;currElement=currElement._parent,prevElement._isViewRoot&&(currEager=!1),result=currElement._getLocalDependency(exports.ProviderAstType.PublicService,dep,currEager)}isBlank(result)&&(result=!dep.isHost||this._viewContext.component.type.isHost||this._viewContext.component.type.reference===dep.token.reference||isPresent(this._viewContext.viewProviders.get(dep.token.reference))?dep:dep.isOptional?result=new CompileDiDependencyMetadata({isValue:!0,value:null}):null)}return isBlank(result)&&this._viewContext.errors.push(new ProviderError("No provider for "+dep.token.name,this._sourceSpan)),result},ProviderElementContext}(),NgModuleProviderAnalyzer=function(){function NgModuleProviderAnalyzer(ngModule,extraProviders,sourceSpan){var _this=this;this._transformedProviders=new Map,this._seenProviders=new Map,this._errors=[],this._allProviders=new Map;var ngModuleTypes=ngModule.transitiveModule.modules.map(function(moduleMeta){return moduleMeta.type});ngModuleTypes.forEach(function(ngModuleType){var ngModuleProvider=new CompileProviderMetadata({token:new CompileTokenMetadata({identifier:ngModuleType}),useClass:ngModuleType});_resolveProviders([ngModuleProvider],exports.ProviderAstType.PublicService,!0,sourceSpan,_this._errors,_this._allProviders)}),_resolveProviders(_normalizeProviders(ngModule.transitiveModule.providers.concat(extraProviders),sourceSpan,this._errors),exports.ProviderAstType.PublicService,!1,sourceSpan,this._errors,this._allProviders)}return NgModuleProviderAnalyzer.prototype.parse=function(){var _this=this;if(MapWrapper.values(this._allProviders).forEach(function(provider){_this._getOrCreateLocalProvider(provider.token,provider.eager)}),this._errors.length>0){var errorString=this._errors.join("\n");throw new Error("Provider parse errors:\n"+errorString)}return MapWrapper.values(this._transformedProviders)},NgModuleProviderAnalyzer.prototype._getOrCreateLocalProvider=function(token,eager){var _this=this,resolvedProvider=this._allProviders.get(token.reference);if(isBlank(resolvedProvider))return null;var transformedProviderAst=this._transformedProviders.get(token.reference);if(isPresent(transformedProviderAst))return transformedProviderAst;if(isPresent(this._seenProviders.get(token.reference)))return this._errors.push(new ProviderError("Cannot instantiate cyclic dependency! "+token.name,resolvedProvider.sourceSpan)),null;this._seenProviders.set(token.reference,!0);var transformedProviders=resolvedProvider.providers.map(function(provider){var transformedDeps,transformedUseValue=provider.useValue,transformedUseExisting=provider.useExisting;if(isPresent(provider.useExisting)){var existingDiDep=_this._getDependency(new CompileDiDependencyMetadata({token:provider.useExisting}),eager,resolvedProvider.sourceSpan);isPresent(existingDiDep.token)?transformedUseExisting=existingDiDep.token:(transformedUseExisting=null,transformedUseValue=existingDiDep.value)}else if(isPresent(provider.useFactory)){var deps=isPresent(provider.deps)?provider.deps:provider.useFactory.diDeps;transformedDeps=deps.map(function(dep){return _this._getDependency(dep,eager,resolvedProvider.sourceSpan)})}else if(isPresent(provider.useClass)){var deps=isPresent(provider.deps)?provider.deps:provider.useClass.diDeps;transformedDeps=deps.map(function(dep){return _this._getDependency(dep,eager,resolvedProvider.sourceSpan)})}return _transformProvider(provider,{useExisting:transformedUseExisting,useValue:transformedUseValue,deps:transformedDeps})});return transformedProviderAst=_transformProviderAst(resolvedProvider,{eager:eager,providers:transformedProviders}),this._transformedProviders.set(token.reference,transformedProviderAst),transformedProviderAst},NgModuleProviderAnalyzer.prototype._getDependency=function(dep,eager,requestorSourceSpan){void 0===eager&&(eager=null);var foundLocal=!1;!dep.isSkipSelf&&isPresent(dep.token)&&(dep.token.reference===resolveIdentifierToken(Identifiers.Injector).reference||dep.token.reference===resolveIdentifierToken(Identifiers.ComponentFactoryResolver).reference?foundLocal=!0:isPresent(this._getOrCreateLocalProvider(dep.token,eager))&&(foundLocal=!0));var result=dep;return dep.isSelf&&!foundLocal&&(dep.isOptional?result=new CompileDiDependencyMetadata({isValue:!0,value:null}):this._errors.push(new ProviderError("No provider for "+dep.token.name,requestorSourceSpan))),result},NgModuleProviderAnalyzer}(),ElementSchemaRegistry=function(){function ElementSchemaRegistry(){}return ElementSchemaRegistry}(),StyleWithImports=function(){function StyleWithImports(style,styleUrls){this.style=style,this.styleUrls=styleUrls}return StyleWithImports}(),_cssImportRe=/@import\s+(?:url\()?\s*(?:(?:['"]([^'"]*))|([^;\)\s]*))[^;]*;?/g,_urlWithSchemaRe=/^([^:\/?#]+):/,NG_CONTENT_SELECT_ATTR="select",NG_CONTENT_ELEMENT="ng-content",LINK_ELEMENT="link",LINK_STYLE_REL_ATTR="rel",LINK_STYLE_HREF_ATTR="href",LINK_STYLE_REL_VALUE="stylesheet",STYLE_ELEMENT="style",SCRIPT_ELEMENT="script",NG_NON_BINDABLE_ATTR="ngNonBindable",NG_PROJECT_AS="ngProjectAs";!function(PreparsedElementType){PreparsedElementType[PreparsedElementType.NG_CONTENT=0]="NG_CONTENT",PreparsedElementType[PreparsedElementType.STYLE=1]="STYLE",PreparsedElementType[PreparsedElementType.STYLESHEET=2]="STYLESHEET",PreparsedElementType[PreparsedElementType.SCRIPT=3]="SCRIPT",PreparsedElementType[PreparsedElementType.OTHER=4]="OTHER"}(PreparsedElementType||(PreparsedElementType={}));var _Mode,PreparsedElement=function(){function PreparsedElement(type,selectAttr,hrefAttr,nonBindable,projectAs){this.type=type,this.selectAttr=selectAttr,this.hrefAttr=hrefAttr,this.nonBindable=nonBindable,this.projectAs=projectAs}return PreparsedElement}(),__extends=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},BIND_NAME_REGEXP=/^(?:(?:(?:(bind-)|(let-)|(ref-|#)|(on-)|(bindon-)|(@))(.+))|\[\(([^\)]+)\)\]|\[([^\]]+)\]|\(([^\)]+)\))$/,KW_BIND_IDX=1,KW_LET_IDX=2,KW_REF_IDX=3,KW_ON_IDX=4,KW_BINDON_IDX=5,KW_AT_IDX=6,IDENT_KW_IDX=7,IDENT_BANANA_BOX_IDX=8,IDENT_PROPERTY_IDX=9,IDENT_EVENT_IDX=10,ANIMATE_PROP_PREFIX="animate-",TEMPLATE_ELEMENT="template",TEMPLATE_ATTR="template",TEMPLATE_ATTR_PREFIX="*",CLASS_ATTR="class",PROPERTY_PARTS_SEPARATOR=".",ATTRIBUTE_PREFIX="attr",CLASS_PREFIX="class",STYLE_PREFIX="style",TEXT_CSS_SELECTOR=CssSelector.parse("*")[0],TEMPLATE_TRANSFORMS=new _angular_core.OpaqueToken("TemplateTransforms"),TemplateParseError=function(_super){function TemplateParseError(message,span,level){_super.call(this,span,message,level)}return __extends(TemplateParseError,_super),TemplateParseError}(ParseError),TemplateParseResult=function(){function TemplateParseResult(templateAst,errors){this.templateAst=templateAst,this.errors=errors}return TemplateParseResult}(),TemplateParser=function(){function TemplateParser(_exprParser,_schemaRegistry,_htmlParser,_console,transforms){this._exprParser=_exprParser,this._schemaRegistry=_schemaRegistry,this._htmlParser=_htmlParser,this._console=_console,this.transforms=transforms}return TemplateParser.prototype.parse=function(component,template,directives,pipes,schemas,templateUrl){var result=this.tryParse(component,template,directives,pipes,schemas,templateUrl),warnings=result.errors.filter(function(error){return error.level===ParseErrorLevel.WARNING}),errors=result.errors.filter(function(error){return error.level===ParseErrorLevel.FATAL});if(warnings.length>0&&this._console.warn("Template parse warnings:\n"+warnings.join("\n")),errors.length>0){var errorString=errors.join("\n");throw new Error("Template parse errors:\n"+errorString)}return result.templateAst},TemplateParser.prototype.tryParse=function(component,template,directives,pipes,schemas,templateUrl){var interpolationConfig;component.template&&(interpolationConfig=InterpolationConfig.fromArray(component.template.interpolation));var result,htmlAstWithErrors=this._htmlParser.parse(template,templateUrl,!0,interpolationConfig),errors=htmlAstWithErrors.errors;if(0==errors.length){var expandedHtmlAst=expandNodes(htmlAstWithErrors.rootNodes);errors.push.apply(errors,expandedHtmlAst.errors),htmlAstWithErrors=new ParseTreeResult(expandedHtmlAst.nodes,errors)}if(htmlAstWithErrors.rootNodes.length>0){var uniqDirectives=removeIdentifierDuplicates(directives),uniqPipes=removeIdentifierDuplicates(pipes),providerViewContext=new ProviderViewContext(component,htmlAstWithErrors.rootNodes[0].sourceSpan),parseVisitor=new TemplateParseVisitor(providerViewContext,uniqDirectives,uniqPipes,schemas,this._exprParser,this._schemaRegistry);result=visitAll(parseVisitor,htmlAstWithErrors.rootNodes,EMPTY_ELEMENT_CONTEXT),errors.push.apply(errors,parseVisitor.errors.concat(providerViewContext.errors))}else result=[];return this._assertNoReferenceDuplicationOnTemplate(result,errors),errors.length>0?new TemplateParseResult(result,errors):(isPresent(this.transforms)&&this.transforms.forEach(function(transform){result=templateVisitAll(transform,result)}),new TemplateParseResult(result,errors))},TemplateParser.prototype._assertNoReferenceDuplicationOnTemplate=function(result,errors){var existingReferences=[];result.filter(function(element){return!!element.references}).forEach(function(element){return element.references.forEach(function(reference){var name=reference.name;if(existingReferences.indexOf(name)<0)existingReferences.push(name);else{var error=new TemplateParseError('Reference "#'+name+'" is defined several times',reference.sourceSpan,ParseErrorLevel.FATAL);errors.push(error)}})})},TemplateParser.decorators=[{type:_angular_core.Injectable}],TemplateParser.ctorParameters=[{type:Parser},{type:ElementSchemaRegistry},{type:I18NHtmlParser},{type:Console},{type:Array,decorators:[{type:_angular_core.Optional},{type:_angular_core.Inject,args:[TEMPLATE_TRANSFORMS]}]}],TemplateParser}(),TemplateParseVisitor=function(){function TemplateParseVisitor(providerViewContext,directives,pipes,_schemas,_exprParser,_schemaRegistry){var _this=this;this.providerViewContext=providerViewContext,this._schemas=_schemas,this._exprParser=_exprParser,this._schemaRegistry=_schemaRegistry,this.selectorMatcher=new SelectorMatcher,this.errors=[],this.directivesIndex=new Map,this.ngContentCount=0,this.pipesByName=new Map;var tempMeta=providerViewContext.component.template;tempMeta&&tempMeta.interpolation&&(this._interpolationConfig={start:tempMeta.interpolation[0],end:tempMeta.interpolation[1]}),directives.forEach(function(directive,index){var selector=CssSelector.parse(directive.selector);_this.selectorMatcher.addSelectables(selector,directive),_this.directivesIndex.set(directive,index)}),pipes.forEach(function(pipe){return _this.pipesByName.set(pipe.name,pipe)})}return TemplateParseVisitor.prototype._reportError=function(message,sourceSpan,level){void 0===level&&(level=ParseErrorLevel.FATAL),this.errors.push(new TemplateParseError(message,sourceSpan,level))},TemplateParseVisitor.prototype._reportParserErrors=function(errors,sourceSpan){for(var _i=0,errors_1=errors;_i<errors_1.length;_i++){var error=errors_1[_i];this._reportError(error.message,sourceSpan)}},TemplateParseVisitor.prototype._parseInterpolation=function(value,sourceSpan){var sourceInfo=sourceSpan.start.toString();try{var ast=this._exprParser.parseInterpolation(value,sourceInfo,this._interpolationConfig);if(ast&&this._reportParserErrors(ast.errors,sourceSpan),this._checkPipes(ast,sourceSpan),isPresent(ast)&&ast.ast.expressions.length>MAX_INTERPOLATION_VALUES)throw new Error("Only support at most "+MAX_INTERPOLATION_VALUES+" interpolation values!");return ast}catch(e){return this._reportError(""+e,sourceSpan),this._exprParser.wrapLiteralPrimitive("ERROR",sourceInfo)}},TemplateParseVisitor.prototype._parseAction=function(value,sourceSpan){var sourceInfo=sourceSpan.start.toString();try{var ast=this._exprParser.parseAction(value,sourceInfo,this._interpolationConfig);return ast&&this._reportParserErrors(ast.errors,sourceSpan),!ast||ast.ast instanceof EmptyExpr?(this._reportError("Empty expressions are not allowed",sourceSpan),this._exprParser.wrapLiteralPrimitive("ERROR",sourceInfo)):(this._checkPipes(ast,sourceSpan),ast)}catch(e){return this._reportError(""+e,sourceSpan),this._exprParser.wrapLiteralPrimitive("ERROR",sourceInfo)}},TemplateParseVisitor.prototype._parseBinding=function(value,sourceSpan){var sourceInfo=sourceSpan.start.toString();try{var ast=this._exprParser.parseBinding(value,sourceInfo,this._interpolationConfig);return ast&&this._reportParserErrors(ast.errors,sourceSpan),this._checkPipes(ast,sourceSpan),ast}catch(e){return this._reportError(""+e,sourceSpan),this._exprParser.wrapLiteralPrimitive("ERROR",sourceInfo)}},TemplateParseVisitor.prototype._parseTemplateBindings=function(value,sourceSpan){var _this=this,sourceInfo=sourceSpan.start.toString();try{var bindingsResult=this._exprParser.parseTemplateBindings(value,sourceInfo);return this._reportParserErrors(bindingsResult.errors,sourceSpan),bindingsResult.templateBindings.forEach(function(binding){isPresent(binding.expression)&&_this._checkPipes(binding.expression,sourceSpan)}),bindingsResult.warnings.forEach(function(warning){_this._reportError(warning,sourceSpan,ParseErrorLevel.WARNING)}),bindingsResult.templateBindings}catch(e){return this._reportError(""+e,sourceSpan),[]}},TemplateParseVisitor.prototype._checkPipes=function(ast,sourceSpan){var _this=this;if(isPresent(ast)){var collector=new PipeCollector;ast.visit(collector),collector.pipes.forEach(function(pipeName){_this.pipesByName.has(pipeName)||_this._reportError("The pipe '"+pipeName+"' could not be found",sourceSpan)})}},TemplateParseVisitor.prototype.visitExpansion=function(expansion,context){return null},TemplateParseVisitor.prototype.visitExpansionCase=function(expansionCase,context){return null},TemplateParseVisitor.prototype.visitText=function(text,parent){var ngContentIndex=parent.findNgContentIndex(TEXT_CSS_SELECTOR),expr=this._parseInterpolation(text.value,text.sourceSpan);return isPresent(expr)?new BoundTextAst(expr,ngContentIndex,text.sourceSpan):new TextAst(text.value,ngContentIndex,text.sourceSpan)},TemplateParseVisitor.prototype.visitAttribute=function(attribute,context){return new AttrAst(attribute.name,attribute.value,attribute.sourceSpan)},TemplateParseVisitor.prototype.visitComment=function(comment,context){return null},TemplateParseVisitor.prototype.visitElement=function(element,parent){var _this=this,nodeName=element.name,preparsedElement=preparseElement(element);if(preparsedElement.type===PreparsedElementType.SCRIPT||preparsedElement.type===PreparsedElementType.STYLE)return null;if(preparsedElement.type===PreparsedElementType.STYLESHEET&&isStyleUrlResolvable(preparsedElement.hrefAttr))return null;var matchableAttrs=[],elementOrDirectiveProps=[],elementOrDirectiveRefs=[],elementVars=[],animationProps=[],events=[],templateElementOrDirectiveProps=[],templateMatchableAttrs=[],templateElementVars=[],hasInlineTemplates=!1,attrs=[],lcElName=splitNsName(nodeName.toLowerCase())[1],isTemplateElement=lcElName==TEMPLATE_ELEMENT;element.attrs.forEach(function(attr){var hasBinding=_this._parseAttr(isTemplateElement,attr,matchableAttrs,elementOrDirectiveProps,animationProps,events,elementOrDirectiveRefs,elementVars),hasTemplateBinding=_this._parseInlineTemplateBinding(attr,templateMatchableAttrs,templateElementOrDirectiveProps,templateElementVars);hasTemplateBinding&&hasInlineTemplates&&_this._reportError("Can't have multiple template bindings on one element. Use only one attribute named 'template' or prefixed with *",attr.sourceSpan),hasBinding||hasTemplateBinding||(attrs.push(_this.visitAttribute(attr,null)),matchableAttrs.push([attr.name,attr.value])),hasTemplateBinding&&(hasInlineTemplates=!0)});var elementCssSelector=createElementCssSelector(nodeName,matchableAttrs),_a=this._parseDirectives(this.selectorMatcher,elementCssSelector),directiveMetas=_a.directives,matchElement=_a.matchElement,references=[],directiveAsts=this._createDirectiveAsts(isTemplateElement,element.name,directiveMetas,elementOrDirectiveProps,elementOrDirectiveRefs,element.sourceSpan,references),elementProps=this._createElementPropertyAsts(element.name,elementOrDirectiveProps,directiveAsts).concat(animationProps),isViewRoot=parent.isTemplateElement||hasInlineTemplates,providerContext=new ProviderElementContext(this.providerViewContext,parent.providerContext,isViewRoot,directiveAsts,attrs,references,element.sourceSpan),children=visitAll(preparsedElement.nonBindable?NON_BINDABLE_VISITOR:this,element.children,ElementContext.create(isTemplateElement,directiveAsts,isTemplateElement?parent.providerContext:providerContext));providerContext.afterElement();var parsedElement,projectionSelector=isPresent(preparsedElement.projectAs)?CssSelector.parse(preparsedElement.projectAs)[0]:elementCssSelector,ngContentIndex=parent.findNgContentIndex(projectionSelector);if(preparsedElement.type===PreparsedElementType.NG_CONTENT)isPresent(element.children)&&element.children.length>0&&this._reportError("<ng-content> element cannot have content. <ng-content> must be immediately followed by </ng-content>",element.sourceSpan),parsedElement=new NgContentAst((this.ngContentCount++),hasInlineTemplates?null:ngContentIndex,element.sourceSpan);else if(isTemplateElement)this._assertAllEventsPublishedByDirectives(directiveAsts,events),this._assertNoComponentsNorElementBindingsOnTemplate(directiveAsts,elementProps,element.sourceSpan),parsedElement=new EmbeddedTemplateAst(attrs,events,references,elementVars,providerContext.transformedDirectiveAsts,providerContext.transformProviders,providerContext.transformedHasViewContainer,children,hasInlineTemplates?null:ngContentIndex,element.sourceSpan);else{this._assertElementExists(matchElement,element),this._assertOnlyOneComponent(directiveAsts,element.sourceSpan);var ngContentIndex_1=hasInlineTemplates?null:parent.findNgContentIndex(projectionSelector);parsedElement=new ElementAst(nodeName,attrs,elementProps,events,references,providerContext.transformedDirectiveAsts,providerContext.transformProviders,providerContext.transformedHasViewContainer,children,hasInlineTemplates?null:ngContentIndex_1,element.sourceSpan)}if(hasInlineTemplates){var templateCssSelector=createElementCssSelector(TEMPLATE_ELEMENT,templateMatchableAttrs),templateDirectiveMetas=this._parseDirectives(this.selectorMatcher,templateCssSelector).directives,templateDirectiveAsts=this._createDirectiveAsts(!0,element.name,templateDirectiveMetas,templateElementOrDirectiveProps,[],element.sourceSpan,[]),templateElementProps=this._createElementPropertyAsts(element.name,templateElementOrDirectiveProps,templateDirectiveAsts);this._assertNoComponentsNorElementBindingsOnTemplate(templateDirectiveAsts,templateElementProps,element.sourceSpan);var templateProviderContext=new ProviderElementContext(this.providerViewContext,parent.providerContext,parent.isTemplateElement,templateDirectiveAsts,[],[],element.sourceSpan);templateProviderContext.afterElement(),parsedElement=new EmbeddedTemplateAst([],[],[],templateElementVars,templateProviderContext.transformedDirectiveAsts,templateProviderContext.transformProviders,templateProviderContext.transformedHasViewContainer,[parsedElement],ngContentIndex,element.sourceSpan)}return parsedElement},TemplateParseVisitor.prototype._parseInlineTemplateBinding=function(attr,targetMatchableAttrs,targetProps,targetVars){var templateBindingsSource=null;if(this._normalizeAttributeName(attr.name)==TEMPLATE_ATTR)templateBindingsSource=attr.value;else if(attr.name.startsWith(TEMPLATE_ATTR_PREFIX)){var key=attr.name.substring(TEMPLATE_ATTR_PREFIX.length);templateBindingsSource=0==attr.value.length?key:key+" "+attr.value}if(isPresent(templateBindingsSource)){for(var bindings=this._parseTemplateBindings(templateBindingsSource,attr.sourceSpan),i=0;i<bindings.length;i++){var binding=bindings[i];binding.keyIsVar?targetVars.push(new VariableAst(binding.key,binding.name,attr.sourceSpan)):isPresent(binding.expression)?this._parsePropertyAst(binding.key,binding.expression,attr.sourceSpan,targetMatchableAttrs,targetProps):(targetMatchableAttrs.push([binding.key,""]),this._parseLiteralAttr(binding.key,null,attr.sourceSpan,targetProps))}return!0}return!1},TemplateParseVisitor.prototype._parseAttr=function(isTemplateElement,attr,targetMatchableAttrs,targetProps,targetAnimationProps,targetEvents,targetRefs,targetVars){var name=this._normalizeAttributeName(attr.name),value=attr.value,srcSpan=attr.sourceSpan,bindParts=name.match(BIND_NAME_REGEXP),hasBinding=!1;if(null!==bindParts)if(hasBinding=!0,isPresent(bindParts[KW_BIND_IDX]))this._parsePropertyOrAnimation(bindParts[IDENT_KW_IDX],value,srcSpan,targetMatchableAttrs,targetProps,targetAnimationProps);else if(bindParts[KW_LET_IDX])if(isTemplateElement){var identifier=bindParts[IDENT_KW_IDX];this._parseVariable(identifier,value,srcSpan,targetVars)}else this._reportError('"let-" is only supported on template elements.',srcSpan);else if(bindParts[KW_REF_IDX]){var identifier=bindParts[IDENT_KW_IDX];this._parseReference(identifier,value,srcSpan,targetRefs)}else bindParts[KW_ON_IDX]?this._parseEvent(bindParts[IDENT_KW_IDX],value,srcSpan,targetMatchableAttrs,targetEvents):bindParts[KW_BINDON_IDX]?(this._parsePropertyOrAnimation(bindParts[IDENT_KW_IDX],value,srcSpan,targetMatchableAttrs,targetProps,targetAnimationProps),this._parseAssignmentEvent(bindParts[IDENT_KW_IDX],value,srcSpan,targetMatchableAttrs,targetEvents)):bindParts[KW_AT_IDX]?("@"==name[0]&&isPresent(value)&&value.length>0&&this._reportError('Assigning animation triggers via @prop="exp" attributes with an expression is invalid. Use property bindings (e.g. [@prop]="exp") or use an attribute without a value (e.g. @prop) instead.',srcSpan,ParseErrorLevel.FATAL),this._parseAnimation(bindParts[IDENT_KW_IDX],value,srcSpan,targetMatchableAttrs,targetAnimationProps)):bindParts[IDENT_BANANA_BOX_IDX]?(this._parsePropertyOrAnimation(bindParts[IDENT_BANANA_BOX_IDX],value,srcSpan,targetMatchableAttrs,targetProps,targetAnimationProps),this._parseAssignmentEvent(bindParts[IDENT_BANANA_BOX_IDX],value,srcSpan,targetMatchableAttrs,targetEvents)):bindParts[IDENT_PROPERTY_IDX]?this._parsePropertyOrAnimation(bindParts[IDENT_PROPERTY_IDX],value,srcSpan,targetMatchableAttrs,targetProps,targetAnimationProps):bindParts[IDENT_EVENT_IDX]&&this._parseEvent(bindParts[IDENT_EVENT_IDX],value,srcSpan,targetMatchableAttrs,targetEvents);else hasBinding=this._parsePropertyInterpolation(name,value,srcSpan,targetMatchableAttrs,targetProps);return hasBinding||this._parseLiteralAttr(name,value,srcSpan,targetProps),hasBinding},TemplateParseVisitor.prototype._normalizeAttributeName=function(attrName){return/^data-/i.test(attrName)?attrName.substring(5):attrName},TemplateParseVisitor.prototype._parseVariable=function(identifier,value,sourceSpan,targetVars){identifier.indexOf("-")>-1&&this._reportError('"-" is not allowed in variable names',sourceSpan),targetVars.push(new VariableAst(identifier,value,sourceSpan))},TemplateParseVisitor.prototype._parseReference=function(identifier,value,sourceSpan,targetRefs){identifier.indexOf("-")>-1&&this._reportError('"-" is not allowed in reference names',sourceSpan),targetRefs.push(new ElementOrDirectiveRef(identifier,value,sourceSpan))},TemplateParseVisitor.prototype._parsePropertyOrAnimation=function(name,expression,sourceSpan,targetMatchableAttrs,targetProps,targetAnimationProps){var animatePropLength=ANIMATE_PROP_PREFIX.length,isAnimationProp="@"==name[0],animationPrefixLength=1;name.substring(0,animatePropLength)==ANIMATE_PROP_PREFIX&&(isAnimationProp=!0,animationPrefixLength=animatePropLength),isAnimationProp?this._parseAnimation(name.substr(animationPrefixLength),expression,sourceSpan,targetMatchableAttrs,targetAnimationProps):this._parsePropertyAst(name,this._parseBinding(expression,sourceSpan),sourceSpan,targetMatchableAttrs,targetProps)},TemplateParseVisitor.prototype._parseAnimation=function(name,expression,sourceSpan,targetMatchableAttrs,targetAnimationProps){isPresent(expression)&&0!=expression.length||(expression="null");var ast=this._parseBinding(expression,sourceSpan);targetMatchableAttrs.push([name,ast.source]),targetAnimationProps.push(new BoundElementPropertyAst(name,exports.PropertyBindingType.Animation,_angular_core.SecurityContext.NONE,ast,null,sourceSpan))},TemplateParseVisitor.prototype._parsePropertyInterpolation=function(name,value,sourceSpan,targetMatchableAttrs,targetProps){var expr=this._parseInterpolation(value,sourceSpan);return!!isPresent(expr)&&(this._parsePropertyAst(name,expr,sourceSpan,targetMatchableAttrs,targetProps),!0)},TemplateParseVisitor.prototype._parsePropertyAst=function(name,ast,sourceSpan,targetMatchableAttrs,targetProps){targetMatchableAttrs.push([name,ast.source]),targetProps.push(new BoundElementOrDirectiveProperty(name,ast,(!1),sourceSpan))},TemplateParseVisitor.prototype._parseAssignmentEvent=function(name,expression,sourceSpan,targetMatchableAttrs,targetEvents){this._parseEvent(name+"Change",expression+"=$event",sourceSpan,targetMatchableAttrs,targetEvents)},TemplateParseVisitor.prototype._parseEvent=function(name,expression,sourceSpan,targetMatchableAttrs,targetEvents){var parts=splitAtColon(name,[null,name]),target=parts[0],eventName=parts[1],ast=this._parseAction(expression,sourceSpan);targetMatchableAttrs.push([name,ast.source]),targetEvents.push(new BoundEventAst(eventName,target,ast,sourceSpan))},TemplateParseVisitor.prototype._parseLiteralAttr=function(name,value,sourceSpan,targetProps){targetProps.push(new BoundElementOrDirectiveProperty(name,this._exprParser.wrapLiteralPrimitive(value,""),(!0),sourceSpan))},TemplateParseVisitor.prototype._parseDirectives=function(selectorMatcher,elementCssSelector){var _this=this,directives=new Array(this.directivesIndex.size),matchElement=!1;return selectorMatcher.match(elementCssSelector,function(selector,directive){directives[_this.directivesIndex.get(directive)]=directive,matchElement=matchElement||selector.hasElementSelector()}),{directives:directives.filter(function(dir){return!!dir}),matchElement:matchElement}},TemplateParseVisitor.prototype._createDirectiveAsts=function(isTemplateElement,elementName,directives,props,elementOrDirectiveRefs,elementSourceSpan,targetReferences){var _this=this,matchedReferences=new Set,component=null,directiveAsts=directives.map(function(directive){var sourceSpan=new ParseSourceSpan(elementSourceSpan.start,elementSourceSpan.end,"Directive "+directive.type.name);directive.isComponent&&(component=directive);var hostProperties=[],hostEvents=[],directiveProperties=[];return _this._createDirectiveHostPropertyAsts(elementName,directive.hostProperties,sourceSpan,hostProperties),_this._createDirectiveHostEventAsts(directive.hostListeners,sourceSpan,hostEvents),_this._createDirectivePropertyAsts(directive.inputs,props,directiveProperties),
elementOrDirectiveRefs.forEach(function(elOrDirRef){(0===elOrDirRef.value.length&&directive.isComponent||directive.exportAs==elOrDirRef.value)&&(targetReferences.push(new ReferenceAst(elOrDirRef.name,identifierToken(directive.type),elOrDirRef.sourceSpan)),matchedReferences.add(elOrDirRef.name))}),new DirectiveAst(directive,directiveProperties,hostProperties,hostEvents,sourceSpan)});return elementOrDirectiveRefs.forEach(function(elOrDirRef){if(elOrDirRef.value.length>0)matchedReferences.has(elOrDirRef.name)||_this._reportError('There is no directive with "exportAs" set to "'+elOrDirRef.value+'"',elOrDirRef.sourceSpan);else if(!component){var refToken=null;isTemplateElement&&(refToken=resolveIdentifierToken(Identifiers.TemplateRef)),targetReferences.push(new ReferenceAst(elOrDirRef.name,refToken,elOrDirRef.sourceSpan))}}),directiveAsts},TemplateParseVisitor.prototype._createDirectiveHostPropertyAsts=function(elementName,hostProps,sourceSpan,targetPropertyAsts){var _this=this;hostProps&&StringMapWrapper.forEach(hostProps,function(expression,propName){if(isString(expression)){var exprAst=_this._parseBinding(expression,sourceSpan);targetPropertyAsts.push(_this._createElementPropertyAst(elementName,propName,exprAst,sourceSpan))}else _this._reportError('Value of the host property binding "'+propName+'" needs to be a string representing an expression but got "'+expression+'" ('+typeof expression+")",sourceSpan)})},TemplateParseVisitor.prototype._createDirectiveHostEventAsts=function(hostListeners,sourceSpan,targetEventAsts){var _this=this;hostListeners&&StringMapWrapper.forEach(hostListeners,function(expression,propName){isString(expression)?_this._parseEvent(propName,expression,sourceSpan,[],targetEventAsts):_this._reportError('Value of the host listener "'+propName+'" needs to be a string representing an expression but got "'+expression+'" ('+typeof expression+")",sourceSpan)})},TemplateParseVisitor.prototype._createDirectivePropertyAsts=function(directiveProperties,boundProps,targetBoundDirectiveProps){if(directiveProperties){var boundPropsByName_1=new Map;boundProps.forEach(function(boundProp){var prevValue=boundPropsByName_1.get(boundProp.name);(isBlank(prevValue)||prevValue.isLiteral)&&boundPropsByName_1.set(boundProp.name,boundProp)}),StringMapWrapper.forEach(directiveProperties,function(elProp,dirProp){var boundProp=boundPropsByName_1.get(elProp);boundProp&&targetBoundDirectiveProps.push(new BoundDirectivePropertyAst(dirProp,boundProp.name,boundProp.expression,boundProp.sourceSpan))})}},TemplateParseVisitor.prototype._createElementPropertyAsts=function(elementName,props,directives){var _this=this,boundElementProps=[],boundDirectivePropsIndex=new Map;return directives.forEach(function(directive){directive.inputs.forEach(function(prop){boundDirectivePropsIndex.set(prop.templateName,prop)})}),props.forEach(function(prop){!prop.isLiteral&&isBlank(boundDirectivePropsIndex.get(prop.name))&&boundElementProps.push(_this._createElementPropertyAst(elementName,prop.name,prop.expression,prop.sourceSpan))}),boundElementProps},TemplateParseVisitor.prototype._createElementPropertyAst=function(elementName,name,ast,sourceSpan){var bindingType,boundPropertyName,securityContext,unit=null,parts=name.split(PROPERTY_PARTS_SEPARATOR);if(1===parts.length){var partValue=parts[0];if("@"==partValue[0])boundPropertyName=partValue.substr(1),bindingType=exports.PropertyBindingType.Animation,securityContext=_angular_core.SecurityContext.NONE;else if(boundPropertyName=this._schemaRegistry.getMappedPropName(partValue),securityContext=this._schemaRegistry.securityContext(elementName,boundPropertyName),bindingType=exports.PropertyBindingType.Property,this._assertNoEventBinding(boundPropertyName,sourceSpan),!this._schemaRegistry.hasProperty(elementName,boundPropertyName,this._schemas)){var errorMsg="Can't bind to '"+boundPropertyName+"' since it isn't a known property of '"+elementName+"'.";elementName.indexOf("-")>-1&&(errorMsg+="\n1. If '"+elementName+"' is an Angular component and it has '"+boundPropertyName+"' input, then verify that it is part of this module."+("\n2. If '"+elementName+"' is a Web Component then add \"CUSTOM_ELEMENTS_SCHEMA\" to the '@NgModule.schema' of this component to suppress this message.\n")),this._reportError(errorMsg,sourceSpan)}}else if(parts[0]==ATTRIBUTE_PREFIX){boundPropertyName=parts[1],this._assertNoEventBinding(boundPropertyName,sourceSpan);var mapPropName=this._schemaRegistry.getMappedPropName(boundPropertyName);securityContext=this._schemaRegistry.securityContext(elementName,mapPropName);var nsSeparatorIdx=boundPropertyName.indexOf(":");if(nsSeparatorIdx>-1){var ns=boundPropertyName.substring(0,nsSeparatorIdx),name_1=boundPropertyName.substring(nsSeparatorIdx+1);boundPropertyName=mergeNsAndName(ns,name_1)}bindingType=exports.PropertyBindingType.Attribute}else parts[0]==CLASS_PREFIX?(boundPropertyName=parts[1],bindingType=exports.PropertyBindingType.Class,securityContext=_angular_core.SecurityContext.NONE):parts[0]==STYLE_PREFIX?(unit=parts.length>2?parts[2]:null,boundPropertyName=parts[1],bindingType=exports.PropertyBindingType.Style,securityContext=_angular_core.SecurityContext.STYLE):(this._reportError("Invalid property name '"+name+"'",sourceSpan),bindingType=null,securityContext=null);return new BoundElementPropertyAst(boundPropertyName,bindingType,securityContext,ast,unit,sourceSpan)},TemplateParseVisitor.prototype._assertNoEventBinding=function(propName,sourceSpan){propName.toLowerCase().startsWith("on")&&this._reportError("Binding to event attribute '"+propName+"' is disallowed "+("for security reasons, please use ("+propName.slice(2)+")=..."),sourceSpan,ParseErrorLevel.FATAL)},TemplateParseVisitor.prototype._findComponentDirectiveNames=function(directives){var componentTypeNames=[];return directives.forEach(function(directive){var typeName=directive.directive.type.name;directive.directive.isComponent&&componentTypeNames.push(typeName)}),componentTypeNames},TemplateParseVisitor.prototype._assertOnlyOneComponent=function(directives,sourceSpan){var componentTypeNames=this._findComponentDirectiveNames(directives);componentTypeNames.length>1&&this._reportError("More than one component: "+componentTypeNames.join(","),sourceSpan)},TemplateParseVisitor.prototype._assertElementExists=function(matchElement,element){var elName=element.name.replace(/^:xhtml:/,"");if(!matchElement&&!this._schemaRegistry.hasElement(elName,this._schemas)){var errorMsg="'"+elName+"' is not a known element:\n"+("1. If '"+elName+"' is an Angular component, then verify that it is part of this module.\n")+("2. If '"+elName+"' is a Web Component then add \"CUSTOM_ELEMENTS_SCHEMA\" to the '@NgModule.schema' of this component to suppress this message.");this._reportError(errorMsg,element.sourceSpan)}},TemplateParseVisitor.prototype._assertNoComponentsNorElementBindingsOnTemplate=function(directives,elementProps,sourceSpan){var _this=this,componentTypeNames=this._findComponentDirectiveNames(directives);componentTypeNames.length>0&&this._reportError("Components on an embedded template: "+componentTypeNames.join(","),sourceSpan),elementProps.forEach(function(prop){_this._reportError("Property binding "+prop.name+' not used by any directive on an embedded template. Make sure that the property name is spelled correctly and all directives are listed in the "directives" section.',sourceSpan)})},TemplateParseVisitor.prototype._assertAllEventsPublishedByDirectives=function(directives,events){var _this=this,allDirectiveEvents=new Set;directives.forEach(function(directive){StringMapWrapper.forEach(directive.directive.outputs,function(eventName){allDirectiveEvents.add(eventName)})}),events.forEach(function(event){!isPresent(event.target)&&allDirectiveEvents.has(event.name)||_this._reportError("Event binding "+event.fullName+' not emitted by any directive on an embedded template. Make sure that the event name is spelled correctly and all directives are listed in the "directives" section.',event.sourceSpan)})},TemplateParseVisitor}(),NonBindableVisitor=function(){function NonBindableVisitor(){}return NonBindableVisitor.prototype.visitElement=function(ast,parent){var preparsedElement=preparseElement(ast);if(preparsedElement.type===PreparsedElementType.SCRIPT||preparsedElement.type===PreparsedElementType.STYLE||preparsedElement.type===PreparsedElementType.STYLESHEET)return null;var attrNameAndValues=ast.attrs.map(function(attrAst){return[attrAst.name,attrAst.value]}),selector=createElementCssSelector(ast.name,attrNameAndValues),ngContentIndex=parent.findNgContentIndex(selector),children=visitAll(this,ast.children,EMPTY_ELEMENT_CONTEXT);return new ElementAst(ast.name,visitAll(this,ast.attrs),[],[],[],[],[],(!1),children,ngContentIndex,ast.sourceSpan)},NonBindableVisitor.prototype.visitComment=function(comment,context){return null},NonBindableVisitor.prototype.visitAttribute=function(attribute,context){return new AttrAst(attribute.name,attribute.value,attribute.sourceSpan)},NonBindableVisitor.prototype.visitText=function(text,parent){var ngContentIndex=parent.findNgContentIndex(TEXT_CSS_SELECTOR);return new TextAst(text.value,ngContentIndex,text.sourceSpan)},NonBindableVisitor.prototype.visitExpansion=function(expansion,context){return expansion},NonBindableVisitor.prototype.visitExpansionCase=function(expansionCase,context){return expansionCase},NonBindableVisitor}(),BoundElementOrDirectiveProperty=function(){function BoundElementOrDirectiveProperty(name,expression,isLiteral,sourceSpan){this.name=name,this.expression=expression,this.isLiteral=isLiteral,this.sourceSpan=sourceSpan}return BoundElementOrDirectiveProperty}(),ElementOrDirectiveRef=function(){function ElementOrDirectiveRef(name,value,sourceSpan){this.name=name,this.value=value,this.sourceSpan=sourceSpan}return ElementOrDirectiveRef}(),ElementContext=function(){function ElementContext(isTemplateElement,_ngContentIndexMatcher,_wildcardNgContentIndex,providerContext){this.isTemplateElement=isTemplateElement,this._ngContentIndexMatcher=_ngContentIndexMatcher,this._wildcardNgContentIndex=_wildcardNgContentIndex,this.providerContext=providerContext}return ElementContext.create=function(isTemplateElement,directives,providerContext){var matcher=new SelectorMatcher,wildcardNgContentIndex=null,component=directives.find(function(directive){return directive.directive.isComponent});if(component)for(var ngContentSelectors=component.directive.template.ngContentSelectors,i=0;i<ngContentSelectors.length;i++){var selector=ngContentSelectors[i];"*"===selector?wildcardNgContentIndex=i:matcher.addSelectables(CssSelector.parse(ngContentSelectors[i]),i)}return new ElementContext(isTemplateElement,matcher,wildcardNgContentIndex,providerContext)},ElementContext.prototype.findNgContentIndex=function(selector){var ngContentIndices=[];return this._ngContentIndexMatcher.match(selector,function(selector,ngContentIndex){ngContentIndices.push(ngContentIndex)}),ngContentIndices.sort(),isPresent(this._wildcardNgContentIndex)&&ngContentIndices.push(this._wildcardNgContentIndex),ngContentIndices.length>0?ngContentIndices[0]:null},ElementContext}(),EMPTY_ELEMENT_CONTEXT=new ElementContext((!0),new SelectorMatcher,null,null),NON_BINDABLE_VISITOR=new NonBindableVisitor,PipeCollector=function(_super){function PipeCollector(){_super.apply(this,arguments),this.pipes=new Set}return __extends(PipeCollector,_super),PipeCollector.prototype.visitPipe=function(ast,context){return this.pipes.add(ast.name),ast.exp.visit(this),this.visitAll(ast.args,context),null},PipeCollector}(RecursiveAstVisitor),CompilerConfig=function(){function CompilerConfig(_a){var _b=void 0===_a?{}:_a,_c=_b.renderTypes,renderTypes=void 0===_c?new DefaultRenderTypes:_c,_d=_b.defaultEncapsulation,defaultEncapsulation=void 0===_d?_angular_core.ViewEncapsulation.Emulated:_d,genDebugInfo=_b.genDebugInfo,logBindingUpdate=_b.logBindingUpdate,_e=_b.useJit,useJit=void 0===_e||_e;this.renderTypes=renderTypes,this.defaultEncapsulation=defaultEncapsulation,this._genDebugInfo=genDebugInfo,this._logBindingUpdate=logBindingUpdate,this.useJit=useJit}return Object.defineProperty(CompilerConfig.prototype,"genDebugInfo",{get:function(){return void 0===this._genDebugInfo?_angular_core.isDevMode():this._genDebugInfo},enumerable:!0,configurable:!0}),Object.defineProperty(CompilerConfig.prototype,"logBindingUpdate",{get:function(){return void 0===this._logBindingUpdate?_angular_core.isDevMode():this._logBindingUpdate},enumerable:!0,configurable:!0}),CompilerConfig}(),RenderTypes=function(){function RenderTypes(){}return Object.defineProperty(RenderTypes.prototype,"renderer",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),Object.defineProperty(RenderTypes.prototype,"renderText",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),Object.defineProperty(RenderTypes.prototype,"renderElement",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),Object.defineProperty(RenderTypes.prototype,"renderComment",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),Object.defineProperty(RenderTypes.prototype,"renderNode",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),Object.defineProperty(RenderTypes.prototype,"renderEvent",{get:function(){return unimplemented$1()},enumerable:!0,configurable:!0}),RenderTypes}(),DefaultRenderTypes=function(){function DefaultRenderTypes(){this.renderText=null,this.renderElement=null,this.renderComment=null,this.renderNode=null,this.renderEvent=null}return Object.defineProperty(DefaultRenderTypes.prototype,"renderer",{get:function(){return resolveIdentifier(Identifiers.Renderer)},enumerable:!0,configurable:!0}),DefaultRenderTypes}(),__extends$11=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},AnimationAst=function(){function AnimationAst(){this.startTime=0,this.playTime=0}return AnimationAst}(),AnimationStateAst=function(_super){function AnimationStateAst(){_super.apply(this,arguments)}return __extends$11(AnimationStateAst,_super),AnimationStateAst}(AnimationAst),AnimationEntryAst=function(_super){function AnimationEntryAst(name,stateDeclarations,stateTransitions){_super.call(this),this.name=name,this.stateDeclarations=stateDeclarations,this.stateTransitions=stateTransitions}return __extends$11(AnimationEntryAst,_super),AnimationEntryAst.prototype.visit=function(visitor,context){return visitor.visitAnimationEntry(this,context)},AnimationEntryAst}(AnimationAst),AnimationStateDeclarationAst=function(_super){function AnimationStateDeclarationAst(stateName,styles){_super.call(this),this.stateName=stateName,this.styles=styles}return __extends$11(AnimationStateDeclarationAst,_super),AnimationStateDeclarationAst.prototype.visit=function(visitor,context){return visitor.visitAnimationStateDeclaration(this,context)},AnimationStateDeclarationAst}(AnimationStateAst),AnimationStateTransitionExpression=function(){function AnimationStateTransitionExpression(fromState,toState){this.fromState=fromState,this.toState=toState}return AnimationStateTransitionExpression}(),AnimationStateTransitionAst=function(_super){function AnimationStateTransitionAst(stateChanges,animation){_super.call(this),this.stateChanges=stateChanges,this.animation=animation}return __extends$11(AnimationStateTransitionAst,_super),AnimationStateTransitionAst.prototype.visit=function(visitor,context){return visitor.visitAnimationStateTransition(this,context)},AnimationStateTransitionAst}(AnimationStateAst),AnimationStepAst=function(_super){function AnimationStepAst(startingStyles,keyframes,duration,delay,easing){_super.call(this),this.startingStyles=startingStyles,this.keyframes=keyframes,this.duration=duration,this.delay=delay,this.easing=easing}return __extends$11(AnimationStepAst,_super),AnimationStepAst.prototype.visit=function(visitor,context){return visitor.visitAnimationStep(this,context)},AnimationStepAst}(AnimationAst),AnimationStylesAst=function(_super){function AnimationStylesAst(styles){_super.call(this),this.styles=styles}return __extends$11(AnimationStylesAst,_super),AnimationStylesAst.prototype.visit=function(visitor,context){return visitor.visitAnimationStyles(this,context)},AnimationStylesAst}(AnimationAst),AnimationKeyframeAst=function(_super){function AnimationKeyframeAst(offset,styles){_super.call(this),this.offset=offset,this.styles=styles}return __extends$11(AnimationKeyframeAst,_super),AnimationKeyframeAst.prototype.visit=function(visitor,context){return visitor.visitAnimationKeyframe(this,context)},AnimationKeyframeAst}(AnimationAst),AnimationWithStepsAst=function(_super){function AnimationWithStepsAst(steps){_super.call(this),this.steps=steps}return __extends$11(AnimationWithStepsAst,_super),AnimationWithStepsAst}(AnimationAst),AnimationGroupAst=function(_super){function AnimationGroupAst(steps){_super.call(this,steps)}return __extends$11(AnimationGroupAst,_super),AnimationGroupAst.prototype.visit=function(visitor,context){return visitor.visitAnimationGroup(this,context)},AnimationGroupAst}(AnimationWithStepsAst),AnimationSequenceAst=function(_super){function AnimationSequenceAst(steps){_super.call(this,steps)}return __extends$11(AnimationSequenceAst,_super),AnimationSequenceAst.prototype.visit=function(visitor,context){return visitor.visitAnimationSequence(this,context)},AnimationSequenceAst}(AnimationWithStepsAst),Math$2=global$1.Math,StylesCollectionEntry=function(){function StylesCollectionEntry(time,value){this.time=time,this.value=value}return StylesCollectionEntry.prototype.matches=function(time,value){return time==this.time&&value==this.value},StylesCollectionEntry}(),StylesCollection=function(){function StylesCollection(){this.styles={}}return StylesCollection.prototype.insertAtTime=function(property,time,value){var tuple=new StylesCollectionEntry(time,value),entries=this.styles[property];isPresent(entries)||(entries=this.styles[property]=[]);for(var insertionIndex=0,i=entries.length-1;i>=0;i--)if(entries[i].time<=time){insertionIndex=i+1;break}ListWrapper.insert(entries,insertionIndex,tuple)},StylesCollection.prototype.getByIndex=function(property,index){var items=this.styles[property];return isPresent(items)?index>=items.length?null:items[index]:null},StylesCollection.prototype.indexOfAtOrBeforeTime=function(property,time){var entries=this.styles[property];if(isPresent(entries))for(var i=entries.length-1;i>=0;i--)if(entries[i].time<=time)return i;return null},StylesCollection}(),__extends$12=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},_INITIAL_KEYFRAME=0,_TERMINAL_KEYFRAME=1,_ONE_SECOND=1e3,AnimationParseError=function(_super){function AnimationParseError(message){_super.call(this,null,message)}return __extends$12(AnimationParseError,_super),AnimationParseError.prototype.toString=function(){return""+this.msg},AnimationParseError}(ParseError),ParsedAnimationResult=function(){function ParsedAnimationResult(ast,errors){this.ast=ast,this.errors=errors}return ParsedAnimationResult}(),_AnimationTimings=function(){function _AnimationTimings(duration,delay,easing){this.duration=duration,this.delay=delay,this.easing=easing}return _AnimationTimings}(),animationCompilationCache=new Map,CompiledAnimationTriggerResult=function(){function CompiledAnimationTriggerResult(name,statesMapStatement,statesVariableName,fnStatement,fnVariable){this.name=name,this.statesMapStatement=statesMapStatement,this.statesVariableName=statesVariableName,this.fnStatement=fnStatement,this.fnVariable=fnVariable}return CompiledAnimationTriggerResult}(),CompiledComponentAnimationResult=function(){function CompiledComponentAnimationResult(outputs,triggers){this.outputs=outputs,this.triggers=triggers}return CompiledComponentAnimationResult}(),AnimationCompiler=function(){function AnimationCompiler(){}return AnimationCompiler.prototype.compileComponent=function(component,template){var compiledAnimations=[],groupedErrors=[],triggerLookup={},componentName=component.type.name;component.template.animations.forEach(function(entry){var result=parseAnimationEntry(entry),triggerName=entry.name;if(result.errors.length>0){var errorMessage='Unable to parse the animation sequence for "'+triggerName+'" due to the following errors:';result.errors.forEach(function(error){errorMessage+="\n-- "+error.msg}),groupedErrors.push(errorMessage)}if(triggerLookup[triggerName])groupedErrors.push('The animation trigger "'+triggerName+'" has already been registered on "'+componentName+'"');else{var factoryName=componentName+"_"+entry.name,visitor=new _AnimationBuilder(triggerName,factoryName),compileResult=visitor.build(result.ast);compiledAnimations.push(compileResult),triggerLookup[entry.name]=compileResult}});var validatedProperties=_validateAnimationProperties(compiledAnimations,template);if(validatedProperties.errors.forEach(function(error){groupedErrors.push(error.msg)}),groupedErrors.length>0){var errorMessageStr="Animation parsing for "+component.type.name+" has failed due to the following errors:";throw groupedErrors.forEach(function(error){return errorMessageStr+="\n- "+error}),new Error(errorMessageStr)}return animationCompilationCache.set(component,compiledAnimations),new CompiledComponentAnimationResult(validatedProperties.outputs,compiledAnimations)},AnimationCompiler}(),_ANIMATION_FACTORY_ELEMENT_VAR=variable("element"),_ANIMATION_DEFAULT_STATE_VAR=variable("defaultStateStyles"),_ANIMATION_FACTORY_VIEW_VAR=variable("view"),_ANIMATION_FACTORY_RENDERER_VAR=_ANIMATION_FACTORY_VIEW_VAR.prop("renderer"),_ANIMATION_CURRENT_STATE_VAR=variable("currentState"),_ANIMATION_NEXT_STATE_VAR=variable("nextState"),_ANIMATION_PLAYER_VAR=variable("player"),_ANIMATION_TIME_VAR=variable("totalTime"),_ANIMATION_START_STATE_STYLES_VAR=variable("startStateStyles"),_ANIMATION_END_STATE_STYLES_VAR=variable("endStateStyles"),_ANIMATION_COLLECTED_STYLES=variable("collectedStyles"),EMPTY_MAP$1=literalMap([]),_AnimationBuilder=function(){function _AnimationBuilder(animationName,factoryName){this.animationName=animationName,this._fnVarName=factoryName+"_factory",this._statesMapVarName=factoryName+"_states",this._statesMapVar=variable(this._statesMapVarName)}return _AnimationBuilder.prototype.visitAnimationStyles=function(ast,context){var stylesArr=[];return context.isExpectingFirstStyleStep&&(stylesArr.push(_ANIMATION_START_STATE_STYLES_VAR),context.isExpectingFirstStyleStep=!1),ast.styles.forEach(function(entry){stylesArr.push(literalMap(StringMapWrapper.keys(entry).map(function(key){return[key,literal(entry[key])]})))}),importExpr(resolveIdentifier(Identifiers.AnimationStyles)).instantiate([importExpr(resolveIdentifier(Identifiers.collectAndResolveStyles)).callFn([_ANIMATION_COLLECTED_STYLES,literalArr(stylesArr)])])},_AnimationBuilder.prototype.visitAnimationKeyframe=function(ast,context){return importExpr(resolveIdentifier(Identifiers.AnimationKeyframe)).instantiate([literal(ast.offset),ast.styles.visit(this,context)])},_AnimationBuilder.prototype.visitAnimationStep=function(ast,context){var _this=this;if(context.endStateAnimateStep===ast)return this._visitEndStateAnimation(ast,context);var startingStylesExpr=ast.startingStyles.visit(this,context),keyframeExpressions=ast.keyframes.map(function(keyframeEntry){return keyframeEntry.visit(_this,context)});return this._callAnimateMethod(ast,startingStylesExpr,literalArr(keyframeExpressions),context)},_AnimationBuilder.prototype._visitEndStateAnimation=function(ast,context){var _this=this,startingStylesExpr=ast.startingStyles.visit(this,context),keyframeExpressions=ast.keyframes.map(function(keyframe){return keyframe.visit(_this,context)}),keyframesExpr=importExpr(resolveIdentifier(Identifiers.balanceAnimationKeyframes)).callFn([_ANIMATION_COLLECTED_STYLES,_ANIMATION_END_STATE_STYLES_VAR,literalArr(keyframeExpressions)]);return this._callAnimateMethod(ast,startingStylesExpr,keyframesExpr,context)},_AnimationBuilder.prototype._callAnimateMethod=function(ast,startingStylesExpr,keyframesExpr,context){return context.totalTransitionTime+=ast.duration+ast.delay,_ANIMATION_FACTORY_RENDERER_VAR.callMethod("animate",[_ANIMATION_FACTORY_ELEMENT_VAR,startingStylesExpr,keyframesExpr,literal(ast.duration),literal(ast.delay),literal(ast.easing)])},_AnimationBuilder.prototype.visitAnimationSequence=function(ast,context){var _this=this,playerExprs=ast.steps.map(function(step){return step.visit(_this,context)});return importExpr(resolveIdentifier(Identifiers.AnimationSequencePlayer)).instantiate([literalArr(playerExprs)])},_AnimationBuilder.prototype.visitAnimationGroup=function(ast,context){var _this=this,playerExprs=ast.steps.map(function(step){return step.visit(_this,context)});return importExpr(resolveIdentifier(Identifiers.AnimationGroupPlayer)).instantiate([literalArr(playerExprs)])},_AnimationBuilder.prototype.visitAnimationStateDeclaration=function(ast,context){var flatStyles={};_getStylesArray(ast).forEach(function(entry){StringMapWrapper.forEach(entry,function(value,key){flatStyles[key]=value})}),context.stateMap.registerState(ast.stateName,flatStyles)},_AnimationBuilder.prototype.visitAnimationStateTransition=function(ast,context){var steps=ast.animation.steps,lastStep=steps[steps.length-1];_isEndStateAnimateStep(lastStep)&&(context.endStateAnimateStep=lastStep),context.totalTransitionTime=0,context.isExpectingFirstStyleStep=!0;var stateChangePreconditions=[];ast.stateChanges.forEach(function(stateChange){stateChangePreconditions.push(_compareToAnimationStateExpr(_ANIMATION_CURRENT_STATE_VAR,stateChange.fromState).and(_compareToAnimationStateExpr(_ANIMATION_NEXT_STATE_VAR,stateChange.toState))),stateChange.fromState!=ANY_STATE&&context.stateMap.registerState(stateChange.fromState),stateChange.toState!=ANY_STATE&&context.stateMap.registerState(stateChange.toState)});var animationPlayerExpr=ast.animation.visit(this,context),reducedStateChangesPrecondition=stateChangePreconditions.reduce(function(a,b){return a.or(b)}),precondition=_ANIMATION_PLAYER_VAR.equals(NULL_EXPR).and(reducedStateChangesPrecondition),animationStmt=_ANIMATION_PLAYER_VAR.set(animationPlayerExpr).toStmt(),totalTimeStmt=_ANIMATION_TIME_VAR.set(literal(context.totalTransitionTime)).toStmt();return new IfStmt(precondition,[animationStmt,totalTimeStmt])},_AnimationBuilder.prototype.visitAnimationEntry=function(ast,context){var _this=this;ast.stateDeclarations.forEach(function(def){return def.visit(_this,context)}),context.stateMap.registerState(DEFAULT_STATE,{});var statements=[];statements.push(_ANIMATION_FACTORY_VIEW_VAR.callMethod("cancelActiveAnimation",[_ANIMATION_FACTORY_ELEMENT_VAR,literal(this.animationName),_ANIMATION_NEXT_STATE_VAR.equals(literal(EMPTY_ANIMATION_STATE))]).toStmt()),statements.push(_ANIMATION_COLLECTED_STYLES.set(EMPTY_MAP$1).toDeclStmt()),statements.push(_ANIMATION_PLAYER_VAR.set(NULL_EXPR).toDeclStmt()),statements.push(_ANIMATION_TIME_VAR.set(literal(0)).toDeclStmt()),statements.push(_ANIMATION_DEFAULT_STATE_VAR.set(this._statesMapVar.key(literal(DEFAULT_STATE))).toDeclStmt()),statements.push(_ANIMATION_START_STATE_STYLES_VAR.set(this._statesMapVar.key(_ANIMATION_CURRENT_STATE_VAR)).toDeclStmt()),statements.push(new IfStmt(_ANIMATION_START_STATE_STYLES_VAR.equals(NULL_EXPR),[_ANIMATION_START_STATE_STYLES_VAR.set(_ANIMATION_DEFAULT_STATE_VAR).toStmt()])),statements.push(_ANIMATION_END_STATE_STYLES_VAR.set(this._statesMapVar.key(_ANIMATION_NEXT_STATE_VAR)).toDeclStmt()),statements.push(new IfStmt(_ANIMATION_END_STATE_STYLES_VAR.equals(NULL_EXPR),[_ANIMATION_END_STATE_STYLES_VAR.set(_ANIMATION_DEFAULT_STATE_VAR).toStmt()]));var RENDER_STYLES_FN=importExpr(resolveIdentifier(Identifiers.renderStyles));return statements.push(RENDER_STYLES_FN.callFn([_ANIMATION_FACTORY_ELEMENT_VAR,_ANIMATION_FACTORY_RENDERER_VAR,importExpr(resolveIdentifier(Identifiers.clearStyles)).callFn([_ANIMATION_START_STATE_STYLES_VAR])]).toStmt()),ast.stateTransitions.forEach(function(transAst){return statements.push(transAst.visit(_this,context))}),statements.push(new IfStmt(_ANIMATION_PLAYER_VAR.equals(NULL_EXPR),[_ANIMATION_PLAYER_VAR.set(importExpr(resolveIdentifier(Identifiers.NoOpAnimationPlayer)).instantiate([])).toStmt()])),statements.push(_ANIMATION_PLAYER_VAR.callMethod("onDone",[fn([],[RENDER_STYLES_FN.callFn([_ANIMATION_FACTORY_ELEMENT_VAR,_ANIMATION_FACTORY_RENDERER_VAR,importExpr(resolveIdentifier(Identifiers.prepareFinalAnimationStyles)).callFn([_ANIMATION_START_STATE_STYLES_VAR,_ANIMATION_END_STATE_STYLES_VAR])]).toStmt()])]).toStmt()),statements.push(_ANIMATION_FACTORY_VIEW_VAR.callMethod("queueAnimation",[_ANIMATION_FACTORY_ELEMENT_VAR,literal(this.animationName),_ANIMATION_PLAYER_VAR,_ANIMATION_TIME_VAR,_ANIMATION_CURRENT_STATE_VAR,_ANIMATION_NEXT_STATE_VAR]).toStmt()),fn([new FnParam(_ANIMATION_FACTORY_VIEW_VAR.name,importType(resolveIdentifier(Identifiers.AppView),[DYNAMIC_TYPE])),new FnParam(_ANIMATION_FACTORY_ELEMENT_VAR.name,DYNAMIC_TYPE),new FnParam(_ANIMATION_CURRENT_STATE_VAR.name,DYNAMIC_TYPE),new FnParam(_ANIMATION_NEXT_STATE_VAR.name,DYNAMIC_TYPE)],statements)},_AnimationBuilder.prototype.build=function(ast){var context=new _AnimationBuilderContext,fnStatement=ast.visit(this,context).toDeclStmt(this._fnVarName),fnVariable=variable(this._fnVarName),lookupMap=[];StringMapWrapper.forEach(context.stateMap.states,function(value,stateName){var variableValue=EMPTY_MAP$1;if(isPresent(value)){var styleMap_1=[];StringMapWrapper.forEach(value,function(value,key){styleMap_1.push([key,literal(value)])}),variableValue=literalMap(styleMap_1)}lookupMap.push([stateName,variableValue])});var compiledStatesMapExpr=this._statesMapVar.set(literalMap(lookupMap)).toDeclStmt();return new CompiledAnimationTriggerResult(this.animationName,compiledStatesMapExpr,this._statesMapVarName,fnStatement,fnVariable)},_AnimationBuilder}(),_AnimationBuilderContext=function(){function _AnimationBuilderContext(){this.stateMap=new _AnimationBuilderStateMap,this.endStateAnimateStep=null,this.isExpectingFirstStyleStep=!1,this.totalTransitionTime=0}return _AnimationBuilderContext}(),_AnimationBuilderStateMap=function(){function _AnimationBuilderStateMap(){this._states={}}return Object.defineProperty(_AnimationBuilderStateMap.prototype,"states",{get:function(){return this._states},enumerable:!0,configurable:!0}),_AnimationBuilderStateMap.prototype.registerState=function(name,value){void 0===value&&(value=null);var existingEntry=this._states[name];isBlank(existingEntry)&&(this._states[name]=value)},_AnimationBuilderStateMap}(),AnimationPropertyValidationOutput=function(){function AnimationPropertyValidationOutput(outputs,errors){this.outputs=outputs,this.errors=errors}return AnimationPropertyValidationOutput}(),_AnimationTemplatePropertyVisitor=function(){function _AnimationTemplatePropertyVisitor(animations){this.errors=[],this.outputs=[],this._animationRegistry=this._buildCompileAnimationLookup(animations)}return _AnimationTemplatePropertyVisitor.prototype._buildCompileAnimationLookup=function(animations){var map={};return animations.forEach(function(entry){map[entry.name]=!0}),map},_AnimationTemplatePropertyVisitor.prototype._validateAnimationInputOutputPairs=function(inputAsts,outputAsts,animationRegistry,isHostLevel){var _this=this,detectedAnimationInputs={};inputAsts.forEach(function(input){if(input.type==exports.PropertyBindingType.Animation){var triggerName=input.name;isPresent(animationRegistry[triggerName])?detectedAnimationInputs[triggerName]=!0:_this.errors.push(new AnimationParseError("Couldn't find an animation entry for "+triggerName))}}),outputAsts.forEach(function(output){if("@"==output.name[0]){var normalizedOutputData=parseAnimationOutputName(output.name.substr(1),_this.errors),triggerName=normalizedOutputData.name,triggerEventPhase=normalizedOutputData.phase;
animationRegistry[triggerName]?detectedAnimationInputs[triggerName]?_this.outputs.push(normalizedOutputData):_this.errors.push(new AnimationParseError("Unable to listen on (@"+triggerName+"."+triggerEventPhase+") because the animation trigger [@"+triggerName+"] isn't being used on the same element")):_this.errors.push(new AnimationParseError("Couldn't find the corresponding "+(isHostLevel?"host-level ":"")+"animation trigger definition for (@"+triggerName+")"))}})},_AnimationTemplatePropertyVisitor.prototype.visitElement=function(ast,ctx){this._validateAnimationInputOutputPairs(ast.inputs,ast.outputs,this._animationRegistry,!1);var componentOnElement=ast.directives.find(function(directive){return directive.directive.isComponent});if(componentOnElement){var cachedComponentAnimations=animationCompilationCache.get(componentOnElement.directive);cachedComponentAnimations&&this._validateAnimationInputOutputPairs(componentOnElement.hostProperties,componentOnElement.hostEvents,this._buildCompileAnimationLookup(cachedComponentAnimations),!0)}templateVisitAll(this,ast.children)},_AnimationTemplatePropertyVisitor.prototype.visitEmbeddedTemplate=function(ast,ctx){templateVisitAll(this,ast.children)},_AnimationTemplatePropertyVisitor.prototype.visitEvent=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitBoundText=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitText=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitNgContent=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitAttr=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitDirective=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitReference=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitVariable=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitDirectiveProperty=function(ast,ctx){},_AnimationTemplatePropertyVisitor.prototype.visitElementProperty=function(ast,ctx){},_AnimationTemplatePropertyVisitor}(),_ValueOutputAstTransformer=function(){function _ValueOutputAstTransformer(){}return _ValueOutputAstTransformer.prototype.visitArray=function(arr,type){var _this=this;return literalArr(arr.map(function(value){return visitValue(value,_this,null)}),type)},_ValueOutputAstTransformer.prototype.visitStringMap=function(map,type){var _this=this,entries=[];return StringMapWrapper.forEach(map,function(value,key){entries.push([key,visitValue(value,_this,null)])}),literalMap(entries,type)},_ValueOutputAstTransformer.prototype.visitPrimitive=function(value,type){return literal(value,type)},_ValueOutputAstTransformer.prototype.visitOther=function(value,type){if(value instanceof CompileIdentifierMetadata)return importExpr(value);if(value instanceof Expression)return value;throw new Error("Illegal state: Don't now how to compile value "+value)},_ValueOutputAstTransformer}(),_DebugState=function(){function _DebugState(nodeIndex,sourceAst){this.nodeIndex=nodeIndex,this.sourceAst=sourceAst}return _DebugState}(),NULL_DEBUG_STATE=new _DebugState(null,null),CompileMethod=function(){function CompileMethod(_view){this._view=_view,this._newState=NULL_DEBUG_STATE,this._currState=NULL_DEBUG_STATE,this._bodyStatements=[],this._debugEnabled=this._view.genConfig.genDebugInfo}return CompileMethod.prototype._updateDebugContextIfNeeded=function(){if(this._newState.nodeIndex!==this._currState.nodeIndex||this._newState.sourceAst!==this._currState.sourceAst){var expr=this._updateDebugContext(this._newState);isPresent(expr)&&this._bodyStatements.push(expr.toStmt())}},CompileMethod.prototype._updateDebugContext=function(newState){if(this._currState=this._newState=newState,this._debugEnabled){var sourceLocation=isPresent(newState.sourceAst)?newState.sourceAst.sourceSpan.start:null;return THIS_EXPR.callMethod("debug",[literal(newState.nodeIndex),isPresent(sourceLocation)?literal(sourceLocation.line):NULL_EXPR,isPresent(sourceLocation)?literal(sourceLocation.col):NULL_EXPR])}return null},CompileMethod.prototype.resetDebugInfoExpr=function(nodeIndex,templateAst){var res=this._updateDebugContext(new _DebugState(nodeIndex,templateAst));return isPresent(res)?res:NULL_EXPR},CompileMethod.prototype.resetDebugInfo=function(nodeIndex,templateAst){this._newState=new _DebugState(nodeIndex,templateAst)},CompileMethod.prototype.addStmt=function(stmt){this._updateDebugContextIfNeeded(),this._bodyStatements.push(stmt)},CompileMethod.prototype.addStmts=function(stmts){this._updateDebugContextIfNeeded(),ListWrapper.addAll(this._bodyStatements,stmts)},CompileMethod.prototype.finish=function(){return this._bodyStatements},CompileMethod.prototype.isEmpty=function(){return 0===this._bodyStatements.length},CompileMethod}(),ViewQueryValues=function(){function ViewQueryValues(view,values){this.view=view,this.values=values}return ViewQueryValues}(),CompileQuery=function(){function CompileQuery(meta,queryList,ownerDirectiveExpression,view){this.meta=meta,this.queryList=queryList,this.ownerDirectiveExpression=ownerDirectiveExpression,this.view=view,this._values=new ViewQueryValues(view,[])}return CompileQuery.prototype.addValue=function(value,view){for(var currentView=view,elPath=[];isPresent(currentView)&&currentView!==this.view;){var parentEl=currentView.declarationElement;elPath.unshift(parentEl),currentView=parentEl.view}var queryListForDirtyExpr=getPropertyInView(this.queryList,view,this.view),viewValues=this._values;elPath.forEach(function(el){var last=viewValues.values.length>0?viewValues.values[viewValues.values.length-1]:null;if(last instanceof ViewQueryValues&&last.view===el.embeddedView)viewValues=last;else{var newViewValues=new ViewQueryValues(el.embeddedView,[]);viewValues.values.push(newViewValues),viewValues=newViewValues}}),viewValues.values.push(value),elPath.length>0&&view.dirtyParentQueriesMethod.addStmt(queryListForDirtyExpr.callMethod("setDirty",[]).toStmt())},CompileQuery.prototype._isStatic=function(){return!this._values.values.some(function(value){return value instanceof ViewQueryValues})},CompileQuery.prototype.afterChildren=function(targetStaticMethod,targetDynamicMethod){var values=createQueryValues(this._values),updateStmts=[this.queryList.callMethod("reset",[literalArr(values)]).toStmt()];if(isPresent(this.ownerDirectiveExpression)){var valueExpr=this.meta.first?this.queryList.prop("first"):this.queryList;updateStmts.push(this.ownerDirectiveExpression.prop(this.meta.propertyName).set(valueExpr).toStmt())}this.meta.first||updateStmts.push(this.queryList.callMethod("notifyOnChanges",[]).toStmt()),this.meta.first&&this._isStatic()?targetStaticMethod.addStmts(updateStmts):targetDynamicMethod.addStmt(new IfStmt(this.queryList.prop("dirty"),updateStmts))},CompileQuery}(),ViewTypeEnum=function(){function ViewTypeEnum(){}return ViewTypeEnum.fromValue=function(value){var viewType=resolveIdentifier(Identifiers.ViewType);switch(value){case ViewType.HOST:return _enumExpression(viewType,"HOST");case ViewType.COMPONENT:return _enumExpression(viewType,"COMPONENT");case ViewType.EMBEDDED:return _enumExpression(viewType,"EMBEDDED");default:throw Error("Inavlid ViewType value: "+value)}},ViewTypeEnum}(),ViewEncapsulationEnum=function(){function ViewEncapsulationEnum(){}return ViewEncapsulationEnum.fromValue=function(value){var viewEncapsulation=resolveIdentifier(Identifiers.ViewEncapsulation);switch(value){case _angular_core.ViewEncapsulation.Emulated:return _enumExpression(viewEncapsulation,"Emulated");case _angular_core.ViewEncapsulation.Native:return _enumExpression(viewEncapsulation,"Native");case _angular_core.ViewEncapsulation.None:return _enumExpression(viewEncapsulation,"None");default:throw Error("Inavlid ViewEncapsulation value: "+value)}},ViewEncapsulationEnum}(),ChangeDetectorStatusEnum=function(){function ChangeDetectorStatusEnum(){}return ChangeDetectorStatusEnum.fromValue=function(value){var changeDetectorStatus=resolveIdentifier(Identifiers.ChangeDetectorStatus);switch(value){case ChangeDetectorStatus.CheckOnce:return _enumExpression(changeDetectorStatus,"CheckOnce");case ChangeDetectorStatus.Checked:return _enumExpression(changeDetectorStatus,"Checked");case ChangeDetectorStatus.CheckAlways:return _enumExpression(changeDetectorStatus,"CheckAlways");case ChangeDetectorStatus.Detached:return _enumExpression(changeDetectorStatus,"Detached");case ChangeDetectorStatus.Errored:return _enumExpression(changeDetectorStatus,"Errored");case ChangeDetectorStatus.Destroyed:return _enumExpression(changeDetectorStatus,"Destroyed");default:throw Error("Inavlid ChangeDetectorStatus value: "+value)}},ChangeDetectorStatusEnum}(),ViewConstructorVars=function(){function ViewConstructorVars(){}return ViewConstructorVars.viewUtils=variable("viewUtils"),ViewConstructorVars.parentInjector=variable("parentInjector"),ViewConstructorVars.declarationEl=variable("declarationEl"),ViewConstructorVars}(),ViewProperties=function(){function ViewProperties(){}return ViewProperties.renderer=THIS_EXPR.prop("renderer"),ViewProperties.projectableNodes=THIS_EXPR.prop("projectableNodes"),ViewProperties.viewUtils=THIS_EXPR.prop("viewUtils"),ViewProperties}(),EventHandlerVars=function(){function EventHandlerVars(){}return EventHandlerVars.event=variable("$event"),EventHandlerVars}(),InjectMethodVars=function(){function InjectMethodVars(){}return InjectMethodVars.token=variable("token"),InjectMethodVars.requestNodeIndex=variable("requestNodeIndex"),InjectMethodVars.notFoundResult=variable("notFoundResult"),InjectMethodVars}(),DetectChangesVars=function(){function DetectChangesVars(){}return DetectChangesVars.throwOnChange=variable("throwOnChange"),DetectChangesVars.changes=variable("changes"),DetectChangesVars.changed=variable("changed"),DetectChangesVars.valUnwrapper=variable("valUnwrapper"),DetectChangesVars}(),__extends$13=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},CompileNode=function(){function CompileNode(parent,view,nodeIndex,renderNode,sourceAst){this.parent=parent,this.view=view,this.nodeIndex=nodeIndex,this.renderNode=renderNode,this.sourceAst=sourceAst}return CompileNode.prototype.isNull=function(){return isBlank(this.renderNode)},CompileNode.prototype.isRootElement=function(){return this.view!=this.parent.view},CompileNode}(),CompileElement=function(_super){function CompileElement(parent,view,nodeIndex,renderNode,sourceAst,component,_directives,_resolvedProvidersArray,hasViewContainer,hasEmbeddedView,references){var _this=this;_super.call(this,parent,view,nodeIndex,renderNode,sourceAst),this.component=component,this._directives=_directives,this._resolvedProvidersArray=_resolvedProvidersArray,this.hasViewContainer=hasViewContainer,this.hasEmbeddedView=hasEmbeddedView,this._compViewExpr=null,this.instances=new Map,this._queryCount=0,this._queries=new Map,this._componentConstructorViewQueryLists=[],this.contentNodesByNgContentIndex=null,this.referenceTokens={},references.forEach(function(ref){return _this.referenceTokens[ref.name]=ref.value}),this.elementRef=importExpr(resolveIdentifier(Identifiers.ElementRef)).instantiate([this.renderNode]),this.instances.set(resolveIdentifierToken(Identifiers.ElementRef).reference,this.elementRef),this.injector=THIS_EXPR.callMethod("injector",[literal(this.nodeIndex)]),this.instances.set(resolveIdentifierToken(Identifiers.Injector).reference,this.injector),this.instances.set(resolveIdentifierToken(Identifiers.Renderer).reference,THIS_EXPR.prop("renderer")),(this.hasViewContainer||this.hasEmbeddedView||isPresent(this.component))&&this._createAppElement()}return __extends$13(CompileElement,_super),CompileElement.createNull=function(){return new CompileElement(null,null,null,null,null,null,[],[],(!1),(!1),[])},CompileElement.prototype._createAppElement=function(){var fieldName="_appEl_"+this.nodeIndex,parentNodeIndex=this.isRootElement()?null:this.parent.nodeIndex;this.view.fields.push(new ClassField(fieldName,importType(resolveIdentifier(Identifiers.AppElement)),[StmtModifier.Private]));var statement=THIS_EXPR.prop(fieldName).set(importExpr(resolveIdentifier(Identifiers.AppElement)).instantiate([literal(this.nodeIndex),literal(parentNodeIndex),THIS_EXPR,this.renderNode])).toStmt();this.view.createMethod.addStmt(statement),this.appElement=THIS_EXPR.prop(fieldName),this.instances.set(resolveIdentifierToken(Identifiers.AppElement).reference,this.appElement)},CompileElement.prototype.createComponentFactoryResolver=function(entryComponents){if(entryComponents&&0!==entryComponents.length){var createComponentFactoryResolverExpr=importExpr(resolveIdentifier(Identifiers.CodegenComponentFactoryResolver)).instantiate([literalArr(entryComponents.map(function(entryComponent){return importExpr(entryComponent)})),injectFromViewParentInjector(resolveIdentifierToken(Identifiers.ComponentFactoryResolver),!1)]),provider=new CompileProviderMetadata({token:resolveIdentifierToken(Identifiers.ComponentFactoryResolver),useValue:createComponentFactoryResolverExpr});this._resolvedProvidersArray.unshift(new ProviderAst(provider.token,(!1),(!0),[provider],exports.ProviderAstType.PrivateService,[],this.sourceAst.sourceSpan))}},CompileElement.prototype.setComponentView=function(compViewExpr){this._compViewExpr=compViewExpr,this.contentNodesByNgContentIndex=ListWrapper.createFixedSize(this.component.template.ngContentSelectors.length);for(var i=0;i<this.contentNodesByNgContentIndex.length;i++)this.contentNodesByNgContentIndex[i]=[]},CompileElement.prototype.setEmbeddedView=function(embeddedView){if(this.embeddedView=embeddedView,isPresent(embeddedView)){var createTemplateRefExpr=importExpr(resolveIdentifier(Identifiers.TemplateRef_)).instantiate([this.appElement,this.embeddedView.viewFactory]),provider=new CompileProviderMetadata({token:resolveIdentifierToken(Identifiers.TemplateRef),useValue:createTemplateRefExpr});this._resolvedProvidersArray.unshift(new ProviderAst(provider.token,(!1),(!0),[provider],exports.ProviderAstType.Builtin,[],this.sourceAst.sourceSpan))}},CompileElement.prototype.beforeChildren=function(){var _this=this;this.hasViewContainer&&this.instances.set(resolveIdentifierToken(Identifiers.ViewContainerRef).reference,this.appElement.prop("vcRef")),this._resolvedProviders=new Map,this._resolvedProvidersArray.forEach(function(provider){return _this._resolvedProviders.set(provider.token.reference,provider)}),MapWrapper.values(this._resolvedProviders).forEach(function(resolvedProvider){var providerValueExpressions=resolvedProvider.providers.map(function(provider){if(isPresent(provider.useExisting))return _this._getDependency(resolvedProvider.providerType,new CompileDiDependencyMetadata({token:provider.useExisting}));if(isPresent(provider.useFactory)){var deps=isPresent(provider.deps)?provider.deps:provider.useFactory.diDeps,depsExpr=deps.map(function(dep){return _this._getDependency(resolvedProvider.providerType,dep)});return importExpr(provider.useFactory).callFn(depsExpr)}if(isPresent(provider.useClass)){var deps=isPresent(provider.deps)?provider.deps:provider.useClass.diDeps,depsExpr=deps.map(function(dep){return _this._getDependency(resolvedProvider.providerType,dep)});return importExpr(provider.useClass).instantiate(depsExpr,importType(provider.useClass))}return convertValueToOutputAst(provider.useValue)}),propName="_"+resolvedProvider.token.name+"_"+_this.nodeIndex+"_"+_this.instances.size,instance=createProviderProperty(propName,resolvedProvider,providerValueExpressions,resolvedProvider.multiProvider,resolvedProvider.eager,_this);_this.instances.set(resolvedProvider.token.reference,instance)});for(var i=0;i<this._directives.length;i++){var directive=this._directives[i],directiveInstance=this.instances.get(identifierToken(directive.type).reference);directive.queries.forEach(function(queryMeta){_this._addQuery(queryMeta,directiveInstance)})}var queriesWithReads=[];if(MapWrapper.values(this._resolvedProviders).forEach(function(resolvedProvider){var queriesForProvider=_this._getQueriesFor(resolvedProvider.token);ListWrapper.addAll(queriesWithReads,queriesForProvider.map(function(query){return new _QueryWithRead(query,resolvedProvider.token)}))}),StringMapWrapper.forEach(this.referenceTokens,function(_,varName){var varValue,token=_this.referenceTokens[varName];varValue=isPresent(token)?_this.instances.get(token.reference):_this.renderNode,_this.view.locals.set(varName,varValue);var varToken=new CompileTokenMetadata({value:varName});ListWrapper.addAll(queriesWithReads,_this._getQueriesFor(varToken).map(function(query){return new _QueryWithRead(query,varToken)}))}),queriesWithReads.forEach(function(queryWithRead){var value;if(isPresent(queryWithRead.read.identifier))value=_this.instances.get(queryWithRead.read.reference);else{var token=_this.referenceTokens[queryWithRead.read.value];value=isPresent(token)?_this.instances.get(token.reference):_this.elementRef}isPresent(value)&&queryWithRead.query.addValue(value,_this.view)}),isPresent(this.component)){var componentConstructorViewQueryList=isPresent(this.component)?literalArr(this._componentConstructorViewQueryLists):NULL_EXPR,compExpr=isPresent(this.getComponent())?this.getComponent():NULL_EXPR;this.view.createMethod.addStmt(this.appElement.callMethod("initComponent",[compExpr,componentConstructorViewQueryList,this._compViewExpr]).toStmt())}},CompileElement.prototype.afterChildren=function(childNodeCount){var _this=this;MapWrapper.values(this._resolvedProviders).forEach(function(resolvedProvider){var providerExpr=_this.instances.get(resolvedProvider.token.reference),providerChildNodeCount=resolvedProvider.providerType===exports.ProviderAstType.PrivateService?0:childNodeCount;_this.view.injectorGetMethod.addStmt(createInjectInternalCondition(_this.nodeIndex,providerChildNodeCount,resolvedProvider,providerExpr))}),MapWrapper.values(this._queries).forEach(function(queries){return queries.forEach(function(query){return query.afterChildren(_this.view.createMethod,_this.view.updateContentQueriesMethod)})})},CompileElement.prototype.addContentNode=function(ngContentIndex,nodeExpr){this.contentNodesByNgContentIndex[ngContentIndex].push(nodeExpr)},CompileElement.prototype.getComponent=function(){return isPresent(this.component)?this.instances.get(identifierToken(this.component.type).reference):null},CompileElement.prototype.getProviderTokens=function(){return MapWrapper.values(this._resolvedProviders).map(function(resolvedProvider){return createDiTokenExpression(resolvedProvider.token)})},CompileElement.prototype._getQueriesFor=function(token){for(var queries,result=[],currentEl=this,distance=0;!currentEl.isNull();)queries=currentEl._queries.get(token.reference),isPresent(queries)&&ListWrapper.addAll(result,queries.filter(function(query){return query.meta.descendants||distance<=1})),currentEl._directives.length>0&&distance++,currentEl=currentEl.parent;return queries=this.view.componentView.viewQueries.get(token.reference),isPresent(queries)&&ListWrapper.addAll(result,queries),result},CompileElement.prototype._addQuery=function(queryMeta,directiveInstance){var propName="_query_"+queryMeta.selectors[0].name+"_"+this.nodeIndex+"_"+this._queryCount++,queryList=createQueryList(queryMeta,directiveInstance,propName,this.view),query=new CompileQuery(queryMeta,queryList,directiveInstance,this.view);return addQueryToTokenMap(this._queries,query),query},CompileElement.prototype._getLocalDependency=function(requestingProviderType,dep){var result=null;if(isBlank(result)&&isPresent(dep.query)&&(result=this._addQuery(dep.query,null).queryList),isBlank(result)&&isPresent(dep.viewQuery)&&(result=createQueryList(dep.viewQuery,null,"_viewQuery_"+dep.viewQuery.selectors[0].name+"_"+this.nodeIndex+"_"+this._componentConstructorViewQueryLists.length,this.view),this._componentConstructorViewQueryLists.push(result)),isPresent(dep.token)){if(isBlank(result)&&dep.token.reference===resolveIdentifierToken(Identifiers.ChangeDetectorRef).reference)return requestingProviderType===exports.ProviderAstType.Component?this._compViewExpr.prop("ref"):getPropertyInView(THIS_EXPR.prop("ref"),this.view,this.view.componentView);if(isBlank(result)){var resolvedProvider=this._resolvedProviders.get(dep.token.reference);if(resolvedProvider&&(requestingProviderType===exports.ProviderAstType.Directive||requestingProviderType===exports.ProviderAstType.PublicService)&&resolvedProvider.providerType===exports.ProviderAstType.PrivateService)return null;result=this.instances.get(dep.token.reference)}}return result},CompileElement.prototype._getDependency=function(requestingProviderType,dep){var currElement=this,result=null;for(dep.isValue&&(result=literal(dep.value)),isBlank(result)&&!dep.isSkipSelf&&(result=this._getLocalDependency(requestingProviderType,dep));isBlank(result)&&!currElement.parent.isNull();)currElement=currElement.parent,result=currElement._getLocalDependency(exports.ProviderAstType.PublicService,new CompileDiDependencyMetadata({token:dep.token}));return isBlank(result)&&(result=injectFromViewParentInjector(dep.token,dep.isOptional)),isBlank(result)&&(result=NULL_EXPR),getPropertyInView(result,this.view,currElement.view)},CompileElement}(CompileNode),_QueryWithRead=function(){function _QueryWithRead(query,match){this.query=query,this.read=isPresent(query.meta.read)?query.meta.read:match}return _QueryWithRead}(),CompilePipe=function(){function CompilePipe(view,meta){var _this=this;this.view=view,this.meta=meta,this._purePipeProxyCount=0,this.instance=THIS_EXPR.prop("_pipe_"+meta.name+"_"+view.pipeCount++);var deps=this.meta.type.diDeps.map(function(diDep){return diDep.token.reference===resolveIdentifierToken(Identifiers.ChangeDetectorRef).reference?getPropertyInView(THIS_EXPR.prop("ref"),_this.view,_this.view.componentView):injectFromViewParentInjector(diDep.token,!1)});this.view.fields.push(new ClassField(this.instance.name,importType(this.meta.type))),this.view.createMethod.resetDebugInfo(null,null),this.view.createMethod.addStmt(THIS_EXPR.prop(this.instance.name).set(importExpr(this.meta.type).instantiate(deps)).toStmt())}return CompilePipe.call=function(view,name,args){var pipe,compView=view.componentView,meta=_findPipeMeta(compView,name);return meta.pure?(pipe=compView.purePipes.get(name),isBlank(pipe)&&(pipe=new CompilePipe(compView,meta),compView.purePipes.set(name,pipe),compView.pipes.push(pipe))):(pipe=new CompilePipe(view,meta),view.pipes.push(pipe)),pipe._call(view,args)},Object.defineProperty(CompilePipe.prototype,"pure",{get:function(){return this.meta.pure},enumerable:!0,configurable:!0}),CompilePipe.prototype._call=function(callingView,args){if(this.meta.pure){var purePipeProxyInstance=THIS_EXPR.prop(this.instance.name+"_"+this._purePipeProxyCount++),pipeInstanceSeenFromPureProxy=getPropertyInView(this.instance,callingView,this.view);return createPureProxy(pipeInstanceSeenFromPureProxy.prop("transform").callMethod(BuiltinMethod.Bind,[pipeInstanceSeenFromPureProxy]),args.length,purePipeProxyInstance,callingView),importExpr(resolveIdentifier(Identifiers.castByValue)).callFn([purePipeProxyInstance,pipeInstanceSeenFromPureProxy.prop("transform")]).callFn(args)}return getPropertyInView(this.instance,callingView,this.view).callMethod("transform",args)},CompilePipe}(),CompileView=function(){function CompileView(component,genConfig,pipeMetas,styles,animations,viewIndex,declarationElement,templateVariableBindings){var _this=this;this.component=component,this.genConfig=genConfig,this.pipeMetas=pipeMetas,this.styles=styles,this.animations=animations,this.viewIndex=viewIndex,this.declarationElement=declarationElement,this.templateVariableBindings=templateVariableBindings,this.nodes=[],this.rootNodesOrAppElements=[],this.bindings=[],this.classStatements=[],this.eventHandlerMethods=[],this.fields=[],this.getters=[],this.disposables=[],this.subscriptions=[],this.purePipes=new Map,this.pipes=[],this.locals=new Map,this.literalArrayCount=0,this.literalMapCount=0,this.pipeCount=0,this.createMethod=new CompileMethod(this),this.animationBindingsMethod=new CompileMethod(this),this.injectorGetMethod=new CompileMethod(this),this.updateContentQueriesMethod=new CompileMethod(this),this.dirtyParentQueriesMethod=new CompileMethod(this),this.updateViewQueriesMethod=new CompileMethod(this),this.detectChangesInInputsMethod=new CompileMethod(this),this.detectChangesRenderPropertiesMethod=new CompileMethod(this),this.afterContentLifecycleCallbacksMethod=new CompileMethod(this),this.afterViewLifecycleCallbacksMethod=new CompileMethod(this),this.destroyMethod=new CompileMethod(this),this.detachMethod=new CompileMethod(this),this.viewType=getViewType(component,viewIndex),this.className="_View_"+component.type.name+viewIndex,this.classType=importType(new CompileIdentifierMetadata({name:this.className})),this.viewFactory=variable(getViewFactoryName(component,viewIndex)),this.viewType===ViewType.COMPONENT||this.viewType===ViewType.HOST?this.componentView=this:this.componentView=this.declarationElement.view.componentView,this.componentContext=getPropertyInView(THIS_EXPR.prop("context"),this,this.componentView);var viewQueries=new Map;if(this.viewType===ViewType.COMPONENT){var directiveInstance=THIS_EXPR.prop("context");ListWrapper.forEachWithIndex(this.component.viewQueries,function(queryMeta,queryIndex){var propName="_viewQuery_"+queryMeta.selectors[0].name+"_"+queryIndex,queryList=createQueryList(queryMeta,directiveInstance,propName,_this),query=new CompileQuery(queryMeta,queryList,directiveInstance,_this);addQueryToTokenMap(viewQueries,query)});var constructorViewQueryCount=0;this.component.type.diDeps.forEach(function(dep){if(isPresent(dep.viewQuery)){var queryList=THIS_EXPR.prop("declarationAppElement").prop("componentConstructorViewQueries").key(literal(constructorViewQueryCount++)),query=new CompileQuery(dep.viewQuery,queryList,null,_this);addQueryToTokenMap(viewQueries,query)}})}this.viewQueries=viewQueries,templateVariableBindings.forEach(function(entry){_this.locals.set(entry[1],THIS_EXPR.prop("context").prop(entry[0]))}),this.declarationElement.isNull()||this.declarationElement.setEmbeddedView(this)}return CompileView.prototype.callPipe=function(name,input,args){return CompilePipe.call(this,name,[input].concat(args))},CompileView.prototype.getLocal=function(name){if(name==EventHandlerVars.event.name)return EventHandlerVars.event;for(var currView=this,result=currView.locals.get(name);isBlank(result)&&isPresent(currView.declarationElement.view);)currView=currView.declarationElement.view,result=currView.locals.get(name);return isPresent(result)?getPropertyInView(result,this,currView):null},CompileView.prototype.createLiteralArray=function(values){if(0===values.length)return importExpr(resolveIdentifier(Identifiers.EMPTY_ARRAY));for(var proxyExpr=THIS_EXPR.prop("_arr_"+this.literalArrayCount++),proxyParams=[],proxyReturnEntries=[],i=0;i<values.length;i++){var paramName="p"+i;proxyParams.push(new FnParam(paramName)),proxyReturnEntries.push(variable(paramName))}return createPureProxy(fn(proxyParams,[new ReturnStatement(literalArr(proxyReturnEntries))],new ArrayType(DYNAMIC_TYPE)),values.length,proxyExpr,this),proxyExpr.callFn(values)},CompileView.prototype.createLiteralMap=function(entries){if(0===entries.length)return importExpr(resolveIdentifier(Identifiers.EMPTY_MAP));for(var proxyExpr=THIS_EXPR.prop("_map_"+this.literalMapCount++),proxyParams=[],proxyReturnEntries=[],values=[],i=0;i<entries.length;i++){var paramName="p"+i;proxyParams.push(new FnParam(paramName)),proxyReturnEntries.push([entries[i][0],variable(paramName)]),values.push(entries[i][1])}return createPureProxy(fn(proxyParams,[new ReturnStatement(literalMap(proxyReturnEntries))],new MapType(DYNAMIC_TYPE)),entries.length,proxyExpr,this),proxyExpr.callFn(values)},CompileView.prototype.afterNodes=function(){var _this=this;MapWrapper.values(this.viewQueries).forEach(function(queries){return queries.forEach(function(query){return query.afterChildren(_this.createMethod,_this.updateViewQueriesMethod)})})},CompileView}(),CompileBinding=function(){function CompileBinding(node,sourceAst){this.node=node,this.sourceAst=sourceAst}return CompileBinding}(),ExpressionWithWrappedValueInfo=function(){function ExpressionWithWrappedValueInfo(expression,needsValueUnwrapper,temporaryCount){this.expression=expression,this.needsValueUnwrapper=needsValueUnwrapper,this.temporaryCount=temporaryCount}return ExpressionWithWrappedValueInfo}();!function(_Mode){_Mode[_Mode.Statement=0]="Statement",_Mode[_Mode.Expression=1]="Expression"}(_Mode||(_Mode={}));var _ComponentIndex,_AstToIrVisitor=function(){function _AstToIrVisitor(_nameResolver,_implicitReceiver,_valueUnwrapper,bindingIndex){this._nameResolver=_nameResolver,this._implicitReceiver=_implicitReceiver,this._valueUnwrapper=_valueUnwrapper,this.bindingIndex=bindingIndex,this._nodeMap=new Map,this._resultMap=new Map,this._currentTemporary=0,this.needsValueUnwrapper=!1,this.temporaryCount=0}return _AstToIrVisitor.prototype.visitBinary=function(ast,mode){var op;switch(ast.operation){case"+":op=BinaryOperator.Plus;break;case"-":op=BinaryOperator.Minus;break;case"*":op=BinaryOperator.Multiply;break;case"/":op=BinaryOperator.Divide;break;case"%":op=BinaryOperator.Modulo;break;case"&&":op=BinaryOperator.And;break;case"||":op=BinaryOperator.Or;break;case"==":op=BinaryOperator.Equals;break;case"!=":op=BinaryOperator.NotEquals;break;case"===":op=BinaryOperator.Identical;break;case"!==":op=BinaryOperator.NotIdentical;break;case"<":op=BinaryOperator.Lower;break;case">":op=BinaryOperator.Bigger;break;case"<=":op=BinaryOperator.LowerEquals;break;case">=":op=BinaryOperator.BiggerEquals;break;default:throw new Error("Unsupported operation "+ast.operation)}return convertToStatementIfNeeded(mode,new BinaryOperatorExpr(op,this.visit(ast.left,_Mode.Expression),this.visit(ast.right,_Mode.Expression)))},_AstToIrVisitor.prototype.visitChain=function(ast,mode){return ensureStatementMode(mode,ast),this.visitAll(ast.expressions,mode)},_AstToIrVisitor.prototype.visitConditional=function(ast,mode){var value=this.visit(ast.condition,_Mode.Expression);return convertToStatementIfNeeded(mode,value.conditional(this.visit(ast.trueExp,_Mode.Expression),this.visit(ast.falseExp,_Mode.Expression)))},_AstToIrVisitor.prototype.visitPipe=function(ast,mode){var input=this.visit(ast.exp,_Mode.Expression),args=this.visitAll(ast.args,_Mode.Expression),value=this._nameResolver.callPipe(ast.name,input,args);return this.needsValueUnwrapper=!0,convertToStatementIfNeeded(mode,this._valueUnwrapper.callMethod("unwrap",[value]))},_AstToIrVisitor.prototype.visitFunctionCall=function(ast,mode){return convertToStatementIfNeeded(mode,this.visit(ast.target,_Mode.Expression).callFn(this.visitAll(ast.args,_Mode.Expression)))},_AstToIrVisitor.prototype.visitImplicitReceiver=function(ast,mode){return ensureExpressionMode(mode,ast),this._implicitReceiver},_AstToIrVisitor.prototype.visitInterpolation=function(ast,mode){ensureExpressionMode(mode,ast);for(var args=[literal(ast.expressions.length)],i=0;i<ast.strings.length-1;i++)args.push(literal(ast.strings[i])),args.push(this.visit(ast.expressions[i],_Mode.Expression));return args.push(literal(ast.strings[ast.strings.length-1])),importExpr(resolveIdentifier(Identifiers.interpolate)).callFn(args)},_AstToIrVisitor.prototype.visitKeyedRead=function(ast,mode){return convertToStatementIfNeeded(mode,this.visit(ast.obj,_Mode.Expression).key(this.visit(ast.key,_Mode.Expression)))},_AstToIrVisitor.prototype.visitKeyedWrite=function(ast,mode){var obj=this.visit(ast.obj,_Mode.Expression),key=this.visit(ast.key,_Mode.Expression),value=this.visit(ast.value,_Mode.Expression);return convertToStatementIfNeeded(mode,obj.key(key).set(value))},_AstToIrVisitor.prototype.visitLiteralArray=function(ast,mode){return convertToStatementIfNeeded(mode,this._nameResolver.createLiteralArray(this.visitAll(ast.expressions,mode)))},_AstToIrVisitor.prototype.visitLiteralMap=function(ast,mode){
for(var parts=[],i=0;i<ast.keys.length;i++)parts.push([ast.keys[i],this.visit(ast.values[i],_Mode.Expression)]);return convertToStatementIfNeeded(mode,this._nameResolver.createLiteralMap(parts))},_AstToIrVisitor.prototype.visitLiteralPrimitive=function(ast,mode){return convertToStatementIfNeeded(mode,literal(ast.value))},_AstToIrVisitor.prototype.visitMethodCall=function(ast,mode){var leftMostSafe=this.leftMostSafeNode(ast);if(leftMostSafe)return this.convertSafeAccess(ast,leftMostSafe,mode);var args=this.visitAll(ast.args,_Mode.Expression),result=null,receiver=this.visit(ast.receiver,_Mode.Expression);if(receiver===this._implicitReceiver){var varExpr=this._nameResolver.getLocal(ast.name);isPresent(varExpr)&&(result=varExpr.callFn(args))}return isBlank(result)&&(result=receiver.callMethod(ast.name,args)),convertToStatementIfNeeded(mode,result)},_AstToIrVisitor.prototype.visitPrefixNot=function(ast,mode){return convertToStatementIfNeeded(mode,not(this.visit(ast.expression,_Mode.Expression)))},_AstToIrVisitor.prototype.visitPropertyRead=function(ast,mode){var leftMostSafe=this.leftMostSafeNode(ast);if(leftMostSafe)return this.convertSafeAccess(ast,leftMostSafe,mode);var result=null,receiver=this.visit(ast.receiver,_Mode.Expression);return receiver===this._implicitReceiver&&(result=this._nameResolver.getLocal(ast.name)),isBlank(result)&&(result=receiver.prop(ast.name)),convertToStatementIfNeeded(mode,result)},_AstToIrVisitor.prototype.visitPropertyWrite=function(ast,mode){var receiver=this.visit(ast.receiver,_Mode.Expression);if(receiver===this._implicitReceiver){var varExpr=this._nameResolver.getLocal(ast.name);if(isPresent(varExpr))throw new Error("Cannot assign to a reference or variable!")}return convertToStatementIfNeeded(mode,receiver.prop(ast.name).set(this.visit(ast.value,_Mode.Expression)))},_AstToIrVisitor.prototype.visitSafePropertyRead=function(ast,mode){return this.convertSafeAccess(ast,this.leftMostSafeNode(ast),mode)},_AstToIrVisitor.prototype.visitSafeMethodCall=function(ast,mode){return this.convertSafeAccess(ast,this.leftMostSafeNode(ast),mode)},_AstToIrVisitor.prototype.visitAll=function(asts,mode){var _this=this;return asts.map(function(ast){return _this.visit(ast,mode)})},_AstToIrVisitor.prototype.visitQuote=function(ast,mode){throw new Error("Quotes are not supported for evaluation!")},_AstToIrVisitor.prototype.visit=function(ast,mode){var result=this._resultMap.get(ast);return result?result:(this._nodeMap.get(ast)||ast).visit(this,mode)},_AstToIrVisitor.prototype.convertSafeAccess=function(ast,leftMostSafe,mode){var temporary,guardedExpression=this.visit(leftMostSafe.receiver,mode);this.needsTemporary(leftMostSafe.receiver)&&(temporary=this.allocateTemporary(),guardedExpression=temporary.set(guardedExpression),this._resultMap.set(leftMostSafe.receiver,temporary));var condition=guardedExpression.isBlank();leftMostSafe instanceof SafeMethodCall?this._nodeMap.set(leftMostSafe,new MethodCall(leftMostSafe.span,leftMostSafe.receiver,leftMostSafe.name,leftMostSafe.args)):this._nodeMap.set(leftMostSafe,new PropertyRead(leftMostSafe.span,leftMostSafe.receiver,leftMostSafe.name));var access=this.visit(ast,mode);return this._nodeMap.delete(leftMostSafe),temporary&&this.releaseTemporary(temporary),condition.conditional(literal(null),access)},_AstToIrVisitor.prototype.leftMostSafeNode=function(ast){var _this=this,visit=function(visitor,ast){return(_this._nodeMap.get(ast)||ast).visit(visitor)};return ast.visit({visitBinary:function(ast){return null},visitChain:function(ast){return null},visitConditional:function(ast){return null},visitFunctionCall:function(ast){return null},visitImplicitReceiver:function(ast){return null},visitInterpolation:function(ast){return null},visitKeyedRead:function(ast){return visit(this,ast.obj)},visitKeyedWrite:function(ast){return null},visitLiteralArray:function(ast){return null},visitLiteralMap:function(ast){return null},visitLiteralPrimitive:function(ast){return null},visitMethodCall:function(ast){return visit(this,ast.receiver)},visitPipe:function(ast){return null},visitPrefixNot:function(ast){return null},visitPropertyRead:function(ast){return visit(this,ast.receiver)},visitPropertyWrite:function(ast){return null},visitQuote:function(ast){return null},visitSafeMethodCall:function(ast){return visit(this,ast.receiver)||ast},visitSafePropertyRead:function(ast){return visit(this,ast.receiver)||ast}})},_AstToIrVisitor.prototype.needsTemporary=function(ast){var _this=this,visit=function(visitor,ast){return ast&&(_this._nodeMap.get(ast)||ast).visit(visitor)},visitSome=function(visitor,ast){return ast.some(function(ast){return visit(visitor,ast)})};return ast.visit({visitBinary:function(ast){return visit(this,ast.left)||visit(this,ast.right)},visitChain:function(ast){return!1},visitConditional:function(ast){return visit(this,ast.condition)||visit(this,ast.trueExp)||visit(this,ast.falseExp)},visitFunctionCall:function(ast){return!0},visitImplicitReceiver:function(ast){return!1},visitInterpolation:function(ast){return visitSome(this,ast.expressions)},visitKeyedRead:function(ast){return!1},visitKeyedWrite:function(ast){return!1},visitLiteralArray:function(ast){return!0},visitLiteralMap:function(ast){return!0},visitLiteralPrimitive:function(ast){return!1},visitMethodCall:function(ast){return!0},visitPipe:function(ast){return!0},visitPrefixNot:function(ast){return visit(this,ast.expression)},visitPropertyRead:function(ast){return!1},visitPropertyWrite:function(ast){return!1},visitQuote:function(ast){return!1},visitSafeMethodCall:function(ast){return!0},visitSafePropertyRead:function(ast){return!1}})},_AstToIrVisitor.prototype.allocateTemporary=function(){var tempNumber=this._currentTemporary++;return this.temporaryCount=Math.max(this._currentTemporary,this.temporaryCount),new ReadVarExpr(temporaryName(this.bindingIndex,tempNumber))},_AstToIrVisitor.prototype.releaseTemporary=function(temporary){if(this._currentTemporary--,temporary.name!=temporaryName(this.bindingIndex,this._currentTemporary))throw new Error("Temporary "+temporary.name+" released out of order")},_AstToIrVisitor}(),CompileElementAnimationOutput=function(){function CompileElementAnimationOutput(listener,output){this.listener=listener,this.output=output}return CompileElementAnimationOutput}(),CompileEventListener=function(){function CompileEventListener(compileElement,eventTarget,eventName,listenerIndex){this.compileElement=compileElement,this.eventTarget=eventTarget,this.eventName=eventName,this._hasComponentHostListener=!1,this._actionResultExprs=[],this._method=new CompileMethod(compileElement.view),this._methodName="_handle_"+santitizeEventName(eventName)+"_"+compileElement.nodeIndex+"_"+listenerIndex,this._eventParam=new FnParam(EventHandlerVars.event.name,importType(this.compileElement.view.genConfig.renderTypes.renderEvent))}return CompileEventListener.getOrCreate=function(compileElement,eventTarget,eventName,targetEventListeners){var listener=targetEventListeners.find(function(listener){return listener.eventTarget==eventTarget&&listener.eventName==eventName});return isBlank(listener)&&(listener=new CompileEventListener(compileElement,eventTarget,eventName,targetEventListeners.length),targetEventListeners.push(listener)),listener},Object.defineProperty(CompileEventListener.prototype,"methodName",{get:function(){return this._methodName},enumerable:!0,configurable:!0}),CompileEventListener.prototype.addAction=function(hostEvent,directive,directiveInstance){isPresent(directive)&&directive.isComponent&&(this._hasComponentHostListener=!0),this._method.resetDebugInfo(this.compileElement.nodeIndex,hostEvent);var context=isPresent(directiveInstance)?directiveInstance:this.compileElement.view.componentContext,actionStmts=convertCdStatementToIr(this.compileElement.view,context,hostEvent.handler,this.compileElement.nodeIndex),lastIndex=actionStmts.length-1;if(lastIndex>=0){var lastStatement=actionStmts[lastIndex],returnExpr=convertStmtIntoExpression(lastStatement),preventDefaultVar=variable("pd_"+this._actionResultExprs.length);this._actionResultExprs.push(preventDefaultVar),isPresent(returnExpr)&&(actionStmts[lastIndex]=preventDefaultVar.set(returnExpr.cast(DYNAMIC_TYPE).notIdentical(literal(!1))).toDeclStmt(null,[StmtModifier.Final]))}this._method.addStmts(actionStmts)},CompileEventListener.prototype.finishMethod=function(){var markPathToRootStart=this._hasComponentHostListener?this.compileElement.appElement.prop("componentView"):THIS_EXPR,resultExpr=literal(!0);this._actionResultExprs.forEach(function(expr){resultExpr=resultExpr.and(expr)});var stmts=[markPathToRootStart.callMethod("markPathToRootAsCheckOnce",[]).toStmt()].concat(this._method.finish()).concat([new ReturnStatement(resultExpr)]);this.compileElement.view.eventHandlerMethods.push(new ClassMethod(this._methodName,[this._eventParam],stmts,BOOL_TYPE,[StmtModifier.Private]))},CompileEventListener.prototype.listenToRenderer=function(){var listenExpr,eventListener=THIS_EXPR.callMethod("eventHandler",[THIS_EXPR.prop(this._methodName).callMethod(BuiltinMethod.Bind,[THIS_EXPR])]);listenExpr=isPresent(this.eventTarget)?ViewProperties.renderer.callMethod("listenGlobal",[literal(this.eventTarget),literal(this.eventName),eventListener]):ViewProperties.renderer.callMethod("listen",[this.compileElement.renderNode,literal(this.eventName),eventListener]);var disposable=variable("disposable_"+this.compileElement.view.disposables.length);this.compileElement.view.disposables.push(disposable),this.compileElement.view.createMethod.addStmt(disposable.set(listenExpr).toDeclStmt(FUNCTION_TYPE,[StmtModifier.Private]))},CompileEventListener.prototype.listenToAnimation=function(output){var outputListener=THIS_EXPR.callMethod("eventHandler",[THIS_EXPR.prop(this._methodName).callMethod(BuiltinMethod.Bind,[THIS_EXPR])]),stmt=THIS_EXPR.callMethod("registerAnimationOutput",[this.compileElement.renderNode,importExpr(resolveIdentifier(Identifiers.AnimationOutput)).instantiate([literal(output.name),literal(output.phase)]),outputListener]).toStmt();this.compileElement.view.createMethod.addStmt(stmt)},CompileEventListener.prototype.listenToDirective=function(directiveInstance,observablePropName){var subscription=variable("subscription_"+this.compileElement.view.subscriptions.length);this.compileElement.view.subscriptions.push(subscription);var eventListener=THIS_EXPR.callMethod("eventHandler",[THIS_EXPR.prop(this._methodName).callMethod(BuiltinMethod.Bind,[THIS_EXPR])]);this.compileElement.view.createMethod.addStmt(subscription.set(directiveInstance.prop(observablePropName).callMethod(BuiltinMethod.SubscribeObservable,[eventListener])).toDeclStmt(null,[StmtModifier.Final]))},CompileEventListener}(),STATE_IS_NEVER_CHECKED=THIS_EXPR.prop("numberOfChecks").identical(new LiteralExpr(0)),NOT_THROW_ON_CHANGES=not(DetectChangesVars.throwOnChange),ViewBinderVisitor=function(){function ViewBinderVisitor(view,animationOutputs){var _this=this;this.view=view,this.animationOutputs=animationOutputs,this._nodeIndex=0,this._animationOutputsMap={},animationOutputs.forEach(function(entry){_this._animationOutputsMap[entry.fullPropertyName]=entry})}return ViewBinderVisitor.prototype.visitBoundText=function(ast,parent){var node=this.view.nodes[this._nodeIndex++];return bindRenderText(ast,node,this.view),null},ViewBinderVisitor.prototype.visitText=function(ast,parent){return this._nodeIndex++,null},ViewBinderVisitor.prototype.visitNgContent=function(ast,parent){return null},ViewBinderVisitor.prototype.visitElement=function(ast,parent){var _this=this,compileElement=this.view.nodes[this._nodeIndex++],eventListeners=[],animationEventListeners=[];return collectEventListeners(ast.outputs,ast.directives,compileElement).forEach(function(entry){if("@"==entry.eventName[0]){var animationOutputName=entry.eventName.substr(1),output=_this._animationOutputsMap[animationOutputName];output&&animationEventListeners.push(new CompileElementAnimationOutput(entry,output))}else eventListeners.push(entry)}),bindAnimationOutputs(animationEventListeners),bindRenderInputs(ast.inputs,compileElement),bindRenderOutputs(eventListeners),ast.directives.forEach(function(directiveAst){var directiveInstance=compileElement.instances.get(directiveAst.directive.type.reference);bindDirectiveInputs(directiveAst,directiveInstance,compileElement),bindDirectiveDetectChangesLifecycleCallbacks(directiveAst,directiveInstance,compileElement),bindDirectiveHostProps(directiveAst,directiveInstance,compileElement),bindDirectiveOutputs(directiveAst,directiveInstance,eventListeners)}),templateVisitAll(this,ast.children,compileElement),ast.directives.forEach(function(directiveAst){var directiveInstance=compileElement.instances.get(directiveAst.directive.type.reference);bindDirectiveAfterContentLifecycleCallbacks(directiveAst.directive,directiveInstance,compileElement),bindDirectiveAfterViewLifecycleCallbacks(directiveAst.directive,directiveInstance,compileElement)}),ast.providers.forEach(function(providerAst){var providerInstance=compileElement.instances.get(providerAst.token.reference);bindInjectableDestroyLifecycleCallbacks(providerAst,providerInstance,compileElement)}),null},ViewBinderVisitor.prototype.visitEmbeddedTemplate=function(ast,parent){var compileElement=this.view.nodes[this._nodeIndex++],eventListeners=collectEventListeners(ast.outputs,ast.directives,compileElement);return ast.directives.forEach(function(directiveAst){var directiveInstance=compileElement.instances.get(directiveAst.directive.type.reference);bindDirectiveInputs(directiveAst,directiveInstance,compileElement),bindDirectiveDetectChangesLifecycleCallbacks(directiveAst,directiveInstance,compileElement),bindDirectiveOutputs(directiveAst,directiveInstance,eventListeners),bindDirectiveAfterContentLifecycleCallbacks(directiveAst.directive,directiveInstance,compileElement),bindDirectiveAfterViewLifecycleCallbacks(directiveAst.directive,directiveInstance,compileElement)}),ast.providers.forEach(function(providerAst){var providerInstance=compileElement.instances.get(providerAst.token.reference);bindInjectableDestroyLifecycleCallbacks(providerAst,providerInstance,compileElement)}),bindView(compileElement.embeddedView,ast.children,this.animationOutputs),null},ViewBinderVisitor.prototype.visitAttr=function(ast,ctx){return null},ViewBinderVisitor.prototype.visitDirective=function(ast,ctx){return null},ViewBinderVisitor.prototype.visitEvent=function(ast,eventTargetAndNames){return null},ViewBinderVisitor.prototype.visitReference=function(ast,ctx){return null},ViewBinderVisitor.prototype.visitVariable=function(ast,ctx){return null},ViewBinderVisitor.prototype.visitDirectiveProperty=function(ast,context){return null},ViewBinderVisitor.prototype.visitElementProperty=function(ast,context){return null},ViewBinderVisitor}(),IMPLICIT_TEMPLATE_VAR="$implicit",CLASS_ATTR$1="class",STYLE_ATTR="style",NG_CONTAINER_TAG="ng-container",parentRenderNodeVar=variable("parentRenderNode"),rootSelectorVar=variable("rootSelector"),ViewFactoryDependency=function(){function ViewFactoryDependency(comp,placeholder){this.comp=comp,this.placeholder=placeholder}return ViewFactoryDependency}(),ComponentFactoryDependency=function(){function ComponentFactoryDependency(comp,placeholder){this.comp=comp,this.placeholder=placeholder}return ComponentFactoryDependency}(),ViewBuilderVisitor=function(){function ViewBuilderVisitor(view,targetDependencies){this.view=view,this.targetDependencies=targetDependencies,this.nestedViewCount=0,this._animationCompiler=new AnimationCompiler}return ViewBuilderVisitor.prototype._isRootNode=function(parent){return parent.view!==this.view},ViewBuilderVisitor.prototype._addRootNodeAndProject=function(node){var projectedNode=_getOuterContainerOrSelf(node),parent=projectedNode.parent,ngContentIndex=projectedNode.sourceAst.ngContentIndex,vcAppEl=node instanceof CompileElement&&node.hasViewContainer?node.appElement:null;this._isRootNode(parent)?this.view.viewType!==ViewType.COMPONENT&&this.view.rootNodesOrAppElements.push(isPresent(vcAppEl)?vcAppEl:node.renderNode):isPresent(parent.component)&&isPresent(ngContentIndex)&&parent.addContentNode(ngContentIndex,isPresent(vcAppEl)?vcAppEl:node.renderNode)},ViewBuilderVisitor.prototype._getParentRenderNode=function(parent){return parent=_getOuterContainerParentOrSelf(parent),this._isRootNode(parent)?this.view.viewType===ViewType.COMPONENT?parentRenderNodeVar:NULL_EXPR:isPresent(parent.component)&&parent.component.template.encapsulation!==_angular_core.ViewEncapsulation.Native?NULL_EXPR:parent.renderNode},ViewBuilderVisitor.prototype.visitBoundText=function(ast,parent){return this._visitText(ast,"",parent)},ViewBuilderVisitor.prototype.visitText=function(ast,parent){return this._visitText(ast,ast.value,parent)},ViewBuilderVisitor.prototype._visitText=function(ast,value,parent){var fieldName="_text_"+this.view.nodes.length;this.view.fields.push(new ClassField(fieldName,importType(this.view.genConfig.renderTypes.renderText)));var renderNode=THIS_EXPR.prop(fieldName),compileNode=new CompileNode(parent,this.view,this.view.nodes.length,renderNode,ast),createRenderNode=THIS_EXPR.prop(fieldName).set(ViewProperties.renderer.callMethod("createText",[this._getParentRenderNode(parent),literal(value),this.view.createMethod.resetDebugInfoExpr(this.view.nodes.length,ast)])).toStmt();return this.view.nodes.push(compileNode),this.view.createMethod.addStmt(createRenderNode),this._addRootNodeAndProject(compileNode),renderNode},ViewBuilderVisitor.prototype.visitNgContent=function(ast,parent){this.view.createMethod.resetDebugInfo(null,ast);var parentRenderNode=this._getParentRenderNode(parent),nodesExpression=ViewProperties.projectableNodes.key(literal(ast.index),new ArrayType(importType(this.view.genConfig.renderTypes.renderNode)));return parentRenderNode!==NULL_EXPR?this.view.createMethod.addStmt(ViewProperties.renderer.callMethod("projectNodes",[parentRenderNode,importExpr(resolveIdentifier(Identifiers.flattenNestedViewRenderNodes)).callFn([nodesExpression])]).toStmt()):this._isRootNode(parent)?this.view.viewType!==ViewType.COMPONENT&&this.view.rootNodesOrAppElements.push(nodesExpression):isPresent(parent.component)&&isPresent(ast.ngContentIndex)&&parent.addContentNode(ast.ngContentIndex,nodesExpression),null},ViewBuilderVisitor.prototype.visitElement=function(ast,parent){var createRenderNodeExpr,_this=this,nodeIndex=this.view.nodes.length,debugContextExpr=this.view.createMethod.resetDebugInfoExpr(nodeIndex,ast);createRenderNodeExpr=0===nodeIndex&&this.view.viewType===ViewType.HOST?THIS_EXPR.callMethod("selectOrCreateHostElement",[literal(ast.name),rootSelectorVar,debugContextExpr]):ast.name===NG_CONTAINER_TAG?ViewProperties.renderer.callMethod("createTemplateAnchor",[this._getParentRenderNode(parent),debugContextExpr]):ViewProperties.renderer.callMethod("createElement",[this._getParentRenderNode(parent),literal(ast.name),debugContextExpr]);var fieldName="_el_"+nodeIndex;this.view.fields.push(new ClassField(fieldName,importType(this.view.genConfig.renderTypes.renderElement))),this.view.createMethod.addStmt(THIS_EXPR.prop(fieldName).set(createRenderNodeExpr).toStmt());for(var renderNode=THIS_EXPR.prop(fieldName),directives=ast.directives.map(function(directiveAst){return directiveAst.directive}),component=directives.find(function(directive){return directive.isComponent}),htmlAttrs=_readHtmlAttrs(ast.attrs),attrNameAndValues=_mergeHtmlAndDirectiveAttrs(htmlAttrs,directives),i=0;i<attrNameAndValues.length;i++){var attrName=attrNameAndValues[i][0];if(ast.name!==NG_CONTAINER_TAG){var attrValue=attrNameAndValues[i][1];this.view.createMethod.addStmt(ViewProperties.renderer.callMethod("setElementAttribute",[renderNode,literal(attrName),literal(attrValue)]).toStmt())}}var compileElement=new CompileElement(parent,this.view,nodeIndex,renderNode,ast,component,directives,ast.providers,ast.hasViewContainer,(!1),ast.references);this.view.nodes.push(compileElement);var compViewExpr=null;if(isPresent(component)){var nestedComponentIdentifier=new CompileIdentifierMetadata({name:getViewFactoryName(component,0)});this.targetDependencies.push(new ViewFactoryDependency(component.type,nestedComponentIdentifier));var entryComponentIdentifiers=component.entryComponents.map(function(entryComponent){var id=new CompileIdentifierMetadata({name:entryComponent.name});return _this.targetDependencies.push(new ComponentFactoryDependency(entryComponent,id)),id});compileElement.createComponentFactoryResolver(entryComponentIdentifiers),compViewExpr=variable("compView_"+nodeIndex),compileElement.setComponentView(compViewExpr),this.view.createMethod.addStmt(compViewExpr.set(importExpr(nestedComponentIdentifier).callFn([ViewProperties.viewUtils,compileElement.injector,compileElement.appElement])).toDeclStmt())}if(compileElement.beforeChildren(),this._addRootNodeAndProject(compileElement),templateVisitAll(this,ast.children,compileElement),compileElement.afterChildren(this.view.nodes.length-nodeIndex-1),isPresent(compViewExpr)){var codeGenContentNodes;codeGenContentNodes=this.view.component.type.isHost?ViewProperties.projectableNodes:literalArr(compileElement.contentNodesByNgContentIndex.map(function(nodes){return createFlatArray(nodes)})),this.view.createMethod.addStmt(compViewExpr.callMethod("create",[compileElement.getComponent(),codeGenContentNodes,NULL_EXPR]).toStmt())}return null},ViewBuilderVisitor.prototype.visitEmbeddedTemplate=function(ast,parent){var nodeIndex=this.view.nodes.length,fieldName="_anchor_"+nodeIndex;this.view.fields.push(new ClassField(fieldName,importType(this.view.genConfig.renderTypes.renderComment))),this.view.createMethod.addStmt(THIS_EXPR.prop(fieldName).set(ViewProperties.renderer.callMethod("createTemplateAnchor",[this._getParentRenderNode(parent),this.view.createMethod.resetDebugInfoExpr(nodeIndex,ast)])).toStmt());var renderNode=THIS_EXPR.prop(fieldName),templateVariableBindings=ast.variables.map(function(varAst){return[varAst.value.length>0?varAst.value:IMPLICIT_TEMPLATE_VAR,varAst.name]}),directives=ast.directives.map(function(directiveAst){return directiveAst.directive}),compileElement=new CompileElement(parent,this.view,nodeIndex,renderNode,ast,null,directives,ast.providers,ast.hasViewContainer,(!0),ast.references);this.view.nodes.push(compileElement);var compiledAnimations=this._animationCompiler.compileComponent(this.view.component,[ast]);this.nestedViewCount++;var embeddedView=new CompileView(this.view.component,this.view.genConfig,this.view.pipeMetas,NULL_EXPR,compiledAnimations.triggers,this.view.viewIndex+this.nestedViewCount,compileElement,templateVariableBindings);return this.nestedViewCount+=buildView(embeddedView,ast.children,this.targetDependencies),compileElement.beforeChildren(),this._addRootNodeAndProject(compileElement),compileElement.afterChildren(0),null},ViewBuilderVisitor.prototype.visitAttr=function(ast,ctx){return null},ViewBuilderVisitor.prototype.visitDirective=function(ast,ctx){return null},ViewBuilderVisitor.prototype.visitEvent=function(ast,eventTargetAndNames){return null},ViewBuilderVisitor.prototype.visitReference=function(ast,ctx){return null},ViewBuilderVisitor.prototype.visitVariable=function(ast,ctx){return null},ViewBuilderVisitor.prototype.visitDirectiveProperty=function(ast,context){return null},ViewBuilderVisitor.prototype.visitElementProperty=function(ast,context){return null},ViewBuilderVisitor}(),ViewCompileResult=function(){function ViewCompileResult(statements,viewFactoryVar,dependencies){this.statements=statements,this.viewFactoryVar=viewFactoryVar,this.dependencies=dependencies}return ViewCompileResult}(),ViewCompiler=function(){function ViewCompiler(_genConfig){this._genConfig=_genConfig,this._animationCompiler=new AnimationCompiler}return ViewCompiler.prototype.compileComponent=function(component,template,styles,pipes){var dependencies=[],compiledAnimations=this._animationCompiler.compileComponent(component,template),statements=[],animationTriggers=compiledAnimations.triggers;animationTriggers.forEach(function(entry){statements.push(entry.statesMapStatement),statements.push(entry.fnStatement)});var view=new CompileView(component,this._genConfig,pipes,styles,animationTriggers,0,CompileElement.createNull(),[]);return buildView(view,template,dependencies),bindView(view,template,compiledAnimations.outputs),finishView(view,statements),new ViewCompileResult(statements,view.viewFactory.name,dependencies)},ViewCompiler.decorators=[{type:_angular_core.Injectable}],ViewCompiler.ctorParameters=[{type:CompilerConfig}],ViewCompiler}(),SourceModule=function(){function SourceModule(moduleUrl,source){this.moduleUrl=moduleUrl,this.source=source}return SourceModule}(),NgModulesSummary=function(){function NgModulesSummary(ngModuleByComponent){this.ngModuleByComponent=ngModuleByComponent}return NgModulesSummary}(),OfflineCompiler=function(){function OfflineCompiler(_metadataResolver,_directiveNormalizer,_templateParser,_styleCompiler,_viewCompiler,_ngModuleCompiler,_outputEmitter,_localeId,_translationFormat){this._metadataResolver=_metadataResolver,this._directiveNormalizer=_directiveNormalizer,this._templateParser=_templateParser,this._styleCompiler=_styleCompiler,this._viewCompiler=_viewCompiler,this._ngModuleCompiler=_ngModuleCompiler,this._outputEmitter=_outputEmitter,this._localeId=_localeId,this._translationFormat=_translationFormat}return OfflineCompiler.prototype.analyzeModules=function(ngModules){var _this=this,ngModuleByComponent=new Map;return ngModules.forEach(function(ngModule){var ngModuleMeta=_this._metadataResolver.getNgModuleMetadata(ngModule);ngModuleMeta.declaredDirectives.forEach(function(dirMeta){dirMeta.isComponent&&ngModuleByComponent.set(dirMeta.type.reference,ngModuleMeta)})}),new NgModulesSummary(ngModuleByComponent)},OfflineCompiler.prototype.clearCache=function(){this._directiveNormalizer.clearCache(),this._metadataResolver.clearCache()},OfflineCompiler.prototype.compile=function(moduleUrl,ngModulesSummary,components,ngModules){var _this=this,fileSuffix=_splitTypescriptSuffix(moduleUrl)[1],statements=[],exportedVars=[],outputSourceModules=[];return exportedVars.push.apply(exportedVars,ngModules.map(function(ngModuleType){return _this._compileModule(ngModuleType,statements)})),Promise.all(components.map(function(compType){var compMeta=_this._metadataResolver.getDirectiveMetadata(compType),ngModule=ngModulesSummary.ngModuleByComponent.get(compType);if(!ngModule)throw new Error("Cannot determine the module for component "+compMeta.type.name+"!");return Promise.all([compMeta].concat(ngModule.transitiveModule.directives).map(function(dirMeta){return _this._directiveNormalizer.normalizeDirective(dirMeta).asyncResult})).then(function(normalizedCompWithDirectives){var compMeta=normalizedCompWithDirectives[0],dirMetas=normalizedCompWithDirectives.slice(1);_assertComponent(compMeta);var stylesCompileResults=_this._styleCompiler.compileComponent(compMeta);stylesCompileResults.externalStylesheets.forEach(function(compiledStyleSheet){outputSourceModules.push(_this._codgenStyles(compiledStyleSheet,fileSuffix))}),exportedVars.push(_this._compileComponentFactory(compMeta,fileSuffix,statements)),exportedVars.push(_this._compileComponent(compMeta,dirMetas,ngModule.transitiveModule.pipes,ngModule.schemas,stylesCompileResults.componentStylesheet,fileSuffix,statements))})})).then(function(){return statements.length>0&&outputSourceModules.unshift(_this._codegenSourceModule(_ngfactoryModuleUrl(moduleUrl),statements,exportedVars)),outputSourceModules})},OfflineCompiler.prototype._compileModule=function(ngModuleType,targetStatements){var ngModule=this._metadataResolver.getNgModuleMetadata(ngModuleType),appCompileResult=this._ngModuleCompiler.compile(ngModule,[new CompileProviderMetadata({token:resolveIdentifierToken(Identifiers.LOCALE_ID),useValue:this._localeId}),new CompileProviderMetadata({token:resolveIdentifierToken(Identifiers.TRANSLATIONS_FORMAT),useValue:this._translationFormat})]);return appCompileResult.dependencies.forEach(function(dep){dep.placeholder.name=_componentFactoryName(dep.comp),dep.placeholder.moduleUrl=_ngfactoryModuleUrl(dep.comp.moduleUrl)}),targetStatements.push.apply(targetStatements,appCompileResult.statements),appCompileResult.ngModuleFactoryVar},OfflineCompiler.prototype._compileComponentFactory=function(compMeta,fileSuffix,targetStatements){var hostMeta=createHostComponentMeta(compMeta),hostViewFactoryVar=this._compileComponent(hostMeta,[compMeta],[],[],null,fileSuffix,targetStatements),compFactoryVar=_componentFactoryName(compMeta.type);return targetStatements.push(variable(compFactoryVar).set(importExpr(resolveIdentifier(Identifiers.ComponentFactory),[importType(compMeta.type)]).instantiate([literal(compMeta.selector),variable(hostViewFactoryVar),importExpr(compMeta.type)],importType(resolveIdentifier(Identifiers.ComponentFactory),[importType(compMeta.type)],[TypeModifier.Const]))).toDeclStmt(null,[StmtModifier.Final])),compFactoryVar},OfflineCompiler.prototype._compileComponent=function(compMeta,directives,pipes,schemas,componentStyles,fileSuffix,targetStatements){var parsedTemplate=this._templateParser.parse(compMeta,compMeta.template.template,directives,pipes,schemas,compMeta.type.name),stylesExpr=componentStyles?variable(componentStyles.stylesVar):literalArr([]),viewResult=this._viewCompiler.compileComponent(compMeta,parsedTemplate,stylesExpr,pipes);return componentStyles&&ListWrapper.addAll(targetStatements,_resolveStyleStatements(componentStyles,fileSuffix)),ListWrapper.addAll(targetStatements,_resolveViewStatements(viewResult)),viewResult.viewFactoryVar},OfflineCompiler.prototype._codgenStyles=function(stylesCompileResult,fileSuffix){return _resolveStyleStatements(stylesCompileResult,fileSuffix),this._codegenSourceModule(_stylesModuleUrl(stylesCompileResult.meta.moduleUrl,stylesCompileResult.isShimmed,fileSuffix),stylesCompileResult.statements,[stylesCompileResult.stylesVar])},OfflineCompiler.prototype._codegenSourceModule=function(moduleUrl,statements,exportedVars){return new SourceModule(moduleUrl,this._outputEmitter.emitStatements(moduleUrl,statements,exportedVars))},OfflineCompiler}(),ResourceLoader=function(){function ResourceLoader(){}return ResourceLoader.prototype.get=function(url){return null},ResourceLoader}(),_ASSET_SCHEME="asset:",DEFAULT_PACKAGE_URL_PROVIDER={provide:_angular_core.PACKAGE_ROOT_URL,useValue:"/"},UrlResolver=function(){function UrlResolver(_packagePrefix){void 0===_packagePrefix&&(_packagePrefix=null),this._packagePrefix=_packagePrefix}return UrlResolver.prototype.resolve=function(baseUrl,url){var resolvedUrl=url;isPresent(baseUrl)&&baseUrl.length>0&&(resolvedUrl=_resolveUrl(baseUrl,resolvedUrl));var resolvedParts=_split(resolvedUrl),prefix=this._packagePrefix;if(isPresent(prefix)&&isPresent(resolvedParts)&&"package"==resolvedParts[_ComponentIndex.Scheme]){var path=resolvedParts[_ComponentIndex.Path];if(this._packagePrefix!==_ASSET_SCHEME)return prefix=StringWrapper.stripRight(prefix,"/"),path=StringWrapper.stripLeft(path,"/"),prefix+"/"+path;var pathSegements=path.split(/\//);resolvedUrl="asset:"+pathSegements[0]+"/lib/"+pathSegements.slice(1).join("/")}return resolvedUrl},UrlResolver.decorators=[{type:_angular_core.Injectable}],UrlResolver.ctorParameters=[{type:void 0,decorators:[{type:_angular_core.Inject,args:[_angular_core.PACKAGE_ROOT_URL]}]}],UrlResolver}(),_splitRe=new RegExp("^(?:([^:/?#.]+):)?(?://(?:([^/?#]*)@)?([\\w\\d\\-\\u0100-\\uffff.%]*)(?::([0-9]+))?)?([^?#]+)?(?:\\?([^#]*))?(?:#(.*))?$");!function(_ComponentIndex){_ComponentIndex[_ComponentIndex.Scheme=1]="Scheme",_ComponentIndex[_ComponentIndex.UserInfo=2]="UserInfo",_ComponentIndex[_ComponentIndex.Domain=3]="Domain",_ComponentIndex[_ComponentIndex.Port=4]="Port",_ComponentIndex[_ComponentIndex.Path=5]="Path",_ComponentIndex[_ComponentIndex.QueryData=6]="QueryData",_ComponentIndex[_ComponentIndex.Fragment=7]="Fragment"}(_ComponentIndex||(_ComponentIndex={}));var DirectiveNormalizer=function(){function DirectiveNormalizer(_resourceLoader,_urlResolver,_htmlParser,_config){
this._resourceLoader=_resourceLoader,this._urlResolver=_urlResolver,this._htmlParser=_htmlParser,this._config=_config,this._resourceLoaderCache=new Map}return DirectiveNormalizer.prototype.clearCache=function(){this._resourceLoaderCache.clear()},DirectiveNormalizer.prototype.clearCacheFor=function(normalizedDirective){var _this=this;normalizedDirective.isComponent&&(this._resourceLoaderCache.delete(normalizedDirective.template.templateUrl),normalizedDirective.template.externalStylesheets.forEach(function(stylesheet){_this._resourceLoaderCache.delete(stylesheet.moduleUrl)}))},DirectiveNormalizer.prototype._fetch=function(url){var result=this._resourceLoaderCache.get(url);return result||(result=this._resourceLoader.get(url),this._resourceLoaderCache.set(url,result)),result},DirectiveNormalizer.prototype.normalizeDirective=function(directive){var _this=this;if(!directive.isComponent)return new SyncAsyncResult(directive,Promise.resolve(directive));var normalizedTemplateAsync,normalizedTemplateSync=null;if(isPresent(directive.template.template))normalizedTemplateSync=this.normalizeTemplateSync(directive.type,directive.template),normalizedTemplateAsync=Promise.resolve(normalizedTemplateSync);else{if(!directive.template.templateUrl)throw new Error("No template specified for component "+directive.type.name);normalizedTemplateAsync=this.normalizeTemplateAsync(directive.type,directive.template)}if(normalizedTemplateSync&&0===normalizedTemplateSync.styleUrls.length){var normalizedDirective=_cloneDirectiveWithTemplate(directive,normalizedTemplateSync);return new SyncAsyncResult(normalizedDirective,Promise.resolve(normalizedDirective))}return new SyncAsyncResult(null,normalizedTemplateAsync.then(function(normalizedTemplate){return _this.normalizeExternalStylesheets(normalizedTemplate)}).then(function(normalizedTemplate){return _cloneDirectiveWithTemplate(directive,normalizedTemplate)}))},DirectiveNormalizer.prototype.normalizeTemplateSync=function(directiveType,template){return this.normalizeLoadedTemplate(directiveType,template,template.template,directiveType.moduleUrl)},DirectiveNormalizer.prototype.normalizeTemplateAsync=function(directiveType,template){var _this=this,templateUrl=this._urlResolver.resolve(directiveType.moduleUrl,template.templateUrl);return this._fetch(templateUrl).then(function(value){return _this.normalizeLoadedTemplate(directiveType,template,value,templateUrl)})},DirectiveNormalizer.prototype.normalizeLoadedTemplate=function(directiveType,templateMeta,template,templateAbsUrl){var interpolationConfig=InterpolationConfig.fromArray(templateMeta.interpolation),rootNodesAndErrors=this._htmlParser.parse(template,directiveType.name,!1,interpolationConfig);if(rootNodesAndErrors.errors.length>0){var errorString=rootNodesAndErrors.errors.join("\n");throw new Error("Template parse errors:\n"+errorString)}var templateMetadataStyles=this.normalizeStylesheet(new CompileStylesheetMetadata({styles:templateMeta.styles,styleUrls:templateMeta.styleUrls,moduleUrl:directiveType.moduleUrl})),visitor=new TemplatePreparseVisitor;visitAll(visitor,rootNodesAndErrors.rootNodes);var templateStyles=this.normalizeStylesheet(new CompileStylesheetMetadata({styles:visitor.styles,styleUrls:visitor.styleUrls,moduleUrl:templateAbsUrl})),allStyles=templateMetadataStyles.styles.concat(templateStyles.styles),allStyleUrls=templateMetadataStyles.styleUrls.concat(templateStyles.styleUrls),encapsulation=templateMeta.encapsulation;return isBlank(encapsulation)&&(encapsulation=this._config.defaultEncapsulation),encapsulation===_angular_core.ViewEncapsulation.Emulated&&0===allStyles.length&&0===allStyleUrls.length&&(encapsulation=_angular_core.ViewEncapsulation.None),new CompileTemplateMetadata({encapsulation:encapsulation,template:template,templateUrl:templateAbsUrl,styles:allStyles,styleUrls:allStyleUrls,externalStylesheets:templateMeta.externalStylesheets,ngContentSelectors:visitor.ngContentSelectors,animations:templateMeta.animations,interpolation:templateMeta.interpolation})},DirectiveNormalizer.prototype.normalizeExternalStylesheets=function(templateMeta){return this._loadMissingExternalStylesheets(templateMeta.styleUrls).then(function(externalStylesheets){return new CompileTemplateMetadata({encapsulation:templateMeta.encapsulation,template:templateMeta.template,templateUrl:templateMeta.templateUrl,styles:templateMeta.styles,styleUrls:templateMeta.styleUrls,externalStylesheets:externalStylesheets,ngContentSelectors:templateMeta.ngContentSelectors,animations:templateMeta.animations,interpolation:templateMeta.interpolation})})},DirectiveNormalizer.prototype._loadMissingExternalStylesheets=function(styleUrls,loadedStylesheets){var _this=this;return void 0===loadedStylesheets&&(loadedStylesheets=new Map),Promise.all(styleUrls.filter(function(styleUrl){return!loadedStylesheets.has(styleUrl)}).map(function(styleUrl){return _this._fetch(styleUrl).then(function(loadedStyle){var stylesheet=_this.normalizeStylesheet(new CompileStylesheetMetadata({styles:[loadedStyle],moduleUrl:styleUrl}));return loadedStylesheets.set(styleUrl,stylesheet),_this._loadMissingExternalStylesheets(stylesheet.styleUrls,loadedStylesheets)})})).then(function(_){return MapWrapper.values(loadedStylesheets)})},DirectiveNormalizer.prototype.normalizeStylesheet=function(stylesheet){var _this=this,allStyleUrls=stylesheet.styleUrls.filter(isStyleUrlResolvable).map(function(url){return _this._urlResolver.resolve(stylesheet.moduleUrl,url)}),allStyles=stylesheet.styles.map(function(style){var styleWithImports=extractStyleUrls(_this._urlResolver,stylesheet.moduleUrl,style);return allStyleUrls.push.apply(allStyleUrls,styleWithImports.styleUrls),styleWithImports.style});return new CompileStylesheetMetadata({styles:allStyles,styleUrls:allStyleUrls,moduleUrl:stylesheet.moduleUrl})},DirectiveNormalizer.decorators=[{type:_angular_core.Injectable}],DirectiveNormalizer.ctorParameters=[{type:ResourceLoader},{type:UrlResolver},{type:HtmlParser},{type:CompilerConfig}],DirectiveNormalizer}(),TemplatePreparseVisitor=function(){function TemplatePreparseVisitor(){this.ngContentSelectors=[],this.styles=[],this.styleUrls=[],this.ngNonBindableStackCount=0}return TemplatePreparseVisitor.prototype.visitElement=function(ast,context){var preparsedElement=preparseElement(ast);switch(preparsedElement.type){case PreparsedElementType.NG_CONTENT:0===this.ngNonBindableStackCount&&this.ngContentSelectors.push(preparsedElement.selectAttr);break;case PreparsedElementType.STYLE:var textContent="";ast.children.forEach(function(child){child instanceof Text&&(textContent+=child.value)}),this.styles.push(textContent);break;case PreparsedElementType.STYLESHEET:this.styleUrls.push(preparsedElement.hrefAttr)}return preparsedElement.nonBindable&&this.ngNonBindableStackCount++,visitAll(this,ast.children),preparsedElement.nonBindable&&this.ngNonBindableStackCount--,null},TemplatePreparseVisitor.prototype.visitComment=function(ast,context){return null},TemplatePreparseVisitor.prototype.visitAttribute=function(ast,context){return null},TemplatePreparseVisitor.prototype.visitText=function(ast,context){return null},TemplatePreparseVisitor.prototype.visitExpansion=function(ast,context){return null},TemplatePreparseVisitor.prototype.visitExpansionCase=function(ast,context){return null},TemplatePreparseVisitor}(),DirectiveResolver=function(){function DirectiveResolver(_reflector){void 0===_reflector&&(_reflector=reflector),this._reflector=_reflector}return DirectiveResolver.prototype.resolve=function(type,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0);var typeMetadata=this._reflector.annotations(_angular_core.resolveForwardRef(type));if(isPresent(typeMetadata)){var metadata=typeMetadata.find(_isDirectiveMetadata);if(isPresent(metadata)){var propertyMetadata=this._reflector.propMetadata(type);return this._mergeWithPropertyMetadata(metadata,propertyMetadata,type)}}if(throwIfNotFound)throw new Error("No Directive annotation found on "+stringify(type));return null},DirectiveResolver.prototype._mergeWithPropertyMetadata=function(dm,propertyMetadata,directiveType){var inputs=[],outputs=[],host={},queries={};return StringMapWrapper.forEach(propertyMetadata,function(metadata,propName){metadata.forEach(function(a){if(a instanceof _angular_core.Input)isPresent(a.bindingPropertyName)?inputs.push(propName+": "+a.bindingPropertyName):inputs.push(propName);else if(a instanceof _angular_core.Output){var output=a;isPresent(output.bindingPropertyName)?outputs.push(propName+": "+output.bindingPropertyName):outputs.push(propName)}else if(a instanceof _angular_core.HostBinding){var hostBinding=a;isPresent(hostBinding.hostPropertyName)?host["["+hostBinding.hostPropertyName+"]"]=propName:host["["+propName+"]"]=propName}else if(a instanceof _angular_core.HostListener){var hostListener=a,args=isPresent(hostListener.args)?hostListener.args.join(", "):"";host["("+hostListener.eventName+")"]=propName+"("+args+")"}else a instanceof _angular_core.Query&&(queries[propName]=a)})}),this._merge(dm,inputs,outputs,host,queries,directiveType)},DirectiveResolver.prototype._extractPublicName=function(def){return splitAtColon(def,[null,def])[1].trim()},DirectiveResolver.prototype._merge=function(dm,inputs,outputs,host,queries,directiveType){var mergedInputs,_this=this;if(isPresent(dm.inputs)){var inputNames_1=dm.inputs.map(function(def){return _this._extractPublicName(def)});inputs.forEach(function(inputDef){var publicName=_this._extractPublicName(inputDef);if(inputNames_1.indexOf(publicName)>-1)throw new Error("Input '"+publicName+"' defined multiple times in '"+stringify(directiveType)+"'")}),mergedInputs=dm.inputs.concat(inputs)}else mergedInputs=inputs;var mergedOutputs;if(isPresent(dm.outputs)){var outputNames_1=dm.outputs.map(function(def){return _this._extractPublicName(def)});outputs.forEach(function(outputDef){var publicName=_this._extractPublicName(outputDef);if(outputNames_1.indexOf(publicName)>-1)throw new Error("Output event '"+publicName+"' defined multiple times in '"+stringify(directiveType)+"'")}),mergedOutputs=dm.outputs.concat(outputs)}else mergedOutputs=outputs;var mergedHost=isPresent(dm.host)?StringMapWrapper.merge(dm.host,host):host,mergedQueries=isPresent(dm.queries)?StringMapWrapper.merge(dm.queries,queries):queries;return dm instanceof _angular_core.Component?new _angular_core.Component({selector:dm.selector,inputs:mergedInputs,outputs:mergedOutputs,host:mergedHost,exportAs:dm.exportAs,moduleId:dm.moduleId,queries:mergedQueries,changeDetection:dm.changeDetection,providers:dm.providers,viewProviders:dm.viewProviders,entryComponents:dm.entryComponents,template:dm.template,templateUrl:dm.templateUrl,styles:dm.styles,styleUrls:dm.styleUrls,encapsulation:dm.encapsulation,animations:dm.animations,interpolation:dm.interpolation}):new _angular_core.Directive({selector:dm.selector,inputs:mergedInputs,outputs:mergedOutputs,host:mergedHost,exportAs:dm.exportAs,queries:mergedQueries,providers:dm.providers})},DirectiveResolver.decorators=[{type:_angular_core.Injectable}],DirectiveResolver.ctorParameters=[{type:ReflectorReader}],DirectiveResolver}(),LIFECYCLE_INTERFACES=MapWrapper.createFromPairs([[LifecycleHooks.OnInit,_angular_core.OnInit],[LifecycleHooks.OnDestroy,_angular_core.OnDestroy],[LifecycleHooks.DoCheck,_angular_core.DoCheck],[LifecycleHooks.OnChanges,_angular_core.OnChanges],[LifecycleHooks.AfterContentInit,_angular_core.AfterContentInit],[LifecycleHooks.AfterContentChecked,_angular_core.AfterContentChecked],[LifecycleHooks.AfterViewInit,_angular_core.AfterViewInit],[LifecycleHooks.AfterViewChecked,_angular_core.AfterViewChecked]]),LIFECYCLE_PROPS=MapWrapper.createFromPairs([[LifecycleHooks.OnInit,"ngOnInit"],[LifecycleHooks.OnDestroy,"ngOnDestroy"],[LifecycleHooks.DoCheck,"ngDoCheck"],[LifecycleHooks.OnChanges,"ngOnChanges"],[LifecycleHooks.AfterContentInit,"ngAfterContentInit"],[LifecycleHooks.AfterContentChecked,"ngAfterContentChecked"],[LifecycleHooks.AfterViewInit,"ngAfterViewInit"],[LifecycleHooks.AfterViewChecked,"ngAfterViewChecked"]]),NgModuleResolver=function(){function NgModuleResolver(_reflector){void 0===_reflector&&(_reflector=reflector),this._reflector=_reflector}return NgModuleResolver.prototype.resolve=function(type,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0);var ngModuleMeta=this._reflector.annotations(type).find(_isNgModuleMetadata);if(isPresent(ngModuleMeta))return ngModuleMeta;if(throwIfNotFound)throw new Error("No NgModule metadata found for '"+stringify(type)+"'.");return null},NgModuleResolver.decorators=[{type:_angular_core.Injectable}],NgModuleResolver.ctorParameters=[{type:ReflectorReader}],NgModuleResolver}(),PipeResolver=function(){function PipeResolver(_reflector){void 0===_reflector&&(_reflector=reflector),this._reflector=_reflector}return PipeResolver.prototype.resolve=function(type,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0);var metas=this._reflector.annotations(_angular_core.resolveForwardRef(type));if(isPresent(metas)){var annotation=metas.find(_isPipeMetadata);if(isPresent(annotation))return annotation}if(throwIfNotFound)throw new Error("No Pipe decorator found on "+stringify(type));return null},PipeResolver.decorators=[{type:_angular_core.Injectable}],PipeResolver.ctorParameters=[{type:ReflectorReader}],PipeResolver}(),__extends$14=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},CompileMetadataResolver=function(){function CompileMetadataResolver(_ngModuleResolver,_directiveResolver,_pipeResolver,_schemaRegistry,_reflector){void 0===_reflector&&(_reflector=reflector),this._ngModuleResolver=_ngModuleResolver,this._directiveResolver=_directiveResolver,this._pipeResolver=_pipeResolver,this._schemaRegistry=_schemaRegistry,this._reflector=_reflector,this._directiveCache=new Map,this._pipeCache=new Map,this._ngModuleCache=new Map,this._ngModuleOfTypes=new Map,this._anonymousTypes=new Map,this._anonymousTypeIndex=0}return CompileMetadataResolver.prototype.sanitizeTokenName=function(token){var identifier=stringify(token);if(identifier.indexOf("(")>=0){var found=this._anonymousTypes.get(token);isBlank(found)&&(this._anonymousTypes.set(token,this._anonymousTypeIndex++),found=this._anonymousTypes.get(token)),identifier="anonymous_token_"+found+"_"}return sanitizeIdentifier(identifier)},CompileMetadataResolver.prototype.clearCacheFor=function(type){this._directiveCache.delete(type),this._pipeCache.delete(type),this._ngModuleOfTypes.delete(type),this._ngModuleCache.clear()},CompileMetadataResolver.prototype.clearCache=function(){this._directiveCache.clear(),this._pipeCache.clear(),this._ngModuleCache.clear(),this._ngModuleOfTypes.clear()},CompileMetadataResolver.prototype.getAnimationEntryMetadata=function(entry){var _this=this,defs=entry.definitions.map(function(def){return _this.getAnimationStateMetadata(def)});return new CompileAnimationEntryMetadata(entry.name,defs)},CompileMetadataResolver.prototype.getAnimationStateMetadata=function(value){if(value instanceof _angular_core.AnimationStateDeclarationMetadata){var styles=this.getAnimationStyleMetadata(value.styles);return new CompileAnimationStateDeclarationMetadata(value.stateNameExpr,styles)}return value instanceof _angular_core.AnimationStateTransitionMetadata?new CompileAnimationStateTransitionMetadata(value.stateChangeExpr,this.getAnimationMetadata(value.steps)):null},CompileMetadataResolver.prototype.getAnimationStyleMetadata=function(value){return new CompileAnimationStyleMetadata(value.offset,value.styles)},CompileMetadataResolver.prototype.getAnimationMetadata=function(value){var _this=this;if(value instanceof _angular_core.AnimationStyleMetadata)return this.getAnimationStyleMetadata(value);if(value instanceof _angular_core.AnimationKeyframesSequenceMetadata)return new CompileAnimationKeyframesSequenceMetadata(value.steps.map(function(entry){return _this.getAnimationStyleMetadata(entry)}));if(value instanceof _angular_core.AnimationAnimateMetadata){var animateData=this.getAnimationMetadata(value.styles);return new CompileAnimationAnimateMetadata(value.timings,animateData)}if(value instanceof _angular_core.AnimationWithStepsMetadata){var steps=value.steps.map(function(step){return _this.getAnimationMetadata(step)});return value instanceof _angular_core.AnimationGroupMetadata?new CompileAnimationGroupMetadata(steps):new CompileAnimationSequenceMetadata(steps)}return null},CompileMetadataResolver.prototype.getDirectiveMetadata=function(directiveType,throwIfNotFound){var _this=this;void 0===throwIfNotFound&&(throwIfNotFound=!0),directiveType=_angular_core.resolveForwardRef(directiveType);var meta=this._directiveCache.get(directiveType);if(isBlank(meta)){var dirMeta=this._directiveResolver.resolve(directiveType,throwIfNotFound);if(!dirMeta)return null;var templateMeta=null,changeDetectionStrategy=null,viewProviders=[],moduleUrl=staticTypeModuleUrl(directiveType),entryComponentMetadata=[],selector=dirMeta.selector;if(dirMeta instanceof _angular_core.Component){var cmpMeta=dirMeta;assertArrayOfStrings("styles",cmpMeta.styles),assertInterpolationSymbols("interpolation",cmpMeta.interpolation);var animations=isPresent(cmpMeta.animations)?cmpMeta.animations.map(function(e){return _this.getAnimationEntryMetadata(e)}):null;assertArrayOfStrings("styles",cmpMeta.styles),assertArrayOfStrings("styleUrls",cmpMeta.styleUrls),templateMeta=new CompileTemplateMetadata({encapsulation:cmpMeta.encapsulation,template:cmpMeta.template,templateUrl:cmpMeta.templateUrl,styles:cmpMeta.styles,styleUrls:cmpMeta.styleUrls,animations:animations,interpolation:cmpMeta.interpolation}),changeDetectionStrategy=cmpMeta.changeDetection,isPresent(dirMeta.viewProviders)&&(viewProviders=this.getProvidersMetadata(dirMeta.viewProviders,entryComponentMetadata,'viewProviders for "'+stringify(directiveType)+'"')),moduleUrl=componentModuleUrl(this._reflector,directiveType,cmpMeta),cmpMeta.entryComponents&&(entryComponentMetadata=flattenArray(cmpMeta.entryComponents).map(function(type){return _this.getTypeMetadata(type,staticTypeModuleUrl(type))}).concat(entryComponentMetadata)),selector||(selector=this._schemaRegistry.getDefaultComponentElementName())}else if(!selector)throw new Error("Directive "+stringify(directiveType)+" has no selector, please add it!");var providers=[];isPresent(dirMeta.providers)&&(providers=this.getProvidersMetadata(dirMeta.providers,entryComponentMetadata,'providers for "'+stringify(directiveType)+'"'));var queries=[],viewQueries=[];isPresent(dirMeta.queries)&&(queries=this.getQueriesMetadata(dirMeta.queries,!1,directiveType),viewQueries=this.getQueriesMetadata(dirMeta.queries,!0,directiveType)),meta=CompileDirectiveMetadata.create({selector:selector,exportAs:dirMeta.exportAs,isComponent:isPresent(templateMeta),type:this.getTypeMetadata(directiveType,moduleUrl),template:templateMeta,changeDetection:changeDetectionStrategy,inputs:dirMeta.inputs,outputs:dirMeta.outputs,host:dirMeta.host,providers:providers,viewProviders:viewProviders,queries:queries,viewQueries:viewQueries,entryComponents:entryComponentMetadata}),this._directiveCache.set(directiveType,meta)}return meta},CompileMetadataResolver.prototype.getNgModuleMetadata=function(moduleType,throwIfNotFound){var _this=this;void 0===throwIfNotFound&&(throwIfNotFound=!0),moduleType=_angular_core.resolveForwardRef(moduleType);var compileMeta=this._ngModuleCache.get(moduleType);if(!compileMeta){var meta=this._ngModuleResolver.resolve(moduleType,throwIfNotFound);if(!meta)return null;var declaredDirectives_1=[],exportedDirectives_1=[],declaredPipes_1=[],exportedPipes_1=[],importedModules_1=[],exportedModules_1=[],providers_1=[],entryComponents_1=[],bootstrapComponents=[],schemas=[];meta.imports&&flattenArray(meta.imports).forEach(function(importedType){var importedModuleType;if(isValidType(importedType))importedModuleType=importedType;else if(importedType&&importedType.ngModule){var moduleWithProviders=importedType;importedModuleType=moduleWithProviders.ngModule,moduleWithProviders.providers&&providers_1.push.apply(providers_1,_this.getProvidersMetadata(moduleWithProviders.providers,entryComponents_1,"provider for the NgModule '"+stringify(importedModuleType)+"'"))}if(!importedModuleType)throw new Error("Unexpected value '"+stringify(importedType)+"' imported by the module '"+stringify(moduleType)+"'");var importedMeta=_this.getNgModuleMetadata(importedModuleType,!1);if(null===importedMeta)throw new Error("Unexpected "+_this._getTypeDescriptor(importedType)+" '"+stringify(importedType)+"' imported by the module '"+stringify(moduleType)+"'");importedModules_1.push(importedMeta)}),meta.exports&&flattenArray(meta.exports).forEach(function(exportedType){if(!isValidType(exportedType))throw new Error("Unexpected value '"+stringify(exportedType)+"' exported by the module '"+stringify(moduleType)+"'");var exportedDirMeta,exportedPipeMeta,exportedModuleMeta;if(exportedDirMeta=_this.getDirectiveMetadata(exportedType,!1))exportedDirectives_1.push(exportedDirMeta);else if(exportedPipeMeta=_this.getPipeMetadata(exportedType,!1))exportedPipes_1.push(exportedPipeMeta);else{if(!(exportedModuleMeta=_this.getNgModuleMetadata(exportedType,!1)))throw new Error("Unexpected "+_this._getTypeDescriptor(exportedType)+" '"+stringify(exportedType)+"' exported by the module '"+stringify(moduleType)+"'");exportedModules_1.push(exportedModuleMeta)}});var transitiveModule_1=this._getTransitiveNgModuleMetadata(importedModules_1,exportedModules_1);if(meta.declarations&&flattenArray(meta.declarations).forEach(function(declaredType){if(!isValidType(declaredType))throw new Error("Unexpected value '"+stringify(declaredType)+"' declared by the module '"+stringify(moduleType)+"'");var declaredDirMeta,declaredPipeMeta;if(declaredDirMeta=_this.getDirectiveMetadata(declaredType,!1))_this._addDirectiveToModule(declaredDirMeta,moduleType,transitiveModule_1,declaredDirectives_1,!0);else{if(!(declaredPipeMeta=_this.getPipeMetadata(declaredType,!1)))throw new Error("Unexpected "+_this._getTypeDescriptor(declaredType)+" '"+stringify(declaredType)+"' declared by the module '"+stringify(moduleType)+"'");_this._addPipeToModule(declaredPipeMeta,moduleType,transitiveModule_1,declaredPipes_1,!0)}}),meta.providers&&providers_1.push.apply(providers_1,this.getProvidersMetadata(meta.providers,entryComponents_1,"provider for the NgModule '"+stringify(moduleType)+"'")),meta.entryComponents&&entryComponents_1.push.apply(entryComponents_1,flattenArray(meta.entryComponents).map(function(type){return _this.getTypeMetadata(type,staticTypeModuleUrl(type))})),meta.bootstrap){var typeMetadata=flattenArray(meta.bootstrap).map(function(type){if(!isValidType(type))throw new Error("Unexpected value '"+stringify(type)+"' used in the bootstrap property of module '"+stringify(moduleType)+"'");return _this.getTypeMetadata(type,staticTypeModuleUrl(type))});bootstrapComponents.push.apply(bootstrapComponents,typeMetadata)}entryComponents_1.push.apply(entryComponents_1,bootstrapComponents),meta.schemas&&schemas.push.apply(schemas,flattenArray(meta.schemas)),(_a=transitiveModule_1.entryComponents).push.apply(_a,entryComponents_1),(_b=transitiveModule_1.providers).push.apply(_b,providers_1),compileMeta=new CompileNgModuleMetadata({type:this.getTypeMetadata(moduleType,staticTypeModuleUrl(moduleType)),providers:providers_1,entryComponents:entryComponents_1,bootstrapComponents:bootstrapComponents,schemas:schemas,declaredDirectives:declaredDirectives_1,exportedDirectives:exportedDirectives_1,declaredPipes:declaredPipes_1,exportedPipes:exportedPipes_1,importedModules:importedModules_1,exportedModules:exportedModules_1,transitiveModule:transitiveModule_1,id:meta.id}),transitiveModule_1.modules.push(compileMeta),this._verifyModule(compileMeta),this._ngModuleCache.set(moduleType,compileMeta)}return compileMeta;var _a,_b},CompileMetadataResolver.prototype._verifyModule=function(moduleMeta){moduleMeta.exportedDirectives.forEach(function(dirMeta){if(!moduleMeta.transitiveModule.directivesSet.has(dirMeta.type.reference))throw new Error("Can't export directive "+stringify(dirMeta.type.reference)+" from "+stringify(moduleMeta.type.reference)+" as it was neither declared nor imported!")}),moduleMeta.exportedPipes.forEach(function(pipeMeta){if(!moduleMeta.transitiveModule.pipesSet.has(pipeMeta.type.reference))throw new Error("Can't export pipe "+stringify(pipeMeta.type.reference)+" from "+stringify(moduleMeta.type.reference)+" as it was neither declared nor imported!")})},CompileMetadataResolver.prototype._getTypeDescriptor=function(type){return null!==this._directiveResolver.resolve(type,!1)?"directive":null!==this._pipeResolver.resolve(type,!1)?"pipe":null!==this._ngModuleResolver.resolve(type,!1)?"module":type.provide?"provider":"value"},CompileMetadataResolver.prototype._addTypeToModule=function(type,moduleType){var oldModule=this._ngModuleOfTypes.get(type);if(oldModule&&oldModule!==moduleType)throw new Error("Type "+stringify(type)+" is part of the declarations of 2 modules: "+stringify(oldModule)+" and "+stringify(moduleType)+"! "+("Please consider moving "+stringify(type)+" to a higher module that imports "+stringify(oldModule)+" and "+stringify(moduleType)+". ")+("You can also create a new NgModule that exports and includes "+stringify(type)+" then import that NgModule in "+stringify(oldModule)+" and "+stringify(moduleType)+"."));this._ngModuleOfTypes.set(type,moduleType)},CompileMetadataResolver.prototype._getTransitiveNgModuleMetadata=function(importedModules,exportedModules){var transitiveModules=getTransitiveModules(importedModules.concat(exportedModules),!0),providers=flattenArray(transitiveModules.map(function(ngModule){return ngModule.providers})),entryComponents=flattenArray(transitiveModules.map(function(ngModule){return ngModule.entryComponents})),transitiveExportedModules=getTransitiveModules(importedModules,!1),directives=flattenArray(transitiveExportedModules.map(function(ngModule){return ngModule.exportedDirectives})),pipes=flattenArray(transitiveExportedModules.map(function(ngModule){return ngModule.exportedPipes}));return new TransitiveCompileNgModuleMetadata(transitiveModules,providers,entryComponents,directives,pipes)},CompileMetadataResolver.prototype._addDirectiveToModule=function(dirMeta,moduleType,transitiveModule,declaredDirectives,force){return void 0===force&&(force=!1),!(!force&&transitiveModule.directivesSet.has(dirMeta.type.reference))&&(transitiveModule.directivesSet.add(dirMeta.type.reference),transitiveModule.directives.push(dirMeta),declaredDirectives.push(dirMeta),this._addTypeToModule(dirMeta.type.reference,moduleType),!0)},CompileMetadataResolver.prototype._addPipeToModule=function(pipeMeta,moduleType,transitiveModule,declaredPipes,force){return void 0===force&&(force=!1),!(!force&&transitiveModule.pipesSet.has(pipeMeta.type.reference))&&(transitiveModule.pipesSet.add(pipeMeta.type.reference),transitiveModule.pipes.push(pipeMeta),declaredPipes.push(pipeMeta),this._addTypeToModule(pipeMeta.type.reference,moduleType),!0)},CompileMetadataResolver.prototype.getTypeMetadata=function(type,moduleUrl,dependencies){return void 0===dependencies&&(dependencies=null),type=_angular_core.resolveForwardRef(type),new CompileTypeMetadata({name:this.sanitizeTokenName(type),moduleUrl:moduleUrl,reference:type,diDeps:this.getDependenciesMetadata(type,dependencies),lifecycleHooks:LIFECYCLE_HOOKS_VALUES.filter(function(hook){return hasLifecycleHook(hook,type)})})},CompileMetadataResolver.prototype.getFactoryMetadata=function(factory,moduleUrl,dependencies){return void 0===dependencies&&(dependencies=null),factory=_angular_core.resolveForwardRef(factory),new CompileFactoryMetadata({name:this.sanitizeTokenName(factory),moduleUrl:moduleUrl,reference:factory,diDeps:this.getDependenciesMetadata(factory,dependencies)})},CompileMetadataResolver.prototype.getPipeMetadata=function(pipeType,throwIfNotFound){void 0===throwIfNotFound&&(throwIfNotFound=!0),pipeType=_angular_core.resolveForwardRef(pipeType);var meta=this._pipeCache.get(pipeType);if(isBlank(meta)){var pipeMeta=this._pipeResolver.resolve(pipeType,throwIfNotFound);if(!pipeMeta)return null;meta=new CompilePipeMetadata({type:this.getTypeMetadata(pipeType,staticTypeModuleUrl(pipeType)),name:pipeMeta.name,pure:pipeMeta.pure}),this._pipeCache.set(pipeType,meta)}return meta},CompileMetadataResolver.prototype.getDependenciesMetadata=function(typeOrFunc,dependencies){var _this=this,hasUnknownDeps=!1,params=isPresent(dependencies)?dependencies:this._reflector.parameters(typeOrFunc);isBlank(params)&&(params=[]);var dependenciesMetadata=params.map(function(param){var isAttribute=!1,isHost=!1,isSelf=!1,isSkipSelf=!1,isOptional=!1,query=null,viewQuery=null,token=null;return isArray(param)?param.forEach(function(paramEntry){paramEntry instanceof _angular_core.Host?isHost=!0:paramEntry instanceof _angular_core.Self?isSelf=!0:paramEntry instanceof _angular_core.SkipSelf?isSkipSelf=!0:paramEntry instanceof _angular_core.Optional?isOptional=!0:paramEntry instanceof _angular_core.Attribute?(isAttribute=!0,token=paramEntry.attributeName):paramEntry instanceof _angular_core.Query?paramEntry.isViewQuery?viewQuery=paramEntry:query=paramEntry:paramEntry instanceof _angular_core.Inject?token=paramEntry.token:isValidType(paramEntry)&&isBlank(token)&&(token=paramEntry)}):token=param,isBlank(token)?(hasUnknownDeps=!0,null):new CompileDiDependencyMetadata({isAttribute:isAttribute,isHost:isHost,isSelf:isSelf,isSkipSelf:isSkipSelf,isOptional:isOptional,query:isPresent(query)?_this.getQueryMetadata(query,null,typeOrFunc):null,viewQuery:isPresent(viewQuery)?_this.getQueryMetadata(viewQuery,null,typeOrFunc):null,token:_this.getTokenMetadata(token)})});if(hasUnknownDeps){var depsTokens=dependenciesMetadata.map(function(dep){return dep?stringify(dep.token):"?"}).join(", ");throw new Error("Can't resolve all parameters for "+stringify(typeOrFunc)+": ("+depsTokens+").")}return dependenciesMetadata},CompileMetadataResolver.prototype.getTokenMetadata=function(token){token=_angular_core.resolveForwardRef(token);var compileToken;return compileToken=new CompileTokenMetadata(isString(token)?{value:token}:{identifier:new CompileIdentifierMetadata({reference:token,name:this.sanitizeTokenName(token),moduleUrl:staticTypeModuleUrl(token)})})},CompileMetadataResolver.prototype.getProvidersMetadata=function(providers,targetEntryComponents,debugInfo){var _this=this,compileProviders=[];return providers.forEach(function(provider,providerIdx){provider=_angular_core.resolveForwardRef(provider),provider&&"object"==typeof provider&&provider.hasOwnProperty("provide")&&(provider=new ProviderMeta(provider.provide,provider));var compileProvider;if(isArray(provider))compileProvider=_this.getProvidersMetadata(provider,targetEntryComponents,debugInfo);else if(provider instanceof ProviderMeta){var tokenMeta=_this.getTokenMetadata(provider.token);tokenMeta.reference===resolveIdentifierToken(Identifiers.ANALYZE_FOR_ENTRY_COMPONENTS).reference?targetEntryComponents.push.apply(targetEntryComponents,_this._getEntryComponentsFromProvider(provider)):compileProvider=_this.getProviderMetadata(provider)}else{if(!isValidType(provider)){var providersInfo=providers.reduce(function(soFar,seenProvider,seenProviderIdx){return seenProviderIdx<providerIdx?soFar.push(""+stringify(seenProvider)):seenProviderIdx==providerIdx?soFar.push("?"+stringify(seenProvider)+"?"):seenProviderIdx==providerIdx+1&&soFar.push("..."),soFar},[]).join(", ");throw new Error("Invalid "+(debugInfo?debugInfo:"provider")+" - only instances of Provider and Type are allowed, got: ["+providersInfo+"]")}compileProvider=_this.getTypeMetadata(provider,staticTypeModuleUrl(provider))}compileProvider&&compileProviders.push(compileProvider)}),compileProviders},CompileMetadataResolver.prototype._getEntryComponentsFromProvider=function(provider){var _this=this,components=[],collectedIdentifiers=[];
if(provider.useFactory||provider.useExisting||provider.useClass)throw new Error("The ANALYZE_FOR_ENTRY_COMPONENTS token only supports useValue!");if(!provider.multi)throw new Error("The ANALYZE_FOR_ENTRY_COMPONENTS token only supports 'multi = true'!");return convertToCompileValue(provider.useValue,collectedIdentifiers),collectedIdentifiers.forEach(function(identifier){var dirMeta=_this.getDirectiveMetadata(identifier.reference,!1);dirMeta&&components.push(dirMeta.type)}),components},CompileMetadataResolver.prototype.getProviderMetadata=function(provider){var compileDeps,compileTypeMetadata=null,compileFactoryMetadata=null;return isPresent(provider.useClass)?(compileTypeMetadata=this.getTypeMetadata(provider.useClass,staticTypeModuleUrl(provider.useClass),provider.dependencies),compileDeps=compileTypeMetadata.diDeps):isPresent(provider.useFactory)&&(compileFactoryMetadata=this.getFactoryMetadata(provider.useFactory,staticTypeModuleUrl(provider.useFactory),provider.dependencies),compileDeps=compileFactoryMetadata.diDeps),new CompileProviderMetadata({token:this.getTokenMetadata(provider.token),useClass:compileTypeMetadata,useValue:convertToCompileValue(provider.useValue,[]),useFactory:compileFactoryMetadata,useExisting:isPresent(provider.useExisting)?this.getTokenMetadata(provider.useExisting):null,deps:compileDeps,multi:provider.multi})},CompileMetadataResolver.prototype.getQueriesMetadata=function(queries,isViewQuery,directiveType){var _this=this,res=[];return StringMapWrapper.forEach(queries,function(query,propertyName){query.isViewQuery===isViewQuery&&res.push(_this.getQueryMetadata(query,propertyName,directiveType))}),res},CompileMetadataResolver.prototype._queryVarBindings=function(selector){return StringWrapper.split(selector,/\s*,\s*/g)},CompileMetadataResolver.prototype.getQueryMetadata=function(q,propertyName,typeOrFunc){var selectors,_this=this;if(isString(q.selector))selectors=this._queryVarBindings(q.selector).map(function(varName){return _this.getTokenMetadata(varName)});else{if(!isPresent(q.selector))throw new Error("Can't construct a query for the property \""+propertyName+'" of "'+stringify(typeOrFunc)+"\" since the query selector wasn't defined.");selectors=[this.getTokenMetadata(q.selector)]}return new CompileQueryMetadata({selectors:selectors,first:q.first,descendants:q.descendants,propertyName:propertyName,read:isPresent(q.read)?this.getTokenMetadata(q.read):null})},CompileMetadataResolver.decorators=[{type:_angular_core.Injectable}],CompileMetadataResolver.ctorParameters=[{type:NgModuleResolver},{type:DirectiveResolver},{type:PipeResolver},{type:ElementSchemaRegistry},{type:ReflectorReader}],CompileMetadataResolver}(),_CompileValueConverter=function(_super){function _CompileValueConverter(){_super.apply(this,arguments)}return __extends$14(_CompileValueConverter,_super),_CompileValueConverter.prototype.visitOther=function(value,targetIdentifiers){var identifier;return identifier=new CompileIdentifierMetadata(isStaticSymbol(value)?{name:value.name,moduleUrl:value.filePath,reference:value}:{reference:value}),targetIdentifiers.push(identifier),identifier},_CompileValueConverter}(ValueTransformer),ComponentFactoryDependency$1=function(){function ComponentFactoryDependency(comp,placeholder){this.comp=comp,this.placeholder=placeholder}return ComponentFactoryDependency}(),NgModuleCompileResult=function(){function NgModuleCompileResult(statements,ngModuleFactoryVar,dependencies){this.statements=statements,this.ngModuleFactoryVar=ngModuleFactoryVar,this.dependencies=dependencies}return NgModuleCompileResult}(),NgModuleCompiler=function(){function NgModuleCompiler(){}return NgModuleCompiler.prototype.compile=function(ngModuleMeta,extraProviders){var sourceFileName=isPresent(ngModuleMeta.type.moduleUrl)?"in NgModule "+ngModuleMeta.type.name+" in "+ngModuleMeta.type.moduleUrl:"in NgModule "+ngModuleMeta.type.name,sourceFile=new ParseSourceFile("",sourceFileName),sourceSpan=new ParseSourceSpan(new ParseLocation(sourceFile,null,null,null),new ParseLocation(sourceFile,null,null,null)),deps=[],bootstrapComponentFactories=[],entryComponentFactories=ngModuleMeta.transitiveModule.entryComponents.map(function(entryComponent){var id=new CompileIdentifierMetadata({name:entryComponent.name});return ngModuleMeta.bootstrapComponents.indexOf(entryComponent)>-1&&bootstrapComponentFactories.push(id),deps.push(new ComponentFactoryDependency$1(entryComponent,id)),id}),builder=new _InjectorBuilder(ngModuleMeta,entryComponentFactories,bootstrapComponentFactories,sourceSpan),providerParser=new NgModuleProviderAnalyzer(ngModuleMeta,extraProviders,sourceSpan);providerParser.parse().forEach(function(provider){return builder.addProvider(provider)});var injectorClass=builder.build(),ngModuleFactoryVar=ngModuleMeta.type.name+"NgFactory",ngModuleFactoryStmt=variable(ngModuleFactoryVar).set(importExpr(resolveIdentifier(Identifiers.NgModuleFactory)).instantiate([variable(injectorClass.name),importExpr(ngModuleMeta.type)],importType(resolveIdentifier(Identifiers.NgModuleFactory),[importType(ngModuleMeta.type)],[TypeModifier.Const]))).toDeclStmt(null,[StmtModifier.Final]),stmts=[injectorClass,ngModuleFactoryStmt];if(ngModuleMeta.id){var registerFactoryStmt=importExpr(resolveIdentifier(Identifiers.RegisterModuleFactoryFn)).callFn([literal(ngModuleMeta.id),variable(ngModuleFactoryVar)]).toStmt();stmts.push(registerFactoryStmt)}return new NgModuleCompileResult(stmts,ngModuleFactoryVar,deps)},NgModuleCompiler.decorators=[{type:_angular_core.Injectable}],NgModuleCompiler.ctorParameters=[],NgModuleCompiler}(),_InjectorBuilder=function(){function _InjectorBuilder(_ngModuleMeta,_entryComponentFactories,_bootstrapComponentFactories,_sourceSpan){this._ngModuleMeta=_ngModuleMeta,this._entryComponentFactories=_entryComponentFactories,this._bootstrapComponentFactories=_bootstrapComponentFactories,this._sourceSpan=_sourceSpan,this._tokens=[],this._instances=new Map,this._fields=[],this._createStmts=[],this._destroyStmts=[],this._getters=[]}return _InjectorBuilder.prototype.addProvider=function(resolvedProvider){var _this=this,providerValueExpressions=resolvedProvider.providers.map(function(provider){return _this._getProviderValue(provider)}),propName="_"+resolvedProvider.token.name+"_"+this._instances.size,instance=this._createProviderProperty(propName,resolvedProvider,providerValueExpressions,resolvedProvider.multiProvider,resolvedProvider.eager);resolvedProvider.lifecycleHooks.indexOf(LifecycleHooks.OnDestroy)!==-1&&this._destroyStmts.push(instance.callMethod("ngOnDestroy",[]).toStmt()),this._tokens.push(resolvedProvider.token),this._instances.set(resolvedProvider.token.reference,instance)},_InjectorBuilder.prototype.build=function(){var _this=this,getMethodStmts=this._tokens.map(function(token){var providerExpr=_this._instances.get(token.reference);return new IfStmt(InjectMethodVars$1.token.identical(createDiTokenExpression(token)),[new ReturnStatement(providerExpr)])}),methods=[new ClassMethod("createInternal",[],this._createStmts.concat(new ReturnStatement(this._instances.get(this._ngModuleMeta.type.reference))),importType(this._ngModuleMeta.type)),new ClassMethod("getInternal",[new FnParam(InjectMethodVars$1.token.name,DYNAMIC_TYPE),new FnParam(InjectMethodVars$1.notFoundResult.name,DYNAMIC_TYPE)],getMethodStmts.concat([new ReturnStatement(InjectMethodVars$1.notFoundResult)]),DYNAMIC_TYPE),new ClassMethod("destroyInternal",[],this._destroyStmts)],ctor=new ClassMethod(null,[new FnParam(InjectorProps.parent.name,importType(resolveIdentifier(Identifiers.Injector)))],[SUPER_EXPR.callFn([variable(InjectorProps.parent.name),literalArr(this._entryComponentFactories.map(function(componentFactory){return importExpr(componentFactory)})),literalArr(this._bootstrapComponentFactories.map(function(componentFactory){return importExpr(componentFactory)}))]).toStmt()]),injClassName=this._ngModuleMeta.type.name+"Injector";return new ClassStmt(injClassName,importExpr(resolveIdentifier(Identifiers.NgModuleInjector),[importType(this._ngModuleMeta.type)]),this._fields,this._getters,ctor,methods)},_InjectorBuilder.prototype._getProviderValue=function(provider){var result,_this=this;if(isPresent(provider.useExisting))result=this._getDependency(new CompileDiDependencyMetadata({token:provider.useExisting}));else if(isPresent(provider.useFactory)){var deps=isPresent(provider.deps)?provider.deps:provider.useFactory.diDeps,depsExpr=deps.map(function(dep){return _this._getDependency(dep)});result=importExpr(provider.useFactory).callFn(depsExpr)}else if(isPresent(provider.useClass)){var deps=isPresent(provider.deps)?provider.deps:provider.useClass.diDeps,depsExpr=deps.map(function(dep){return _this._getDependency(dep)});result=importExpr(provider.useClass).instantiate(depsExpr,importType(provider.useClass))}else result=convertValueToOutputAst(provider.useValue);return result},_InjectorBuilder.prototype._createProviderProperty=function(propName,provider,providerValueExpressions,isMulti,isEager){var resolvedProviderValueExpr,type;if(isMulti?(resolvedProviderValueExpr=literalArr(providerValueExpressions),type=new ArrayType(DYNAMIC_TYPE)):(resolvedProviderValueExpr=providerValueExpressions[0],type=providerValueExpressions[0].type),isBlank(type)&&(type=DYNAMIC_TYPE),isEager)this._fields.push(new ClassField(propName,type)),this._createStmts.push(THIS_EXPR.prop(propName).set(resolvedProviderValueExpr).toStmt());else{var internalField="_"+propName;this._fields.push(new ClassField(internalField,type));var getterStmts=[new IfStmt(THIS_EXPR.prop(internalField).isBlank(),[THIS_EXPR.prop(internalField).set(resolvedProviderValueExpr).toStmt()]),new ReturnStatement(THIS_EXPR.prop(internalField))];this._getters.push(new ClassGetter(propName,getterStmts,type))}return THIS_EXPR.prop(propName)},_InjectorBuilder.prototype._getDependency=function(dep){var result=null;if(dep.isValue&&(result=literal(dep.value)),dep.isSkipSelf||(!dep.token||dep.token.reference!==resolveIdentifierToken(Identifiers.Injector).reference&&dep.token.reference!==resolveIdentifierToken(Identifiers.ComponentFactoryResolver).reference||(result=THIS_EXPR),isBlank(result)&&(result=this._instances.get(dep.token.reference))),isBlank(result)){var args=[createDiTokenExpression(dep.token)];dep.isOptional&&args.push(NULL_EXPR),result=InjectorProps.parent.callMethod("get",args)}return result},_InjectorBuilder}(),InjectorProps=function(){function InjectorProps(){}return InjectorProps.parent=THIS_EXPR.prop("parent"),InjectorProps}(),InjectMethodVars$1=function(){function InjectMethodVars(){}return InjectMethodVars.token=variable("token"),InjectMethodVars.notFoundResult=variable("notFoundResult"),InjectMethodVars}(),_SINGLE_QUOTE_ESCAPE_STRING_RE=/'|\\|\n|\r|\$/g,_LEGAL_IDENTIFIER_RE=/^[$A-Z_][0-9A-Z_$]*$/i,CATCH_ERROR_VAR$2=variable("error"),CATCH_STACK_VAR$2=variable("stack"),_EmittedLine=function(){function _EmittedLine(indent){this.indent=indent,this.parts=[]}return _EmittedLine}(),EmitterVisitorContext=function(){function EmitterVisitorContext(_exportedVars,_indent){this._exportedVars=_exportedVars,this._indent=_indent,this._classes=[],this._lines=[new _EmittedLine(_indent)]}return EmitterVisitorContext.createRoot=function(exportedVars){return new EmitterVisitorContext(exportedVars,0)},Object.defineProperty(EmitterVisitorContext.prototype,"_currentLine",{get:function(){return this._lines[this._lines.length-1]},enumerable:!0,configurable:!0}),EmitterVisitorContext.prototype.isExportedVar=function(varName){return this._exportedVars.indexOf(varName)!==-1},EmitterVisitorContext.prototype.println=function(lastPart){void 0===lastPart&&(lastPart=""),this.print(lastPart,!0)},EmitterVisitorContext.prototype.lineIsEmpty=function(){return 0===this._currentLine.parts.length},EmitterVisitorContext.prototype.print=function(part,newLine){void 0===newLine&&(newLine=!1),part.length>0&&this._currentLine.parts.push(part),newLine&&this._lines.push(new _EmittedLine(this._indent))},EmitterVisitorContext.prototype.removeEmptyLastLine=function(){this.lineIsEmpty()&&this._lines.pop()},EmitterVisitorContext.prototype.incIndent=function(){this._indent++,this._currentLine.indent=this._indent},EmitterVisitorContext.prototype.decIndent=function(){this._indent--,this._currentLine.indent=this._indent},EmitterVisitorContext.prototype.pushClass=function(clazz){this._classes.push(clazz)},EmitterVisitorContext.prototype.popClass=function(){return this._classes.pop()},Object.defineProperty(EmitterVisitorContext.prototype,"currentClass",{get:function(){return this._classes.length>0?this._classes[this._classes.length-1]:null},enumerable:!0,configurable:!0}),EmitterVisitorContext.prototype.toSource=function(){var lines=this._lines;return 0===lines[lines.length-1].parts.length&&(lines=lines.slice(0,lines.length-1)),lines.map(function(line){return line.parts.length>0?_createIndent(line.indent)+line.parts.join(""):""}).join("\n")},EmitterVisitorContext}(),AbstractEmitterVisitor=function(){function AbstractEmitterVisitor(_escapeDollarInStrings){this._escapeDollarInStrings=_escapeDollarInStrings}return AbstractEmitterVisitor.prototype.visitExpressionStmt=function(stmt,ctx){return stmt.expr.visitExpression(this,ctx),ctx.println(";"),null},AbstractEmitterVisitor.prototype.visitReturnStmt=function(stmt,ctx){return ctx.print("return "),stmt.value.visitExpression(this,ctx),ctx.println(";"),null},AbstractEmitterVisitor.prototype.visitIfStmt=function(stmt,ctx){ctx.print("if ("),stmt.condition.visitExpression(this,ctx),ctx.print(") {");var hasElseCase=isPresent(stmt.falseCase)&&stmt.falseCase.length>0;return stmt.trueCase.length<=1&&!hasElseCase?(ctx.print(" "),this.visitAllStatements(stmt.trueCase,ctx),ctx.removeEmptyLastLine(),ctx.print(" ")):(ctx.println(),ctx.incIndent(),this.visitAllStatements(stmt.trueCase,ctx),ctx.decIndent(),hasElseCase&&(ctx.println("} else {"),ctx.incIndent(),this.visitAllStatements(stmt.falseCase,ctx),ctx.decIndent())),ctx.println("}"),null},AbstractEmitterVisitor.prototype.visitThrowStmt=function(stmt,ctx){return ctx.print("throw "),stmt.error.visitExpression(this,ctx),ctx.println(";"),null},AbstractEmitterVisitor.prototype.visitCommentStmt=function(stmt,ctx){var lines=stmt.comment.split("\n");return lines.forEach(function(line){ctx.println("// "+line)}),null},AbstractEmitterVisitor.prototype.visitWriteVarExpr=function(expr,ctx){var lineWasEmpty=ctx.lineIsEmpty();return lineWasEmpty||ctx.print("("),ctx.print(expr.name+" = "),expr.value.visitExpression(this,ctx),lineWasEmpty||ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitWriteKeyExpr=function(expr,ctx){var lineWasEmpty=ctx.lineIsEmpty();return lineWasEmpty||ctx.print("("),expr.receiver.visitExpression(this,ctx),ctx.print("["),expr.index.visitExpression(this,ctx),ctx.print("] = "),expr.value.visitExpression(this,ctx),lineWasEmpty||ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitWritePropExpr=function(expr,ctx){var lineWasEmpty=ctx.lineIsEmpty();return lineWasEmpty||ctx.print("("),expr.receiver.visitExpression(this,ctx),ctx.print("."+expr.name+" = "),expr.value.visitExpression(this,ctx),lineWasEmpty||ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitInvokeMethodExpr=function(expr,ctx){expr.receiver.visitExpression(this,ctx);var name=expr.name;return isPresent(expr.builtin)&&(name=this.getBuiltinMethodName(expr.builtin),isBlank(name))?null:(ctx.print("."+name+"("),this.visitAllExpressions(expr.args,ctx,","),ctx.print(")"),null)},AbstractEmitterVisitor.prototype.visitInvokeFunctionExpr=function(expr,ctx){return expr.fn.visitExpression(this,ctx),ctx.print("("),this.visitAllExpressions(expr.args,ctx,","),ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitReadVarExpr=function(ast,ctx){var varName=ast.name;if(isPresent(ast.builtin))switch(ast.builtin){case BuiltinVar.Super:varName="super";break;case BuiltinVar.This:varName="this";break;case BuiltinVar.CatchError:varName=CATCH_ERROR_VAR$2.name;break;case BuiltinVar.CatchStack:varName=CATCH_STACK_VAR$2.name;break;default:throw new Error("Unknown builtin variable "+ast.builtin)}return ctx.print(varName),null},AbstractEmitterVisitor.prototype.visitInstantiateExpr=function(ast,ctx){return ctx.print("new "),ast.classExpr.visitExpression(this,ctx),ctx.print("("),this.visitAllExpressions(ast.args,ctx,","),ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitLiteralExpr=function(ast,ctx,absentValue){void 0===absentValue&&(absentValue="null");var value=ast.value;return isString(value)?ctx.print(escapeIdentifier(value,this._escapeDollarInStrings)):isBlank(value)?ctx.print(absentValue):ctx.print(""+value),null},AbstractEmitterVisitor.prototype.visitConditionalExpr=function(ast,ctx){return ctx.print("("),ast.condition.visitExpression(this,ctx),ctx.print("? "),ast.trueCase.visitExpression(this,ctx),ctx.print(": "),ast.falseCase.visitExpression(this,ctx),ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitNotExpr=function(ast,ctx){return ctx.print("!"),ast.condition.visitExpression(this,ctx),null},AbstractEmitterVisitor.prototype.visitBinaryOperatorExpr=function(ast,ctx){var opStr;switch(ast.operator){case BinaryOperator.Equals:opStr="==";break;case BinaryOperator.Identical:opStr="===";break;case BinaryOperator.NotEquals:opStr="!=";break;case BinaryOperator.NotIdentical:opStr="!==";break;case BinaryOperator.And:opStr="&&";break;case BinaryOperator.Or:opStr="||";break;case BinaryOperator.Plus:opStr="+";break;case BinaryOperator.Minus:opStr="-";break;case BinaryOperator.Divide:opStr="/";break;case BinaryOperator.Multiply:opStr="*";break;case BinaryOperator.Modulo:opStr="%";break;case BinaryOperator.Lower:opStr="<";break;case BinaryOperator.LowerEquals:opStr="<=";break;case BinaryOperator.Bigger:opStr=">";break;case BinaryOperator.BiggerEquals:opStr=">=";break;default:throw new Error("Unknown operator "+ast.operator)}return ctx.print("("),ast.lhs.visitExpression(this,ctx),ctx.print(" "+opStr+" "),ast.rhs.visitExpression(this,ctx),ctx.print(")"),null},AbstractEmitterVisitor.prototype.visitReadPropExpr=function(ast,ctx){return ast.receiver.visitExpression(this,ctx),ctx.print("."),ctx.print(ast.name),null},AbstractEmitterVisitor.prototype.visitReadKeyExpr=function(ast,ctx){return ast.receiver.visitExpression(this,ctx),ctx.print("["),ast.index.visitExpression(this,ctx),ctx.print("]"),null},AbstractEmitterVisitor.prototype.visitLiteralArrayExpr=function(ast,ctx){var useNewLine=ast.entries.length>1;return ctx.print("[",useNewLine),ctx.incIndent(),this.visitAllExpressions(ast.entries,ctx,",",useNewLine),ctx.decIndent(),ctx.print("]",useNewLine),null},AbstractEmitterVisitor.prototype.visitLiteralMapExpr=function(ast,ctx){var _this=this,useNewLine=ast.entries.length>1;return ctx.print("{",useNewLine),ctx.incIndent(),this.visitAllObjects(function(entry){ctx.print(escapeIdentifier(entry[0],_this._escapeDollarInStrings,!1)+": "),entry[1].visitExpression(_this,ctx)},ast.entries,ctx,",",useNewLine),ctx.decIndent(),ctx.print("}",useNewLine),null},AbstractEmitterVisitor.prototype.visitAllExpressions=function(expressions,ctx,separator,newLine){var _this=this;void 0===newLine&&(newLine=!1),this.visitAllObjects(function(expr){return expr.visitExpression(_this,ctx)},expressions,ctx,separator,newLine)},AbstractEmitterVisitor.prototype.visitAllObjects=function(handler,expressions,ctx,separator,newLine){void 0===newLine&&(newLine=!1);for(var i=0;i<expressions.length;i++)i>0&&ctx.print(separator,newLine),handler(expressions[i]);newLine&&ctx.println()},AbstractEmitterVisitor.prototype.visitAllStatements=function(statements,ctx){var _this=this;statements.forEach(function(stmt){return stmt.visitStatement(_this,ctx)})},AbstractEmitterVisitor}(),__extends$15=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},_debugModuleUrl="asset://debug/lib",TypeScriptEmitter=function(){function TypeScriptEmitter(_importGenerator){this._importGenerator=_importGenerator}return TypeScriptEmitter.prototype.emitStatements=function(moduleUrl,stmts,exportedVars){var _this=this,converter=new _TsEmitterVisitor(moduleUrl),ctx=EmitterVisitorContext.createRoot(exportedVars);converter.visitAllStatements(stmts,ctx);var srcParts=[];return converter.importsWithPrefixes.forEach(function(prefix,importedModuleUrl){srcParts.push("imp"+("ort * as "+prefix+" from '"+_this._importGenerator.getImportPath(moduleUrl,importedModuleUrl)+"';"))}),srcParts.push(ctx.toSource()),srcParts.join("\n")},TypeScriptEmitter}(),_TsEmitterVisitor=function(_super){function _TsEmitterVisitor(_moduleUrl){_super.call(this,!1),this._moduleUrl=_moduleUrl,this.importsWithPrefixes=new Map}return __extends$15(_TsEmitterVisitor,_super),_TsEmitterVisitor.prototype.visitType=function(t,ctx,defaultType){void 0===defaultType&&(defaultType="any"),isPresent(t)?t.visitType(this,ctx):ctx.print(defaultType)},_TsEmitterVisitor.prototype.visitLiteralExpr=function(ast,ctx){_super.prototype.visitLiteralExpr.call(this,ast,ctx,"(null as any)")},_TsEmitterVisitor.prototype.visitExternalExpr=function(ast,ctx){return this._visitIdentifier(ast.value,ast.typeParams,ctx),null},_TsEmitterVisitor.prototype.visitDeclareVarStmt=function(stmt,ctx){return ctx.isExportedVar(stmt.name)&&ctx.print("export "),stmt.hasModifier(StmtModifier.Final)?ctx.print("const"):ctx.print("var"),ctx.print(" "+stmt.name+":"),this.visitType(stmt.type,ctx),ctx.print(" = "),stmt.value.visitExpression(this,ctx),ctx.println(";"),null},_TsEmitterVisitor.prototype.visitCastExpr=function(ast,ctx){return ctx.print("(<"),ast.type.visitType(this,ctx),ctx.print(">"),ast.value.visitExpression(this,ctx),ctx.print(")"),null},_TsEmitterVisitor.prototype.visitDeclareClassStmt=function(stmt,ctx){var _this=this;return ctx.pushClass(stmt),ctx.isExportedVar(stmt.name)&&ctx.print("export "),ctx.print("class "+stmt.name),isPresent(stmt.parent)&&(ctx.print(" extends "),stmt.parent.visitExpression(this,ctx)),ctx.println(" {"),ctx.incIndent(),stmt.fields.forEach(function(field){return _this._visitClassField(field,ctx)}),isPresent(stmt.constructorMethod)&&this._visitClassConstructor(stmt,ctx),stmt.getters.forEach(function(getter){return _this._visitClassGetter(getter,ctx)}),stmt.methods.forEach(function(method){return _this._visitClassMethod(method,ctx)}),ctx.decIndent(),ctx.println("}"),ctx.popClass(),null},_TsEmitterVisitor.prototype._visitClassField=function(field,ctx){field.hasModifier(StmtModifier.Private)&&ctx.print("/*private*/ "),ctx.print(field.name),ctx.print(":"),this.visitType(field.type,ctx),ctx.println(";")},_TsEmitterVisitor.prototype._visitClassGetter=function(getter,ctx){getter.hasModifier(StmtModifier.Private)&&ctx.print("private "),ctx.print("get "+getter.name+"()"),ctx.print(":"),this.visitType(getter.type,ctx),ctx.println(" {"),ctx.incIndent(),this.visitAllStatements(getter.body,ctx),ctx.decIndent(),ctx.println("}")},_TsEmitterVisitor.prototype._visitClassConstructor=function(stmt,ctx){ctx.print("constructor("),this._visitParams(stmt.constructorMethod.params,ctx),ctx.println(") {"),ctx.incIndent(),this.visitAllStatements(stmt.constructorMethod.body,ctx),ctx.decIndent(),ctx.println("}")},_TsEmitterVisitor.prototype._visitClassMethod=function(method,ctx){method.hasModifier(StmtModifier.Private)&&ctx.print("private "),ctx.print(method.name+"("),this._visitParams(method.params,ctx),ctx.print("):"),this.visitType(method.type,ctx,"void"),ctx.println(" {"),ctx.incIndent(),this.visitAllStatements(method.body,ctx),ctx.decIndent(),ctx.println("}")},_TsEmitterVisitor.prototype.visitFunctionExpr=function(ast,ctx){return ctx.print("("),this._visitParams(ast.params,ctx),ctx.print("):"),this.visitType(ast.type,ctx,"void"),ctx.println(" => {"),ctx.incIndent(),this.visitAllStatements(ast.statements,ctx),ctx.decIndent(),ctx.print("}"),null},_TsEmitterVisitor.prototype.visitDeclareFunctionStmt=function(stmt,ctx){return ctx.isExportedVar(stmt.name)&&ctx.print("export "),ctx.print("function "+stmt.name+"("),this._visitParams(stmt.params,ctx),ctx.print("):"),this.visitType(stmt.type,ctx,"void"),ctx.println(" {"),ctx.incIndent(),this.visitAllStatements(stmt.statements,ctx),ctx.decIndent(),ctx.println("}"),null},_TsEmitterVisitor.prototype.visitTryCatchStmt=function(stmt,ctx){ctx.println("try {"),ctx.incIndent(),this.visitAllStatements(stmt.bodyStmts,ctx),ctx.decIndent(),ctx.println("} catch ("+CATCH_ERROR_VAR$2.name+") {"),ctx.incIndent();var catchStmts=[CATCH_STACK_VAR$2.set(CATCH_ERROR_VAR$2.prop("stack")).toDeclStmt(null,[StmtModifier.Final])].concat(stmt.catchStmts);return this.visitAllStatements(catchStmts,ctx),ctx.decIndent(),ctx.println("}"),null},_TsEmitterVisitor.prototype.visitBuiltintType=function(type,ctx){var typeStr;switch(type.name){case BuiltinTypeName.Bool:typeStr="boolean";break;case BuiltinTypeName.Dynamic:typeStr="any";break;case BuiltinTypeName.Function:typeStr="Function";break;case BuiltinTypeName.Number:typeStr="number";break;case BuiltinTypeName.Int:typeStr="number";break;case BuiltinTypeName.String:typeStr="string";break;default:throw new Error("Unsupported builtin type "+type.name)}return ctx.print(typeStr),null},_TsEmitterVisitor.prototype.visitExternalType=function(ast,ctx){return this._visitIdentifier(ast.value,ast.typeParams,ctx),null},_TsEmitterVisitor.prototype.visitArrayType=function(type,ctx){return this.visitType(type.of,ctx),ctx.print("[]"),null},_TsEmitterVisitor.prototype.visitMapType=function(type,ctx){return ctx.print("{[key: string]:"),this.visitType(type.valueType,ctx),ctx.print("}"),null},_TsEmitterVisitor.prototype.getBuiltinMethodName=function(method){var name;switch(method){case BuiltinMethod.ConcatArray:name="concat";break;case BuiltinMethod.SubscribeObservable:name="subscribe";break;case BuiltinMethod.Bind:name="bind";break;default:throw new Error("Unknown builtin method: "+method)}return name},_TsEmitterVisitor.prototype._visitParams=function(params,ctx){var _this=this;this.visitAllObjects(function(param){ctx.print(param.name),ctx.print(":"),_this.visitType(param.type,ctx)},params,ctx,",")},_TsEmitterVisitor.prototype._visitIdentifier=function(value,typeParams,ctx){var _this=this;if(isBlank(value.name))throw new Error("Internal error: unknown identifier "+value);if(isPresent(value.moduleUrl)&&value.moduleUrl!=this._moduleUrl){var prefix=this.importsWithPrefixes.get(value.moduleUrl);isBlank(prefix)&&(prefix="import"+this.importsWithPrefixes.size,this.importsWithPrefixes.set(value.moduleUrl,prefix)),ctx.print(prefix+".")}value.reference&&value.reference.members?(ctx.print(value.reference.name),ctx.print("."),ctx.print(value.reference.members.join("."))):ctx.print(value.name),isPresent(typeParams)&&typeParams.length>0&&(ctx.print("<"),this.visitAllObjects(function(type){return type.visitType(_this,ctx)},typeParams,ctx,","),ctx.print(">"))},_TsEmitterVisitor}(AbstractEmitterVisitor),_ExecutionContext=function(){function _ExecutionContext(parent,instance,className,vars){this.parent=parent,this.instance=instance,this.className=className,this.vars=vars}return _ExecutionContext.prototype.createChildWihtLocalVars=function(){return new _ExecutionContext(this,this.instance,this.className,new Map)},_ExecutionContext}(),ReturnValue=function(){function ReturnValue(value){this.value=value}return ReturnValue}(),StatementInterpreter=function(){function StatementInterpreter(){}return StatementInterpreter.prototype.debugAst=function(ast){return debugOutputAstAsTypeScript(ast)},StatementInterpreter.prototype.visitDeclareVarStmt=function(stmt,ctx){return ctx.vars.set(stmt.name,stmt.value.visitExpression(this,ctx)),null},StatementInterpreter.prototype.visitWriteVarExpr=function(expr,ctx){for(var value=expr.value.visitExpression(this,ctx),currCtx=ctx;null!=currCtx;){if(currCtx.vars.has(expr.name))return currCtx.vars.set(expr.name,value),value;currCtx=currCtx.parent}throw new Error("Not declared variable "+expr.name)},StatementInterpreter.prototype.visitReadVarExpr=function(ast,ctx){var varName=ast.name;if(isPresent(ast.builtin))switch(ast.builtin){case BuiltinVar.Super:return ctx.instance.__proto__;case BuiltinVar.This:return ctx.instance;case BuiltinVar.CatchError:varName=CATCH_ERROR_VAR$1;break;case BuiltinVar.CatchStack:varName=CATCH_STACK_VAR$1;break;default:throw new Error("Unknown builtin variable "+ast.builtin)}for(var currCtx=ctx;null!=currCtx;){if(currCtx.vars.has(varName))return currCtx.vars.get(varName);currCtx=currCtx.parent}throw new Error("Not declared variable "+varName)},StatementInterpreter.prototype.visitWriteKeyExpr=function(expr,ctx){var receiver=expr.receiver.visitExpression(this,ctx),index=expr.index.visitExpression(this,ctx),value=expr.value.visitExpression(this,ctx);return receiver[index]=value,value},StatementInterpreter.prototype.visitWritePropExpr=function(expr,ctx){var receiver=expr.receiver.visitExpression(this,ctx),value=expr.value.visitExpression(this,ctx);return receiver[expr.name]=value,value},StatementInterpreter.prototype.visitInvokeMethodExpr=function(expr,ctx){var result,receiver=expr.receiver.visitExpression(this,ctx),args=this.visitAllExpressions(expr.args,ctx);if(isPresent(expr.builtin))switch(expr.builtin){case BuiltinMethod.ConcatArray:result=ListWrapper.concat(receiver,args[0]);break;case BuiltinMethod.SubscribeObservable:result=receiver.subscribe({next:args[0]});break;case BuiltinMethod.Bind:result=receiver.bind(args[0]);break;default:throw new Error("Unknown builtin method "+expr.builtin)}else result=receiver[expr.name].apply(receiver,args);return result},StatementInterpreter.prototype.visitInvokeFunctionExpr=function(stmt,ctx){var args=this.visitAllExpressions(stmt.args,ctx),fnExpr=stmt.fn;if(fnExpr instanceof ReadVarExpr&&fnExpr.builtin===BuiltinVar.Super)return ctx.instance.constructor.prototype.constructor.apply(ctx.instance,args),null;var fn=stmt.fn.visitExpression(this,ctx);return fn.apply(null,args)},StatementInterpreter.prototype.visitReturnStmt=function(stmt,ctx){return new ReturnValue(stmt.value.visitExpression(this,ctx))},StatementInterpreter.prototype.visitDeclareClassStmt=function(stmt,ctx){var clazz=createDynamicClass(stmt,ctx,this);return ctx.vars.set(stmt.name,clazz),null},StatementInterpreter.prototype.visitExpressionStmt=function(stmt,ctx){return stmt.expr.visitExpression(this,ctx)},StatementInterpreter.prototype.visitIfStmt=function(stmt,ctx){var condition=stmt.condition.visitExpression(this,ctx);return condition?this.visitAllStatements(stmt.trueCase,ctx):isPresent(stmt.falseCase)?this.visitAllStatements(stmt.falseCase,ctx):null},StatementInterpreter.prototype.visitTryCatchStmt=function(stmt,ctx){try{return this.visitAllStatements(stmt.bodyStmts,ctx)}catch(e){var childCtx=ctx.createChildWihtLocalVars();return childCtx.vars.set(CATCH_ERROR_VAR$1,e),childCtx.vars.set(CATCH_STACK_VAR$1,e.stack),this.visitAllStatements(stmt.catchStmts,childCtx)}},StatementInterpreter.prototype.visitThrowStmt=function(stmt,ctx){throw stmt.error.visitExpression(this,ctx)},StatementInterpreter.prototype.visitCommentStmt=function(stmt,context){return null},StatementInterpreter.prototype.visitInstantiateExpr=function(ast,ctx){var args=this.visitAllExpressions(ast.args,ctx),clazz=ast.classExpr.visitExpression(this,ctx);return new(clazz.bind.apply(clazz,[void 0].concat(args)))},StatementInterpreter.prototype.visitLiteralExpr=function(ast,ctx){return ast.value},StatementInterpreter.prototype.visitExternalExpr=function(ast,ctx){return ast.value.reference},StatementInterpreter.prototype.visitConditionalExpr=function(ast,ctx){return ast.condition.visitExpression(this,ctx)?ast.trueCase.visitExpression(this,ctx):isPresent(ast.falseCase)?ast.falseCase.visitExpression(this,ctx):null},StatementInterpreter.prototype.visitNotExpr=function(ast,ctx){return!ast.condition.visitExpression(this,ctx)},StatementInterpreter.prototype.visitCastExpr=function(ast,ctx){return ast.value.visitExpression(this,ctx)},StatementInterpreter.prototype.visitFunctionExpr=function(ast,ctx){var paramNames=ast.params.map(function(param){
return param.name});return _declareFn(paramNames,ast.statements,ctx,this)},StatementInterpreter.prototype.visitDeclareFunctionStmt=function(stmt,ctx){var paramNames=stmt.params.map(function(param){return param.name});return ctx.vars.set(stmt.name,_declareFn(paramNames,stmt.statements,ctx,this)),null},StatementInterpreter.prototype.visitBinaryOperatorExpr=function(ast,ctx){var _this=this,lhs=function(){return ast.lhs.visitExpression(_this,ctx)},rhs=function(){return ast.rhs.visitExpression(_this,ctx)};switch(ast.operator){case BinaryOperator.Equals:return lhs()==rhs();case BinaryOperator.Identical:return lhs()===rhs();case BinaryOperator.NotEquals:return lhs()!=rhs();case BinaryOperator.NotIdentical:return lhs()!==rhs();case BinaryOperator.And:return lhs()&&rhs();case BinaryOperator.Or:return lhs()||rhs();case BinaryOperator.Plus:return lhs()+rhs();case BinaryOperator.Minus:return lhs()-rhs();case BinaryOperator.Divide:return lhs()/rhs();case BinaryOperator.Multiply:return lhs()*rhs();case BinaryOperator.Modulo:return lhs()%rhs();case BinaryOperator.Lower:return lhs()<rhs();case BinaryOperator.LowerEquals:return lhs()<=rhs();case BinaryOperator.Bigger:return lhs()>rhs();case BinaryOperator.BiggerEquals:return lhs()>=rhs();default:throw new Error("Unknown operator "+ast.operator)}},StatementInterpreter.prototype.visitReadPropExpr=function(ast,ctx){var result,receiver=ast.receiver.visitExpression(this,ctx);return result=receiver[ast.name]},StatementInterpreter.prototype.visitReadKeyExpr=function(ast,ctx){var receiver=ast.receiver.visitExpression(this,ctx),prop=ast.index.visitExpression(this,ctx);return receiver[prop]},StatementInterpreter.prototype.visitLiteralArrayExpr=function(ast,ctx){return this.visitAllExpressions(ast.entries,ctx)},StatementInterpreter.prototype.visitLiteralMapExpr=function(ast,ctx){var _this=this,result={};return ast.entries.forEach(function(entry){return result[entry[0]]=entry[1].visitExpression(_this,ctx)}),result},StatementInterpreter.prototype.visitAllExpressions=function(expressions,ctx){var _this=this;return expressions.map(function(expr){return expr.visitExpression(_this,ctx)})},StatementInterpreter.prototype.visitAllStatements=function(statements,ctx){for(var i=0;i<statements.length;i++){var stmt=statements[i],val=stmt.visitStatement(this,ctx);if(val instanceof ReturnValue)return val}return null},StatementInterpreter}(),CATCH_ERROR_VAR$1="error",CATCH_STACK_VAR$1="stack",__extends$17=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},AbstractJsEmitterVisitor=function(_super){function AbstractJsEmitterVisitor(){_super.call(this,!1)}return __extends$17(AbstractJsEmitterVisitor,_super),AbstractJsEmitterVisitor.prototype.visitDeclareClassStmt=function(stmt,ctx){var _this=this;return ctx.pushClass(stmt),this._visitClassConstructor(stmt,ctx),isPresent(stmt.parent)&&(ctx.print(stmt.name+".prototype = Object.create("),stmt.parent.visitExpression(this,ctx),ctx.println(".prototype);")),stmt.getters.forEach(function(getter){return _this._visitClassGetter(stmt,getter,ctx)}),stmt.methods.forEach(function(method){return _this._visitClassMethod(stmt,method,ctx)}),ctx.popClass(),null},AbstractJsEmitterVisitor.prototype._visitClassConstructor=function(stmt,ctx){ctx.print("function "+stmt.name+"("),isPresent(stmt.constructorMethod)&&this._visitParams(stmt.constructorMethod.params,ctx),ctx.println(") {"),ctx.incIndent(),isPresent(stmt.constructorMethod)&&stmt.constructorMethod.body.length>0&&(ctx.println("var self = this;"),this.visitAllStatements(stmt.constructorMethod.body,ctx)),ctx.decIndent(),ctx.println("}")},AbstractJsEmitterVisitor.prototype._visitClassGetter=function(stmt,getter,ctx){ctx.println("Object.defineProperty("+stmt.name+".prototype, '"+getter.name+"', { get: function() {"),ctx.incIndent(),getter.body.length>0&&(ctx.println("var self = this;"),this.visitAllStatements(getter.body,ctx)),ctx.decIndent(),ctx.println("}});")},AbstractJsEmitterVisitor.prototype._visitClassMethod=function(stmt,method,ctx){ctx.print(stmt.name+".prototype."+method.name+" = function("),this._visitParams(method.params,ctx),ctx.println(") {"),ctx.incIndent(),method.body.length>0&&(ctx.println("var self = this;"),this.visitAllStatements(method.body,ctx)),ctx.decIndent(),ctx.println("};")},AbstractJsEmitterVisitor.prototype.visitReadVarExpr=function(ast,ctx){if(ast.builtin===BuiltinVar.This)ctx.print("self");else{if(ast.builtin===BuiltinVar.Super)throw new Error("'super' needs to be handled at a parent ast node, not at the variable level!");_super.prototype.visitReadVarExpr.call(this,ast,ctx)}return null},AbstractJsEmitterVisitor.prototype.visitDeclareVarStmt=function(stmt,ctx){return ctx.print("var "+stmt.name+" = "),stmt.value.visitExpression(this,ctx),ctx.println(";"),null},AbstractJsEmitterVisitor.prototype.visitCastExpr=function(ast,ctx){return ast.value.visitExpression(this,ctx),null},AbstractJsEmitterVisitor.prototype.visitInvokeFunctionExpr=function(expr,ctx){var fnExpr=expr.fn;return fnExpr instanceof ReadVarExpr&&fnExpr.builtin===BuiltinVar.Super?(ctx.currentClass.parent.visitExpression(this,ctx),ctx.print(".call(this"),expr.args.length>0&&(ctx.print(", "),this.visitAllExpressions(expr.args,ctx,",")),ctx.print(")")):_super.prototype.visitInvokeFunctionExpr.call(this,expr,ctx),null},AbstractJsEmitterVisitor.prototype.visitFunctionExpr=function(ast,ctx){return ctx.print("function("),this._visitParams(ast.params,ctx),ctx.println(") {"),ctx.incIndent(),this.visitAllStatements(ast.statements,ctx),ctx.decIndent(),ctx.print("}"),null},AbstractJsEmitterVisitor.prototype.visitDeclareFunctionStmt=function(stmt,ctx){return ctx.print("function "+stmt.name+"("),this._visitParams(stmt.params,ctx),ctx.println(") {"),ctx.incIndent(),this.visitAllStatements(stmt.statements,ctx),ctx.decIndent(),ctx.println("}"),null},AbstractJsEmitterVisitor.prototype.visitTryCatchStmt=function(stmt,ctx){ctx.println("try {"),ctx.incIndent(),this.visitAllStatements(stmt.bodyStmts,ctx),ctx.decIndent(),ctx.println("} catch ("+CATCH_ERROR_VAR$2.name+") {"),ctx.incIndent();var catchStmts=[CATCH_STACK_VAR$2.set(CATCH_ERROR_VAR$2.prop("stack")).toDeclStmt(null,[StmtModifier.Final])].concat(stmt.catchStmts);return this.visitAllStatements(catchStmts,ctx),ctx.decIndent(),ctx.println("}"),null},AbstractJsEmitterVisitor.prototype._visitParams=function(params,ctx){this.visitAllObjects(function(param){return ctx.print(param.name)},params,ctx,",")},AbstractJsEmitterVisitor.prototype.getBuiltinMethodName=function(method){var name;switch(method){case BuiltinMethod.ConcatArray:name="concat";break;case BuiltinMethod.SubscribeObservable:name="subscribe";break;case BuiltinMethod.Bind:name="bind";break;default:throw new Error("Unknown builtin method: "+method)}return name},AbstractJsEmitterVisitor}(AbstractEmitterVisitor),__extends$16=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},JitEmitterVisitor=function(_super){function JitEmitterVisitor(){_super.apply(this,arguments),this._evalArgNames=[],this._evalArgValues=[]}return __extends$16(JitEmitterVisitor,_super),JitEmitterVisitor.prototype.getArgs=function(){for(var result={},i=0;i<this._evalArgNames.length;i++)result[this._evalArgNames[i]]=this._evalArgValues[i];return result},JitEmitterVisitor.prototype.visitExternalExpr=function(ast,ctx){var value=ast.value.reference,id=this._evalArgValues.indexOf(value);if(id===-1){id=this._evalArgValues.length,this._evalArgValues.push(value);var name=isPresent(ast.value.name)?sanitizeIdentifier(ast.value.name):"val";this._evalArgNames.push(sanitizeIdentifier("jit_"+name+id))}return ctx.print(this._evalArgNames[id]),null},JitEmitterVisitor}(AbstractJsEmitterVisitor),ShadowCss=function(){function ShadowCss(){this.strictStyling=!0}return ShadowCss.prototype.shimCssText=function(cssText,selector,hostSelector){void 0===hostSelector&&(hostSelector="");var sourceMappingUrl=extractSourceMappingUrl(cssText);return cssText=stripComments(cssText),cssText=this._insertDirectives(cssText),this._scopeCssText(cssText,selector,hostSelector)+sourceMappingUrl},ShadowCss.prototype._insertDirectives=function(cssText){return cssText=this._insertPolyfillDirectivesInCssText(cssText),this._insertPolyfillRulesInCssText(cssText)},ShadowCss.prototype._insertPolyfillDirectivesInCssText=function(cssText){return StringWrapper.replaceAllMapped(cssText,_cssContentNextSelectorRe,function(m){return m[1]+"{"})},ShadowCss.prototype._insertPolyfillRulesInCssText=function(cssText){return StringWrapper.replaceAllMapped(cssText,_cssContentRuleRe,function(m){var rule=m[0];return rule=StringWrapper.replace(rule,m[1],""),rule=StringWrapper.replace(rule,m[2],""),m[3]+rule})},ShadowCss.prototype._scopeCssText=function(cssText,scopeSelector,hostSelector){var unscoped=this._extractUnscopedRulesFromCssText(cssText);return cssText=this._insertPolyfillHostInCssText(cssText),cssText=this._convertColonHost(cssText),cssText=this._convertColonHostContext(cssText),cssText=this._convertShadowDOMSelectors(cssText),isPresent(scopeSelector)&&(cssText=this._scopeSelectors(cssText,scopeSelector,hostSelector)),cssText=cssText+"\n"+unscoped,cssText.trim()},ShadowCss.prototype._extractUnscopedRulesFromCssText=function(cssText){var m,r="";for(_cssContentUnscopedRuleRe.lastIndex=0;null!==(m=_cssContentUnscopedRuleRe.exec(cssText));){var rule=m[0];rule=StringWrapper.replace(rule,m[2],""),rule=StringWrapper.replace(rule,m[1],m[3]),r+=rule+"\n\n"}return r},ShadowCss.prototype._convertColonHost=function(cssText){return this._convertColonRule(cssText,_cssColonHostRe,this._colonHostPartReplacer)},ShadowCss.prototype._convertColonHostContext=function(cssText){return this._convertColonRule(cssText,_cssColonHostContextRe,this._colonHostContextPartReplacer)},ShadowCss.prototype._convertColonRule=function(cssText,regExp,partReplacer){return StringWrapper.replaceAllMapped(cssText,regExp,function(m){if(isPresent(m[2])){for(var parts=m[2].split(","),r=[],i=0;i<parts.length;i++){var p=parts[i];if(isBlank(p))break;p=p.trim(),r.push(partReplacer(_polyfillHostNoCombinator,p,m[3]))}return r.join(",")}return _polyfillHostNoCombinator+m[3]})},ShadowCss.prototype._colonHostContextPartReplacer=function(host,part,suffix){return StringWrapper.contains(part,_polyfillHost)?this._colonHostPartReplacer(host,part,suffix):host+part+suffix+", "+part+" "+host+suffix},ShadowCss.prototype._colonHostPartReplacer=function(host,part,suffix){return host+StringWrapper.replace(part,_polyfillHost,"")+suffix},ShadowCss.prototype._convertShadowDOMSelectors=function(cssText){return _shadowDOMSelectorsRe.reduce(function(result,pattern){return StringWrapper.replaceAll(result,pattern," ")},cssText)},ShadowCss.prototype._scopeSelectors=function(cssText,scopeSelector,hostSelector){var _this=this;return processRules(cssText,function(rule){var selector=rule.selector,content=rule.content;return"@"!=rule.selector[0]||rule.selector.startsWith("@page")?selector=_this._scopeSelector(rule.selector,scopeSelector,hostSelector,_this.strictStyling):(rule.selector.startsWith("@media")||rule.selector.startsWith("@supports"))&&(content=_this._scopeSelectors(rule.content,scopeSelector,hostSelector)),new CssRule(selector,content)})},ShadowCss.prototype._scopeSelector=function(selector,scopeSelector,hostSelector,strict){var _this=this;return selector.split(",").map(function(part){return part.trim().split(_shadowDeepSelectors)}).map(function(deepParts){var shallowPart=deepParts[0],otherParts=deepParts.slice(1),applyScope=function(shallowPart){return _this._selectorNeedsScoping(shallowPart,scopeSelector)?strict?_this._applyStrictSelectorScope(shallowPart,scopeSelector,hostSelector):_this._applySelectorScope(shallowPart,scopeSelector,hostSelector):shallowPart};return[applyScope(shallowPart)].concat(otherParts).join(" ")}).join(", ")},ShadowCss.prototype._selectorNeedsScoping=function(selector,scopeSelector){var re=this._makeScopeMatcher(scopeSelector);return!re.test(selector)},ShadowCss.prototype._makeScopeMatcher=function(scopeSelector){var lre=/\[/g,rre=/\]/g;return scopeSelector=StringWrapper.replaceAll(scopeSelector,lre,"\\["),scopeSelector=StringWrapper.replaceAll(scopeSelector,rre,"\\]"),new RegExp("^("+scopeSelector+")"+_selectorReSuffix,"m")},ShadowCss.prototype._applySelectorScope=function(selector,scopeSelector,hostSelector){return this._applySimpleSelectorScope(selector,scopeSelector,hostSelector)},ShadowCss.prototype._applySimpleSelectorScope=function(selector,scopeSelector,hostSelector){if(_polyfillHostRe.lastIndex=0,_polyfillHostRe.test(selector)){var replaceBy=this.strictStyling?"["+hostSelector+"]":scopeSelector;return selector=StringWrapper.replace(selector,_polyfillHostNoCombinator,replaceBy),StringWrapper.replaceAll(selector,_polyfillHostRe,replaceBy+" ")}return scopeSelector+" "+selector},ShadowCss.prototype._applyStrictSelectorScope=function(selector,scopeSelector,hostSelector){var _this=this,isRe=/\[is=([^\]]*)\]/g;scopeSelector=scopeSelector.replace(isRe,function(_){for(var parts=[],_i=1;_i<arguments.length;_i++)parts[_i-1]=arguments[_i];return parts[0]});for(var res,attrName="["+scopeSelector+"]",_scopeSelectorPart=function(p){var scopedP=p.trim();if(0==scopedP.length)return"";if(p.indexOf(_polyfillHostNoCombinator)>-1)scopedP=_this._applySimpleSelectorScope(p,scopeSelector,hostSelector);else{var t=p.replace(_polyfillHostRe,"");if(t.length>0){var matches=t.match(/([^:]*)(:*)(.*)/);null!==matches&&(scopedP=matches[1]+attrName+matches[2]+matches[3])}}return scopedP},sep=/( |>|\+|~)\s*/g,scopeAfter=selector.indexOf(_polyfillHostNoCombinator),scoped="",startIndex=0;null!==(res=sep.exec(selector));){var separator=res[1],part=selector.slice(startIndex,res.index).trim(),scopedPart=startIndex>=scopeAfter?_scopeSelectorPart(part):part;scoped+=scopedPart+" "+separator+" ",startIndex=sep.lastIndex}return scoped+_scopeSelectorPart(selector.substring(startIndex))},ShadowCss.prototype._insertPolyfillHostInCssText=function(selector){return selector.replace(_colonHostContextRe,_polyfillHostContext).replace(_colonHostRe,_polyfillHost)},ShadowCss}(),_cssContentNextSelectorRe=/polyfill-next-selector[^}]*content:[\s]*?['"](.*?)['"][;\s]*}([^{]*?){/gim,_cssContentRuleRe=/(polyfill-rule)[^}]*(content:[\s]*['"](.*?)['"])[;\s]*[^}]*}/gim,_cssContentUnscopedRuleRe=/(polyfill-unscoped-rule)[^}]*(content:[\s]*['"](.*?)['"])[;\s]*[^}]*}/gim,_polyfillHost="-shadowcsshost",_polyfillHostContext="-shadowcsscontext",_parenSuffix=")(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))?([^,{]*)",_cssColonHostRe=new RegExp("("+_polyfillHost+_parenSuffix,"gim"),_cssColonHostContextRe=new RegExp("("+_polyfillHostContext+_parenSuffix,"gim"),_polyfillHostNoCombinator=_polyfillHost+"-no-combinator",_shadowDOMSelectorsRe=[/::shadow/g,/::content/g,/\/shadow-deep\//g,/\/shadow\//g],_shadowDeepSelectors=/(?:>>>)|(?:\/deep\/)/g,_selectorReSuffix="([>\\s~+[.,{:][\\s\\S]*)?$",_polyfillHostRe=/-shadowcsshost/gim,_colonHostRe=/:host/gim,_colonHostContextRe=/:host-context/gim,_commentRe=/\/\*\s*[\s\S]*?\*\//g,_sourceMappingUrlRe=/\/\*\s*#\s*sourceMappingURL=[\s\S]+?\*\//,_ruleRe=/(\s*)([^;\{\}]+?)(\s*)((?:{%BLOCK%}?\s*;?)|(?:\s*;))/g,_curlyRe=/([{}])/g,OPEN_CURLY="{",CLOSE_CURLY="}",BLOCK_PLACEHOLDER="%BLOCK%",CssRule=function(){function CssRule(selector,content){this.selector=selector,this.content=content}return CssRule}(),StringWithEscapedBlocks=function(){function StringWithEscapedBlocks(escapedString,blocks){this.escapedString=escapedString,this.blocks=blocks}return StringWithEscapedBlocks}(),COMPONENT_VARIABLE="%COMP%",HOST_ATTR="_nghost-"+COMPONENT_VARIABLE,CONTENT_ATTR="_ngcontent-"+COMPONENT_VARIABLE,StylesCompileDependency=function(){function StylesCompileDependency(moduleUrl,isShimmed,valuePlaceholder){this.moduleUrl=moduleUrl,this.isShimmed=isShimmed,this.valuePlaceholder=valuePlaceholder}return StylesCompileDependency}(),StylesCompileResult=function(){function StylesCompileResult(componentStylesheet,externalStylesheets){this.componentStylesheet=componentStylesheet,this.externalStylesheets=externalStylesheets}return StylesCompileResult}(),CompiledStylesheet=function(){function CompiledStylesheet(statements,stylesVar,dependencies,isShimmed,meta){this.statements=statements,this.stylesVar=stylesVar,this.dependencies=dependencies,this.isShimmed=isShimmed,this.meta=meta}return CompiledStylesheet}(),StyleCompiler=function(){function StyleCompiler(_urlResolver){this._urlResolver=_urlResolver,this._shadowCss=new ShadowCss}return StyleCompiler.prototype.compileComponent=function(comp){var _this=this,externalStylesheets=[],componentStylesheet=this._compileStyles(comp,new CompileStylesheetMetadata({styles:comp.template.styles,styleUrls:comp.template.styleUrls,moduleUrl:comp.type.moduleUrl}),!0);return comp.template.externalStylesheets.forEach(function(stylesheetMeta){var compiledStylesheet=_this._compileStyles(comp,stylesheetMeta,!1);externalStylesheets.push(compiledStylesheet)}),new StylesCompileResult(componentStylesheet,externalStylesheets)},StyleCompiler.prototype._compileStyles=function(comp,stylesheet,isComponentStylesheet){for(var _this=this,shim=comp.template.encapsulation===_angular_core.ViewEncapsulation.Emulated,styleExpressions=stylesheet.styles.map(function(plainStyle){return literal(_this._shimIfNeeded(plainStyle,shim))}),dependencies=[],i=0;i<stylesheet.styleUrls.length;i++){var identifier=new CompileIdentifierMetadata({name:getStylesVarName(null)});dependencies.push(new StylesCompileDependency(stylesheet.styleUrls[i],shim,identifier)),styleExpressions.push(new ExternalExpr(identifier))}var stylesVar=getStylesVarName(isComponentStylesheet?comp:null),stmt=variable(stylesVar).set(literalArr(styleExpressions,new ArrayType(DYNAMIC_TYPE,[TypeModifier.Const]))).toDeclStmt(null,[StmtModifier.Final]);return new CompiledStylesheet([stmt],stylesVar,dependencies,shim,stylesheet)},StyleCompiler.prototype._shimIfNeeded=function(style,shim){return shim?this._shadowCss.shimCssText(style,CONTENT_ATTR,HOST_ATTR):style},StyleCompiler.decorators=[{type:_angular_core.Injectable}],StyleCompiler.ctorParameters=[{type:UrlResolver}],StyleCompiler}(),RuntimeCompiler=function(){function RuntimeCompiler(_injector,_metadataResolver,_templateNormalizer,_templateParser,_styleCompiler,_viewCompiler,_ngModuleCompiler,_compilerConfig){this._injector=_injector,this._metadataResolver=_metadataResolver,this._templateNormalizer=_templateNormalizer,this._templateParser=_templateParser,this._styleCompiler=_styleCompiler,this._viewCompiler=_viewCompiler,this._ngModuleCompiler=_ngModuleCompiler,this._compilerConfig=_compilerConfig,this._compiledTemplateCache=new Map,this._compiledHostTemplateCache=new Map,this._compiledNgModuleCache=new Map}return Object.defineProperty(RuntimeCompiler.prototype,"injector",{get:function(){return this._injector},enumerable:!0,configurable:!0}),RuntimeCompiler.prototype.compileModuleSync=function(moduleType){return this._compileModuleAndComponents(moduleType,!0).syncResult},RuntimeCompiler.prototype.compileModuleAsync=function(moduleType){return this._compileModuleAndComponents(moduleType,!1).asyncResult},RuntimeCompiler.prototype.compileModuleAndAllComponentsSync=function(moduleType){return this._compileModuleAndAllComponents(moduleType,!0).syncResult},RuntimeCompiler.prototype.compileModuleAndAllComponentsAsync=function(moduleType){return this._compileModuleAndAllComponents(moduleType,!1).asyncResult},RuntimeCompiler.prototype._compileModuleAndComponents=function(moduleType,isSync){var componentPromise=this._compileComponents(moduleType,isSync),ngModuleFactory=this._compileModule(moduleType);return new SyncAsyncResult(ngModuleFactory,componentPromise.then(function(){return ngModuleFactory}))},RuntimeCompiler.prototype._compileModuleAndAllComponents=function(moduleType,isSync){var _this=this,componentPromise=this._compileComponents(moduleType,isSync),ngModuleFactory=this._compileModule(moduleType),moduleMeta=this._metadataResolver.getNgModuleMetadata(moduleType),componentFactories=[],templates=new Set;moduleMeta.transitiveModule.modules.forEach(function(moduleMeta){moduleMeta.declaredDirectives.forEach(function(dirMeta){if(dirMeta.isComponent){var template=_this._createCompiledHostTemplate(dirMeta.type.reference);templates.add(template),componentFactories.push(template.proxyComponentFactory)}})});var syncResult=new _angular_core.ModuleWithComponentFactories(ngModuleFactory,componentFactories),compile=function(){return templates.forEach(function(template){_this._compileTemplate(template)}),syncResult},asyncResult=isSync?Promise.resolve(compile()):componentPromise.then(compile);return new SyncAsyncResult(syncResult,asyncResult)},RuntimeCompiler.prototype._compileModule=function(moduleType){var _this=this,ngModuleFactory=this._compiledNgModuleCache.get(moduleType);if(!ngModuleFactory){var moduleMeta_1=this._metadataResolver.getNgModuleMetadata(moduleType),extraProviders=[this._metadataResolver.getProviderMetadata(new ProviderMeta(_angular_core.Compiler,{useFactory:function(){return new ModuleBoundCompiler(_this,moduleMeta_1.type.reference)}}))],compileResult=this._ngModuleCompiler.compile(moduleMeta_1,extraProviders);compileResult.dependencies.forEach(function(dep){dep.placeholder.reference=_this._assertComponentKnown(dep.comp.reference,!0).proxyComponentFactory,dep.placeholder.name="compFactory_"+dep.comp.name}),ngModuleFactory=this._compilerConfig.useJit?jitStatements(moduleMeta_1.type.name+".ngfactory.js",compileResult.statements,compileResult.ngModuleFactoryVar):interpretStatements(compileResult.statements,compileResult.ngModuleFactoryVar),this._compiledNgModuleCache.set(moduleMeta_1.type.reference,ngModuleFactory)}return ngModuleFactory},RuntimeCompiler.prototype._compileComponents=function(mainModule,isSync){var _this=this,templates=new Set,loadingPromises=[],ngModule=this._metadataResolver.getNgModuleMetadata(mainModule);ngModule.transitiveModule.modules.forEach(function(localModuleMeta){localModuleMeta.declaredDirectives.forEach(function(dirMeta){dirMeta.isComponent&&(templates.add(_this._createCompiledTemplate(dirMeta,localModuleMeta)),dirMeta.entryComponents.forEach(function(entryComponentType){templates.add(_this._createCompiledHostTemplate(entryComponentType.reference))}))}),localModuleMeta.entryComponents.forEach(function(entryComponentType){templates.add(_this._createCompiledHostTemplate(entryComponentType.reference))})}),templates.forEach(function(template){if(template.loading){if(isSync)throw new ComponentStillLoadingError(template.compType.reference);loadingPromises.push(template.loading)}});var compile=function(){templates.forEach(function(template){_this._compileTemplate(template)})};return isSync?(compile(),Promise.resolve(null)):Promise.all(loadingPromises).then(compile)},RuntimeCompiler.prototype.clearCacheFor=function(type){this._compiledNgModuleCache.delete(type),this._metadataResolver.clearCacheFor(type),this._compiledHostTemplateCache.delete(type);var compiledTemplate=this._compiledTemplateCache.get(type);compiledTemplate&&(this._templateNormalizer.clearCacheFor(compiledTemplate.normalizedCompMeta),this._compiledTemplateCache.delete(type))},RuntimeCompiler.prototype.clearCache=function(){this._metadataResolver.clearCache(),this._compiledTemplateCache.clear(),this._compiledHostTemplateCache.clear(),this._templateNormalizer.clearCache(),this._compiledNgModuleCache.clear()},RuntimeCompiler.prototype._createCompiledHostTemplate=function(compType){var compiledTemplate=this._compiledHostTemplateCache.get(compType);if(isBlank(compiledTemplate)){var compMeta=this._metadataResolver.getDirectiveMetadata(compType);assertComponent(compMeta);var hostMeta=createHostComponentMeta(compMeta);compiledTemplate=new CompiledTemplate((!0),compMeta.selector,compMeta.type,[compMeta],[],[],this._templateNormalizer.normalizeDirective(hostMeta)),this._compiledHostTemplateCache.set(compType,compiledTemplate)}return compiledTemplate},RuntimeCompiler.prototype._createCompiledTemplate=function(compMeta,ngModule){var compiledTemplate=this._compiledTemplateCache.get(compMeta.type.reference);return isBlank(compiledTemplate)&&(assertComponent(compMeta),compiledTemplate=new CompiledTemplate((!1),compMeta.selector,compMeta.type,ngModule.transitiveModule.directives,ngModule.transitiveModule.pipes,ngModule.schemas,this._templateNormalizer.normalizeDirective(compMeta)),this._compiledTemplateCache.set(compMeta.type.reference,compiledTemplate)),compiledTemplate},RuntimeCompiler.prototype._assertComponentKnown=function(compType,isHost){var compiledTemplate=isHost?this._compiledHostTemplateCache.get(compType):this._compiledTemplateCache.get(compType);if(!compiledTemplate)throw isHost?new Error("Illegal state: Compiled view for component "+stringify(compType)+" does not exist!"):new Error("Component "+stringify(compType)+" is not part of any NgModule or the module has not been imported into your module.");return compiledTemplate},RuntimeCompiler.prototype._assertComponentLoaded=function(compType,isHost){var compiledTemplate=this._assertComponentKnown(compType,isHost);if(compiledTemplate.loading)throw new Error("Illegal state: CompiledTemplate for "+stringify(compType)+" (isHost: "+isHost+") is still loading!");return compiledTemplate},RuntimeCompiler.prototype._compileTemplate=function(template){var _this=this;if(!template.isCompiled){var compMeta=template.normalizedCompMeta,externalStylesheetsByModuleUrl=new Map,stylesCompileResult=this._styleCompiler.compileComponent(compMeta);stylesCompileResult.externalStylesheets.forEach(function(r){externalStylesheetsByModuleUrl.set(r.meta.moduleUrl,r)}),this._resolveStylesCompileResult(stylesCompileResult.componentStylesheet,externalStylesheetsByModuleUrl);var viewCompMetas=template.viewComponentTypes.map(function(compType){return _this._assertComponentLoaded(compType,!1).normalizedCompMeta}),parsedTemplate=this._templateParser.parse(compMeta,compMeta.template.template,template.viewDirectives.concat(viewCompMetas),template.viewPipes,template.schemas,compMeta.type.name),compileResult=this._viewCompiler.compileComponent(compMeta,parsedTemplate,variable(stylesCompileResult.componentStylesheet.stylesVar),template.viewPipes);compileResult.dependencies.forEach(function(dep){var depTemplate;if(dep instanceof ViewFactoryDependency){var vfd=dep;depTemplate=_this._assertComponentLoaded(vfd.comp.reference,!1),vfd.placeholder.reference=depTemplate.proxyViewFactory,vfd.placeholder.name="viewFactory_"+vfd.comp.name}else if(dep instanceof ComponentFactoryDependency){var cfd=dep;depTemplate=_this._assertComponentLoaded(cfd.comp.reference,!0),cfd.placeholder.reference=depTemplate.proxyComponentFactory,cfd.placeholder.name="compFactory_"+cfd.comp.name}});var factory,statements=stylesCompileResult.componentStylesheet.statements.concat(compileResult.statements);factory=this._compilerConfig.useJit?jitStatements(""+template.compType.name+(template.isHost?"_Host":"")+".ngfactory.js",statements,compileResult.viewFactoryVar):interpretStatements(statements,compileResult.viewFactoryVar),template.compiled(factory)}},RuntimeCompiler.prototype._resolveStylesCompileResult=function(result,externalStylesheetsByModuleUrl){var _this=this;result.dependencies.forEach(function(dep,i){var nestedCompileResult=externalStylesheetsByModuleUrl.get(dep.moduleUrl),nestedStylesArr=_this._resolveAndEvalStylesCompileResult(nestedCompileResult,externalStylesheetsByModuleUrl);dep.valuePlaceholder.reference=nestedStylesArr,dep.valuePlaceholder.name="importedStyles"+i})},RuntimeCompiler.prototype._resolveAndEvalStylesCompileResult=function(result,externalStylesheetsByModuleUrl){return this._resolveStylesCompileResult(result,externalStylesheetsByModuleUrl),this._compilerConfig.useJit?jitStatements(result.meta.moduleUrl+".css.js",result.statements,result.stylesVar):interpretStatements(result.statements,result.stylesVar)},RuntimeCompiler.decorators=[{type:_angular_core.Injectable}],RuntimeCompiler.ctorParameters=[{type:_angular_core.Injector},{type:CompileMetadataResolver},{type:DirectiveNormalizer},{type:TemplateParser},{type:StyleCompiler},{type:ViewCompiler},{type:NgModuleCompiler},{type:CompilerConfig}],RuntimeCompiler}(),CompiledTemplate=function(){function CompiledTemplate(isHost,selector,compType,viewDirectivesAndComponents,viewPipes,schemas,_normalizeResult){var _this=this;this.isHost=isHost,this.compType=compType,this.viewPipes=viewPipes,this.schemas=schemas,this._viewFactory=null,this.loading=null,this._normalizedCompMeta=null,this.isCompiled=!1,this.isCompiledWithDeps=!1,this.viewComponentTypes=[],this.viewDirectives=[],viewDirectivesAndComponents.forEach(function(dirMeta){dirMeta.isComponent?_this.viewComponentTypes.push(dirMeta.type.reference):_this.viewDirectives.push(dirMeta)}),this.proxyViewFactory=function(){for(var args=[],_i=0;_i<arguments.length;_i++)args[_i-0]=arguments[_i];if(!_this._viewFactory)throw new Error("Illegal state: CompiledTemplate for "+stringify(_this.compType)+" is not compiled yet!");return _this._viewFactory.apply(null,args)},this.proxyComponentFactory=isHost?new _angular_core.ComponentFactory(selector,this.proxyViewFactory,compType.reference):null,_normalizeResult.syncResult?this._normalizedCompMeta=_normalizeResult.syncResult:this.loading=_normalizeResult.asyncResult.then(function(normalizedCompMeta){_this._normalizedCompMeta=normalizedCompMeta,_this.loading=null})}return Object.defineProperty(CompiledTemplate.prototype,"normalizedCompMeta",{get:function(){if(this.loading)throw new Error("Template is still loading for "+this.compType.name+"!");return this._normalizedCompMeta},enumerable:!0,configurable:!0}),CompiledTemplate.prototype.compiled=function(viewFactory){this._viewFactory=viewFactory,this.isCompiled=!0},CompiledTemplate.prototype.depsCompiled=function(){this.isCompiledWithDeps=!0},CompiledTemplate}(),ModuleBoundCompiler=function(){function ModuleBoundCompiler(_delegate,_ngModule){this._delegate=_delegate,this._ngModule=_ngModule}return Object.defineProperty(ModuleBoundCompiler.prototype,"_injector",{get:function(){return this._delegate.injector},enumerable:!0,configurable:!0}),ModuleBoundCompiler.prototype.compileModuleSync=function(moduleType){return this._delegate.compileModuleSync(moduleType)},ModuleBoundCompiler.prototype.compileModuleAsync=function(moduleType){return this._delegate.compileModuleAsync(moduleType)},ModuleBoundCompiler.prototype.compileModuleAndAllComponentsSync=function(moduleType){return this._delegate.compileModuleAndAllComponentsSync(moduleType)},ModuleBoundCompiler.prototype.compileModuleAndAllComponentsAsync=function(moduleType){return this._delegate.compileModuleAndAllComponentsAsync(moduleType)},ModuleBoundCompiler.prototype.clearCache=function(){this._delegate.clearCache()},ModuleBoundCompiler.prototype.clearCacheFor=function(type){this._delegate.clearCacheFor(type)},ModuleBoundCompiler}(),SECURITY_SCHEMA={};registerContext(_angular_core.SecurityContext.HTML,["iframe|srcdoc","*|innerHTML","*|outerHTML"]),registerContext(_angular_core.SecurityContext.STYLE,["*|style"]),registerContext(_angular_core.SecurityContext.URL,["*|formAction","area|href","area|ping","audio|src","a|href","a|ping","blockquote|cite","body|background","del|cite","form|action","img|src","img|srcset","input|src","ins|cite","q|cite","source|src","source|srcset","track|src","video|poster","video|src"]),registerContext(_angular_core.SecurityContext.RESOURCE_URL,["applet|code","applet|codebase","base|href","embed|src","frame|src","head|profile","html|manifest","iframe|src","link|href","media|src","object|codebase","object|data","script|src"]);/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
var __extends$18=this&&this.__extends||function(d,b){function __(){this.constructor=d}for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p]);d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)},BOOLEAN="boolean",NUMBER="number",STRING="string",OBJECT="object",SCHEMA=["[Element]|textContent,%classList,className,id,innerHTML,*beforecopy,*beforecut,*beforepaste,*copy,*cut,*paste,*search,*selectstart,*webkitfullscreenchange,*webkitfullscreenerror,*wheel,outerHTML,#scrollLeft,#scrollTop","[HTMLElement]^[Element]|accessKey,contentEditable,dir,!draggable,!hidden,innerText,lang,*abort,*beforecopy,*beforecut,*beforepaste,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*message,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*mozfullscreenchange,*mozfullscreenerror,*mozpointerlockchange,*mozpointerlockerror,*paste,*pause,*play,*playing,*progress,*ratechange,*reset,*resize,*scroll,*search,*seeked,*seeking,*select,*selectstart,*show,*stalled,*submit,*suspend,*timeupdate,*toggle,*volumechange,*waiting,*webglcontextcreationerror,*webglcontextlost,*webglcontextrestored,*webkitfullscreenchange,*webkitfullscreenerror,*wheel,outerText,!spellcheck,%style,#tabIndex,title,!translate","abbr,address,article,aside,b,bdi,bdo,cite,code,dd,dfn,dt,em,figcaption,figure,footer,header,i,kbd,main,mark,nav,noscript,rb,rp,rt,rtc,ruby,s,samp,section,small,strong,sub,sup,u,var,wbr^[HTMLElement]|accessKey,contentEditable,dir,!draggable,!hidden,innerText,lang,*abort,*beforecopy,*beforecut,*beforepaste,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*message,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*mozfullscreenchange,*mozfullscreenerror,*mozpointerlockchange,*mozpointerlockerror,*paste,*pause,*play,*playing,*progress,*ratechange,*reset,*resize,*scroll,*search,*seeked,*seeking,*select,*selectstart,*show,*stalled,*submit,*suspend,*timeupdate,*toggle,*volumechange,*waiting,*webglcontextcreationerror,*webglcontextlost,*webglcontextrestored,*webkitfullscreenchange,*webkitfullscreenerror,*wheel,outerText,!spellcheck,%style,#tabIndex,title,!translate","media^[HTMLElement]|!autoplay,!controls,%crossOrigin,#currentTime,!defaultMuted,#defaultPlaybackRate,!disableRemotePlayback,!loop,!muted,*encrypted,#playbackRate,preload,src,%srcObject,#volume",":svg:^[HTMLElement]|*abort,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*cuechange,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*pause,*play,*playing,*progress,*ratechange,*reset,*resize,*scroll,*seeked,*seeking,*select,*show,*stalled,*submit,*suspend,*timeupdate,*toggle,*volumechange,*waiting,%style,#tabIndex",":svg:graphics^:svg:|",":svg:animation^:svg:|*begin,*end,*repeat",":svg:geometry^:svg:|",":svg:componentTransferFunction^:svg:|",":svg:gradient^:svg:|",":svg:textContent^:svg:graphics|",":svg:textPositioning^:svg:textContent|","a^[HTMLElement]|charset,coords,download,hash,host,hostname,href,hreflang,name,password,pathname,ping,port,protocol,referrerPolicy,rel,rev,search,shape,target,text,type,username","area^[HTMLElement]|alt,coords,hash,host,hostname,href,!noHref,password,pathname,ping,port,protocol,referrerPolicy,search,shape,target,username","audio^media|","br^[HTMLElement]|clear","base^[HTMLElement]|href,target","body^[HTMLElement]|aLink,background,bgColor,link,*beforeunload,*blur,*error,*focus,*hashchange,*languagechange,*load,*message,*offline,*online,*pagehide,*pageshow,*popstate,*rejectionhandled,*resize,*scroll,*storage,*unhandledrejection,*unload,text,vLink","button^[HTMLElement]|!autofocus,!disabled,formAction,formEnctype,formMethod,!formNoValidate,formTarget,name,type,value","canvas^[HTMLElement]|#height,#width","content^[HTMLElement]|select","dl^[HTMLElement]|!compact","datalist^[HTMLElement]|","details^[HTMLElement]|!open","dialog^[HTMLElement]|!open,returnValue","dir^[HTMLElement]|!compact","div^[HTMLElement]|align","embed^[HTMLElement]|align,height,name,src,type,width","fieldset^[HTMLElement]|!disabled,name","font^[HTMLElement]|color,face,size","form^[HTMLElement]|acceptCharset,action,autocomplete,encoding,enctype,method,name,!noValidate,target","frame^[HTMLElement]|frameBorder,longDesc,marginHeight,marginWidth,name,!noResize,scrolling,src","frameset^[HTMLElement]|cols,*beforeunload,*blur,*error,*focus,*hashchange,*languagechange,*load,*message,*offline,*online,*pagehide,*pageshow,*popstate,*rejectionhandled,*resize,*scroll,*storage,*unhandledrejection,*unload,rows","hr^[HTMLElement]|align,color,!noShade,size,width","head^[HTMLElement]|","h1,h2,h3,h4,h5,h6^[HTMLElement]|align","html^[HTMLElement]|version","iframe^[HTMLElement]|align,!allowFullscreen,frameBorder,height,longDesc,marginHeight,marginWidth,name,referrerPolicy,%sandbox,scrolling,src,srcdoc,width","img^[HTMLElement]|align,alt,border,%crossOrigin,#height,#hspace,!isMap,longDesc,lowsrc,name,referrerPolicy,sizes,src,srcset,useMap,#vspace,#width","input^[HTMLElement]|accept,align,alt,autocapitalize,autocomplete,!autofocus,!checked,!defaultChecked,defaultValue,dirName,!disabled,%files,formAction,formEnctype,formMethod,!formNoValidate,formTarget,#height,!incremental,!indeterminate,max,#maxLength,min,#minLength,!multiple,name,pattern,placeholder,!readOnly,!required,selectionDirection,#selectionEnd,#selectionStart,#size,src,step,type,useMap,value,%valueAsDate,#valueAsNumber,#width","keygen^[HTMLElement]|!autofocus,challenge,!disabled,keytype,name","li^[HTMLElement]|type,#value","label^[HTMLElement]|htmlFor","legend^[HTMLElement]|align","link^[HTMLElement]|as,charset,%crossOrigin,!disabled,href,hreflang,integrity,media,rel,%relList,rev,%sizes,target,type","map^[HTMLElement]|name","marquee^[HTMLElement]|behavior,bgColor,direction,height,#hspace,#loop,#scrollAmount,#scrollDelay,!trueSpeed,#vspace,width","menu^[HTMLElement]|!compact","meta^[HTMLElement]|content,httpEquiv,name,scheme","meter^[HTMLElement]|#high,#low,#max,#min,#optimum,#value","ins,del^[HTMLElement]|cite,dateTime","ol^[HTMLElement]|!compact,!reversed,#start,type","object^[HTMLElement]|align,archive,border,code,codeBase,codeType,data,!declare,height,#hspace,name,standby,type,useMap,#vspace,width","optgroup^[HTMLElement]|!disabled,label","option^[HTMLElement]|!defaultSelected,!disabled,label,!selected,text,value","output^[HTMLElement]|defaultValue,%htmlFor,name,value","p^[HTMLElement]|align","param^[HTMLElement]|name,type,value,valueType","picture^[HTMLElement]|","pre^[HTMLElement]|#width","progress^[HTMLElement]|#max,#value","q,blockquote,cite^[HTMLElement]|","script^[HTMLElement]|!async,charset,%crossOrigin,!defer,event,htmlFor,integrity,src,text,type","select^[HTMLElement]|!autofocus,!disabled,#length,!multiple,name,!required,#selectedIndex,#size,value","shadow^[HTMLElement]|","source^[HTMLElement]|media,sizes,src,srcset,type","span^[HTMLElement]|","style^[HTMLElement]|!disabled,media,type","caption^[HTMLElement]|align","th,td^[HTMLElement]|abbr,align,axis,bgColor,ch,chOff,#colSpan,headers,height,!noWrap,#rowSpan,scope,vAlign,width","col,colgroup^[HTMLElement]|align,ch,chOff,#span,vAlign,width","table^[HTMLElement]|align,bgColor,border,%caption,cellPadding,cellSpacing,frame,rules,summary,%tFoot,%tHead,width","tr^[HTMLElement]|align,bgColor,ch,chOff,vAlign","tfoot,thead,tbody^[HTMLElement]|align,ch,chOff,vAlign","template^[HTMLElement]|","textarea^[HTMLElement]|autocapitalize,!autofocus,#cols,defaultValue,dirName,!disabled,#maxLength,#minLength,name,placeholder,!readOnly,!required,#rows,selectionDirection,#selectionEnd,#selectionStart,value,wrap","title^[HTMLElement]|text","track^[HTMLElement]|!default,kind,label,src,srclang","ul^[HTMLElement]|!compact,type","unknown^[HTMLElement]|","video^media|#height,poster,#width",":svg:a^:svg:graphics|",":svg:animate^:svg:animation|",":svg:animateMotion^:svg:animation|",":svg:animateTransform^:svg:animation|",":svg:circle^:svg:geometry|",":svg:clipPath^:svg:graphics|",":svg:cursor^:svg:|",":svg:defs^:svg:graphics|",":svg:desc^:svg:|",":svg:discard^:svg:|",":svg:ellipse^:svg:geometry|",":svg:feBlend^:svg:|",":svg:feColorMatrix^:svg:|",":svg:feComponentTransfer^:svg:|",":svg:feComposite^:svg:|",":svg:feConvolveMatrix^:svg:|",":svg:feDiffuseLighting^:svg:|",":svg:feDisplacementMap^:svg:|",":svg:feDistantLight^:svg:|",":svg:feDropShadow^:svg:|",":svg:feFlood^:svg:|",":svg:feFuncA^:svg:componentTransferFunction|",":svg:feFuncB^:svg:componentTransferFunction|",":svg:feFuncG^:svg:componentTransferFunction|",":svg:feFuncR^:svg:componentTransferFunction|",":svg:feGaussianBlur^:svg:|",":svg:feImage^:svg:|",":svg:feMerge^:svg:|",":svg:feMergeNode^:svg:|",":svg:feMorphology^:svg:|",":svg:feOffset^:svg:|",":svg:fePointLight^:svg:|",":svg:feSpecularLighting^:svg:|",":svg:feSpotLight^:svg:|",":svg:feTile^:svg:|",":svg:feTurbulence^:svg:|",":svg:filter^:svg:|",":svg:foreignObject^:svg:graphics|",":svg:g^:svg:graphics|",":svg:image^:svg:graphics|",":svg:line^:svg:geometry|",":svg:linearGradient^:svg:gradient|",":svg:mpath^:svg:|",":svg:marker^:svg:|",":svg:mask^:svg:|",":svg:metadata^:svg:|",":svg:path^:svg:geometry|",":svg:pattern^:svg:|",":svg:polygon^:svg:geometry|",":svg:polyline^:svg:geometry|",":svg:radialGradient^:svg:gradient|",":svg:rect^:svg:geometry|",":svg:svg^:svg:graphics|#currentScale,#zoomAndPan",":svg:script^:svg:|type",":svg:set^:svg:animation|",":svg:stop^:svg:|",":svg:style^:svg:|!disabled,media,title,type",":svg:switch^:svg:graphics|",":svg:symbol^:svg:|",":svg:tspan^:svg:textPositioning|",":svg:text^:svg:textPositioning|",":svg:textPath^:svg:textContent|",":svg:title^:svg:|",":svg:use^:svg:graphics|",":svg:view^:svg:|#zoomAndPan","data^[HTMLElement]|value","menuitem^[HTMLElement]|type,label,icon,!disabled,!checked,radiogroup,!default","summary^[HTMLElement]|","time^[HTMLElement]|dateTime"],_ATTR_TO_PROP={class:"className",formaction:"formAction",innerHtml:"innerHTML",readonly:"readOnly",tabindex:"tabIndex"},DomElementSchemaRegistry=function(_super){function DomElementSchemaRegistry(){var _this=this;_super.call(this),this._schema={},SCHEMA.forEach(function(encodedType){var type={},_a=encodedType.split("|"),strType=_a[0],strProperties=_a[1],properties=strProperties.split(","),_b=strType.split("^"),typeNames=_b[0],superName=_b[1];typeNames.split(",").forEach(function(tag){return _this._schema[tag.toLowerCase()]=type});var superType=superName&&_this._schema[superName.toLowerCase()];superType&&Object.keys(superType).forEach(function(prop){type[prop]=superType[prop]}),properties.forEach(function(property){if(property.length>0)switch(property[0]){case"*":break;case"!":type[property.substring(1)]=BOOLEAN;break;case"#":type[property.substring(1)]=NUMBER;break;case"%":type[property.substring(1)]=OBJECT;break;default:type[property]=STRING}})})}return __extends$18(DomElementSchemaRegistry,_super),DomElementSchemaRegistry.prototype.hasProperty=function(tagName,propName,schemaMetas){if(schemaMetas.some(function(schema){return schema.name===_angular_core.NO_ERRORS_SCHEMA.name}))return!0;if(tagName.indexOf("-")>-1){if("ng-container"===tagName||"ng-content"===tagName)return!1;if(schemaMetas.some(function(schema){return schema.name===_angular_core.CUSTOM_ELEMENTS_SCHEMA.name}))return!0}var elementProperties=this._schema[tagName.toLowerCase()]||this._schema.unknown;return!!elementProperties[propName]},DomElementSchemaRegistry.prototype.hasElement=function(tagName,schemaMetas){if(schemaMetas.some(function(schema){return schema.name===_angular_core.NO_ERRORS_SCHEMA.name}))return!0;if(tagName.indexOf("-")>-1){if("ng-container"===tagName||"ng-content"===tagName)return!0;if(schemaMetas.some(function(schema){return schema.name===_angular_core.CUSTOM_ELEMENTS_SCHEMA.name}))return!0}return!!this._schema[tagName.toLowerCase()]},DomElementSchemaRegistry.prototype.securityContext=function(tagName,propName){tagName=tagName.toLowerCase(),propName=propName.toLowerCase();var ctx=SECURITY_SCHEMA[tagName+"|"+propName];return ctx?ctx:(ctx=SECURITY_SCHEMA["*|"+propName],ctx?ctx:_angular_core.SecurityContext.NONE)},DomElementSchemaRegistry.prototype.getMappedPropName=function(propName){return _ATTR_TO_PROP[propName]||propName},DomElementSchemaRegistry.prototype.getDefaultComponentElementName=function(){return"ng-component"},DomElementSchemaRegistry.decorators=[{type:_angular_core.Injectable}],DomElementSchemaRegistry.ctorParameters=[],DomElementSchemaRegistry}(ElementSchemaRegistry),_NO_RESOURCE_LOADER={get:function(url){throw new Error("No ResourceLoader implementation has been provided. Can't read the url \""+url+'"')}},COMPILER_PROVIDERS=[{provide:Reflector,useValue:reflector},{provide:ReflectorReader,useExisting:Reflector},{provide:ResourceLoader,useValue:_NO_RESOURCE_LOADER},Console,Lexer,Parser,HtmlParser,{provide:I18NHtmlParser,useFactory:function(parser,translations,format){return new I18NHtmlParser(parser,translations,format)},deps:[HtmlParser,[new _angular_core.Optional,new _angular_core.Inject(_angular_core.TRANSLATIONS)],[new _angular_core.Optional,new _angular_core.Inject(_angular_core.TRANSLATIONS_FORMAT)]]},TemplateParser,DirectiveNormalizer,CompileMetadataResolver,DEFAULT_PACKAGE_URL_PROVIDER,StyleCompiler,ViewCompiler,NgModuleCompiler,{provide:CompilerConfig,useValue:new CompilerConfig},RuntimeCompiler,{provide:_angular_core.Compiler,useExisting:RuntimeCompiler},DomElementSchemaRegistry,{provide:ElementSchemaRegistry,useExisting:DomElementSchemaRegistry},UrlResolver,DirectiveResolver,PipeResolver,NgModuleResolver],RuntimeCompilerFactory=function(){function RuntimeCompilerFactory(defaultOptions){this._defaultOptions=[{useDebug:_angular_core.isDevMode(),useJit:!0,defaultEncapsulation:_angular_core.ViewEncapsulation.Emulated}].concat(defaultOptions)}return RuntimeCompilerFactory.prototype.createCompiler=function(options){void 0===options&&(options=[]);var mergedOptions=_mergeOptions(this._defaultOptions.concat(options)),injector=_angular_core.ReflectiveInjector.resolveAndCreate([COMPILER_PROVIDERS,{provide:CompilerConfig,useFactory:function(){return new CompilerConfig({genDebugInfo:mergedOptions.useDebug,useJit:mergedOptions.useJit,defaultEncapsulation:mergedOptions.defaultEncapsulation,logBindingUpdate:mergedOptions.useDebug})},deps:[]},mergedOptions.providers]);return injector.get(_angular_core.Compiler)},RuntimeCompilerFactory.decorators=[{type:_angular_core.Injectable}],RuntimeCompilerFactory.ctorParameters=[{type:Array,decorators:[{type:_angular_core.Inject,args:[_angular_core.COMPILER_OPTIONS]}]}],RuntimeCompilerFactory}(),platformCoreDynamic=_angular_core.createPlatformFactory(_angular_core.platformCore,"coreDynamic",[{provide:_angular_core.COMPILER_OPTIONS,useValue:{},multi:!0},{provide:_angular_core.CompilerFactory,useClass:RuntimeCompilerFactory},{provide:_angular_core.PLATFORM_INITIALIZER,useValue:_initReflector,multi:!0}]),_ASSET_URL_RE=/asset:([^\/]+)\/([^\/]+)\/(.+)/,ImportGenerator=function(){function ImportGenerator(){}return ImportGenerator.parseAssetUrl=function(url){return AssetUrl.parse(url)},ImportGenerator}(),AssetUrl=function(){function AssetUrl(packageName,firstLevelDir,modulePath){this.packageName=packageName,this.firstLevelDir=firstLevelDir,this.modulePath=modulePath}return AssetUrl.parse=function(url,allowNonMatching){void 0===allowNonMatching&&(allowNonMatching=!0);var match=url.match(_ASSET_URL_RE);if(null!==match)return new AssetUrl(match[1],match[2],match[3]);if(allowNonMatching)return null;throw new Error("Url "+url+" is not a valid asset: url")},AssetUrl}(),__compiler_private__={SelectorMatcher:SelectorMatcher,CssSelector:CssSelector,AssetUrl:AssetUrl,ImportGenerator:ImportGenerator,CompileMetadataResolver:CompileMetadataResolver,HtmlParser:HtmlParser,InterpolationConfig:InterpolationConfig,DirectiveNormalizer:DirectiveNormalizer,Lexer:Lexer,Parser:Parser,ParseLocation:ParseLocation,ParseError:ParseError,ParseErrorLevel:ParseErrorLevel,ParseSourceFile:ParseSourceFile,ParseSourceSpan:ParseSourceSpan,TemplateParser:TemplateParser,DomElementSchemaRegistry:DomElementSchemaRegistry,StyleCompiler:StyleCompiler,ViewCompiler:ViewCompiler,NgModuleCompiler:NgModuleCompiler,TypeScriptEmitter:TypeScriptEmitter};exports.COMPILER_PROVIDERS=COMPILER_PROVIDERS,exports.CompileDiDependencyMetadata=CompileDiDependencyMetadata,exports.CompileDirectiveMetadata=CompileDirectiveMetadata,exports.CompileFactoryMetadata=CompileFactoryMetadata,exports.CompileIdentifierMetadata=CompileIdentifierMetadata,exports.CompileMetadataWithIdentifier=CompileMetadataWithIdentifier,exports.CompilePipeMetadata=CompilePipeMetadata,exports.CompileProviderMetadata=CompileProviderMetadata,exports.CompileQueryMetadata=CompileQueryMetadata,exports.CompileTemplateMetadata=CompileTemplateMetadata,exports.CompileTokenMetadata=CompileTokenMetadata,exports.CompileTypeMetadata=CompileTypeMetadata,exports.CompilerConfig=CompilerConfig,exports.DEFAULT_PACKAGE_URL_PROVIDER=DEFAULT_PACKAGE_URL_PROVIDER,exports.DirectiveResolver=DirectiveResolver,exports.NgModuleResolver=NgModuleResolver,exports.OfflineCompiler=OfflineCompiler,exports.PipeResolver=PipeResolver,exports.RenderTypes=RenderTypes,exports.ResourceLoader=ResourceLoader,exports.RuntimeCompiler=RuntimeCompiler,exports.SourceModule=SourceModule,exports.TEMPLATE_TRANSFORMS=TEMPLATE_TRANSFORMS,exports.UrlResolver=UrlResolver,exports.createOfflineCompileUrlResolver=createOfflineCompileUrlResolver,exports.platformCoreDynamic=platformCoreDynamic,exports.DEFAULT_INTERPOLATION_CONFIG=DEFAULT_INTERPOLATION_CONFIG,exports.InterpolationConfig=InterpolationConfig,exports.ElementSchemaRegistry=ElementSchemaRegistry,exports.I18NHtmlParser=I18NHtmlParser,exports.MessageBundle=MessageBundle,exports.Xliff=Xliff,exports.Xmb=Xmb,exports.Xtb=Xtb,exports.TextAst=TextAst,exports.BoundTextAst=BoundTextAst,exports.AttrAst=AttrAst,exports.BoundElementPropertyAst=BoundElementPropertyAst,exports.BoundEventAst=BoundEventAst,exports.ReferenceAst=ReferenceAst,exports.VariableAst=VariableAst,exports.ElementAst=ElementAst,exports.EmbeddedTemplateAst=EmbeddedTemplateAst,exports.BoundDirectivePropertyAst=BoundDirectivePropertyAst,exports.DirectiveAst=DirectiveAst,exports.ProviderAst=ProviderAst,exports.NgContentAst=NgContentAst,exports.templateVisitAll=templateVisitAll,exports.__compiler_private__=__compiler_private__});
