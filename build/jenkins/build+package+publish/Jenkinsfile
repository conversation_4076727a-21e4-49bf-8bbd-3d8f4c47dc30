#!groovy
@Library('sso-shared') _

def generateEl8AutomationRpmInGithub() {
    def url = "https://api.github.com/repos/AlticeLabsProjects/sso-actions/actions/workflows/build_package_publish.yml/dispatches"
    echo "URL: ${url}"
    echo "GITHUB REQUEST JSON: ${GITHUB_REQUEST_JSON}"
    def response = sh(script: "curl -i -X POST -H \"Accept: application/vnd.github.v3+json\" -H \"authorization: Bearer \$GITHUB_PSW\" -d '${GITHUB_REQUEST_JSON}' \"${url}\" ", returnStdout: true).trim()
    echo "RESPONSE: \n${response}"
}

pipeline {

    parameters {
        booleanParam(name: 'GITHUB_DEPLOY', defaultValue: true, description: 'Enable/Disable github docker deployment')
        string(name: 'NODEJS_VERSION', defaultValue: "20")
    }

    environment {
        MAVEN_SETTINGS = "-s build/settings.xml -gs /home/<USER>/.m2/settings.xml"
        MAVEN_SETTINGS_PARALLEL = "-T 1C ${MAVEN_SETTINGS}"

        GITHUB = credentials('ci-sso-alb-id')
        GITHUB_RPM_PROFILES="-Pdist,rpm${env.BRANCH_NAME.startsWith("tags/") ? ",release-build" : ""}"
        GITHUB_MVN_PACKAGE_CMD = "mvn -f src/dist/rpm/pom.xml package -s build/settings.xml ${GITHUB_RPM_PROFILES} -Ddistname=el8"
        GITHUB_YUM_UPLOAD_REPOS = "dev,testing,${BRANCH_NAME.startsWith("tags/") ? "rpm-el8-x86-64-prod" : "rpm-el8-x86-64-prod-testing"}"
        GITHUB_REQUEST_JSON = """
            {
                "ref":"main",
                "inputs": {
                    "branch":"${BRANCH_NAME}",
                    "image":"ghcr.io/alticelabsprojects/sso5/sso5-el8:1",
                    "java_version":"8",
                    "environment":"oss",
                    "mvn_build_cmd":"${GITHUB_MVN_PACKAGE_CMD}",
                    "repository_type":"svn",
                    "repository":"nossis-tsc",
                    "yum_upload_repos":"${GITHUB_YUM_UPLOAD_REPOS}"
                }
            }
        """
        JIRA_REST = credentials("JIRACloudREST")
        MAVEN_SETTINGS_YAMLDOC = "${MAVEN_SETTINGS} -Dmaven.yamldoc.plugin.username=${JIRA_REST_USR} -Dmaven.yamldoc.plugin.password=${JIRA_REST_PSW}"
    }

    options {
        timestamps()
        disableConcurrentBuilds()
    }

    agent {
        dockerfile {
            dir 'build/jenkins-docker'
            filename 'Dockerfile.el8'
            additionalBuildArgs  '--build-arg NODEJS_VERSION=${NODEJS_VERSION}'
            label 'docker && internet'
            args '-u 2222:2223 -v /home/<USER>/.ssh:/home/<USER>/.ssh -v /home/<USER>/m2 --privileged -v ${WORKSPACE}/docker:/var/lib/docker --network host'
        }
    }

    triggers {
        pollSCM 'H/30 * * * *'
    }

    stages {

        stage('Prepare container') {
            steps {
                sh 'ln -s $DOCKER_CONFIG/config.json ~/.docker/'
                sh 'sudo bash -c "/docker-ce/start.sh"'
                sh 'mvn -version'
                sh 'node --version'
                sh 'npm --version'
                sh 'docker info'
            }
        }

        stage('Docker Registry Github Login') {
            when {
                expression {
                    return params.GITHUB_DEPLOY
                }
            }
            steps {
                sh "docker login ghcr.io -u '${GITHUB_USR}' -p ${GITHUB_PSW}"
            }
        }

        stage('Compiling') {
            steps {
                ssoMaven('-f pom.xml clean install ${MAVEN_SETTINGS_PARALLEL} -DskipTests -Dmdep.analyze.skip=true')
            }
        }

        stage('Deployment') {
            parallel {
                stage('RPM Testing el7') {
                    when {
                        anyOf {
                            branch 'trunk'
                            branch 'branches/*'
                        }
                    }
                    steps {
                        ssoMaven('-f src/dist/rpm/pom.xml package -s build/settings.xml -gs /home/<USER>/.m2/settings.xml -Pdist,rpm')
                        yumUpload('nossis-tsc')
                    }
                }

                stage('RPM Release el7') {
                    when {
                        branch 'tags/*'
                    }
                    steps {
                        ssoMaven("-f src/dist/rpm/pom.xml package ${MAVEN_SETTINGS} -Pdist,rpm,release-build")
                        yumUploadFinal('nossis-tsc')
                    }
                }

                stage('Invoke GitHub Actions Workflow to upload RPM testing/release el8') {
                    steps {
                        generateEl8AutomationRpmInGithub()
                    }
                }

                stage('Docker Harbor Testing') {
                    when {
                        allOf {
                            anyOf {
                                branch 'trunk'
                                branch 'branches/*'
                            }
                            expression {
                                return !params.GITHUB_DEPLOY
                            }
                        }
                    }
                    steps {
                        ssoMaven('-f src/dist/docker/pom.xml deploy ${MAVEN_SETTINGS} -Pdocker,docker-dev -T 0.5C')
                    }
                }

                stage('Docker Harbor Release') {
                    when {
                        allOf {
                            branch 'tags/*'
                            expression {
                                return !params.GITHUB_DEPLOY
                            }
                        }
                    }
                    steps {
                        ssoMaven('-f src/dist/docker/pom.xml deploy ${MAVEN_SETTINGS} -Pdocker,docker-release -T 0.5C')
                    }
                }

                stage('Docker Harbor & Github Testing') {
                    when {
                        allOf {
                            anyOf {
                                branch 'trunk'
                                branch 'branches/*'
                            }
                            expression {
                                return params.GITHUB_DEPLOY
                            }
                        }
                    }
                    steps {
                        ssoMaven('-f src/dist/docker/pom.xml deploy ${MAVEN_SETTINGS} -Pdocker,docker-dev,github,github-dev -T 0.5C')
                    }
                }

                stage('Docker Harbor & Github Release') {
                    when {
                        allOf {
                            branch 'tags/*'
                            expression {
                                return params.GITHUB_DEPLOY
                            }
                        }
                    }
                    steps {
                        ssoMaven('-f src/dist/docker/pom.xml deploy ${MAVEN_SETTINGS} -Pdocker,docker-release,github,github-release -T 0.5C')
                    }
                }

                stage('Backend Unit Tests') {
                    steps {
                        ssoMaven(' -f pom.xml surefire:test ${MAVEN_SETTINGS} -T 0.25C')
                    }
                }

                stage('Frontend Unit Tests') {
                    steps {
                        ssoMaven(' -f src/frontend/play/pom.xml exec:exec@npm-test ${MAVEN_SETTINGS}')
                    }
                }

                stage('Backend Artifactory deploy') {
                    steps {
                        ssoMaven(" -f pom.xml jar:jar source:jar deploy:deploy ${MAVEN_SETTINGS} -T 0.25C")
                    }
                }

                stage('Frontend Artifactory deploy') {
                    steps {
                        ssoMaven('-f src/frontend/play/pom.xml exec:exec@gradle-deploy ${MAVEN_SETTINGS}')
                    }
                }

                stage('Dependency Analyze') {
                    steps {
                        ssoMaven('-f pom.xml dependency:analyze-only ${MAVEN_SETTINGS}')
                    }
                }

                stage('Generating documentation'){
                    steps {
                        ssoMaven('-f src/dist/docker/pom.xml deploy -P documentation ${MAVEN_SETTINGS_YAMLDOC}')
                    }
                }
            }
        }

        stage('Logout') {
            steps {
                sh 'find src/dist -name *.rpm'
                sh 'docker images'
                sh "docker logout ghcr.io"
                sh 'sudo bash -c "/docker-ce/prune.sh"'
                sh 'sudo bash -c "/docker-ce/stop.sh"'
            }
        }
    }

    post {
        changed {
            sendNotifications(currentBuild.result, '${FULFILLMENT_RECIPIENTS}')
        }

        cleanup {
            sh 'sudo bash -c "rm -rf ${WORKSPACE}/docker"'
        }
    }
}
