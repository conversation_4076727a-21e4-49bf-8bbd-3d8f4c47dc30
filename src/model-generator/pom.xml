<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.nossis-tsc</groupId>
        <artifactId>nossis-tsc</artifactId>
        <version>5.2.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>nossis-tsc-entities-catalog-model-generator</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!-- nossis-inv -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-http-api-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-monitoring</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-core-http-api-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-entities-catalog</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-core-persistence-api</artifactId>
        </dependency>


        <!-- nossis-tsc-->
        <dependency>
            <groupId>pt.ptinovacao.nossis-tsc</groupId>
            <artifactId>nossis-tsc-entities-catalog-model</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-tsc</groupId>
            <artifactId>nossis-tsc-entities-catalog-core</artifactId>
        </dependency>
        <!-- others -->
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-components-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>
    </dependencies>
</project>