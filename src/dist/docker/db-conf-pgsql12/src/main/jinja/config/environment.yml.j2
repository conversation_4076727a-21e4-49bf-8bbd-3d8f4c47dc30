{% set ud_version = project.version.value | cut('-SNAPSHOT') -%}
{% set nossis_inv_version = version.pt.ptinovacao.get('nossis-inv').value | cut('-SNAPSHOT') -%}
{{ raw }}
#' Path for directory(ies) that contain(s) one or more template files.
#' $string
#' %optional
#' @ "/opt/alticelabs/<ud>/ /opt/alticelabs/<ud-2>/"
#TEMPLATE_DIRS:

#' Path for file(s) that contain(s) additional customization variables.
#' *Note:* If you extend this image and want to add one more default inventory file,
#' you need to do it like this DEFAULTS_INV="$DEFAULTS_INV /<config-dir>/<inventory-file>.yml"
#' $string
#' %default
DEFAULTS_INV: "$DEFAULTS_INV /config/defaults-nossis-tsc-db-conf-pgsql12.yml"

#' The default password through which to connect to the db
#' $string
#' %default
DB_DEFAULT_PWD: "nossistsc"

#' The pgsql database port.
#' $integer
#' %default
DB_PORT: 5432

#' Nossis TSC version used for database migration purposes.
#' *Notes*
#' * Should only be set when downgrading the database version.
#' * Value must not be higher than {{ endraw }}{{ ud_version }}{{ raw }}.
#' $string
#' %optional
#' @ "{{ endraw }}{{ ud_version }}{{ raw }}"
#NOSSIS_TSC_VERSION:

#' Nossis Inventory version used for database migration purposes.
#' *Notes*
#' * Should only be set when downgrading the database version.
#' * Value must not be higher than {{ endraw }}{{ nossis_inv_version }}{{ raw }}.
#' $string
#' %optional
#' @ "{{ endraw }}{{ nossis_inv_version }}{{ raw }}"
#NOSSIS_INV_VERSION:
{{ endraw }}
