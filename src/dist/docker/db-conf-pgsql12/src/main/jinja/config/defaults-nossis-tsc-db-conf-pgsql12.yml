---

#' Specifies if all permissions are granted to nossis-tsc users that will be created by migrate.
#' Possible values: *YES* or *NO*.
#' $string
#' %default
#' @'NO'
is_admin_user: 'YES'

#' Specifies the tablespace data name.
#' $string
#' %default
ts_nossistsc_data: 'ts_nossistsc_data'
#' Specifies the tablespace indx name.
#' $string
#' %default
ts_nossistsc_indx: 'ts_nossistsc_indx'

#' Specifies the postgres version. Auxiliary variable used in the
#' _nossis_tsc_db_ts_nossistsc_data_location_ e _nossis_tsc_db_ts_nossistsc_indx_location_ variables.
#' $integer
#' %default
pgsql_version: 12

#' Specifies the directory's path where the tablespace TS_NOSSISTSC_DATA will be stored (postgresql).
#' *Note*: Directory needs to be created first.
#' $string
#' %default
nossis_tsc_db_ts_nossistsc_data_location: '/var/lib/pgsql/{{ pgsql_version }}/nossistsc/data'
#' Specifies the directory's path where the tablespace TS_NOSSISTSC_INDX will be stored (postgresql).
#' *Note*: Directory needs to be created first.
#' $string
#' %default
nossis_tsc_db_ts_nossistsc_indx_location: '/var/lib/pgsql/{{ pgsql_version }}/nossistsc/indx'

#' Specifies the nossis-inv catalog base data l tablespace name.
#' $string
#' %default
ts_nossisinv_cat_base_data_l: 'ts_nossistsc_data'
#' Specifies the nossis-inv catalog base data s tablespace name.
#' $string
#' %default
ts_nossisinv_cat_base_data_s: 'ts_nossistsc_data'
#' Specifies the nossis-inv catalog base index l tablespace name.
#' $string
#' %default
ts_nossisinv_cat_base_index_l: 'ts_nossistsc_indx'
#' Specifies the nossis-inv catalog base index s tablespace name.
#' $string
#' %default
ts_nossisinv_cat_base_index_s: 'ts_nossistsc_indx'

#' Specifies if the tablespaces are created.
#' $boolean
#' %default
nossis_tsc_db_create_tablespaces: True
