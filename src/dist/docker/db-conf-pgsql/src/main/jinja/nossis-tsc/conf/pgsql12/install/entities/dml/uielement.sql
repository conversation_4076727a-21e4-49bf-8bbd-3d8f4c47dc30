-- search
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 8, 1, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (2, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'description', 'INPUT', 'FULL', 6, 1, NULL, 9, 1, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (3, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'state', 'MULTI_SELECT', 'FULL', 6, 1, NULL, 1000, 1, 50, 0);

-- version create
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (100, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 8, 2, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (101, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'description', 'TEXT_AREA', 'FULL', 6, 1, NULL, 9, 2, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (102, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'state', 'INPUT', 'FULL', 6, 0, NULL, 1000, 2, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (103, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'characteristics_placeholder', 'DYNAMIC_CONTENT', 'FULL', 3, 1, null, 6, 2, 1, 0);

-- version edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (105, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 8, 4, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (106, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'description', 'TEXT_AREA', 'FULL', 6, 1, NULL, 9, 4, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (107, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'state', 'INPUT', 'FULL', 6, 0, NULL, 1000, 4, 0, 0);
-- characteristics (dummy)
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (108, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 1001, 4, 1, 0);

-- version view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (110, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 8, 3, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (111, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'description', 'INPUT', 'FULL', 6, 1, NULL, 9, 3, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (112, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'state', 'INPUT', 'FULL', 6, 0, NULL, 1000, 3, 0, 0);
-- characteristics (dummy)
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (113, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'version_name', 'INPUT', 'FULL', 6, 1, NULL, 1001, 3, 1, 0);

-- search attributes
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (200, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_name', 'INPUT', 'FULL', 6, 1, NULL, 1100, 80, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (201, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'attribute_description', 'INPUT', 'FULL', 6, 1, NULL, 1101, 80, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (202, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'attribute_type', 'SELECT', 'FULL', 6, 1, NULL, 1102, 80, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (203, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 3, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 100, null, 80, 50, 0);

-- attribute type view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (300, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_name', 'INPUT', 'FULL', 6, 1, NULL, 1201, 100, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (301, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'type', 'INPUT', 'FULL', 6, 1, NULL, 1200, 100, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (302, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'version', 'DYNAMIC_CONTENT', 'FULL', 6, 0, 201, NULL, 100, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (303, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_description', 'TEXT_AREA', 'FULL', 3, 1, NULL, 1202, 100, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (304, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'tags', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 200, null, 100, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (305, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'specific_placeholder', 'DYNAMIC_CONTENT', 'FULL', 3, 1, null, 6, 100, 101, 0);

-- attribute type create
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (320, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_name', 'INPUT', 'FULL', 6, 1, NULL, 1201, 101, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (321, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'type', 'SELECT', 'FULL', 6, 1, NULL, 1200, 101, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (322, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'version', 'INPUT', 'FULL', 6, 0, 201, NULL, 101, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (323, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_description', 'TEXT_AREA', 'FULL', 2, 1, NULL, 1202, 101, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (324, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 200, null, 101, 100, 0);

-- attribute type edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (340, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_name', 'INPUT', 'FULL', 6, 1, NULL, 1201, 102, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (341, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'type', 'SELECT', 'FULL', 6, 1, NULL, 1200, 102, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (342, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'version', 'INPUT', 'FULL', 6, 0, 201, NULL, 102, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (343, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'attribute_description', 'TEXT_AREA', 'FULL', 2, 1, NULL, 1202, 102, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (344, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 200, null, 102, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (345, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'specific_placeholder', 'DYNAMIC_CONTENT', 'FULL', 3, 1, null, 6, 102, 101, 0);

-- cfs search
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (400, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'cfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1110, 20, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (401, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'cfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1111, 20, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (402, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 400, NULL, 20, 50, 0);

-- cfs view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (420, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 400, NULL, 21, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (421, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 402, NULL, 21, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (422, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'cfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1110, 21, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (423, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'cfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1111, 21, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (424, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'cfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1111, 21, 150, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (425, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 3, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 401, NULL, 21, 151, 0);

-- cfs create
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (440, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 400, NULL, 22, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (441, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 402, NULL, 22, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (442, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'cfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1110, 22, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (443, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'cfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1111, 22, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (444, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 401, NULL, 22, 151, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (445, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'cfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1111, 22, 150, 0);

-- cfs edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (460, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 400, NULL, 23, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (461, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 402, NULL, 23, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (462, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'cfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1110, 23, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (463, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'cfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1111, 23, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (464, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 401, NULL, 23, 151, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (465, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'cfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1111, 23, 150, 0);

-- rfs search
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (600, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1120, 40, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (601, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1121, 40, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (602, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 500, NULL, 40, 50, 0);

-- rfs view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (620, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 500, NULL, 41, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (621, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 502, NULL, 41, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (622, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1120, 41, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (623, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1121, 41, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (624, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'rfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1121, 41, 200, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (625, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 3, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 501, NULL, 41, 201, 0);

-- rfs create
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (640, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 500, NULL, 42, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (641, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 502, NULL, 42, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (642, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1120, 42, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (643, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1121, 42, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (644, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 501, NULL, 42, 201, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (645, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'rfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1121, 42, 200, 0);

-- rfs edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (660, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 500, NULL, 43, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (661, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 502, NULL, 43, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (662, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1120, 43, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (663, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1121, 43, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (664, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 501, NULL, 43, 201, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (665, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'rfs_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1121, 43, 200, 0);

-- resource search
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (800, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'resource_name', 'INPUT', 'FULL', 6, 1, NULL, 1130, 60, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (801, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'resource_description', 'INPUT', 'FULL', 6, 1, NULL, 1131, 60, 50, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (802, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 600, NULL, 60, 50, 0);

-- resource view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (820, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 6, 1, 600, NULL, 61, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (821, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 602, NULL, 61, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (822, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'resource_name', 'INPUT', 'FULL', 6, 1, NULL, 1130, 61, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (823, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'resource_description', 'INPUT', 'FULL', 6, 1, NULL, 1131, 61, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (824, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'resource_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1131, 61, 250, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (825, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 3, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 601, NULL, 61, 251, 0);

-- resource create
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (840, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 600, NULL, 62, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (841, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 602, NULL, 62, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (842, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1130, 62, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (843, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1131, 62, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (844, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 601, NULL, 62, 251, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (845, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'resources_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1131, 62, 250, 0);

-- resource edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (860, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 600, NULL, 63, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (861, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 602, NULL, 63, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (862, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'rfs_name', 'INPUT', 'FULL', 6, 1, NULL, 1130, 63, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (863, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'rfs_description', 'INPUT', 'FULL', 6, 1, NULL, 1131, 63, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (864, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 601, NULL, 63, 251, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (865, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'resources_diagram', 'DYNAMIC_CONTENT', 'FULL', 6, 1, NULL, 1131, 63, 250, 0);

-- component view
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1000, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 2, 'tags', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 700, NULL, 120, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1001, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 0, 'version', 'INPUT', 'FULL', 6, 0, 702, NULL, 120, 100, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1002, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'component_name', 'INPUT', 'FULL', 6, 1, NULL, 1100, 120, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1003, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'component_type', 'INPUT', 'FULL', 6, 1, NULL, 1253, 120, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1004, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 1, 'component_description', 'INPUT', 'FULL', 3, 1, NULL, 1101, 120, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1005, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 3, 'atributes', 'DYNAMIC_CONTENT', 'FULL', 3, 1, 701, NULL, 120, 300, 0);

-- component edit
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1020, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'component_name', 'INPUT', 'FULL', 6, 1, NULL, 1251, 121, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1021, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'version', 'INPUT', 'FULL', 6, 0, 702, NULL, 121, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1022, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'component_description', 'TEXT_AREA', 'FULL', 2, 1, NULL, 1252, 121, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1023, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'tags', 'DYNAMIC_CONTENT', 'FULL', 2, 1, 700, NULL, 121, 100, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1024, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'specific_placeholder', 'DYNAMIC_CONTENT', 'FULL', 3, 1, NULL, 6, 121, 101, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1025, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'component_id', 'INPUT', 'FULL', 3, 0, NULL, 1, 121, 0, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1026, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 3, 'type', 'SELECT', 'FULL', 6, 1, NULL, 1253, 121, 0, 1);
