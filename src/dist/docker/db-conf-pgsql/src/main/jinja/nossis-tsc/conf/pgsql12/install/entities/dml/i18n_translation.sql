------------------------------------------------------------------------------------------------------------------------
-- System inherited attribute internationalization translation
------------------------------------------------------------------------------------------------------------------------

-- attr : name
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (50000, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Nome', 167);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (50001, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Name', 167);
-- attr : description
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (50002, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Descrição', 168);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (50003, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Description', 168);

------------------------------------------------------------------------------------------------------------------------
-- Cat Entities [1000-1999]
------------------------------------------------------------------------------------------------------------------------

-- Tag
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1000, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Tag', 1000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1001, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Tag', 1000);
-- Version
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1002, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Versão', 1001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1003, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Version', 1001);
-- Aggregated Type
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1004, localtimestamp, localtimestamp, 'system', 'system', 1002, 'pt', 'Atributos');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1005, localtimestamp, localtimestamp, 'system', 'system', 1002, 'en', 'Attributes');
-- Attribute Type
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1006, localtimestamp, localtimestamp, 'system', 'system', 1003, 'pt', 'Tipo');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1007, localtimestamp, localtimestamp, 'system', 'system', 1003, 'en', 'Type');
-- Cfs
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1008, localtimestamp, localtimestamp, 'system', 'system', 1004, 'pt', 'Cfs');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1009, localtimestamp, localtimestamp, 'system', 'system', 1004, 'en', 'Cfs');
-- Rfs
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1010, localtimestamp, localtimestamp, 'system', 'system', 1005, 'pt', 'Rfs');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1011, localtimestamp, localtimestamp, 'system', 'system', 1005, 'en', 'Rfs');
-- Resource
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1012, localtimestamp, localtimestamp, 'system', 'system', 1006, 'pt', 'Recurso');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1013, localtimestamp, localtimestamp, 'system', 'system', 1006, 'en', 'Resource');
-- Component
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1014, localtimestamp, localtimestamp, 'system', 'system', 1007, 'pt', 'Componente');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1015, localtimestamp, localtimestamp, 'system', 'system', 1007, 'en', 'Component');
-- Entities
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1016, localtimestamp, localtimestamp, 'system', 'system', 1008, 'pt', 'Entidades');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1017, localtimestamp, localtimestamp, 'system', 'system', 1008, 'en', 'Entities');

-----------------------------
-- Constraints [1100-1199] --
-----------------------------

-- Constraints
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1100, localtimestamp, localtimestamp, 'system', 'system', 1100, 'pt', 'Restrição');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1101, localtimestamp, localtimestamp, 'system', 'system', 1100, 'en', 'Constraint');

-- Range Constraints
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1120, localtimestamp, localtimestamp, 'system', 'system', 1101, 'pt', 'Intervalo');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1121, localtimestamp, localtimestamp, 'system', 'system', 1101, 'en', 'Range');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1122, localtimestamp, localtimestamp, 'system', 'system', 1102, 'pt', 'Intervalo mínimo');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1123, localtimestamp, localtimestamp, 'system', 'system', 1102, 'en', 'Min range');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1124, localtimestamp, localtimestamp, 'system', 'system', 1103, 'pt', 'Intervalo máximo');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1125, localtimestamp, localtimestamp, 'system', 'system', 1103, 'en', 'Max range');

-- Size Constraints
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1140, localtimestamp, localtimestamp, 'system', 'system', 1104, 'pt', 'Dimensão');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1141, localtimestamp, localtimestamp, 'system', 'system', 1104, 'en', 'Size');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1142, localtimestamp, localtimestamp, 'system', 'system', 1105, 'pt', 'Dimensão mínima');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1143, localtimestamp, localtimestamp, 'system', 'system', 1105, 'en', 'Min size');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1144, localtimestamp, localtimestamp, 'system', 'system', 1106, 'pt', 'Dimensão máxima');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1145, localtimestamp, localtimestamp, 'system', 'system', 1106, 'en', 'Max size');

-- Regex Constraints
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1160, localtimestamp, localtimestamp, 'system', 'system', 1107, 'pt', 'Expressão regular');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1161, localtimestamp, localtimestamp, 'system', 'system', 1107, 'en', 'Regular expression');

-- Enum Constraints
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1180, localtimestamp, localtimestamp, 'system', 'system', 1108, 'pt', 'Enumerado');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1181, localtimestamp, localtimestamp, 'system', 'system', 1108, 'en', 'Enumerate');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1182, localtimestamp, localtimestamp, 'system', 'system', 1109, 'pt', 'Valor');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1183, localtimestamp, localtimestamp, 'system', 'system', 1109, 'en', 'Value');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1184, localtimestamp, localtimestamp, 'system', 'system', 1110, 'pt', 'Posição');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1185, localtimestamp, localtimestamp, 'system', 'system', 1110, 'en', 'Position');

-- Float Constraint
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1200, localtimestamp, localtimestamp, 'system', 'system', 1111, 'pt', 'Float');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (1201, localtimestamp, localtimestamp, 'system', 'system', 1111, 'en', 'Float');

---------------------------
-- Relations [2000-2099] --
---------------------------

-- Entity Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2000, localtimestamp, localtimestamp, 'system', 'system', 1200, 'pt', 'Relação de entidades');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2001, localtimestamp, localtimestamp, 'system', 'system', 1200, 'en', 'Entity relation');

-- Entity Relation Type
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2020, localtimestamp, localtimestamp, 'system', 'system', 1201, 'pt', 'Tipo de relação');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2021, localtimestamp, localtimestamp, 'system', 'system', 1201, 'en', 'Relation type');

-- Cfs Cfs Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2040, localtimestamp, localtimestamp, 'system', 'system', 1202, 'pt', 'Relação entre cfs');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2041, localtimestamp, localtimestamp, 'system', 'system', 1202, 'en', 'Cfs relation');
-- Cfs Rfs Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2042, localtimestamp, localtimestamp, 'system', 'system', 1203, 'pt', 'Relação entre cfs e rfs');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2043, localtimestamp, localtimestamp, 'system', 'system', 1203, 'en', 'Cfs rfs relation');
-- Cfs Resource Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2044, localtimestamp, localtimestamp, 'system', 'system', 1204, 'pt', 'Relação entre cfs e resource');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2045, localtimestamp, localtimestamp, 'system', 'system', 1204, 'en', 'Cfs resource relation');

-- Rfs Rfs Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2060, localtimestamp, localtimestamp, 'system', 'system', 1206, 'pt', 'Relação entre rfs');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2061, localtimestamp, localtimestamp, 'system', 'system', 1206, 'en', 'Rfs relation');
-- Rfs Resource Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2062, localtimestamp, localtimestamp, 'system', 'system', 1207, 'pt', 'Relação entre rfs e recursos');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2063, localtimestamp, localtimestamp, 'system', 'system', 1207, 'en', 'Rfs resources relation');

-- Resource Resource Relation
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2080, localtimestamp, localtimestamp, 'system', 'system', 1209, 'pt', 'Relação entre recursos');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (2081, localtimestamp, localtimestamp, 'system', 'system', 1209, 'en', 'Resource relation');

------------------------------------------------------------------------------------------------------------------------
-- Cat Attribute Types [3000-3999]
------------------------------------------------------------------------------------------------------------------------

-- Attribute Types List
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (3000, localtimestamp, localtimestamp, 'system', 'system', 2000, 'pt', 'Lista de tipos de atributos');
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (3001, localtimestamp, localtimestamp, 'system', 'system', 2000, 'en', 'Attribute types list');

------------------------------------------------------------------------------------------------------------------------
-- Cat Enum Attribute Values [4000-4999]
------------------------------------------------------------------------------------------------------------------------

-- Attribute Type Date
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4000, localtimestamp, localtimestamp, 'system', 'system', 3000, 'pt', 'data');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4001, localtimestamp, localtimestamp, 'system', 'system', 3000, 'en', 'date');

-- Attribute Type String
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4002, localtimestamp, localtimestamp, 'system', 'system', 3001, 'pt', 'string');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4003, localtimestamp, localtimestamp, 'system', 'system', 3001, 'en', 'string');

-- Attribute Type Number
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4004, localtimestamp, localtimestamp, 'system', 'system', 3002, 'pt', 'numérico');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4005, localtimestamp, localtimestamp, 'system', 'system', 3002, 'en', 'number');

-- Attribute Type Float
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4006, localtimestamp, localtimestamp, 'system', 'system', 3003, 'pt', 'float');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4007, localtimestamp, localtimestamp, 'system', 'system', 3003, 'en', 'float');

-- Attribute Type Enumeration
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4008, localtimestamp, localtimestamp, 'system', 'system', 3004, 'pt', 'enumerador');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4009, localtimestamp, localtimestamp, 'system', 'system', 3004, 'en', 'enumeration');

-- Attribute Type Compound
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4010, localtimestamp, localtimestamp, 'system', 'system', 3005, 'pt', 'componente');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (4011, localtimestamp, localtimestamp, 'system', 'system', 3005, 'en', 'compound');

------------------------------------------------------------------------------------------------------------------------
-- Version States [5000-5999]
------------------------------------------------------------------------------------------------------------------------

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5000, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Desenvolvimento', 4000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5001, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Development', 4000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5002, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Testes', 4001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5003, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Testing', 4001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5004, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Abandonada', 4002);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5005, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Abandoned', 4002);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5006, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Produção', 4003);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5007, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Production', 4003);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5008, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Bloqueada', 4004);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5009, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Blocked', 4004);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5010, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Obsoleta', 4005);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (5011, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Deprecated', 4005);

------------------------------------------------------------------------------------------------------------------------
-- Other [6000-6999]
------------------------------------------------------------------------------------------------------------------------

-- Version.STATE
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6000, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Estado', 5000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6001, localtimestamp, localtimestamp, 'system', 'system', 'en', 'State', 5000);
-- Version.NAME
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6002, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Nome', 5001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6003, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Name', 5001);

-- Attribute
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6004, localtimestamp, localtimestamp, 'system', 'system', 5002, 'pt', 'Atributo');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6005, localtimestamp, localtimestamp, 'system', 'system', 5002, 'en', 'Attribute');
-- Attribute.OPTIONAL
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6006, localtimestamp, localtimestamp, 'system', 'system', 5003, 'pt', 'Opcional');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6007, localtimestamp, localtimestamp, 'system', 'system', 5003, 'en', 'Optional');

-- Characteristics
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6008, localtimestamp, localtimestamp, 'system', 'system', 5004, 'pt', 'Características');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6009, localtimestamp, localtimestamp, 'system', 'system', 5004, 'en', 'Characteristics');

-- General characteristics
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6010, localtimestamp, localtimestamp, 'system', 'system', 5005, 'pt', 'Caracterização base');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6011, localtimestamp, localtimestamp, 'system', 'system', 5005, 'en', 'General characteristics');

-- Attribute definition
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6012, localtimestamp, localtimestamp, 'system', 'system', 5006, 'pt', 'Caraterização específica');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6013, localtimestamp, localtimestamp, 'system', 'system', 5006, 'en', 'Attribute definition');

-- Composition Diagram
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6014, localtimestamp, localtimestamp, 'system', 'system', 5007, 'pt', 'Diagrama de composição');
INSERT INTO I18N_TRANSLATION (id, created_at, updated_at, user_create, user_update, id_label, locale, translation)
VALUES (6015, localtimestamp, localtimestamp, 'system', 'system', 5007, 'en', 'Composition diagram');

-- Version.STATE_DATE
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6016, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Actualização do estado', 5008);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (6017, localtimestamp, localtimestamp, 'system', 'system', 'en', 'State update', 5008);