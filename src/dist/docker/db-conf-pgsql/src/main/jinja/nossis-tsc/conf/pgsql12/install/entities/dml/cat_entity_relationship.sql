------------------------------------------------------------------------------------------------------------------------
-- Aggregated Types [100-199]
------------------------------------------------------------------------------------------------------------------------

-- Aggregated Type <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (100, localtimestamp, localtimestamp, 'system', 'system', 'AGGREGATED TYPE TAGS', 'AGGREGATED TYPE TAGS RELATION', 1000000000, 0, NULL, 1000000000, 0, 'tags', 8, 6, 1, 1002, 1000);
-- Aggregated Type <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (101, localtimestamp, localtimestamp, 'system', 'system', 'AGGREGATED TYPE VERSION', 'AGGREGATED TYPE VERSION RELATION', 1000000000, 0, NULL, 1, 1, 'version', 8, 7, 1, 1002, 1001);

-- Aggregated Entity <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (102, localtimestamp, localtimestamp, 'system', 'system', 'AGGREGATED ENTITY TAGS', 'AGGREGATED ENTITY TAGS RELATION', 1000000000, 0, NULL, 1000000000, 0, 'tags', 30, 6, 1, 1008, 1000);
-- Aggregated Entity <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (103, localtimestamp, localtimestamp, 'system', 'system', 'AGGREGATED ENTITY VERSION', 'AGGREGATED ENTITY VERSION RELATION', 1000000000, 0, NULL, 1, 1, 'version', 30, 7, 1, 1008, 1001);

------------------------------------------------------------------------------------------------------------------------
-- Attribute Types [200-299]
------------------------------------------------------------------------------------------------------------------------

-- Attribute Type <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (200, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_TAGS', 'ATTRTYPE_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 9, 1, 1000, 1003);
-- Attribute Type <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (201, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_VERSION', 'ATTRTYPE_VERSION RELATION', 1, 1, 'version', 1, 0, NULL , 7, 9, 1, 1001, 1003);
-- Attribute <-> attribute type
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (202, localtimestamp, localtimestamp, 'system', 'system', 'ATTRIBUTE_ATTRTYPE', 'ATTRIBUTE_ATTRTYPE RELATION', 1, 0, 'atributeType', 1000000000, 0, 'atributes' , 9, 16, 1, 1003, 5002);
-- Attribute <-> component type
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (203, localtimestamp, localtimestamp, 'system', 'system', 'ATTRIBUTE_COMPTYPE', 'ATTRIBUTE_COMPTYPE RELATION', 1, 0, 'componentType', 1000000000, 0, 'atributesType' , 20, 16, 1, 1003, 5002);
-- Attribute <-> tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (204, localtimestamp, localtimestamp, 'system', 'system', 'ATTRIBUTE_TAGS', 'ATTRIBUTE_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 16, 1, 1000, 5002);

------------------------------------------------------------------------------------------------------------------------
-- Constraints [300-399]
------------------------------------------------------------------------------------------------------------------------

-- Attribute Type <-> Constraints
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (300, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_RANGECONSTRAINTS', 'ATTRTYPE_RANGECONSTRAINTS RELATION', 1000000000, 0, 'constraints', 1, 0, NULL , 11, 9, 0, 1101, 1003);
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (301, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_SIZECONSTRAINTS', 'ATTRTYPE_SIZEECONSTRAINTS RELATION', 1000000000, 0, 'constraints', 1, 0, NULL , 12, 9, 0, 1104, 1003);
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (302, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_REGEXPCONSTRAINTS', 'ATTRTYPE_REGEXPCONSTRAINTS RELATION', 1000000000, 0, 'constraints', 1, 0, NULL , 13, 9, 0, 1107, 1003);
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (303, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_ENUMCONSTRAINTS', 'ATTRTYPE_ENUMCONSTRAINTS RELATION', 1000000000, 0, 'constraints', 1, 0, NULL, 14, 9, 0, 1108, 1003);
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (304, localtimestamp, localtimestamp, 'system', 'system', 'ATTRTYPE_FLOATRANGECONSTRAINTS', 'ATTRTYPE_FLOATRANGECONSTRAINTS RELATION', 1000000000, 0, 'constraints', 1, 0, NULL, 29, 9, 0, 1111, 1003);

-- Enum Constraint <-> Values
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (325, localtimestamp, localtimestamp, 'system', 'system', 'ENUMCONSTRAINTS_VALUES', 'ENUMCONSTRAINTS_VALUES RELATION', 1000000000, 0, 'valueList', 1, 0, NULL , 15, 14, 1, 1108, 1109);

------------------------------------------------------------------------------------------------------------------------
-- Cfs [400-499]
------------------------------------------------------------------------------------------------------------------------

-- Cfs <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (400, localtimestamp, localtimestamp, 'system', 'system', 'CFS_TAGS', 'CFS_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 17, 1, 1000, 1004);
-- Cfs <-> Attribute
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (401, localtimestamp, localtimestamp, 'system', 'system', 'CFS_ATTRIBUTES', 'CFS_ATTRIBUTES RELATION', 1000000000, 0, 'atributes', 1, 1, NULL , 16, 17, 1, 5002, 1004);
-- Cfs <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (402, localtimestamp, localtimestamp, 'system', 'system', 'CFS_VERSION', 'CFS_VERSION RELATION', 1, 1, 'version', 1000000000, 0, NULL , 7, 17, 1, 1001, 1004);

-- Cfs <-> CfsCfsRelation - childCfs
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (403, localtimestamp, localtimestamp, 'system', 'system', 'CFS_CHILD_CFS', 'CFS_CHILD_CFS RELATION', 1000000000, 0, 'childCfs', 1, 0, NULL , 23, 17, 1, 1202, 1004);
-- EntityRelation <-> Cfs childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (404, localtimestamp, localtimestamp, 'system', 'system', 'CFSCFSRELATION_CHILD_CFS', 'CFSCFSRELATION_CHILD_CFS RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childCfs', 17, 23, 1, 1004, 1202);
-- EntityRelation <-> Cfs parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (405, localtimestamp, localtimestamp, 'system', 'system', 'CFSCFSRELATION_PARENT_CFS', 'CFSCFSRELATION_PARENT_CFS RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentCfs', 17, 23, 1, 1004, 1202);

-- Cfs <-> CfsRfsRelation - childRfs
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (406, localtimestamp, localtimestamp, 'system', 'system', 'CFS_CHILD_RFS', 'CFS_CHILD_RFS RELATION', 1000000000, 0, 'childRfs', 1, 0, NULL , 27, 17, 1, 1203, 1004);
-- EntityRelation <-> Rfs childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (407, localtimestamp, localtimestamp, 'system', 'system', 'CFSRFSRELATION_CHILD_RFS', 'CFSRFSRELATION_CHILD_RFS RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childRfs', 17, 27, 1, 1004, 1203);
-- EntityRelation <-> Cfs parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (408, localtimestamp, localtimestamp, 'system', 'system', 'CFSRFSRELATION_PARENT_CFS', 'CFSRFSRELATION_PARENT_CFS RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentCfs', 18, 27, 1, 1005, 1203);

-- Cfs <-> CfsResourceRelation - childResource
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (409, localtimestamp, localtimestamp, 'system', 'system', 'CFS_CHILD_RESOURCE', 'CFS_CHILD_RESOURCE RELATION', 1000000000, 0, 'childResources', 1, 0, NULL , 26, 17, 1, 1204, 1004);
-- EntityRelation <-> Resource childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (410, localtimestamp, localtimestamp, 'system', 'system', 'CFSRESOURCERELATION_CHILD_RESOURCE', 'CFSRESOURCERELATION_CHILD_RESOURCE RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childResources', 17, 26, 1, 1004, 1204);
-- EntityRelation <-> Cfs parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (411, localtimestamp, localtimestamp, 'system', 'system', 'CFSRESOURCERELATION_PARENT_CFS', 'CFSRESOURCERELATION_PARENT_CFS RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentCfs', 19, 26, 1, 1006, 1204);

------------------------------------------------------------------------------------------------------------------------
-- Rfs [500-599]
------------------------------------------------------------------------------------------------------------------------

-- Rfs <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (500, localtimestamp, localtimestamp, 'system', 'system', 'RFS_TAGS', 'RFS_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 18, 1, 1000, 1005);
-- Rfs <-> Attribute
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (501, localtimestamp, localtimestamp, 'system', 'system', 'RFS_ATTRIBUTES', 'RFS_ATTRIBUTES RELATION', 1000000000, 0, 'atributes', 1, 1, NULL , 16, 18, 1, 5002, 1005);
-- Rfs <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (502, localtimestamp, localtimestamp, 'system', 'system', 'RFS_VERSION', 'RFS_VERSION RELATION', 1, 1, 'version', 1000000000, 0, NULL , 7, 18, 1, 1001, 1005);

-- Rfs <-> RfsRfsRelation - childRfs
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (503, localtimestamp, localtimestamp, 'system', 'system', 'RFS_CHILD_RFS', 'RFS_CHILD_RFS RELATION', 1000000000, 0, 'childRfs', 1, 0, NULL , 24, 18, 1, 1206, 1005);
-- EntityRelation <-> Rfs childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (504, localtimestamp, localtimestamp, 'system', 'system', 'RFSRFSRELATION_CHILD_RFS', 'RFSRFSRELATION_CHILD_RFS RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childRfs', 18, 24, 1, 1005, 1206);
-- EntityRelation <-> Rfs parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (505, localtimestamp, localtimestamp, 'system', 'system', 'RFSRFSRELATION_PARENT_RFS', 'RFSRFSRELATION_PARENT_RFS RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentRfs', 18, 24, 1, 1005, 1206);

-- Rfs <-> RfsResourceRelation - childResource
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (506, localtimestamp, localtimestamp, 'system', 'system', 'RFS_CHILD_RESOURCE', 'RFS_CHILD_RESOURCE RELATION', 1000000000, 0, 'childResources', 1, 0, NULL , 28, 18, 1, 1207, 1005);
-- EntityRelation <-> Resource childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (507, localtimestamp, localtimestamp, 'system', 'system', 'RFSRESOURCERELATION_CHILD_RESOURCE', 'RFSRESOURCERELATION_CHILD_RESOURCE RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childResources', 18, 28, 1, 1005, 1207);
-- EntityRelation <-> Rfs parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (508, localtimestamp, localtimestamp, 'system', 'system', 'RFSRESOURCERELATION_PARENT_RFS', 'RFSRESOURCERELATION_PARENT_RFS RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentRfs', 19, 28, 1, 1006, 1207);

------------------------------------------------------------------------------------------------------------------------
-- Resource [600-699]
------------------------------------------------------------------------------------------------------------------------

-- Resource <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (600, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCE_TAGS', 'RESOURCE_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 19, 1, 1000, 1006);
-- Resource <-> Attribute
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (601, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCE_ATTRIBUTES', 'RESOURCE_ATTRIBUTES RELATION', 1000000000, 0, 'atributes', 1, 1, NULL , 16, 19, 1, 5002, 1006);
-- Resource <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (602, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCE_VERSION', 'RESOURCE_VERSION RELATION', 1, 1, 'version', 1000000000, 0, NULL , 7, 19, 1, 1001, 1006);

-- Resource <-> ResourceResourceRelation - childResources
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (603, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCE_CHILD_RESOURCE', 'RESOURCE_CHILD_RESOURCE RELATION', 1000000000, 0, 'childResources', 1, 0, NULL , 25, 19, 1, 1209, 1006);
-- EntityRelation <-> Resource childEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (604, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCERESOURCERELATION_CHILD_RESOURCE', 'RESOURCERESOURCERELATION_CHILD_RESOURCE RELATION', 1, 0, 'childEntity', 1000000000, 0, 'childResources', 19, 25, 1, 1006, 1209);
-- EntityRelation <-> Resource parentEntity
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (605, localtimestamp, localtimestamp, 'system', 'system', 'RESOURCERESOURCERELATION_PARENT_RESOURCE', 'RESOURCERESOURCERELATION_PARENT_RESOURCE RELATION', 1, 0, 'parentEntity', 1000000000, 0, 'parentResources', 19, 25, 1, 1006, 1209);

------------------------------------------------------------------------------------------------------------------------
-- Component [700-799]
------------------------------------------------------------------------------------------------------------------------

-- Component <-> Tags
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (700, localtimestamp, localtimestamp, 'system', 'system', 'COMPONENT_TAGS', 'COMPONENT_TAGS RELATION', 1000000000, 0, 'tags', 1000000000, 0, NULL , 6, 20, 1, 1000, 1007);
-- Component <-> Attribute
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (701, localtimestamp, localtimestamp, 'system', 'system', 'COMPONENT_ATTRIBUTES', 'COMPONENT_ATTRIBUTES RELATION', 1000000000, 0, 'atributes', 1, 1, NULL , 16, 20, 1, 5002, 1007);
-- Component <-> Version
INSERT INTO cat_entity_relationship (id, created_at, updated_at, user_create, user_update, name, description, max_a_cardinality, min_a_cardinality, role_a_name, max_b_cardinality, min_b_cardinality, role_b_name, id_side_a, id_side_b, to_validate, id_i18n_label_a, id_i18n_label_b)
VALUES (702, localtimestamp, localtimestamp, 'system', 'system', 'COMPONENT_VERSION', 'COMPONENT_VERSION RELATION', 1, 1, 'version', 1000000000, 0, NULL , 7, 20, 1, 1001, 1007);