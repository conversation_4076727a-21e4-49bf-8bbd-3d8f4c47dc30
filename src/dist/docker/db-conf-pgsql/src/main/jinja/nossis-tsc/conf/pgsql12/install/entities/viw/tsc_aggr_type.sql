--------------------------------------------------------
--  DDL for View TSC_AGGREGATED_TYPE
--------------------------------------------------------
CREATE OR REPLACE VIEW tsc_aggr_type AS
  SELECT
    attrtype.id,
    attrtype.name,
    attrtype.created_at,
    attrtype.updated_at,
    attrtype.user_create,
    attrtype.user_update,
    attrtype.description,
    attrtype.version_id,
    8 as id_cat_entity,
    attrtype.id_cat_entity as TYPE_CAT_ID,
    cast(enumtype.id as character varying) AS type_id,
    attrtype.external_code
  FROM (
         SELECT
           atype.id,
           atype.name,
           atype.created_at,
           atype.updated_at,
           atype.user_create,
           atype.user_update,
           atype.description,
           atype.version_id,
           atype.id_cat_entity,
           atype.type,
           external_code
         FROM tsc_attr_type atype
         UNION ALL
         SELECT
           ctype.id,
           ctype.name,
           ctype.created_at,
           ctype.updated_at,
           ctype.user_create,
           ctype.user_update,
           ctype.description,
           ctype.version_id,
           ctype.id_cat_entity,
           '2005',
           external_code
         FROM tsc_component ctype
       ) attrtype, cat_enum_attribute_value enumtype
  WHERE attrtype.type = cast(enumtype.id AS CHARACTER VARYING);