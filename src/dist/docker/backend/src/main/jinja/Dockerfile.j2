{% set ud_version = project.version.value|cut('-SNAPSHOT') %}
{% set is_snapshot = project.version.value is string_endingwith('-SNAPSHOT') %}
{% set nacommons_version = version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') %}
{% set nossisinv_version = version.pt.ptinovacao.get('nossis-inv').value|cut('-SNAPSHOT') %}
{% set is_nacommons_snapshot = version.pt.ptinovacao.get('na-commons').value is string_endingwith('-SNAPSHOT') %}
FROM rdocker.ptin.corppt.com/na-commons{% if is_nacommons_snapshot %}-tst{% endif %}/na-commons-{{ docker.os.value }}-jdk8:{{ nacommons_version }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ ud_version }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if is_snapshot %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

USER root

COPY --chown=2016:2016 files/nossis-tsc/conf /opt/alticelabs/nossis-tsc/conf/nossis-tsc-backend
COPY --chown=2016:2016 files/nossis-tsc /opt/alticelabs/nossis-tsc
COPY --chown=2016:2016 files/entrypoint.sh /entrypoint.sh
COPY --chown=2016:2016 config/all.yml /config/all.yml
COPY --chown=2016:2016 config/defaults-nossis-tsc-backend.yml /config/defaults-nossis-tsc-backend.yml
COPY --chown=2016:2016 config/environment.yml /config/environment.yml

RUN (groupadd -f alabs || true) && groupmod -o -g 2016 alabs && \
    (useradd -f -1 -g alabs alabs || true) && usermod -o -u 2016 alabs && \
    mkdir -p /var/log/alticelabs/nossis-tsc /var/opt/alticelabs/nossis-tsc /var/opt/alticelabs/nossis-inv && \
    chown -R 2016:2016 /opt/alticelabs/nossis-tsc /opt/alticelabs/pdsngtools /var/log/alticelabs/nossis-tsc /var/opt/alticelabs/nossis-tsc /var/opt/alticelabs/nossis-inv && \
    chmod +x /entrypoint.sh

ENV TEMPLATE_DIRS=""
ENV DEFAULTS_INV=""

USER 2016:2016

ENTRYPOINT ["/entrypoint.sh"]