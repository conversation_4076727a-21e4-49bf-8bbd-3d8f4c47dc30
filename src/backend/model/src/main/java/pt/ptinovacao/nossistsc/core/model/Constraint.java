package pt.ptinovacao.nossistsc.core.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractBusinessEntityWithAttributes;
import pt.ptinovacao.netwin.backend.entities.httpapi.rest.ResourceName;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;

import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "TSC_CONSTRAINT")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "TYPE", discriminatorType = DiscriminatorType.STRING)
@ResourceName(entityName = "Constraint", resourceName =  "constraints", singleName = "constraint")
@JsonSubTypes({
        @JsonSubTypes.Type(value = EnumConstraint.class, name = "ENUMC"),
        @JsonSubTypes.Type(value = RangeConstraint.class, name = "RANGE"),
        @JsonSubTypes.Type(value = FloatRangeConstraint.class, name = "FLOAT_RANGE"),
        @JsonSubTypes.Type(value = RegExpConstraint.class, name= "REGEXP"),
        @JsonSubTypes.Type(value = SizeConstraint.class, name = "SIZE")
})
@JsonTypeInfo( use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
        property = "type")
public abstract class Constraint extends AbstractBusinessEntityWithAttributes {

    private AttributeType attributeType;

    private String type;

    @Column(name = "TYPE", updatable = false, insertable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @ManyToOne()
    public AttributeType getAttributeType() {
        return attributeType;
    }

    public void setAttributeType(AttributeType attributeType) {
        this.attributeType = attributeType;
    }

    @Override
    public void instantiateDynAttributes(CatEntity catEntity) {
    }

    @Override
    public void prePersist(CatEntity catEntity) {
        Date now = new Date();
        if (getCreatedAt() == null) {
            setCreatedAt(now);
        }
        setUpdatedAt(now);
    }

    public Constraint clone() {
        try {
            Constraint clone = getClass().newInstance();
            clone.setId(getId());
            clone.setIdCatEntity(getIdCatEntity());
            clone.setName(getName());
            clone.setDescription(getDescription());
            clone.setAttributeType(getAttributeType());
            clone.setType(getType());
            clone.setCode(getCode());
            clone.setCreatedAt(getCreatedAt());
            clone.setOperation(getOperation());
            clone.setExternalCode(getExternalCode());
            clone.setUpdatedAt(getUpdatedAt());
            clone.setUserCreate(getUserCreate());
            clone.setUserUpdate(getUserUpdate());
            return  clone;
        } catch (Exception e) {
            return null;
        }
    }
}
