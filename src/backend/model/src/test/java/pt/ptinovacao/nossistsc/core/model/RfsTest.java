package pt.ptinovacao.nossistsc.core.model;

import org.hibernate.Query;
import org.hibernate.Session;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import pt.ptinovacao.nossistsc.SessionFactoryRule;

import java.util.Calendar;

/**
 * Created by hugo-l-pais on 16-05-2017.
 */
public class RfsTest {
    @Rule
    public final SessionFactoryRule sf = new SessionFactoryRule();



    @Test
    @Ignore
    public void cfsCreateWithVersionAndTag_success(){
        Session session = sf.createSession();

//        session.beginTransaction();
//
//        Version version = new Version();
//        version.setCreatedAt(Calendar.getInstance().getTime());
//        version.setUpdatedAt(Calendar.getInstance().getTime());
//        version.setIdCatEntity(1L);
//        version.setName("v1.1.1");
//
//        Tag tag = new Tag();
//        tag.setCreatedAt(Calendar.getInstance().getTime());
//        tag.setUpdatedAt(Calendar.getInstance().getTime());
//        tag.setIdCatEntity(2L);
//        tag.setName("TAG DE TESTE");
//        tag.setDescription("DESC");
//
//        session.save(version);
//
//        session.save(version);
//
//        Cfs cfs = new Cfs();
//        cfs.setVersion(version);
//        cfs.setCreatedAt(Calendar.getInstance().getTime());
//        cfs.setUpdatedAt(Calendar.getInstance().getTime());
//        cfs.setIdCatEntity(1L);
//
//        tag.getTaggedEntities().add(cfs);

    }

    @Ignore @Test
    public void rfsCreate_success_justRfs() {
        Session session = sf.createSession();


        session.beginTransaction();

        Version version = new Version();
        version.setCreatedAt(Calendar.getInstance().getTime());
        version.setUpdatedAt(Calendar.getInstance().getTime());
       // version.setIdCatEntity(1L);
        //version.setName("v1.1.1");
        session.save(version);

        Cfs cfs = new Cfs();
        cfs.setVersion(version);
        cfs.setCreatedAt(Calendar.getInstance().getTime());
        cfs.setUpdatedAt(Calendar.getInstance().getTime());
        cfs.setIdCatEntity(1L);
        cfs.setName("CFS.JOEL");
        session.save(cfs);


        Rfs rfs = new Rfs();
        rfs.setVersion(version);
        rfs.setCreatedAt(Calendar.getInstance().getTime());
        rfs.setUpdatedAt(Calendar.getInstance().getTime());
        rfs.setIdCatEntity(1L);
        rfs.setName("RFS.BATATAS");
        session.save(rfs);
        rfs = new Rfs();
        rfs.setVersion(version);
        rfs.setCreatedAt(Calendar.getInstance().getTime());
        rfs.setUpdatedAt(Calendar.getInstance().getTime());
        rfs.setIdCatEntity(1L);
        rfs.setName("RFS.BATATAS");
        session.save(rfs);

        Rfs rfs2 = (Rfs) session.get(Rfs.class, 2L);

        Component component = new Component();
        component.setVersion(version);
        component.setCreatedAt(Calendar.getInstance().getTime());
        component.setUpdatedAt(Calendar.getInstance().getTime());
        component.setIdCatEntity(2L);
        component.setName("component1");
        //component.add(rfs2);
        session.save(component);

        System.out.println(rfs2);
        Attribute attribute = new Attribute();
        attribute.setCreatedAt(Calendar.getInstance().getTime());
        attribute.setUpdatedAt(Calendar.getInstance().getTime());
        attribute.setIdCatEntity(2L);
        attribute.setName("attribute1");

        RfsRfsRelation er = new RfsRfsRelation();
        er.setParentEntity(rfs);
        er.setChildEntity(rfs2);
        er.setRelationType("ASSOCIATION");
        er.setCreatedAt(Calendar.getInstance().getTime());
        er.setUpdatedAt(Calendar.getInstance().getTime());
        session.save(er);


        //attribute.getEntities().add(component);
        session.save(attribute);
        session.getTransaction().commit();

        session.refresh(rfs);
        session.refresh(cfs);

        Query query = session.createQuery("FROM Component comp JOIN comp.entities ent where ent.id = 1");
        // Component comps = (Component) query.list().get(0);

        rfs = (Rfs) session.get(Rfs.class, 2L);
        if(query != null && query.list() != null && !query.list().isEmpty())


        System.out.println(rfs);
    }

}