package pt.ptinovacao.nossistsc.core.implementation.services.clone;

import pt.ptinovacao.asf.componentsmanager.Components;
import pt.ptinovacao.netwin.backend.core.httpapi.rest.serializers.GenericSerializer;
import pt.ptinovacao.netwin.kernel.httpapi.rest.MediaType;
import pt.ptinovacao.netwin.kernel.httpapi.rest.RestUtil;
import pt.ptinovacao.netwin.kernel.monitoring.Monitoring;
import pt.ptinovacao.nossistsc.core.implementation.services.AttributeTypeService;
import pt.ptinovacao.nossistsc.core.model.AttributeType;

import javax.inject.Singleton;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import java.util.Map;

@Singleton
@Path("nossistsc/attributeTypes")
public class AttributeTypeCloneService {

    private static final AttributeTypeService ATTRIBUTE_TYPE_SERVICE = Components.get(AttributeTypeService.COMPONENT_NAME, AttributeTypeService.class);
    private static final GenericSerializer SERIALIZER = Components.get(GenericSerializer.COMPONENT_NAME(), GenericSerializer.class);

    @POST
    @Path("/{id}/clone")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_LD_JSON)
    public Response cloneAttributeType(@PathParam("id") Long id,
                                       String payload,
                                       @Context UriInfo uriInfo,
                                       @QueryParam("$expand") String expand,
                                       @QueryParam("$select") String select,
                                       @QueryParam("$exclude") String exclude) throws Exception {
        final long t0 = Monitoring.getTime();
        final Response response;
        if (id != null) {
            final AttributeType clonedAttributeType = ATTRIBUTE_TYPE_SERVICE.cloneAttributeType(id, payload);
            if (clonedAttributeType != null) {
                final Map<String, Object> createdVersionMap = SERIALIZER.serialize(
                        clonedAttributeType,
                        uriInfo.getBaseUri(),
                        SERIALIZER.stringToMap(select),
                        SERIALIZER.stringToMap(exclude),
                        SERIALIZER.stringToMap(expand),
                        false);
                response = RestUtil.getResponseCreated(null, createdVersionMap);
            } else {
                response = RestUtil.getResponseGone();
            }
        } else {
            response = RestUtil.getResponseBadRequest("Cannot clone attributeType", "", false);
        }
        final long t1 = Monitoring.getTime();
        Monitoring.addOkSample(Monitoring.makeKey(VersionCloneService.class.getSimpleName(), "cloneAttributeType"), t1 - t0);
        return response;
    }
}