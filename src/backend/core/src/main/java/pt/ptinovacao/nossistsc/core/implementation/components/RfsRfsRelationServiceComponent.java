package pt.ptinovacao.nossistsc.core.implementation.components;

import com.google.common.base.MoreObjects;
import pt.ptinovacao.netwin.backend.core.restservicesv2.AbstractCrudServiceComponentV2;
import pt.ptinovacao.nossistsc.core.model.RfsRfsRelation;

import java.util.ArrayList;
import java.util.List;

public class RfsRfsRelationServiceComponent extends AbstractCrudServiceComponentV2<RfsRfsRelation> {

    public static final String COMPONENT_NAME = RfsRfsRelationServiceComponent.class.getSimpleName();

    public RfsRfsRelationServiceComponent() {
        super(RfsRfsRelation.class);
    }

    public void createRfsRfsRelation(List<RfsRfsRelation> rfsRfsRelations) {
        for (RfsRfsRelation rfsRfsRelation : MoreObjects.firstNonNull(rfsRfsRelations, new ArrayList<RfsRfsRelation>())) {
            create(rfsRfsRelation);
        }
    }

    public void deleteRfsRfsRelation(List<RfsRfsRelation> rfsRfsRelations) {
        for (RfsRfsRelation rfsRfsRelation : MoreObjects.firstNonNull(rfsRfsRelations, new ArrayList<RfsRfsRelation>())) {
            delete(rfsRfsRelation);
        }
    }

}
