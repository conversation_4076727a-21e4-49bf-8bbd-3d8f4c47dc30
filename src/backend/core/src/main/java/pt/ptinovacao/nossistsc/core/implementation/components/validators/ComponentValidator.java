package pt.ptinovacao.nossistsc.core.implementation.components.validators;


import com.google.common.base.MoreObjects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.asf.componentsmanager.ComponentInit;
import pt.ptinovacao.asf.componentsmanager.Components;
import pt.ptinovacao.netwin.backend.core.persistence.interf.Persistence;
import pt.ptinovacao.netwin.backend.core.persistence.validator.CatalogValidator;
import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractEntity;
import pt.ptinovacao.netwin.backend.modules.catalogeditor.implementation.servicescomponents.CatAttributeTypeServiceComponent;
import pt.ptinovacao.netwin.backend.modules.catalogeditor.implementation.servicescomponents.CatEnumeratedAttributeValueServiceComponent;
import pt.ptinovacao.netwin.kernel.context.Context;
import pt.ptinovacao.nossistsc.core.implementation.components.AttributeTypeComponent;
import pt.ptinovacao.nossistsc.core.implementation.components.ComponentServiceComponent;
import pt.ptinovacao.nossistsc.core.implementation.components.VersionServiceComponent;
import pt.ptinovacao.nossistsc.core.model.Attribute;
import pt.ptinovacao.nossistsc.core.model.CatalogEntityWithAttr;
import pt.ptinovacao.nossistsc.core.model.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.addEntityToPaths;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.fillEmptyRelationTypePathsWithEntity;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.mergeSetsOfPathsByRelationTypes;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.validateAttributes;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.validateRelationsDepthAndCircular;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.validateSameVersion;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.validateUniqueEntityNameAndVersion;
import static pt.ptinovacao.nossistsc.core.implementation.components.validators.ValidatorUtils.validateVersionInMutableState;

public class ComponentValidator extends CatalogValidator implements ComponentInit, RelationPathsGetterValidator {

    public static final String COMPONENT_NAME = "persistence.validator.COMPONENT";

    private static final Logger LOGGER = LoggerFactory.getLogger(ComponentValidator.class);
    private ComponentServiceComponent componentServiceComponent;
    private AttributeTypeComponent attributeTypeComponent;
    private AttributeValidator attributeValidator;
    private CatAttributeTypeServiceComponent catAttributeTypeServiceComponent;
    private CatEnumeratedAttributeValueServiceComponent catEnumeratedAttributeValueServiceComponent;
    private VersionServiceComponent versionServiceComponent;

    @Override
    public void init() {
        this.componentServiceComponent = Components.get(ComponentServiceComponent.COMPONENT_NAME, ComponentServiceComponent.class);
        this.attributeTypeComponent = Components.get(AttributeTypeComponent.COMPONENT_NAME, AttributeTypeComponent.class);
        this.catAttributeTypeServiceComponent = Components.get(CatAttributeTypeServiceComponent.COMPONENT_NAME, CatAttributeTypeServiceComponent.class);
        this.catEnumeratedAttributeValueServiceComponent = Components.get(CatEnumeratedAttributeValueServiceComponent.COMPONENT_NAME, CatEnumeratedAttributeValueServiceComponent.class);
        this.versionServiceComponent = Components.get(VersionServiceComponent.COMPONENT_NAME, VersionServiceComponent.class);
        this.attributeValidator = new AttributeValidator();
    }

    @Override
    public <T extends AbstractEntity> void validateCreate(Persistence p, T entity) {
        LOGGER.debug("Validating create!");
        super.validateCreate(p, entity);

        LOGGER.debug("Custom validating Component create!");
        customValidate(p, (Component) entity);
    }

    @Override
    public <T extends AbstractEntity> void validateUpdate(Persistence p, T entity) {

        LOGGER.debug("Validating update!");
        super.validateUpdate(p, entity);

        LOGGER.debug("Custom validating Component update!");
        customValidate(p, (Component) entity);
    }

    @Override
    public <T extends AbstractEntity> void validateDelete(Persistence p, T entity) {
        super.validateDelete(p, entity);
        LOGGER.debug("Validating version is in mutable state!");
        if (entity instanceof Component) {
            validateVersionInMutableState(p, ((Component) entity).getVersion(), versionServiceComponent);
        }
    }

    private void customValidate(Persistence persistence, Component entity) {

        if (entity.getId() != null) {
            LOGGER.debug("Validating same version!");
            validateSameVersion(componentServiceComponent.get(entity.getId()), entity, persistence.readCatalogById(entity.getIdCatEntity()).getI18n(Context.locale().getLanguage()));
        }

        LOGGER.debug("Validating Attributes");
        validateAttributes(persistence, entity, entity.getAtributes(), attributeValidator, attributeTypeComponent, componentServiceComponent,
                persistence.readCatalogById(entity.getIdCatEntity()).getI18n(Context.locale().getLanguage()));

        LOGGER.debug("Validating name and version uniqueness!");
        validateUniqueEntityNameAndVersion(persistence, entity,
                componentServiceComponent.findByNameAndVersion(entity.getName(), entity.getVersion()),
                versionServiceComponent.get(entity.getVersion().getId()));

        LOGGER.debug("Validating version is in mutable state!");
        validateVersionInMutableState(persistence, entity.getVersion(), versionServiceComponent);

        if (MoreObjects.firstNonNull(entity.getAtributes(), new ArrayList<Attribute>()).stream().anyMatch(a->a.getComponentType() != null)){
            validateRelationsDepthAndCircular(entity, this,
                    MoreObjects.firstNonNull(componentServiceComponent.findByVersion(entity.getVersion()), new ArrayList<>()),
                    catAttributeTypeServiceComponent, catEnumeratedAttributeValueServiceComponent);
        }
    }

    @Override
    public <T extends CatalogEntityWithAttr> Map<String, Set<List<T>>> getRelationsPaths(T entity, Set<String> possibleRelationTypes, boolean findParentPaths) {
        Component componentEntity = (Component) entity;
        Map<String, Set<List<T>>> compPathsThroughAttributes = new HashMap<>();
        Set<Component> componentRelatives = MoreObjects.firstNonNull(findParentPaths ? componentEntity.getAtributesType() : componentEntity.getAtributes(), new ArrayList<Attribute>())
                .stream()
                .filter(a -> findParentPaths ? a.getComponent() != null : a.getComponentType() != null)
                .map(findParentPaths ? Attribute::getComponent : Attribute::getComponentType)
                .collect(Collectors.toSet());
        for (Component relative : componentRelatives) {
            Component otherComponent;
            if (relative != null && relative.getId() != null && (otherComponent = componentServiceComponent.get(relative.getId())) != null) {
                Map<String, Set<List<T>>> otherPaths = getRelationsPaths(
                        (T) otherComponent,
                        Collections.singleton("ComponentTypeRelation"),
                        findParentPaths);
                addEntityToPaths(entity, otherPaths, findParentPaths);
                compPathsThroughAttributes = mergeSetsOfPathsByRelationTypes(compPathsThroughAttributes, otherPaths);
            }
        }
        fillEmptyRelationTypePathsWithEntity(entity, Collections.singleton("ComponentTypeRelation"), compPathsThroughAttributes);
        return compPathsThroughAttributes;
    }
}
