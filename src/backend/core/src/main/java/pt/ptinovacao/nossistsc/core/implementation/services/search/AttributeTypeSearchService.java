package pt.ptinovacao.nossistsc.core.implementation.services.search;

import pt.ptinovacao.asf.componentsmanager.ComponentInit;
import pt.ptinovacao.netwin.backend.entities.httpapi.rest.ResourceName;
import pt.ptinovacao.netwin.backend.modules.search.implementation.services.AbstractSearchService;
import pt.ptinovacao.nossistsc.core.model.AttributeType;

public class AttributeTypeSearchService extends AbstractSearchService implements ComponentInit {

    public static final String COMPONENT_NAME = AttributeType.class.getAnnotation(ResourceName.class).resourceName() + "SearchService";

    @Override
    public void init() {
    }
}

