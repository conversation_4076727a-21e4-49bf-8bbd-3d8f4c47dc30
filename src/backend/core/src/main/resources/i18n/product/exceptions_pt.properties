EXCEPTION-NOSSISTSC.00001.user.message=O nome '{name}' j\u00E1 est\u00E1 a ser utilizado.
EXCEPTION-NOSSISTSC.00001.developer.message=O nome '{name}' j\u00E1 est\u00E1 a ser utilizado.
EXCEPTION-NOSSISTSC.00002.user.message=O tipo primitivo '{name}' \u00E9 inv\u00E1lido.
EXCEPTION-NOSSISTSC.00002.developer.message=O tipo primitivo '{name}' \u00E9 inv\u00E1lido.
EXCEPTION-NOSSISTSC.00003.user.message=O {parentEntity} '{parentEntityName}' j\u00E1 cont\u00E9m o {entity} '{entityName}'.
EXCEPTION-NOSSISTSC.00003.developer.message=O {parentEntity} '{parentEntityName}' j\u00E1 cont\u00E9m o {entity} '{entityName}'.
EXCEPTION-NOSSISTSC.00004.user.message=A vers\u00E3o do {entity} n\u00E3o pode ser alterada de '{oldversion}' para '{newversion}'.
EXCEPTION-NOSSISTSC.00004.developer.message=A vers\u00E3o do {entity} n\u00E3o pode ser alterada de '{oldversion}' para '{newversion}'.
EXCEPTION-NOSSISTSC.00005.user.message=Entidade '{type}' n\u00E3o suporta opera\u00E7\u00F5es do tipo '{operation}'.
EXCEPTION-NOSSISTSC.00005.developer.message=Entidade '{type}' n\u00E3o suporta opera\u00E7\u00F5es do tipo '{operation}'.
EXCEPTION-NOSSISTSC.00006.user.message=O campo '{entity}' n\u00E3o pode ser nulo nem vazio.
EXCEPTION-NOSSISTSC.00006.developer.message=O campo '{entity}' n\u00E3o pode ser nulo nem vazio.
EXCEPTION-NOSSISTSC.00007.user.message=A vers\u00E3o do {entityType} com o nome '{entityName}' n\u00E3o coincide com a vers\u00E3o do Atributo com o nome '{attributeName}'.
EXCEPTION-NOSSISTSC.00007.developer.message=A vers\u00E3o do {entityType} com o nome '{entityName}' n\u00E3o coincide com a vers\u00E3o do Atributo com o nome '{attributeName}'.
EXCEPTION-NOSSISTSC.00008.user.message=N\u00E3o \u00E9 poss\u00EDvel estabelecer a rela\u00E7\u00E3o {relationType} entre {parentEntity} e {childEntity}: rela\u00E7\u00E3o inv\u00E1lida.
EXCEPTION-NOSSISTSC.00008.developer.message=N\u00E3o \u00E9 poss\u00EDvel estabelecer a rela\u00E7\u00E3o {relationType} entre {parentEntity} e {childEntity}: rela\u00E7\u00E3o inv\u00E1lida.
EXCEPTION-NOSSISTSC.00009.user.message=O estabelecimento de uma das rela\u00E7\u00F5es violar\u00E1 a pol\u00EDtica de profundidade m\u00E1xima.
EXCEPTION-NOSSISTSC.00009.developer.message=O estabelecimento de uma das rela\u00E7\u00F5es violar\u00E1 a pol\u00EDtica de profundidade m\u00E1xima.
EXCEPTION-NOSSISTSC.00010.user.message=Depend\u00EAncia circular detetada.
EXCEPTION-NOSSISTSC.00010.developer.message=Depend\u00EAncia circular detetada.
EXCEPTION-NOSSISTSC.00011.user.message=A vers\u00E3o '{name}' est\u00E1 no estado imut\u00E1vel '{state}'. Para permitir altera\u00E7\u00F5es, deve estar num dos seguintes estados '{mutableStates}'.
EXCEPTION-NOSSISTSC.00011.developer.message=A vers\u00E3o '{name}' est\u00E1 no estado imut\u00E1vel '{state}'. Para permitir altera\u00E7\u00F5es, deve estar num dos seguintes estados '{mutableStates}'.
EXCEPTION-NOSSISTSC.00012.user.message=As vers\u00F5es na rela\u00E7\u00E3o entre o {entity1Type} '{entity1Name}' e o {entity2Type} '{entity2Name}' s\u00E3o diferentes.
EXCEPTION-NOSSISTSC.00012.developer.message=As vers\u00F5es na rela\u00E7\u00E3o entre o {entity1Type} '{entity1Name}' e o {entity2Type} '{entity2Name}' s\u00E3o diferentes.
EXCEPTION-NOSSISTSC.00013.user.message=Entidade filha da rela\u00E7\u00E3o com {entityType} '{entityName}' inv\u00E1lida.
EXCEPTION-NOSSISTSC.00013.developer.message=Entidade filha da rela\u00E7\u00E3o com {entityType} '{entityName}' inv\u00E1lida.
EXCEPTION-NOSSISTSC.00014.user.message=N\u00E3o pode haver m\u00FAltiplas inst\u00E2ncias de rela\u00E7\u00F5es '{relationType}' entre o {parentType} '{parentName}' e o {childType} '{childName}'.
EXCEPTION-NOSSISTSC.00014.developer.message=N\u00E3o pode haver m\u00FAltiplas inst\u00E2ncias de rela\u00E7\u00F5es '{relationType}' entre o {parentType} '{parentName}' e o {childType} '{childName}'.
EXCEPTION-NOSSISTSC.00015.user.message=Restri\u00E7\u00E3o do tipo '{constraintType}' inv\u00E1lida. Para adicionar uma restri\u00E7\u00E3o do tipo '{constraintType}', a propriedade 'primitiveTypes' do AttributeType deve ser [{enum.primitiveTypes}].
EXCEPTION-NOSSISTSC.00015.developer.message=Restri\u00E7\u00E3o do tipo '{constraintType}' inv\u00E1lida. Para adicionar uma restri\u00E7\u00E3o do tipo '{constraintType}', a propriedade 'primitiveTypes' do AttributeType deve ser [{enum.primitiveTypes}].
EXCEPTION-NOSSISTSC.00016.user.message=Restri\u00E7\u00E3o do tipo 'ENUMC' n\u00E3o tem lista de poss\u00EDveis valores.
EXCEPTION-NOSSISTSC.00016.developer.message=Restri\u00E7\u00E3o do tipo 'ENUMC' n\u00E3o tem lista de poss\u00EDveis valores.
EXCEPTION-NOSSISTSC.00017.user.message=Restri\u00E7\u00E3o do tipo 'SIZE' inv\u00E1lida com valores (min={min.value}, max={max.value}).
EXCEPTION-NOSSISTSC.00017.developer.message=Restri\u00E7\u00E3o do tipo 'SIZE' inv\u00E1lida com valores (min={min.value}, max={max.value}).
EXCEPTION-NOSSISTSC.00018.user.message=Restri\u00E7\u00E3o do tipo 'RANGE' inv\u00E1lida com valores (minRange={min.value}, maxRange={max.value}).
EXCEPTION-NOSSISTSC.00018.developer.message=Restri\u00E7\u00E3o do tipo 'RANGE' inv\u00E1lida com valores (minRange={min.value}, maxRange={max.value}).
EXCEPTION-NOSSISTSC.00019.user.message=O Componente pai e tipo do Atributo '{attribute.name}' n\u00E3o podem ser o mesmo.
EXCEPTION-NOSSISTSC.00019.developer.message=O Componente pai e tipo do Atributo '{attribute.name}' n\u00E3o podem ser o mesmo.
EXCEPTION-NOSSISTSC.00020.user.message=Vers\u00E3o n\u00E3o existe.
EXCEPTION-NOSSISTSC.00020.developer.message=Vers\u00E3o n\u00E3o existe.
EXCEPTION-NOSSISTSC.00021.user.message=A Vers\u00E3o '{versionName}' j\u00E1 cont\u00E9m o {entity} '{entityName}'.
EXCEPTION-NOSSISTSC.00021.developer.message=A Vers\u00E3o '{versionName}' j\u00E1 cont\u00E9m o {entity} '{entityName}'.
EXCEPTION-NOSSISTSC.00022.user.message=Restri\u00E7\u00E3o do tipo 'FLOATRANGE' inv\u00E1lida com valores (minRange={min.value}, maxRange={max.value}).
EXCEPTION-NOSSISTSC.00022.developer.message=Restri\u00E7\u00E3o do tipo 'FLOATRANGE' inv\u00E1lida com valores (minRange={min.value}, maxRange={max.value}).
EXCEPTION-NOSSISTSC.00023.user.message=Rela\u00E7\u00E3o com entidade pai inv\u00E1lida detetada.
EXCEPTION-NOSSISTSC.00023.developer.message=Rela\u00E7\u00E3o com entidade pai inv\u00E1lida detetada.
EXCEPTION-NOSSISTSC.00024.user.message=Nome de restri\u00E7\u00E3o duplicado detetado no contexto do Tipo de Atributo Simples '{attributeType}'.
EXCEPTION-NOSSISTSC.00024.developer.message=Nome de restri\u00E7\u00E3o duplicado detetado no contexto do Tipo de Atributo Simples '{attributeType}'.