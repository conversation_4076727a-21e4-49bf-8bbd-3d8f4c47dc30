{"/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/commons-stores/version-context-info.state.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/commons-stores/version-context-info.state.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 66}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 11, "column": 2}}, "3": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 42}}, "4": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 42}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 36}}, "6": {"start": {"line": 16, "column": 23}, "end": {"line": 16, "column": 49}}, "7": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": 49}}, "8": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 45}}, "9": {"start": {"line": 17, "column": 30}, "end": {"line": 17, "column": 45}}, "10": {"start": {"line": 18, "column": 24}, "end": {"line": 18, "column": 51}}, "11": {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 51}}, "12": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 55}}, "13": {"start": {"line": 19, "column": 30}, "end": {"line": 19, "column": 55}}, "14": {"start": {"line": 20, "column": 28}, "end": {"line": 20, "column": 59}}, "15": {"start": {"line": 20, "column": 37}, "end": {"line": 20, "column": 59}}, "16": {"start": {"line": 21, "column": 16}, "end": {"line": 27, "column": 1}}, "17": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 26}}, "18": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 60}}, "19": {"start": {"line": 31, "column": 31}, "end": {"line": 31, "column": 60}}, "20": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 66}}, "21": {"start": {"line": 32, "column": 35}, "end": {"line": 32, "column": 66}}, "22": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 89}}, "23": {"start": {"line": 33, "column": 37}, "end": {"line": 33, "column": 89}}, "24": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 24}}, "25": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "26": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 17}}, "27": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}, "28": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 17}}, "29": {"start": {"line": 47, "column": 2}, "end": {"line": 50, "column": 4}}, "30": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "31": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 17}}, "32": {"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": 3}}, "33": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 17}}, "34": {"start": {"line": 59, "column": 2}, "end": {"line": 62, "column": 4}}, "35": {"start": {"line": 65, "column": 2}, "end": {"line": 67, "column": 3}}, "36": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 17}}, "37": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "38": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 17}}, "39": {"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 4}}, "40": {"start": {"line": 77, "column": 2}, "end": {"line": 79, "column": 3}}, "41": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 17}}, "42": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 12}}, "43": {"start": {"line": 85, "column": 2}, "end": {"line": 87, "column": 3}}, "44": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 17}}, "45": {"start": {"line": 88, "column": 17}, "end": {"line": 88, "column": 47}}, "46": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 52}}, "47": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 47}}, "48": {"start": {"line": 92, "column": 16}, "end": {"line": 98, "column": 1}}, "49": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 22}}, "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 42}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 23}, "end": {"line": 16, "column": 24}}, "loc": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": 49}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 22}}, "loc": {"start": {"line": 17, "column": 30}, "end": {"line": 17, "column": 45}}, "line": 17}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 18, "column": 24}, "end": {"line": 18, "column": 25}}, "loc": {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 51}}, "line": 18}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 22}}, "loc": {"start": {"line": 19, "column": 30}, "end": {"line": 19, "column": 55}}, "line": 19}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 20, "column": 28}, "end": {"line": 20, "column": 29}}, "loc": {"start": {"line": 20, "column": 37}, "end": {"line": 20, "column": 59}}, "line": 20}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 19}}, "loc": {"start": {"line": 31, "column": 31}, "end": {"line": 31, "column": 60}}, "line": 31}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 21}}, "loc": {"start": {"line": 32, "column": 35}, "end": {"line": 32, "column": 66}}, "line": 32}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 22}}, "loc": {"start": {"line": 33, "column": 37}, "end": {"line": 33, "column": 89}}, "line": 33}, "9": {"name": "resetState", "decl": {"start": {"line": 37, "column": 9}, "end": {"line": 37, "column": 19}}, "loc": {"start": {"line": 37, "column": 22}, "end": {"line": 39, "column": 1}}, "line": 37}, "10": {"name": "setVersionName", "decl": {"start": {"line": 40, "column": 9}, "end": {"line": 40, "column": 23}}, "loc": {"start": {"line": 40, "column": 44}, "end": {"line": 51, "column": 1}}, "line": 40}, "11": {"name": "setVersionId", "decl": {"start": {"line": 52, "column": 9}, "end": {"line": 52, "column": 21}}, "loc": {"start": {"line": 52, "column": 40}, "end": {"line": 63, "column": 1}}, "line": 52}, "12": {"name": "setVersionState", "decl": {"start": {"line": 64, "column": 9}, "end": {"line": 64, "column": 24}}, "loc": {"start": {"line": 64, "column": 46}, "end": {"line": 75, "column": 1}}, "line": 64}, "13": {"name": "setVersionInfo", "decl": {"start": {"line": 76, "column": 9}, "end": {"line": 76, "column": 23}}, "loc": {"start": {"line": 76, "column": 39}, "end": {"line": 91, "column": 1}}, "line": 76}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 37}, "end": {"line": 33, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 37}, "end": {"line": 33, "column": 69}}, {"start": {"line": 33, "column": 73}, "end": {"line": 33, "column": 89}}], "line": 33}, "1": {"loc": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}], "line": 41}, "2": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}, "type": "if", "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}, {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 3}}], "line": 44}, "3": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "type": "if", "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}], "line": 53}, "4": {"loc": {"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": 3}}, "type": "if", "locations": [{"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": 3}}, {"start": {"line": 56, "column": 2}, "end": {"line": 58, "column": 3}}], "line": 56}, "5": {"loc": {"start": {"line": 65, "column": 2}, "end": {"line": 67, "column": 3}}, "type": "if", "locations": [{"start": {"line": 65, "column": 2}, "end": {"line": 67, "column": 3}}, {"start": {"line": 65, "column": 2}, "end": {"line": 67, "column": 3}}], "line": 65}, "6": {"loc": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "type": "if", "locations": [{"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}], "line": 68}, "7": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 79, "column": 3}}, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 79, "column": 3}}, {"start": {"line": 77, "column": 2}, "end": {"line": 79, "column": 3}}], "line": 77}, "8": {"loc": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 20}}, {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 50}}], "line": 77}, "9": {"loc": {"start": {"line": 85, "column": 2}, "end": {"line": 87, "column": 3}}, "type": "if", "locations": [{"start": {"line": 85, "column": 2}, "end": {"line": 87, "column": 3}}, {"start": {"line": 85, "column": 2}, "end": {"line": 87, "column": 3}}], "line": 85}, "10": {"loc": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 27}}, {"start": {"line": 85, "column": 31}, "end": {"line": 85, "column": 56}}, {"start": {"line": 85, "column": 60}, "end": {"line": 85, "column": 87}}], "line": 85}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0, "5": 1, "6": 1, "7": 0, "8": 1, "9": 0, "10": 1, "11": 0, "12": 1, "13": 0, "14": 1, "15": 0, "16": 1, "17": 1, "18": 1, "19": 0, "20": 1, "21": 0, "22": 1, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 1, "49": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0, 0]}, "inputSourceMap": {"version": 3, "names": ["initialStateObj", "Object", "freeze", "versionName", "versionId", "versionState", "initialState", "exports", "getVersionName", "state", "getVersionId", "getVersionState", "isStateReset", "isValidVersionState", "getters", "isValidId", "isValidName", "isValidState", "resetState", "setVersionName", "setVersionId", "setVersionState", "setVersionInfo", "params", "state1", "state2", "setters"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/commons-stores/", "sources": ["version-context-info.state.js"], "sourcesContent": ["const initialStateObj = Object.freeze({\n  versionName: '',\n  versionId: '',\n  versionState: -1,\n})\n\nexport const initialState = () => initialStateObj\n\n// getters\n\nconst getVersionName = (state) => state.versionName\nconst getVersionId = (state) => state.versionId\nconst getVersionState = (state) => state.versionState\nconst isStateReset = (state) => state === initialStateObj\nconst isValidVersionState = (state) => state.versionState > 0\n\nexport const getters = {\n  versionName: getVersionName,\n  versionId: getVersionId,\n  versionState: getVersionState,\n  isStateReset,\n  isValidVersionState\n}\n\n// helpers\n\nconst isValidId = (versionId) => typeof versionId === 'string'\nconst isValidName = (versionName) => typeof versionName === 'string'\nconst isValidState = (versionState) => typeof versionState === 'number' && versionState > 0\n\n// setters\n\nfunction resetState () {\n  return initialState()\n}\n\nfunction setVersionName (state, versionName) {\n  if (state.versionName === versionName) {\n    return state\n  }\n\n  if (!isValidName(versionName)) {\n    return state\n  }\n\n  return {\n    ...state,\n    versionName: versionName\n  }\n}\n\nfunction setVersionId (state, versionId) {\n  if (state.versionId === versionId) {\n    return state\n  }\n\n  if (!isValidId(versionId)) {\n    return state\n  }\n\n  return {\n    ...state,\n    versionId: versionId\n  }\n}\n\nfunction setVersionState (state, versionState) {\n  if (state.versionState === versionState) {\n    return state\n  }\n\n  if (!isValidState(versionState)) {\n    return state\n  }\n\n  return {\n    ...state,\n    versionState\n  }\n}\n\nfunction setVersionInfo (state, params) {\n  if (params == null || typeof params !== 'object') {\n    return state\n  }\n\n  const { versionId, versionName, versionState } = params\n\n  if (!isValidId(versionId) ||\n      !isValidName(versionName) ||\n      !isValidState(versionState)) {\n    return state\n  }\n\n  const state1 = setVersionId(state, versionId)\n  const state2 = setVersionName(state1, versionName)\n  return setVersionState(state2, versionState)\n}\n\nexport const setters = {\n  resetState: resetState,\n  setVersionName: setVersionName,\n  setVersionId: setVersionId,\n  setVersionState: setVersionState,\n  setVersionInfo: setVersionInfo\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC;EACpCC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AAEK,MAAMC,YAAY,GAAGA,CAAA,KAAMN,eAAe;;AAEjD;AAAAO,OAAA,CAAAD,YAAA,GAAAA,YAAA;AAEA,MAAME,cAAc,GAAIC,KAAK,IAAKA,KAAK,CAACN,WAAW;AACnD,MAAMO,YAAY,GAAID,KAAK,IAAKA,KAAK,CAACL,SAAS;AAC/C,MAAMO,eAAe,GAAIF,KAAK,IAAKA,KAAK,CAACJ,YAAY;AACrD,MAAMO,YAAY,GAAIH,KAAK,IAAKA,KAAK,KAAKT,eAAe;AACzD,MAAMa,mBAAmB,GAAIJ,KAAK,IAAKA,KAAK,CAACJ,YAAY,GAAG,CAAC;AAEtD,MAAMS,OAAO,GAAG;EACrBX,WAAW,EAAEK,cAAc;EAC3BJ,SAAS,EAAEM,YAAY;EACvBL,YAAY,EAAEM,eAAe;EAC7BC,YAAY;EACZC;AACF,CAAC;;AAED;AAAAN,OAAA,CAAAO,OAAA,GAAAA,OAAA;AAEA,MAAMC,SAAS,GAAIX,SAAS,IAAK,OAAOA,SAAS,KAAK,QAAQ;AAC9D,MAAMY,WAAW,GAAIb,WAAW,IAAK,OAAOA,WAAW,KAAK,QAAQ;AACpE,MAAMc,YAAY,GAAIZ,YAAY,IAAK,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,GAAG,CAAC;;AAE3F;;AAEA,SAASa,UAAUA,CAAA,EAAI;EACrB,OAAOZ,YAAY,CAAC,CAAC;AACvB;AAEA,SAASa,cAAcA,CAAEV,KAAK,EAAEN,WAAW,EAAE;EAC3C,IAAIM,KAAK,CAACN,WAAW,KAAKA,WAAW,EAAE;IACrC,OAAOM,KAAK;EACd;EAEA,IAAI,CAACO,WAAW,CAACb,WAAW,CAAC,EAAE;IAC7B,OAAOM,KAAK;EACd;EAEA,OAAO;IACL,GAAGA,KAAK;IACRN,WAAW,EAAEA;EACf,CAAC;AACH;AAEA,SAASiB,YAAYA,CAAEX,KAAK,EAAEL,SAAS,EAAE;EACvC,IAAIK,KAAK,CAACL,SAAS,KAAKA,SAAS,EAAE;IACjC,OAAOK,KAAK;EACd;EAEA,IAAI,CAACM,SAAS,CAACX,SAAS,CAAC,EAAE;IACzB,OAAOK,KAAK;EACd;EAEA,OAAO;IACL,GAAGA,KAAK;IACRL,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASiB,eAAeA,CAAEZ,KAAK,EAAEJ,YAAY,EAAE;EAC7C,IAAII,KAAK,CAACJ,YAAY,KAAKA,YAAY,EAAE;IACvC,OAAOI,KAAK;EACd;EAEA,IAAI,CAACQ,YAAY,CAACZ,YAAY,CAAC,EAAE;IAC/B,OAAOI,KAAK;EACd;EAEA,OAAO;IACL,GAAGA,KAAK;IACRJ;EACF,CAAC;AACH;AAEA,SAASiB,cAAcA,CAAEb,KAAK,EAAEc,MAAM,EAAE;EACtC,IAAIA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAChD,OAAOd,KAAK;EACd;EAEA,MAAM;IAAEL,SAAS;IAAED,WAAW;IAAEE;EAAa,CAAC,GAAGkB,MAAM;EAEvD,IAAI,CAACR,SAAS,CAACX,SAAS,CAAC,IACrB,CAACY,WAAW,CAACb,WAAW,CAAC,IACzB,CAACc,YAAY,CAACZ,YAAY,CAAC,EAAE;IAC/B,OAAOI,KAAK;EACd;EAEA,MAAMe,MAAM,GAAGJ,YAAY,CAACX,KAAK,EAAEL,SAAS,CAAC;EAC7C,MAAMqB,MAAM,GAAGN,cAAc,CAACK,MAAM,EAAErB,WAAW,CAAC;EAClD,OAAOkB,eAAe,CAACI,MAAM,EAAEpB,YAAY,CAAC;AAC9C;AAEO,MAAMqB,OAAO,GAAG;EACrBR,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,YAAY,EAAEA,YAAY;EAC1BC,eAAe,EAAEA,eAAe;EAChCC,cAAc,EAAEA;AAClB,CAAC;AAAAf,OAAA,CAAAmB,OAAA,GAAAA,OAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "51c14a32391e8f325894a7f2692514e89fd29e40", "contentHash": "34956a21aaf3ca45590ba4f07495882ba525a39fa55fb5330cd5132b29720c1b"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/docs/demo-common-tsc-routes.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/docs/demo-common-tsc-routes.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 1983}}, "2": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 53}}, "3": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 52}}, "4": {"start": {"line": 14, "column": 4}, "end": {"line": 84, "column": 4}}, "5": {"start": {"line": 15, "column": 16}, "end": {"line": 65, "column": 3}}, "6": {"start": {"line": 16, "column": 25}, "end": {"line": 16, "column": 39}}, "7": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 87}}, "8": {"start": {"line": 17, "column": 58}, "end": {"line": 17, "column": 86}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}, "10": {"start": {"line": 19, "column": 6}, "end": {"line": 23, "column": 8}}, "11": {"start": {"line": 20, "column": 19}, "end": {"line": 20, "column": 22}}, "12": {"start": {"line": 21, "column": 34}, "end": {"line": 21, "column": 52}}, "13": {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": 74}}, "14": {"start": {"line": 25, "column": 22}, "end": {"line": 28, "column": 11}}, "15": {"start": {"line": 25, "column": 57}, "end": {"line": 25, "column": 84}}, "16": {"start": {"line": 25, "column": 116}, "end": {"line": 28, "column": 5}}, "17": {"start": {"line": 29, "column": 24}, "end": {"line": 37, "column": 5}}, "18": {"start": {"line": 30, "column": 6}, "end": {"line": 36, "column": 19}}, "19": {"start": {"line": 31, "column": 8}, "end": {"line": 34, "column": 9}}, "20": {"start": {"line": 32, "column": 22}, "end": {"line": 32, "column": 44}}, "21": {"start": {"line": 33, "column": 10}, "end": {"line": 33, "column": 67}}, "22": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 27}}, "23": {"start": {"line": 38, "column": 23}, "end": {"line": 43, "column": 5}}, "24": {"start": {"line": 39, "column": 33}, "end": {"line": 39, "column": 54}}, "25": {"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 9}}, "26": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 81}}, "27": {"start": {"line": 44, "column": 33}, "end": {"line": 59, "column": 5}}, "28": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "29": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 18}}, "30": {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 40}}, "31": {"start": {"line": 49, "column": 6}, "end": {"line": 58, "column": 9}}, "32": {"start": {"line": 50, "column": 28}, "end": {"line": 50, "column": 47}}, "33": {"start": {"line": 51, "column": 8}, "end": {"line": 56, "column": 9}}, "34": {"start": {"line": 52, "column": 10}, "end": {"line": 55, "column": 12}}, "35": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 19}}, "36": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": 6}}, "37": {"start": {"line": 66, "column": 2}, "end": {"line": 83, "column": 4}}, "38": {"start": {"line": 67, "column": 17}, "end": {"line": 70, "column": 5}}, "39": {"start": {"line": 69, "column": 20}, "end": {"line": 69, "column": 25}}, "40": {"start": {"line": 71, "column": 18}, "end": {"line": 74, "column": 5}}, "41": {"start": {"line": 73, "column": 20}, "end": {"line": 73, "column": 26}}, "42": {"start": {"line": 75, "column": 17}, "end": {"line": 78, "column": 5}}, "43": {"start": {"line": 77, "column": 20}, "end": {"line": 77, "column": 25}}, "44": {"start": {"line": 79, "column": 20}, "end": {"line": 82, "column": 5}}, "45": {"start": {"line": 81, "column": 20}, "end": {"line": 81, "column": 28}}, "46": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 47}}, "47": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 42}}, "48": {"start": {"line": 88, "column": 26}, "end": {"line": 88, "column": 45}}, "49": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 46}}, "50": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 52}}, "51": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 42}}, "52": {"start": {"line": 92, "column": 26}, "end": {"line": 92, "column": 52}}, "53": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 46}}, "54": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 50}}, "55": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 40}}, "56": {"start": {"line": 97, "column": 20}, "end": {"line": 97, "column": 55}}, "57": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 34}}, "58": {"start": {"line": 99, "column": 22}, "end": {"line": 99, "column": 45}}, "59": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 38}}, "60": {"start": {"line": 101, "column": 27}, "end": {"line": 101, "column": 63}}, "61": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 48}}, "62": {"start": {"line": 103, "column": 21}, "end": {"line": 103, "column": 51}}, "63": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 36}}, "64": {"start": {"line": 105, "column": 22}, "end": {"line": 105, "column": 42}}, "65": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 38}}, "66": {"start": {"line": 107, "column": 22}, "end": {"line": 107, "column": 48}}, "67": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 38}}, "68": {"start": {"line": 110, "column": 33}, "end": {"line": 110, "column": 72}}, "69": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 60}}, "70": {"start": {"line": 112, "column": 39}, "end": {"line": 112, "column": 80}}, "71": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 72}}, "72": {"start": {"line": 114, "column": 41}, "end": {"line": 114, "column": 84}}, "73": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 76}}, "74": {"start": {"line": 116, "column": 28}, "end": {"line": 116, "column": 61}}, "75": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 50}}, "76": {"start": {"line": 119, "column": 27}, "end": {"line": 119, "column": 73}}, "77": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 48}}, "78": {"start": {"line": 121, "column": 30}, "end": {"line": 121, "column": 83}}, "79": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 54}}, "80": {"start": {"line": 123, "column": 20}, "end": {"line": 123, "column": 83}}, "81": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 34}}, "82": {"start": {"line": 126, "column": 22}, "end": {"line": 126, "column": 37}}, "83": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 38}}, "84": {"start": {"line": 128, "column": 22}, "end": {"line": 128, "column": 55}}, "85": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 38}}, "86": {"start": {"line": 130, "column": 20}, "end": {"line": 130, "column": 45}}, "87": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 34}}, "88": {"start": {"line": 132, "column": 20}, "end": {"line": 132, "column": 50}}, "89": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 34}}, "90": {"start": {"line": 135, "column": 18}, "end": {"line": 135, "column": 34}}, "91": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 30}}, "92": {"start": {"line": 137, "column": 18}, "end": {"line": 137, "column": 37}}, "93": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 30}}, "94": {"start": {"line": 139, "column": 16}, "end": {"line": 139, "column": 35}}, "95": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 26}}, "96": {"start": {"line": 141, "column": 18}, "end": {"line": 141, "column": 40}}, "97": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 30}}, "98": {"start": {"line": 143, "column": 33}, "end": {"line": 143, "column": 59}}, "99": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 60}}, "100": {"start": {"line": 145, "column": 30}, "end": {"line": 145, "column": 69}}, "101": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 54}}, "102": {"start": {"line": 147, "column": 29}, "end": {"line": 147, "column": 67}}, "103": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 52}}, "104": {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 37}}, "105": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 38}}, "106": {"start": {"line": 152, "column": 20}, "end": {"line": 152, "column": 44}}, "107": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 34}}, "108": {"start": {"line": 154, "column": 20}, "end": {"line": 154, "column": 50}}, "109": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 34}}, "110": {"start": {"line": 156, "column": 22}, "end": {"line": 156, "column": 55}}, "111": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 38}}, "112": {"start": {"line": 159, "column": 16}, "end": {"line": 159, "column": 35}}, "113": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 26}}, "114": {"start": {"line": 161, "column": 18}, "end": {"line": 161, "column": 34}}, "115": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 30}}, "116": {"start": {"line": 163, "column": 18}, "end": {"line": 163, "column": 40}}, "117": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 30}}, "118": {"start": {"line": 165, "column": 18}, "end": {"line": 165, "column": 37}}, "119": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 30}}, "120": {"start": {"line": 167, "column": 33}, "end": {"line": 167, "column": 59}}, "121": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 60}}, "122": {"start": {"line": 169, "column": 30}, "end": {"line": 169, "column": 69}}, "123": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 54}}, "124": {"start": {"line": 171, "column": 29}, "end": {"line": 171, "column": 67}}, "125": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 52}}, "126": {"start": {"line": 174, "column": 27}, "end": {"line": 174, "column": 47}}, "127": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 48}}, "128": {"start": {"line": 176, "column": 25}, "end": {"line": 176, "column": 54}}, "129": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 44}}, "130": {"start": {"line": 178, "column": 25}, "end": {"line": 178, "column": 60}}, "131": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 44}}, "132": {"start": {"line": 180, "column": 27}, "end": {"line": 180, "column": 65}}, "133": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 48}}, "134": {"start": {"line": 183, "column": 21}, "end": {"line": 183, "column": 45}}, "135": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 36}}, "136": {"start": {"line": 185, "column": 23}, "end": {"line": 185, "column": 44}}, "137": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 40}}, "138": {"start": {"line": 187, "column": 23}, "end": {"line": 187, "column": 47}}, "139": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 40}}, "140": {"start": {"line": 189, "column": 23}, "end": {"line": 189, "column": 50}}, "141": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 40}}, "142": {"start": {"line": 191, "column": 38}, "end": {"line": 191, "column": 69}}, "143": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 70}}, "144": {"start": {"line": 193, "column": 35}, "end": {"line": 193, "column": 79}}, "145": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 64}}, "146": {"start": {"line": 195, "column": 34}, "end": {"line": 195, "column": 72}}, "147": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 62}}, "148": {"start": {"line": 198, "column": 40}, "end": {"line": 198, "column": 82}}, "149": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 74}}, "150": {"start": {"line": 201, "column": 28}, "end": {"line": 201, "column": 49}}, "151": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 50}}, "152": {"start": {"line": 203, "column": 26}, "end": {"line": 203, "column": 59}}, "153": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 46}}, "154": {"start": {"line": 205, "column": 26}, "end": {"line": 205, "column": 65}}, "155": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 46}}, "156": {"start": {"line": 207, "column": 28}, "end": {"line": 207, "column": 67}}, "157": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 50}}, "158": {"start": {"line": 210, "column": 22}, "end": {"line": 210, "column": 47}}, "159": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 38}}, "160": {"start": {"line": 212, "column": 24}, "end": {"line": 212, "column": 46}}, "161": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 42}}, "162": {"start": {"line": 214, "column": 24}, "end": {"line": 214, "column": 49}}, "163": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 42}}, "164": {"start": {"line": 216, "column": 24}, "end": {"line": 216, "column": 52}}, "165": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 42}}, "166": {"start": {"line": 218, "column": 23}, "end": {"line": 218, "column": 55}}, "167": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 40}}, "168": {"start": {"line": 220, "column": 22}, "end": {"line": 220, "column": 47}}, "169": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 38}}, "170": {"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 46}}, "171": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 42}}, "172": {"start": {"line": 224, "column": 24}, "end": {"line": 224, "column": 49}}, "173": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 42}}, "174": {"start": {"line": 226, "column": 24}, "end": {"line": 226, "column": 52}}, "175": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 42}}, "176": {"start": {"line": 228, "column": 23}, "end": {"line": 228, "column": 55}}, "177": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 40}}, "178": {"start": {"line": 231, "column": 35}, "end": {"line": 231, "column": 75}}, "179": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 64}}, "180": {"start": {"line": 233, "column": 42}, "end": {"line": 233, "column": 85}}, "181": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 78}}, "182": {"start": {"line": 236, "column": 33}, "end": {"line": 236, "column": 70}}, "183": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 60}}, "184": {"start": {"line": 238, "column": 28}, "end": {"line": 238, "column": 72}}, "185": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 50}}, "186": {"start": {"line": 240, "column": 25}, "end": {"line": 240, "column": 62}}, "187": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 44}}, "188": {"start": {"line": 242, "column": 19}, "end": {"line": 242, "column": 34}}, "189": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 32}}, "190": {"start": {"line": 244, "column": 0}, "end": {"line": 332, "column": 3}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 5}, "end": {"line": 14, "column": 6}}, "loc": {"start": {"line": 14, "column": 11}, "end": {"line": 84, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 16}, "end": {"line": 15, "column": 17}}, "loc": {"start": {"line": 15, "column": 23}, "end": {"line": 65, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 43}, "end": {"line": 17, "column": 44}}, "loc": {"start": {"line": 17, "column": 58}, "end": {"line": 17, "column": 86}}, "line": 17}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 14}}, "loc": {"start": {"line": 20, "column": 19}, "end": {"line": 20, "column": 22}}, "line": 20}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 21}}, "loc": {"start": {"line": 21, "column": 34}, "end": {"line": 21, "column": 52}}, "line": 21}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 22, "column": 30}, "end": {"line": 22, "column": 31}}, "loc": {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": 74}}, "line": 22}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 25, "column": 42}, "end": {"line": 25, "column": 43}}, "loc": {"start": {"line": 25, "column": 57}, "end": {"line": 25, "column": 84}}, "line": 25}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 25, "column": 93}, "end": {"line": 25, "column": 94}}, "loc": {"start": {"line": 25, "column": 116}, "end": {"line": 28, "column": 5}}, "line": 25}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 25}}, "loc": {"start": {"line": 29, "column": 41}, "end": {"line": 37, "column": 5}}, "line": 29}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 30, "column": 30}, "end": {"line": 30, "column": 31}}, "loc": {"start": {"line": 30, "column": 45}, "end": {"line": 36, "column": 7}}, "line": 30}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 38, "column": 23}, "end": {"line": 38, "column": 24}}, "loc": {"start": {"line": 38, "column": 37}, "end": {"line": 43, "column": 5}}, "line": 38}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 40, "column": 76}, "end": {"line": 40, "column": 77}}, "loc": {"start": {"line": 40, "column": 113}, "end": {"line": 42, "column": 7}}, "line": 40}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 44, "column": 33}, "end": {"line": 44, "column": 34}}, "loc": {"start": {"line": 44, "column": 40}, "end": {"line": 59, "column": 5}}, "line": 44}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 49, "column": 32}, "end": {"line": 49, "column": 33}}, "loc": {"start": {"line": 49, "column": 53}, "end": {"line": 58, "column": 7}}, "line": 49}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 67, "column": 9}, "end": {"line": 67, "column": 10}}, "loc": {"start": {"line": 67, "column": 17}, "end": {"line": 70, "column": 5}}, "line": 67}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 69, "column": 14}, "end": {"line": 69, "column": 15}}, "loc": {"start": {"line": 69, "column": 20}, "end": {"line": 69, "column": 25}}, "line": 69}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 11}}, "loc": {"start": {"line": 71, "column": 18}, "end": {"line": 74, "column": 5}}, "line": 71}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 73, "column": 14}, "end": {"line": 73, "column": 15}}, "loc": {"start": {"line": 73, "column": 20}, "end": {"line": 73, "column": 26}}, "line": 73}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 75, "column": 9}, "end": {"line": 75, "column": 10}}, "loc": {"start": {"line": 75, "column": 17}, "end": {"line": 78, "column": 5}}, "line": 75}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 77, "column": 14}, "end": {"line": 77, "column": 15}}, "loc": {"start": {"line": 77, "column": 20}, "end": {"line": 77, "column": 25}}, "line": 77}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 79, "column": 12}, "end": {"line": 79, "column": 13}}, "loc": {"start": {"line": 79, "column": 20}, "end": {"line": 82, "column": 5}}, "line": 79}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 81, "column": 14}, "end": {"line": 81, "column": 15}}, "loc": {"start": {"line": 81, "column": 20}, "end": {"line": 81, "column": 28}}, "line": 81}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}, {"start": {"line": 18, "column": 4}, "end": {"line": 24, "column": 5}}], "line": 18}, "1": {"loc": {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 65}, "end": {"line": 22, "column": 67}}, {"start": {"line": 22, "column": 70}, "end": {"line": 22, "column": 74}}], "line": 22}, "2": {"loc": {"start": {"line": 29, "column": 25}, "end": {"line": 29, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 34}, "end": {"line": 29, "column": 36}}], "line": 29}, "3": {"loc": {"start": {"line": 31, "column": 8}, "end": {"line": 34, "column": 9}}, "type": "if", "locations": [{"start": {"line": 31, "column": 8}, "end": {"line": 34, "column": 9}}, {"start": {"line": 31, "column": 8}, "end": {"line": 34, "column": 9}}], "line": 31}, "4": {"loc": {"start": {"line": 33, "column": 17}, "end": {"line": 33, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 39}, "end": {"line": 33, "column": 61}}, {"start": {"line": 33, "column": 64}, "end": {"line": 33, "column": 66}}], "line": 33}, "5": {"loc": {"start": {"line": 41, "column": 15}, "end": {"line": 41, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 15}, "end": {"line": 41, "column": 42}}, {"start": {"line": 41, "column": 46}, "end": {"line": 41, "column": 80}}], "line": 41}, "6": {"loc": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "type": "if", "locations": [{"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}], "line": 45}, "7": {"loc": {"start": {"line": 51, "column": 8}, "end": {"line": 56, "column": 9}}, "type": "if", "locations": [{"start": {"line": 51, "column": 8}, "end": {"line": 56, "column": 9}}, {"start": {"line": 51, "column": 8}, "end": {"line": 56, "column": 9}}], "line": 51}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 72, "7": 72, "8": 285, "9": 72, "10": 26, "11": 0, "12": 0, "13": 0, "14": 46, "15": 206, "16": 49, "17": 46, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 46, "24": 0, "25": 0, "26": 0, "27": 46, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 46, "37": 1, "38": 44, "39": 0, "40": 16, "41": 0, "42": 6, "43": 0, "44": 6, "45": 0, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 1, "84": 1, "85": 1, "86": 1, "87": 1, "88": 1, "89": 1, "90": 1, "91": 1, "92": 1, "93": 1, "94": 1, "95": 1, "96": 1, "97": 1, "98": 1, "99": 1, "100": 1, "101": 1, "102": 1, "103": 1, "104": 1, "105": 1, "106": 1, "107": 1, "108": 1, "109": 1, "110": 1, "111": 1, "112": 1, "113": 1, "114": 1, "115": 1, "116": 1, "117": 1, "118": 1, "119": 1, "120": 1, "121": 1, "122": 1, "123": 1, "124": 1, "125": 1, "126": 1, "127": 1, "128": 1, "129": 1, "130": 1, "131": 1, "132": 1, "133": 1, "134": 1, "135": 1, "136": 1, "137": 1, "138": 1, "139": 1, "140": 1, "141": 1, "142": 1, "143": 1, "144": 1, "145": 1, "146": 1, "147": 1, "148": 1, "149": 1, "150": 1, "151": 1, "152": 1, "153": 1, "154": 1, "155": 1, "156": 1, "157": 1, "158": 1, "159": 1, "160": 1, "161": 1, "162": 1, "163": 1, "164": 1, "165": 1, "166": 1, "167": 1, "168": 1, "169": 1, "170": 1, "171": 1, "172": 1, "173": 1, "174": 1, "175": 1, "176": 1, "177": 1, "178": 1, "179": 1, "180": 1, "181": 1, "182": 1, "183": 1, "184": 1, "185": 1, "186": 1, "187": 1, "188": 1, "189": 1, "190": 1}, "f": {"0": 1, "1": 72, "2": 285, "3": 0, "4": 0, "5": 0, "6": 206, "7": 49, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 44, "15": 0, "16": 16, "17": 0, "18": 6, "19": 0, "20": 6, "21": 0}, "b": {"0": [26, 46], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}, "inputSourceMap": {"version": 3, "names": ["_provider", "require", "_arrayUtils", "GET", "POST", "PUT", "DELETE", "route", "url", "pathSections", "split", "isSimpleUrl", "every", "pathSection", "startsWith", "matchesUrl", "urlToMatch", "getParametersFromUrl", "urlParams", "filter", "reduce", "acc", "slice", "generateUrl", "params", "map", "key", "toString", "join", "urlToMatchSections", "arrayEquals", "pathSectionToMatch", "urlSections", "val", "index", "method", "versionViewPage", "exports", "versionSearchPage", "versionEditPage", "versionCreatePage", "versionsSelect", "versionInfo", "versionUpdate", "versionStateChange", "versionClone", "versionCreate", "versionDelete", "versionSearchTableConfig", "versionViewEntitiesTableConfig", "versionViewAttributesTableConfig", "versionStateMachine", "compositionDiagram", "newCompositionDiagram", "entityNodes", "cfsSearchPage", "cfsCreatePage", "cfsViewPage", "cfsEditPage", "cfsCreate", "cfsUpdate", "cfsInfo", "cfsDelete", "cfsCloneWithoutRelations", "cfsCloneWithRelations", "cfsSearchTableConfig", "rfsSearchPage", "rfsViewPage", "rfsEditPage", "rfsCreatePage", "rfsInfo", "rfsCreate", "rfsDelete", "rfsUpdate", "rfsCloneWithoutRelations", "rfsCloneWithRelations", "rfsSearchTableConfig", "resourceSearchPage", "resourceViewPage", "resourceEditPage", "resourceCreatePage", "resourceInfo", "resourceCreate", "resourceUpdate", "resourceDelete", "resourceCloneWithoutRelations", "resourceCloneWithRelations", "resourceSearchTableConfig", "entityViewAttributesTableConfig", "attributeSearchPage", "attributeViewPage", "attributeEditPage", "attributeCreatePage", "attributeInfo", "attributeCreate", "attributeUpdate", "attributeDelete", "attributeClone", "componentInfo", "componentCreate", "componentUpdate", "componentDelete", "componentClone", "attributeSearchTableConfig", "attributeViewComponentTableConfig", "attributesPrimitiveTypes", "statesConfiguration", "entityAttributes", "selectTags", "provide"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/docs/", "sources": ["demo-common-tsc-routes.js"], "sourcesContent": ["import { provide } from '~tsc-utils/routes/provider'\nimport { arrayEquals } from '~utils/array-utils.util'\n\nconst { GET, POST, PUT, DELETE } = (() => {\n  const route = (url) => {\n    const pathSections = url.split('/')\n    const isSimpleUrl = pathSections.every(pathSection => !pathSection.startsWith(':'))\n\n    if (isSimpleUrl) {\n      return {\n        url: () => url,\n        matchesUrl: (urlToMatch) => url === urlToMatch,\n        getParametersFromUrl: (urlToMatch) => url === urlToMatch ? {} : null\n      }\n    }\n\n    const urlParams = pathSections\n      .filter(pathSection => pathSection.startsWith(':'))\n      .reduce((acc, pathSection) => ({ ...acc, [pathSection]: pathSection.slice(1) }), {})\n\n    const generateUrl = (params = {}) => {\n      return pathSections.map(pathSection => {\n        if (pathSection.startsWith(':')) {\n          const key = urlParams[pathSection]\n          return params[key] != null ? params[key].toString() : ''\n        }\n        return pathSection\n      }).join('/')\n    }\n\n    const matchesUrl = (urlToMatch) => {\n      const urlToMatchSections = urlToMatch.split('/')\n      return arrayEquals(pathSections, urlToMatchSections, (pathSection, pathSectionToMatch) => {\n        return pathSection.startsWith(':') || pathSection === pathSectionToMatch\n      })\n    }\n\n    const getParametersFromUrl = (url) => {\n      if (!matchesUrl(url)) {\n        return {}\n      }\n      const urlSections = url.split('/')\n\n      return urlSections.reduce((acc, val, index) => {\n        const pathSection = pathSections[index]\n        if (pathSection.startsWith(':')) {\n          return { ...acc, [urlParams[pathSection]]: val }\n        }\n        return acc\n      })\n    }\n\n    return {\n      url: generateUrl,\n      matchesUrl,\n      getParametersFromUrl\n    }\n  }\n\n  return {\n    GET: (url) => ({ ...route(url), method: () => 'GET' }),\n    POST: (url) => ({ ...route(url), method: () => 'POST' }),\n    PUT: (url) => ({ ...route(url), method: () => 'PUT' }),\n    DELETE: (url) => ({ ...route(url), method: () => 'DELETE' })\n  }\n})()\n// VERSION PAGES\nexport const versionViewPage = GET('/tsc/version/:id')\nexport const versionSearchPage = GET('/tsc/version')\nexport const versionEditPage = GET('/tsc/version/:id/edit')\nexport const versionCreatePage = GET('/tsc/version/create')\n// VERSION CRUD\nexport const versionsSelect = GET('/tsc/versions/select')\nexport const versionInfo = GET('/tsc/version/info/:versionId')\nexport const versionUpdate = PUT('/tsc/version/:id')\nexport const versionStateChange = POST('/tsc/version/:id/statechange')\nexport const versionClone = POST('/tsc/version/:id/clone')\nexport const versionCreate = POST('/tsc/version')\nexport const versionDelete = DELETE('/tsc/version/:id')\n// VERSION UTIL\nexport const versionSearchTableConfig = GET('/tsc/table-config/version-search')\nexport const versionViewEntitiesTableConfig = GET('/tsc/table-config/version/entities')\nexport const versionViewAttributesTableConfig = GET('/tsc/table-config/version/attributes')\nexport const versionStateMachine = GET('/tsc/version/state-machine')\n// COMPOSITION DIAGRAM\nexport const compositionDiagram = GET('/tsc/compositionDiagram/:entityType/:id')\nexport const newCompositionDiagram = GET('/tsc/:entityType/compositionDiagram/:versionId')\nexport const entityNodes = GET('/tsc/compositionDiagram/sidebar/:entityType/:versionName')\n// CFS PAGE\nexport const cfsSearchPage = GET('/tsc/cfs')\nexport const cfsCreatePage = GET('/tsc/cfs/create/:versionId')\nexport const cfsViewPage = GET('/tsc/cfs/:entityId')\nexport const cfsEditPage = GET('/tsc/cfs/:entityId/edit')\n// CFS CRUD\nexport const cfsCreate = POST('/tsc/cfs')\nexport const cfsUpdate = PUT('/tsc/cfs/:id')\nexport const cfsInfo = GET('/tsc/cfs/:id')\nexport const cfsDelete = DELETE('/tsc/cfs/:id')\nexport const cfsCloneWithoutRelations = POST('/tsc/cfs/:id/clone')\nexport const cfsCloneWithRelations = POST('/tsc/cfs/:id/cloneWithRelations')\nexport const cfsSearchTableConfig = GET('/tsc/table-config/entity/search')\n// RFS PAGES\nexport const rfsSearchPage = GET('/tsc/rfs')\nexport const rfsViewPage = GET('tsc/rfs/:entityId')\nexport const rfsEditPage = GET('/tsc/rfs/:entityId/edit')\nexport const rfsCreatePage = GET('/tsc/rfs/create/:versionId')\n// RFS CRUD\nexport const rfsInfo = GET('/tsc/rfs/:id')\nexport const rfsCreate = POST('/tsc/rfs')\nexport const rfsDelete = DELETE('/tsc/rfs/:id')\nexport const rfsUpdate = PUT('/tsc/rfs/:id')\nexport const rfsCloneWithoutRelations = POST('/tsc/rfs/:id/clone')\nexport const rfsCloneWithRelations = POST('/tsc/rfs/:id/cloneWithRelations')\nexport const rfsSearchTableConfig = GET('/tsc/table-config/entity/search')\n// RESOURCE PAGES\nexport const resourceSearchPage = GET('/tsc/resource')\nexport const resourceViewPage = GET('tsc/resource/:entityId')\nexport const resourceEditPage = GET('/tsc/resource/:entityId/edit')\nexport const resourceCreatePage = GET('/tsc/resource/create/:versionId')\n// RESOURCE CRUD\nexport const resourceInfo = GET('/tsc/resource/:id')\nexport const resourceCreate = POST('/tsc/resource')\nexport const resourceUpdate = PUT('/tsc/resource/:id')\nexport const resourceDelete = DELETE('/tsc/resource/:id')\nexport const resourceCloneWithoutRelations = POST('/tsc/resource/:id/clone')\nexport const resourceCloneWithRelations = POST('/tsc/resource/:id/cloneWithRelations')\nexport const resourceSearchTableConfig = GET('/tsc/table-config/entity/search')\n// ENTITY UTIL\nexport const entityViewAttributesTableConfig = GET('/tsc/table-config/entity/attributes')\n// ATTRIBUTE PAGES\nexport const attributeSearchPage = GET('/tsc/attribute')\nexport const attributeViewPage = GET('tsc/attribute/:attributeId')\nexport const attributeEditPage = GET('/tsc/attribute/:attributeId/edit')\nexport const attributeCreatePage = GET('/tsc/attribute/create/:versionId')\n// ATTRIBUTE CRUD\nexport const attributeInfo = GET('/tsc/attribute/:id')\nexport const attributeCreate = POST('/tsc/attribute')\nexport const attributeUpdate = PUT('/tsc/attribute/:id')\nexport const attributeDelete = DELETE('/tsc/attribute/:id')\nexport const attributeClone = POST('/tsc/attribute/:id/clone')\nexport const componentInfo = GET('/tsc/component/:id')\nexport const componentCreate = POST('/tsc/component')\nexport const componentUpdate = PUT('/tsc/component/:id')\nexport const componentDelete = DELETE('/tsc/component/:id')\nexport const componentClone = POST('/tsc/component/:id/clone')\n// ATTRIBUTE UTILS\nexport const attributeSearchTableConfig = GET('tsc/table-config/attribute/search')\nexport const attributeViewComponentTableConfig = GET('tsc/table-config/attribute/component')\n// UTIL ROUTES\nexport const attributesPrimitiveTypes = GET('/tsc/attributes/primitiveTypes')\nexport const statesConfiguration = GET('/tsc/config/statePermissions/:stateId')\nexport const entityAttributes = GET('/tsc/aggregatedtype/attributes')\nexport const selectTags = GET('/tsc/tag')\n\nprovide({\n  // VERSION PAGES\n  versionSearchPage,\n  versionCreatePage,\n  versionEditPage,\n  versionViewPage,\n  // VERSION CRUD\n  versionInfo,\n  versionsSelect,\n  versionCreate,\n  versionUpdate,\n  versionStateChange,\n  versionClone,\n  versionDelete,\n  // VERSION UTIL\n  versionSearchTableConfig,\n  versionViewEntitiesTableConfig,\n  versionViewAttributesTableConfig,\n  versionStateMachine,\n  // COMPOSITION DIAGRAM\n  entityNodes,\n  compositionDiagram,\n  newCompositionDiagram,\n  // CFS PAGES\n  cfsSearchPage,\n  cfsCreatePage,\n  cfsViewPage,\n  cfsEditPage,\n  // CFS CRUD\n  cfsInfo,\n  cfsCreate,\n  cfsUpdate,\n  cfsDelete,\n  cfsCloneWithoutRelations,\n  cfsCloneWithRelations,\n  cfsSearchTableConfig,\n  // RFS PAGES\n  rfsSearchPage,\n  rfsViewPage,\n  rfsEditPage,\n  rfsCreatePage,\n  // RFS CRUD\n  rfsInfo,\n  rfsCreate,\n  rfsDelete,\n  rfsUpdate,\n  rfsCloneWithoutRelations,\n  rfsCloneWithRelations,\n  rfsSearchTableConfig,\n  // RESOURCE PAGES\n  resourceSearchPage,\n  resourceViewPage,\n  resourceEditPage,\n  resourceCreatePage,\n  // RESOURCE CRUD\n  resourceInfo,\n  resourceCreate,\n  resourceUpdate,\n  resourceDelete,\n  resourceCloneWithoutRelations,\n  resourceCloneWithRelations,\n  resourceSearchTableConfig,\n  // ENTITY UTIL\n  entityViewAttributesTableConfig,\n  // ATTRIBUTE PAGES\n  attributeSearchPage,\n  attributeViewPage,\n  attributeEditPage,\n  attributeCreatePage,\n  // ATTRIBUTE CRUD\n  attributeInfo,\n  attributeCreate,\n  attributeUpdate,\n  attributeDelete,\n  attributeClone,\n  componentInfo,\n  componentCreate,\n  componentUpdate,\n  componentDelete,\n  componentClone,\n  // ATTRIBUTE UTILS\n  attributeSearchTableConfig,\n  attributeViewComponentTableConfig,\n  // UTIL ROUTES\n  selectTags,\n  entityAttributes,\n  statesConfiguration,\n  attributesPrimitiveTypes,\n})\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAEA,MAAM;EAAEE,GAAG;EAAEC,IAAI;EAAEC,GAAG;EAAEC;AAAO,CAAC,GAAG,CAAC,MAAM;EACxC,MAAMC,KAAK,GAAIC,GAAG,IAAK;IACrB,MAAMC,YAAY,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IACnC,MAAMC,WAAW,GAAGF,YAAY,CAACG,KAAK,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,CAAC;IAEnF,IAAIH,WAAW,EAAE;MACf,OAAO;QACLH,GAAG,EAAEA,CAAA,KAAMA,GAAG;QACdO,UAAU,EAAGC,UAAU,IAAKR,GAAG,KAAKQ,UAAU;QAC9CC,oBAAoB,EAAGD,UAAU,IAAKR,GAAG,KAAKQ,UAAU,GAAG,CAAC,CAAC,GAAG;MAClE,CAAC;IACH;IAEA,MAAME,SAAS,GAAGT,YAAY,CAC3BU,MAAM,CAACN,WAAW,IAAIA,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,CAAC,CAClDM,MAAM,CAAC,CAACC,GAAG,EAAER,WAAW,MAAM;MAAE,GAAGQ,GAAG;MAAE,CAACR,WAAW,GAAGA,WAAW,CAACS,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtF,MAAMC,WAAW,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;MACnC,OAAOf,YAAY,CAACgB,GAAG,CAACZ,WAAW,IAAI;QACrC,IAAIA,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC/B,MAAMY,GAAG,GAAGR,SAAS,CAACL,WAAW,CAAC;UAClC,OAAOW,MAAM,CAACE,GAAG,CAAC,IAAI,IAAI,GAAGF,MAAM,CAACE,GAAG,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAG,EAAE;QAC1D;QACA,OAAOd,WAAW;MACpB,CAAC,CAAC,CAACe,IAAI,CAAC,GAAG,CAAC;IACd,CAAC;IAED,MAAMb,UAAU,GAAIC,UAAU,IAAK;MACjC,MAAMa,kBAAkB,GAAGb,UAAU,CAACN,KAAK,CAAC,GAAG,CAAC;MAChD,OAAO,IAAAoB,uBAAW,EAACrB,YAAY,EAAEoB,kBAAkB,EAAE,CAAChB,WAAW,EAAEkB,kBAAkB,KAAK;QACxF,OAAOlB,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,WAAW,KAAKkB,kBAAkB;MAC1E,CAAC,CAAC;IACJ,CAAC;IAED,MAAMd,oBAAoB,GAAIT,GAAG,IAAK;MACpC,IAAI,CAACO,UAAU,CAACP,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,CAAC;MACX;MACA,MAAMwB,WAAW,GAAGxB,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;MAElC,OAAOsB,WAAW,CAACZ,MAAM,CAAC,CAACC,GAAG,EAAEY,GAAG,EAAEC,KAAK,KAAK;QAC7C,MAAMrB,WAAW,GAAGJ,YAAY,CAACyB,KAAK,CAAC;QACvC,IAAIrB,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC/B,OAAO;YAAE,GAAGO,GAAG;YAAE,CAACH,SAAS,CAACL,WAAW,CAAC,GAAGoB;UAAI,CAAC;QAClD;QACA,OAAOZ,GAAG;MACZ,CAAC,CAAC;IACJ,CAAC;IAED,OAAO;MACLb,GAAG,EAAEe,WAAW;MAChBR,UAAU;MACVE;IACF,CAAC;EACH,CAAC;EAED,OAAO;IACLd,GAAG,EAAGK,GAAG,KAAM;MAAE,GAAGD,KAAK,CAACC,GAAG,CAAC;MAAE2B,MAAM,EAAEA,CAAA,KAAM;IAAM,CAAC,CAAC;IACtD/B,IAAI,EAAGI,GAAG,KAAM;MAAE,GAAGD,KAAK,CAACC,GAAG,CAAC;MAAE2B,MAAM,EAAEA,CAAA,KAAM;IAAO,CAAC,CAAC;IACxD9B,GAAG,EAAGG,GAAG,KAAM;MAAE,GAAGD,KAAK,CAACC,GAAG,CAAC;MAAE2B,MAAM,EAAEA,CAAA,KAAM;IAAM,CAAC,CAAC;IACtD7B,MAAM,EAAGE,GAAG,KAAM;MAAE,GAAGD,KAAK,CAACC,GAAG,CAAC;MAAE2B,MAAM,EAAEA,CAAA,KAAM;IAAS,CAAC;EAC7D,CAAC;AACH,CAAC,EAAE,CAAC;AACJ;AACO,MAAMC,eAAe,GAAGjC,GAAG,CAAC,kBAAkB,CAAC;AAAAkC,OAAA,CAAAD,eAAA,GAAAA,eAAA;AAC/C,MAAME,iBAAiB,GAAGnC,GAAG,CAAC,cAAc,CAAC;AAAAkC,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAC7C,MAAMC,eAAe,GAAGpC,GAAG,CAAC,uBAAuB,CAAC;AAAAkC,OAAA,CAAAE,eAAA,GAAAA,eAAA;AACpD,MAAMC,iBAAiB,GAAGrC,GAAG,CAAC,qBAAqB,CAAC;AAC3D;AAAAkC,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AACO,MAAMC,cAAc,GAAGtC,GAAG,CAAC,sBAAsB,CAAC;AAAAkC,OAAA,CAAAI,cAAA,GAAAA,cAAA;AAClD,MAAMC,WAAW,GAAGvC,GAAG,CAAC,8BAA8B,CAAC;AAAAkC,OAAA,CAAAK,WAAA,GAAAA,WAAA;AACvD,MAAMC,aAAa,GAAGtC,GAAG,CAAC,kBAAkB,CAAC;AAAAgC,OAAA,CAAAM,aAAA,GAAAA,aAAA;AAC7C,MAAMC,kBAAkB,GAAGxC,IAAI,CAAC,8BAA8B,CAAC;AAAAiC,OAAA,CAAAO,kBAAA,GAAAA,kBAAA;AAC/D,MAAMC,YAAY,GAAGzC,IAAI,CAAC,wBAAwB,CAAC;AAAAiC,OAAA,CAAAQ,YAAA,GAAAA,YAAA;AACnD,MAAMC,aAAa,GAAG1C,IAAI,CAAC,cAAc,CAAC;AAAAiC,OAAA,CAAAS,aAAA,GAAAA,aAAA;AAC1C,MAAMC,aAAa,GAAGzC,MAAM,CAAC,kBAAkB,CAAC;AACvD;AAAA+B,OAAA,CAAAU,aAAA,GAAAA,aAAA;AACO,MAAMC,wBAAwB,GAAG7C,GAAG,CAAC,kCAAkC,CAAC;AAAAkC,OAAA,CAAAW,wBAAA,GAAAA,wBAAA;AACxE,MAAMC,8BAA8B,GAAG9C,GAAG,CAAC,oCAAoC,CAAC;AAAAkC,OAAA,CAAAY,8BAAA,GAAAA,8BAAA;AAChF,MAAMC,gCAAgC,GAAG/C,GAAG,CAAC,sCAAsC,CAAC;AAAAkC,OAAA,CAAAa,gCAAA,GAAAA,gCAAA;AACpF,MAAMC,mBAAmB,GAAGhD,GAAG,CAAC,4BAA4B,CAAC;AACpE;AAAAkC,OAAA,CAAAc,mBAAA,GAAAA,mBAAA;AACO,MAAMC,kBAAkB,GAAGjD,GAAG,CAAC,yCAAyC,CAAC;AAAAkC,OAAA,CAAAe,kBAAA,GAAAA,kBAAA;AACzE,MAAMC,qBAAqB,GAAGlD,GAAG,CAAC,gDAAgD,CAAC;AAAAkC,OAAA,CAAAgB,qBAAA,GAAAA,qBAAA;AACnF,MAAMC,WAAW,GAAGnD,GAAG,CAAC,0DAA0D,CAAC;AAC1F;AAAAkC,OAAA,CAAAiB,WAAA,GAAAA,WAAA;AACO,MAAMC,aAAa,GAAGpD,GAAG,CAAC,UAAU,CAAC;AAAAkC,OAAA,CAAAkB,aAAA,GAAAA,aAAA;AACrC,MAAMC,aAAa,GAAGrD,GAAG,CAAC,4BAA4B,CAAC;AAAAkC,OAAA,CAAAmB,aAAA,GAAAA,aAAA;AACvD,MAAMC,WAAW,GAAGtD,GAAG,CAAC,oBAAoB,CAAC;AAAAkC,OAAA,CAAAoB,WAAA,GAAAA,WAAA;AAC7C,MAAMC,WAAW,GAAGvD,GAAG,CAAC,yBAAyB,CAAC;AACzD;AAAAkC,OAAA,CAAAqB,WAAA,GAAAA,WAAA;AACO,MAAMC,SAAS,GAAGvD,IAAI,CAAC,UAAU,CAAC;AAAAiC,OAAA,CAAAsB,SAAA,GAAAA,SAAA;AAClC,MAAMC,SAAS,GAAGvD,GAAG,CAAC,cAAc,CAAC;AAAAgC,OAAA,CAAAuB,SAAA,GAAAA,SAAA;AACrC,MAAMC,OAAO,GAAG1D,GAAG,CAAC,cAAc,CAAC;AAAAkC,OAAA,CAAAwB,OAAA,GAAAA,OAAA;AACnC,MAAMC,SAAS,GAAGxD,MAAM,CAAC,cAAc,CAAC;AAAA+B,OAAA,CAAAyB,SAAA,GAAAA,SAAA;AACxC,MAAMC,wBAAwB,GAAG3D,IAAI,CAAC,oBAAoB,CAAC;AAAAiC,OAAA,CAAA0B,wBAAA,GAAAA,wBAAA;AAC3D,MAAMC,qBAAqB,GAAG5D,IAAI,CAAC,iCAAiC,CAAC;AAAAiC,OAAA,CAAA2B,qBAAA,GAAAA,qBAAA;AACrE,MAAMC,oBAAoB,GAAG9D,GAAG,CAAC,iCAAiC,CAAC;AAC1E;AAAAkC,OAAA,CAAA4B,oBAAA,GAAAA,oBAAA;AACO,MAAMC,aAAa,GAAG/D,GAAG,CAAC,UAAU,CAAC;AAAAkC,OAAA,CAAA6B,aAAA,GAAAA,aAAA;AACrC,MAAMC,WAAW,GAAGhE,GAAG,CAAC,mBAAmB,CAAC;AAAAkC,OAAA,CAAA8B,WAAA,GAAAA,WAAA;AAC5C,MAAMC,WAAW,GAAGjE,GAAG,CAAC,yBAAyB,CAAC;AAAAkC,OAAA,CAAA+B,WAAA,GAAAA,WAAA;AAClD,MAAMC,aAAa,GAAGlE,GAAG,CAAC,4BAA4B,CAAC;AAC9D;AAAAkC,OAAA,CAAAgC,aAAA,GAAAA,aAAA;AACO,MAAMC,OAAO,GAAGnE,GAAG,CAAC,cAAc,CAAC;AAAAkC,OAAA,CAAAiC,OAAA,GAAAA,OAAA;AACnC,MAAMC,SAAS,GAAGnE,IAAI,CAAC,UAAU,CAAC;AAAAiC,OAAA,CAAAkC,SAAA,GAAAA,SAAA;AAClC,MAAMC,SAAS,GAAGlE,MAAM,CAAC,cAAc,CAAC;AAAA+B,OAAA,CAAAmC,SAAA,GAAAA,SAAA;AACxC,MAAMC,SAAS,GAAGpE,GAAG,CAAC,cAAc,CAAC;AAAAgC,OAAA,CAAAoC,SAAA,GAAAA,SAAA;AACrC,MAAMC,wBAAwB,GAAGtE,IAAI,CAAC,oBAAoB,CAAC;AAAAiC,OAAA,CAAAqC,wBAAA,GAAAA,wBAAA;AAC3D,MAAMC,qBAAqB,GAAGvE,IAAI,CAAC,iCAAiC,CAAC;AAAAiC,OAAA,CAAAsC,qBAAA,GAAAA,qBAAA;AACrE,MAAMC,oBAAoB,GAAGzE,GAAG,CAAC,iCAAiC,CAAC;AAC1E;AAAAkC,OAAA,CAAAuC,oBAAA,GAAAA,oBAAA;AACO,MAAMC,kBAAkB,GAAG1E,GAAG,CAAC,eAAe,CAAC;AAAAkC,OAAA,CAAAwC,kBAAA,GAAAA,kBAAA;AAC/C,MAAMC,gBAAgB,GAAG3E,GAAG,CAAC,wBAAwB,CAAC;AAAAkC,OAAA,CAAAyC,gBAAA,GAAAA,gBAAA;AACtD,MAAMC,gBAAgB,GAAG5E,GAAG,CAAC,8BAA8B,CAAC;AAAAkC,OAAA,CAAA0C,gBAAA,GAAAA,gBAAA;AAC5D,MAAMC,kBAAkB,GAAG7E,GAAG,CAAC,iCAAiC,CAAC;AACxE;AAAAkC,OAAA,CAAA2C,kBAAA,GAAAA,kBAAA;AACO,MAAMC,YAAY,GAAG9E,GAAG,CAAC,mBAAmB,CAAC;AAAAkC,OAAA,CAAA4C,YAAA,GAAAA,YAAA;AAC7C,MAAMC,cAAc,GAAG9E,IAAI,CAAC,eAAe,CAAC;AAAAiC,OAAA,CAAA6C,cAAA,GAAAA,cAAA;AAC5C,MAAMC,cAAc,GAAG9E,GAAG,CAAC,mBAAmB,CAAC;AAAAgC,OAAA,CAAA8C,cAAA,GAAAA,cAAA;AAC/C,MAAMC,cAAc,GAAG9E,MAAM,CAAC,mBAAmB,CAAC;AAAA+B,OAAA,CAAA+C,cAAA,GAAAA,cAAA;AAClD,MAAMC,6BAA6B,GAAGjF,IAAI,CAAC,yBAAyB,CAAC;AAAAiC,OAAA,CAAAgD,6BAAA,GAAAA,6BAAA;AACrE,MAAMC,0BAA0B,GAAGlF,IAAI,CAAC,sCAAsC,CAAC;AAAAiC,OAAA,CAAAiD,0BAAA,GAAAA,0BAAA;AAC/E,MAAMC,yBAAyB,GAAGpF,GAAG,CAAC,iCAAiC,CAAC;AAC/E;AAAAkC,OAAA,CAAAkD,yBAAA,GAAAA,yBAAA;AACO,MAAMC,+BAA+B,GAAGrF,GAAG,CAAC,qCAAqC,CAAC;AACzF;AAAAkC,OAAA,CAAAmD,+BAAA,GAAAA,+BAAA;AACO,MAAMC,mBAAmB,GAAGtF,GAAG,CAAC,gBAAgB,CAAC;AAAAkC,OAAA,CAAAoD,mBAAA,GAAAA,mBAAA;AACjD,MAAMC,iBAAiB,GAAGvF,GAAG,CAAC,4BAA4B,CAAC;AAAAkC,OAAA,CAAAqD,iBAAA,GAAAA,iBAAA;AAC3D,MAAMC,iBAAiB,GAAGxF,GAAG,CAAC,kCAAkC,CAAC;AAAAkC,OAAA,CAAAsD,iBAAA,GAAAA,iBAAA;AACjE,MAAMC,mBAAmB,GAAGzF,GAAG,CAAC,kCAAkC,CAAC;AAC1E;AAAAkC,OAAA,CAAAuD,mBAAA,GAAAA,mBAAA;AACO,MAAMC,aAAa,GAAG1F,GAAG,CAAC,oBAAoB,CAAC;AAAAkC,OAAA,CAAAwD,aAAA,GAAAA,aAAA;AAC/C,MAAMC,eAAe,GAAG1F,IAAI,CAAC,gBAAgB,CAAC;AAAAiC,OAAA,CAAAyD,eAAA,GAAAA,eAAA;AAC9C,MAAMC,eAAe,GAAG1F,GAAG,CAAC,oBAAoB,CAAC;AAAAgC,OAAA,CAAA0D,eAAA,GAAAA,eAAA;AACjD,MAAMC,eAAe,GAAG1F,MAAM,CAAC,oBAAoB,CAAC;AAAA+B,OAAA,CAAA2D,eAAA,GAAAA,eAAA;AACpD,MAAMC,cAAc,GAAG7F,IAAI,CAAC,0BAA0B,CAAC;AAAAiC,OAAA,CAAA4D,cAAA,GAAAA,cAAA;AACvD,MAAMC,aAAa,GAAG/F,GAAG,CAAC,oBAAoB,CAAC;AAAAkC,OAAA,CAAA6D,aAAA,GAAAA,aAAA;AAC/C,MAAMC,eAAe,GAAG/F,IAAI,CAAC,gBAAgB,CAAC;AAAAiC,OAAA,CAAA8D,eAAA,GAAAA,eAAA;AAC9C,MAAMC,eAAe,GAAG/F,GAAG,CAAC,oBAAoB,CAAC;AAAAgC,OAAA,CAAA+D,eAAA,GAAAA,eAAA;AACjD,MAAMC,eAAe,GAAG/F,MAAM,CAAC,oBAAoB,CAAC;AAAA+B,OAAA,CAAAgE,eAAA,GAAAA,eAAA;AACpD,MAAMC,cAAc,GAAGlG,IAAI,CAAC,0BAA0B,CAAC;AAC9D;AAAAiC,OAAA,CAAAiE,cAAA,GAAAA,cAAA;AACO,MAAMC,0BAA0B,GAAGpG,GAAG,CAAC,mCAAmC,CAAC;AAAAkC,OAAA,CAAAkE,0BAAA,GAAAA,0BAAA;AAC3E,MAAMC,iCAAiC,GAAGrG,GAAG,CAAC,sCAAsC,CAAC;AAC5F;AAAAkC,OAAA,CAAAmE,iCAAA,GAAAA,iCAAA;AACO,MAAMC,wBAAwB,GAAGtG,GAAG,CAAC,gCAAgC,CAAC;AAAAkC,OAAA,CAAAoE,wBAAA,GAAAA,wBAAA;AACtE,MAAMC,mBAAmB,GAAGvG,GAAG,CAAC,uCAAuC,CAAC;AAAAkC,OAAA,CAAAqE,mBAAA,GAAAA,mBAAA;AACxE,MAAMC,gBAAgB,GAAGxG,GAAG,CAAC,gCAAgC,CAAC;AAAAkC,OAAA,CAAAsE,gBAAA,GAAAA,gBAAA;AAC9D,MAAMC,UAAU,GAAGzG,GAAG,CAAC,UAAU,CAAC;AAAAkC,OAAA,CAAAuE,UAAA,GAAAA,UAAA;AAEzC,IAAAC,iBAAO,EAAC;EACN;EACAvE,iBAAiB;EACjBE,iBAAiB;EACjBD,eAAe;EACfH,eAAe;EACf;EACAM,WAAW;EACXD,cAAc;EACdK,aAAa;EACbH,aAAa;EACbC,kBAAkB;EAClBC,YAAY;EACZE,aAAa;EACb;EACAC,wBAAwB;EACxBC,8BAA8B;EAC9BC,gCAAgC;EAChCC,mBAAmB;EACnB;EACAG,WAAW;EACXF,kBAAkB;EAClBC,qBAAqB;EACrB;EACAE,aAAa;EACbC,aAAa;EACbC,WAAW;EACXC,WAAW;EACX;EACAG,OAAO;EACPF,SAAS;EACTC,SAAS;EACTE,SAAS;EACTC,wBAAwB;EACxBC,qBAAqB;EACrBC,oBAAoB;EACpB;EACAC,aAAa;EACbC,WAAW;EACXC,WAAW;EACXC,aAAa;EACb;EACAC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,wBAAwB;EACxBC,qBAAqB;EACrBC,oBAAoB;EACpB;EACAC,kBAAkB;EAClBC,gBAAgB;EAChBC,gBAAgB;EAChBC,kBAAkB;EAClB;EACAC,YAAY;EACZC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,6BAA6B;EAC7BC,0BAA0B;EAC1BC,yBAAyB;EACzB;EACAC,+BAA+B;EAC/B;EACAC,mBAAmB;EACnBC,iBAAiB;EACjBC,iBAAiB;EACjBC,mBAAmB;EACnB;EACAC,aAAa;EACbC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,aAAa;EACbC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,cAAc;EACd;EACAC,0BAA0B;EAC1BC,iCAAiC;EACjC;EACAI,UAAU;EACVD,gBAAgB;EAChBD,mBAAmB;EACnBD;AACF,CAAC,CAAC"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e7e984f8eb0796f908f196cb3a1011fbf075f573", "contentHash": "60f525c98c55756e1ed403cea194c3fa5c9744085adab194d170e179670b203d"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/provider.ts": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/provider.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "2": {"start": {"line": 7, "column": 20}, "end": {"line": 11, "column": 1}}, "3": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 111}}, "4": {"start": {"line": 8, "column": 70}, "end": {"line": 8, "column": 93}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 86}}, "6": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 20}}, "7": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 19}}, "8": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 76}}, "9": {"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 27}}, "10": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 26}}, "11": {"start": {"line": 15, "column": 16}, "end": {"line": 24, "column": 4}}, "12": {"start": {"line": 16, "column": 48}, "end": {"line": 19, "column": 5}}, "13": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 163}}, "14": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 15}}, "15": {"start": {"line": 27, "column": 17}, "end": {"line": 115, "column": 2}}, "16": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 28}}, "17": {"start": {"line": 117, "column": 16}, "end": {"line": 135, "column": 1}}, "18": {"start": {"line": 118, "column": 2}, "end": {"line": 134, "column": 5}}, "19": {"start": {"line": 118, "column": 51}, "end": {"line": 118, "column": 72}}, "20": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "21": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 151}}, "22": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 13}}, "23": {"start": {"line": 128, "column": 4}, "end": {"line": 133, "column": 7}}, "24": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 21}}, "loc": {"start": {"line": 7, "column": 46}, "end": {"line": 11, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 53}, "end": {"line": 8, "column": 54}}, "loc": {"start": {"line": 8, "column": 70}, "end": {"line": 8, "column": 93}}, "line": 8}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 9}, "end": {"line": 10, "column": 10}}, "loc": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 19}}, "line": 10}, "3": {"name": "routerDefinition", "decl": {"start": {"line": 13, "column": 9}, "end": {"line": 13, "column": 25}}, "loc": {"start": {"line": 13, "column": 28}, "end": {"line": 26, "column": 1}}, "line": 13}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 18}}, "loc": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 26}}, "line": 14}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 16, "column": 11}, "end": {"line": 16, "column": 12}}, "loc": {"start": {"line": 16, "column": 48}, "end": {"line": 19, "column": 5}}, "line": 16}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 20, "column": 9}, "end": {"line": 20, "column": 10}}, "loc": {"start": {"line": 20, "column": 27}, "end": {"line": 23, "column": 5}}, "line": 20}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 117, "column": 16}, "end": {"line": 117, "column": 17}}, "loc": {"start": {"line": 117, "column": 34}, "end": {"line": 135, "column": 1}}, "line": 117}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 118, "column": 40}, "end": {"line": 118, "column": 41}}, "loc": {"start": {"line": 118, "column": 51}, "end": {"line": 118, "column": 72}}, "line": 118}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 118, "column": 82}, "end": {"line": 118, "column": 83}}, "loc": {"start": {"line": 123, "column": 9}, "end": {"line": 134, "column": 3}}, "line": 123}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 111}}, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 94}}, {"start": {"line": 8, "column": 98}, "end": {"line": 8, "column": 111}}], "line": 8}, "1": {"loc": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 162}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 150}}, {"start": {"line": 22, "column": 153}, "end": {"line": 22, "column": 162}}], "line": 22}, "2": {"loc": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 150}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 111}, "end": {"line": 22, "column": 123}}, {"start": {"line": 22, "column": 126}, "end": {"line": 22, "column": 150}}], "line": 22}, "3": {"loc": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 108}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 81}}, {"start": {"line": 22, "column": 85}, "end": {"line": 22, "column": 108}}], "line": 22}, "4": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}], "line": 124}, "5": {"loc": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 101}}, "type": "binary-expr", "locations": [{"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 33}}, {"start": {"line": 124, "column": 37}, "end": {"line": 124, "column": 65}}, {"start": {"line": 124, "column": 69}, "end": {"line": 124, "column": 101}}], "line": 124}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 1, "9": 72, "10": 0, "11": 72, "12": 0, "13": 0, "14": 72, "15": 1, "16": 1, "17": 1, "18": 1, "19": 72, "20": 72, "21": 0, "22": 0, "23": 72, "24": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 72, "4": 0, "5": 0, "6": 0, "7": 1, "8": 72, "9": 72}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 72], "5": [72, 72, 72]}, "inputSourceMap": {"version": 3, "names": ["warnMessage", "targetRouter", "method", "routerName", "Object", "entries", "instance", "find", "_", "router", "console", "error", "String", "validMethods", "routerDefinition", "proxy", "Proxy", "apply", "target", "thisArg", "argumentsList", "url", "get", "prop", "_router$prop", "includes", "undefined", "freeze", "versionSearchPage", "versionCreatePage", "versionViewPage", "versionEditPage", "versionInfo", "versionsSelect", "versionCreate", "versionUpdate", "versionStateChange", "versionClone", "versionDelete", "versionViewEntitiesTableConfig", "versionViewAttributesTableConfig", "versionSearchTableConfig", "versionStateMachine", "entityNodes", "compositionDiagram", "newCompositionDiagram", "cfsCreatePage", "cfsSearchPage", "cfsViewPage", "cfsEditPage", "cfsInfo", "cfsCreate", "cfsUpdate", "cfsDelete", "cfsCloneWithoutRelations", "cfsCloneWithRelations", "rfsSearchPage", "rfsViewPage", "rfsEditPage", "rfsCreatePage", "rfsInfo", "rfsCreate", "rfsDelete", "rfsUpdate", "rfsCloneWithoutRelations", "rfsCloneWithRelations", "resourceSearchPage", "resourceViewPage", "resourceEditPage", "resourceCreatePage", "resourceInfo", "resourceCreate", "resourceUpdate", "resourceDelete", "resourceCloneWithoutRelations", "resourceCloneWithRelations", "entityViewAttributesTableConfig", "cfsSearchTableConfig", "rfsSearchTableConfig", "resourceSearchTableConfig", "attributeSearchPage", "attributeViewPage", "attributeEditPage", "attributeCreatePage", "attributeInfo", "componentInfo", "attributeCreate", "componentCreate", "attributeUpdate", "componentUpdate", "attributeDelete", "componentDelete", "attributeClone", "componentClone", "attributeSearchTableConfig", "attributeViewComponentTableConfig", "attributesPrimitiveTypes", "entityAttributes", "statesConfiguration", "selectTags", "exports", "provide", "implementation", "filter", "key", "for<PERSON>ach", "matchesUrl", "getParametersFromUrl", "assign"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/", "sources": ["provider.ts"], "sourcesContent": ["const warnMessage = (targetRouter, method: string | symbol) => {\n  const [routerName] = Object.entries(instance).find(([_, router]) => router === targetRouter) || ['undefined']\n  console.error(`router ${routerName} implementation ${String(method)} not provided`)\n  return () => null\n}\n\nconst validMethods = ['url', 'method', 'matchesUrl', 'getParametersFromUrl']\nfunction routerDefinition<T extends object | undefined = undefined> (): CallableRoute<T> {\n  const router = () => ({})\n  const proxy = new Proxy(router, {\n    apply: (target, thisArg, argumentsList) => ({ method: proxy.method(...argumentsList), url: proxy.url(...argumentsList) }),\n    get: (target, prop) => validMethods.includes(prop as string) ? router[prop] ?? warnMessage(proxy, prop) : undefined\n  })\n  return proxy\n}\n\nexport const instance = Object.freeze({\n  // VERSION PAGES\n  versionSearchPage: routerDefinition(),\n  versionCreatePage: routerDefinition(),\n  versionViewPage: routerDefinition<{ id: string }>(),\n  versionEditPage: routerDefinition<{ id: string }>(),\n  // VERSION CRUD\n  versionInfo: routerDefinition<{ versionId: string }>(),\n  versionsSelect: routerDefinition(),\n  versionCreate: routerDefinition(),\n  versionUpdate: routerDefinition<{ id: string }>(),\n  versionStateChange: routerDefinition<{ id: string }>(),\n  versionClone: routerDefinition<{ id: string }>(),\n  versionDelete: routerDefinition<{ id: string }>(),\n  // VERSION UTIL\n  versionViewEntitiesTableConfig: routerDefinition(),\n  versionViewAttributesTableConfig: routerDefinition(),\n  versionSearchTableConfig: routerDefinition(),\n  versionStateMachine: routerDefinition(),\n  // COMPOSITION DIAGRAM\n  entityNodes: routerDefinition<{ entityType: string, versionName: string }>(),\n  compositionDiagram: routerDefinition<{ entityType: string, id: string }>(),\n  newCompositionDiagram: routerDefinition<{ entityType: string, versionId: string }>(),\n  // CFS Pages\n  cfsCreatePage: routerDefinition<{ versionId: string }>(),\n  cfsSearchPage: routerDefinition(),\n  cfsViewPage: routerDefinition<{ entityId: string }>(),\n  cfsEditPage: routerDefinition<{ entityId: string }>(),\n  // CFS Crud\n  cfsInfo: routerDefinition<{ id: string }>(),\n  cfsCreate: routerDefinition(),\n  cfsUpdate: routerDefinition<{ id: string }>(),\n  cfsDelete: routerDefinition<{ id: string }>(),\n  cfsCloneWithoutRelations: routerDefinition<{ id: string }>(),\n  cfsCloneWithRelations: routerDefinition<{ id: string }>(),\n  // RFS Pages\n  rfsSearchPage: routerDefinition(),\n  rfsViewPage: routerDefinition<{ entityId: string }>(),\n  rfsEditPage: routerDefinition<{ entityId: string }>(),\n  rfsCreatePage: routerDefinition<{ versionId: string }>(),\n  // RFS Crud\n  rfsInfo: routerDefinition<{ id: string }>(),\n  rfsCreate: routerDefinition(),\n  rfsDelete: routerDefinition<{ id: string }>(),\n  rfsUpdate: routerDefinition<{ id: string }>(),\n  rfsCloneWithoutRelations: routerDefinition<{ id: string }>(),\n  rfsCloneWithRelations: routerDefinition<{ id: string }>(),\n  // RESOURCE Pages\n  resourceSearchPage: routerDefinition(),\n  resourceViewPage: routerDefinition<{ entityId: string }>(),\n  resourceEditPage: routerDefinition<{ entityId: string }>(),\n  resourceCreatePage: routerDefinition<{ versionId: string }>(),\n  // RESOURCE Crud\n  resourceInfo: routerDefinition<{ id: string }>(),\n  resourceCreate: routerDefinition(),\n  resourceUpdate: routerDefinition<{ id: string }>(),\n  resourceDelete: routerDefinition<{ id: string }>(),\n  resourceCloneWithoutRelations: routerDefinition<{ id: string }>(),\n  resourceCloneWithRelations: routerDefinition<{ id: string }>(),\n  // ENTITY Util\n  entityViewAttributesTableConfig: routerDefinition(),\n  cfsSearchTableConfig: routerDefinition(),\n  rfsSearchTableConfig: routerDefinition(),\n  resourceSearchTableConfig: routerDefinition(),\n  // ATTRIBUTE PAGES\n  attributeSearchPage: routerDefinition(),\n  attributeViewPage: routerDefinition<{ attributeId: string, entityType: string }>(),\n  attributeEditPage: routerDefinition<{ attributeId: string, entityType: string }>(),\n  attributeCreatePage: routerDefinition<{ versionId: string }>(),\n  // ATTRIBUTE CRUD\n  attributeInfo: routerDefinition<{ id: string }>(),\n  componentInfo: routerDefinition<{ id: string }>(),\n  attributeCreate: routerDefinition(),\n  componentCreate: routerDefinition(),\n  attributeUpdate: routerDefinition<{ id: string }>(),\n  componentUpdate: routerDefinition<{ id: string }>(),\n  attributeDelete: routerDefinition<{ id: string }>(),\n  componentDelete: routerDefinition<{ id: string }>(),\n  attributeClone: routerDefinition<{ id: string }>(),\n  componentClone: routerDefinition<{ id: string }>(),\n  // ATTRIBUTE UTILS\n  attributeSearchTableConfig: routerDefinition(),\n  attributeViewComponentTableConfig: routerDefinition(),\n  // UTIL ROUTES\n  attributesPrimitiveTypes: routerDefinition(),\n  entityAttributes: routerDefinition<{ versionId: string }>(),\n  statesConfiguration: routerDefinition(),\n  selectTags: routerDefinition(),\n})\n\ntype RouteFromCallable<C extends Route<any>> = C extends Route<infer T> ? Route<T> : unknown\n\ntype RouteImpl = {\n  [Property in keyof typeof instance]?: RouteFromCallable<typeof instance[Property]>;\n}\n\nexport const provide = (implementation: RouteImpl) => {\n  Object.entries(implementation)\n    .filter(([key]) => instance[key] != null)\n    .forEach(([key, { url, method, matchesUrl, getParametersFromUrl }]) => {\n      if (typeof url !== 'function' || typeof method !== 'function' || typeof matchesUrl !== 'function') {\n        console.error('Each router must have of type ' +\n          '{ url: (...args) => string , method: (...args) => string, matchesUrl: (string) => boolean }.')\n        return\n      }\n      Object.assign(instance[key], { url, method, matchesUrl, getParametersFromUrl })\n    })\n}\n\n// type definitions\n\ninterface BaseRoute {\n  matchesUrl: (urlToMatch: string) => boolean\n  getParametersFromUrl: (url: string) => object | null\n}\n\ninterface SimpleRoute extends BaseRoute {\n  url: () => string\n  method: () => string\n}\n\ninterface ParamRoute<RouteParams extends object> extends BaseRoute{\n  url: (params: RouteParams extends object ? RouteParams : undefined) => string\n  method: (params: RouteParams extends object ? RouteParams : undefined) => string\n}\n\ntype Route<RouteParams extends object | undefined> = RouteParams extends undefined ? SimpleRoute : ParamRoute<RouteParams>\n\ninterface ParamCallableRoute<RouteParams extends object> extends ParamRoute<RouteParams>{\n  (params: RouteParams): CalculatedRoute\n}\n\ninterface SimpleCallableRoute extends SimpleRoute{\n  (): CalculatedRoute\n}\n\ntype CallableRoute<RouteParams extends object | undefined> = RouteParams extends undefined ? SimpleCallableRoute : ParamCallableRoute<RouteParams>\n\ninterface CalculatedRoute {\n  url: string\n  method: string\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,WAAW,GAAGA,CAACC,YAAY,EAAEC,MAAuB,KAAK;EAC7D,MAAM,CAACC,UAAU,CAAC,GAAGC,MAAM,CAACC,OAAO,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,CAAC,EAAEC,MAAM,CAAC,KAAKA,MAAM,KAAKR,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC;EAC7GS,OAAO,CAACC,KAAK,CAAE,UAASR,UAAW,mBAAkBS,MAAM,CAACV,MAAM,CAAE,eAAc,CAAC;EACnF,OAAO,MAAM,IAAI;AACnB,CAAC;AAED,MAAMW,YAAY,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,sBAAsB,CAAC;AAC5E,SAASC,gBAAgBA,CAAA,EAAgE;EACvF,MAAML,MAAM,GAAGA,CAAA,MAAO,CAAC,CAAC,CAAC;EACzB,MAAMM,KAAK,GAAG,IAAIC,KAAK,CAACP,MAAM,EAAE;IAC9BQ,KAAK,EAAEA,CAACC,MAAM,EAAEC,OAAO,EAAEC,aAAa,MAAM;MAAElB,MAAM,EAAEa,KAAK,CAACb,MAAM,CAAC,GAAGkB,aAAa,CAAC;MAAEC,GAAG,EAAEN,KAAK,CAACM,GAAG,CAAC,GAAGD,aAAa;IAAE,CAAC,CAAC;IACzHE,GAAG,EAAEA,CAACJ,MAAM,EAAEK,IAAI;MAAA,IAAAC,YAAA;MAAA,OAAKX,YAAY,CAACY,QAAQ,CAACF,IAAc,CAAC,IAAAC,YAAA,GAAGf,MAAM,CAACc,IAAI,CAAC,cAAAC,YAAA,cAAAA,YAAA,GAAIxB,WAAW,CAACe,KAAK,EAAEQ,IAAI,CAAC,GAAGG,SAAS;IAAA;EACrH,CAAC,CAAC;EACF,OAAOX,KAAK;AACd;AAEO,MAAMT,QAAQ,GAAGF,MAAM,CAACuB,MAAM,CAAC;EACpC;EACAC,iBAAiB,EAAEd,gBAAgB,CAAC,CAAC;EACrCe,iBAAiB,EAAEf,gBAAgB,CAAC,CAAC;EACrCgB,eAAe,EAAEhB,gBAAgB,CAAiB,CAAC;EACnDiB,eAAe,EAAEjB,gBAAgB,CAAiB,CAAC;EACnD;EACAkB,WAAW,EAAElB,gBAAgB,CAAwB,CAAC;EACtDmB,cAAc,EAAEnB,gBAAgB,CAAC,CAAC;EAClCoB,aAAa,EAAEpB,gBAAgB,CAAC,CAAC;EACjCqB,aAAa,EAAErB,gBAAgB,CAAiB,CAAC;EACjDsB,kBAAkB,EAAEtB,gBAAgB,CAAiB,CAAC;EACtDuB,YAAY,EAAEvB,gBAAgB,CAAiB,CAAC;EAChDwB,aAAa,EAAExB,gBAAgB,CAAiB,CAAC;EACjD;EACAyB,8BAA8B,EAAEzB,gBAAgB,CAAC,CAAC;EAClD0B,gCAAgC,EAAE1B,gBAAgB,CAAC,CAAC;EACpD2B,wBAAwB,EAAE3B,gBAAgB,CAAC,CAAC;EAC5C4B,mBAAmB,EAAE5B,gBAAgB,CAAC,CAAC;EACvC;EACA6B,WAAW,EAAE7B,gBAAgB,CAA8C,CAAC;EAC5E8B,kBAAkB,EAAE9B,gBAAgB,CAAqC,CAAC;EAC1E+B,qBAAqB,EAAE/B,gBAAgB,CAA4C,CAAC;EACpF;EACAgC,aAAa,EAAEhC,gBAAgB,CAAwB,CAAC;EACxDiC,aAAa,EAAEjC,gBAAgB,CAAC,CAAC;EACjCkC,WAAW,EAAElC,gBAAgB,CAAuB,CAAC;EACrDmC,WAAW,EAAEnC,gBAAgB,CAAuB,CAAC;EACrD;EACAoC,OAAO,EAAEpC,gBAAgB,CAAiB,CAAC;EAC3CqC,SAAS,EAAErC,gBAAgB,CAAC,CAAC;EAC7BsC,SAAS,EAAEtC,gBAAgB,CAAiB,CAAC;EAC7CuC,SAAS,EAAEvC,gBAAgB,CAAiB,CAAC;EAC7CwC,wBAAwB,EAAExC,gBAAgB,CAAiB,CAAC;EAC5DyC,qBAAqB,EAAEzC,gBAAgB,CAAiB,CAAC;EACzD;EACA0C,aAAa,EAAE1C,gBAAgB,CAAC,CAAC;EACjC2C,WAAW,EAAE3C,gBAAgB,CAAuB,CAAC;EACrD4C,WAAW,EAAE5C,gBAAgB,CAAuB,CAAC;EACrD6C,aAAa,EAAE7C,gBAAgB,CAAwB,CAAC;EACxD;EACA8C,OAAO,EAAE9C,gBAAgB,CAAiB,CAAC;EAC3C+C,SAAS,EAAE/C,gBAAgB,CAAC,CAAC;EAC7BgD,SAAS,EAAEhD,gBAAgB,CAAiB,CAAC;EAC7CiD,SAAS,EAAEjD,gBAAgB,CAAiB,CAAC;EAC7CkD,wBAAwB,EAAElD,gBAAgB,CAAiB,CAAC;EAC5DmD,qBAAqB,EAAEnD,gBAAgB,CAAiB,CAAC;EACzD;EACAoD,kBAAkB,EAAEpD,gBAAgB,CAAC,CAAC;EACtCqD,gBAAgB,EAAErD,gBAAgB,CAAuB,CAAC;EAC1DsD,gBAAgB,EAAEtD,gBAAgB,CAAuB,CAAC;EAC1DuD,kBAAkB,EAAEvD,gBAAgB,CAAwB,CAAC;EAC7D;EACAwD,YAAY,EAAExD,gBAAgB,CAAiB,CAAC;EAChDyD,cAAc,EAAEzD,gBAAgB,CAAC,CAAC;EAClC0D,cAAc,EAAE1D,gBAAgB,CAAiB,CAAC;EAClD2D,cAAc,EAAE3D,gBAAgB,CAAiB,CAAC;EAClD4D,6BAA6B,EAAE5D,gBAAgB,CAAiB,CAAC;EACjE6D,0BAA0B,EAAE7D,gBAAgB,CAAiB,CAAC;EAC9D;EACA8D,+BAA+B,EAAE9D,gBAAgB,CAAC,CAAC;EACnD+D,oBAAoB,EAAE/D,gBAAgB,CAAC,CAAC;EACxCgE,oBAAoB,EAAEhE,gBAAgB,CAAC,CAAC;EACxCiE,yBAAyB,EAAEjE,gBAAgB,CAAC,CAAC;EAC7C;EACAkE,mBAAmB,EAAElE,gBAAgB,CAAC,CAAC;EACvCmE,iBAAiB,EAAEnE,gBAAgB,CAA8C,CAAC;EAClFoE,iBAAiB,EAAEpE,gBAAgB,CAA8C,CAAC;EAClFqE,mBAAmB,EAAErE,gBAAgB,CAAwB,CAAC;EAC9D;EACAsE,aAAa,EAAEtE,gBAAgB,CAAiB,CAAC;EACjDuE,aAAa,EAAEvE,gBAAgB,CAAiB,CAAC;EACjDwE,eAAe,EAAExE,gBAAgB,CAAC,CAAC;EACnCyE,eAAe,EAAEzE,gBAAgB,CAAC,CAAC;EACnC0E,eAAe,EAAE1E,gBAAgB,CAAiB,CAAC;EACnD2E,eAAe,EAAE3E,gBAAgB,CAAiB,CAAC;EACnD4E,eAAe,EAAE5E,gBAAgB,CAAiB,CAAC;EACnD6E,eAAe,EAAE7E,gBAAgB,CAAiB,CAAC;EACnD8E,cAAc,EAAE9E,gBAAgB,CAAiB,CAAC;EAClD+E,cAAc,EAAE/E,gBAAgB,CAAiB,CAAC;EAClD;EACAgF,0BAA0B,EAAEhF,gBAAgB,CAAC,CAAC;EAC9CiF,iCAAiC,EAAEjF,gBAAgB,CAAC,CAAC;EACrD;EACAkF,wBAAwB,EAAElF,gBAAgB,CAAC,CAAC;EAC5CmF,gBAAgB,EAAEnF,gBAAgB,CAAwB,CAAC;EAC3DoF,mBAAmB,EAAEpF,gBAAgB,CAAC,CAAC;EACvCqF,UAAU,EAAErF,gBAAgB,CAAC;AAC/B,CAAC,CAAC;AAAAsF,OAAA,CAAA9F,QAAA,GAAAA,QAAA;AAQK,MAAM+F,OAAO,GAAIC,cAAyB,IAAK;EACpDlG,MAAM,CAACC,OAAO,CAACiG,cAAc,CAAC,CAC3BC,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,KAAKlG,QAAQ,CAACkG,GAAG,CAAC,IAAI,IAAI,CAAC,CACxCC,OAAO,CAAC,CAAC,CAACD,GAAG,EAAE;IAAEnF,GAAG;IAAEnB,MAAM;IAAEwG,UAAU;IAAEC;EAAqB,CAAC,CAAC,KAAK;IACrE,IAAI,OAAOtF,GAAG,KAAK,UAAU,IAAI,OAAOnB,MAAM,KAAK,UAAU,IAAI,OAAOwG,UAAU,KAAK,UAAU,EAAE;MACjGhG,OAAO,CAACC,KAAK,CAAC,gCAAgC,GAC5C,8FAA8F,CAAC;MACjG;IACF;IACAP,MAAM,CAACwG,MAAM,CAACtG,QAAQ,CAACkG,GAAG,CAAC,EAAE;MAAEnF,GAAG;MAAEnB,MAAM;MAAEwG,UAAU;MAAEC;IAAqB,CAAC,CAAC;EACjF,CAAC,CAAC;AACN,CAAC;;AAED;AAAAP,OAAA,CAAAC,OAAA,GAAAA,OAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "da148ab75d34c38c8b1d8adc4af2e5851db5a996", "contentHash": "77a4ce6c1173312a9020b8bd106130d53043aad7d2aa8134f6313d5dd1c96460"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/array-utils.util.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/array-utils.util.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 34}}, "2": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 67}}, "3": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "4": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 17}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 17, "column": 3}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 16}}, "7": {"start": {"line": 18, "column": 18}, "end": {"line": 18, "column": 86}}, "8": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 27}}, "9": {"start": {"line": 20, "column": 2}, "end": {"line": 24, "column": 3}}, "10": {"start": {"line": 20, "column": 15}, "end": {"line": 20, "column": 16}}, "11": {"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, "12": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 19}}, "13": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 14}}}, "fnMap": {"0": {"name": "defaultEqualityCheck", "decl": {"start": {"line": 7, "column": 9}, "end": {"line": 7, "column": 29}}, "loc": {"start": {"line": 7, "column": 42}, "end": {"line": 10, "column": 1}}, "line": 7}, "1": {"name": "arrayEquals", "decl": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 20}}, "loc": {"start": {"line": 11, "column": 49}, "end": {"line": 26, "column": 1}}, "line": 11}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 22}}, {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 44}}, {"start": {"line": 9, "column": 48}, "end": {"line": 9, "column": 66}}], "line": 9}, "1": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}], "line": 12}, "2": {"loc": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 28}}, {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 54}}, {"start": {"line": 12, "column": 58}, "end": {"line": 12, "column": 89}}], "line": 12}, "3": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 17, "column": 3}}, "type": "if", "locations": [{"start": {"line": 15, "column": 2}, "end": {"line": 17, "column": 3}}, {"start": {"line": 15, "column": 2}, "end": {"line": 17, "column": 3}}], "line": 15}, "4": {"loc": {"start": {"line": 18, "column": 18}, "end": {"line": 18, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 53}, "end": {"line": 18, "column": 63}}, {"start": {"line": 18, "column": 66}, "end": {"line": 18, "column": 86}}], "line": 18}, "5": {"loc": {"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, "type": "if", "locations": [{"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}, {"start": {"line": 21, "column": 4}, "end": {"line": 23, "column": 5}}], "line": 21}}, "s": {"0": 1, "1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}, "inputSourceMap": {"version": 3, "names": ["defaultEqualityCheck", "val1", "val2", "Number", "isNaN", "arrayEquals", "array1", "array2", "comparator", "Array", "isArray", "length", "isEqual", "len", "i"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/", "sources": ["array-utils.util.js"], "sourcesContent": ["function defaultEqualityCheck (val1, val2) {\n  // return true if equal or both NaN because NaN !== NaN\n  return val1 === val2 || (Number.isNaN(val1) && Number.isNaN(val2))\n}\n\nexport function arrayEquals (array1, array2, comparator) {\n  if (!Array.isArray(array1) || !Array.isArray(array2) || array1.length !== array2.length) {\n    return false\n  }\n  if (array1 === array2) {\n    return true\n  }\n\n  const isEqual = typeof comparator === 'function' ? comparator : defaultEqualityCheck\n\n  const len = array1.length\n  for (let i = 0; i < len; ++i) {\n    if (!isEqual(array1[i], array2[i])) {\n      return false\n    }\n  }\n  return true\n}\n"], "mappings": ";;;;;;AAAA,SAASA,oBAAoBA,CAAEC,IAAI,EAAEC,IAAI,EAAE;EACzC;EACA,OAAOD,IAAI,KAAKC,IAAI,IAAKC,MAAM,CAACC,KAAK,CAACH,IAAI,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,IAAI,CAAE;AACpE;AAEO,SAASG,WAAWA,CAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACvD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,IAAI,CAACG,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,IAAID,MAAM,CAACK,MAAM,KAAKJ,MAAM,CAACI,MAAM,EAAE;IACvF,OAAO,KAAK;EACd;EACA,IAAIL,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,MAAMK,OAAO,GAAG,OAAOJ,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAGR,oBAAoB;EAEpF,MAAMa,GAAG,GAAGP,MAAM,CAACK,MAAM;EACzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAE,EAAEC,CAAC,EAAE;IAC5B,IAAI,CAACF,OAAO,CAACN,MAAM,CAACQ,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,CAAC,CAAC,EAAE;MAClC,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e1a62c7f8a3dc807b604aaab7da3fa3a20a17e5b", "contentHash": "7f664fe74e36f9a5a64dcd62ad93b969f9fc636404e0a75d9e0ad3055a79d92c"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/provider.ts": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/provider.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 109}}, "2": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 87}}, "3": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 87}}, "4": {"start": {"line": 8, "column": 24}, "end": {"line": 21, "column": 1}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 29}}, "6": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 14}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 38}}, "8": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 14}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 28}}, "10": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 14}}, "11": {"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 56}}, "12": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 34}}, "13": {"start": {"line": 24, "column": 17}, "end": {"line": 26, "column": 1}}, "14": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 28}}, "15": {"start": {"line": 28, "column": 16}, "end": {"line": 34, "column": 1}}, "16": {"start": {"line": 29, "column": 25}, "end": {"line": 31, "column": 4}}, "17": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 75}}, "18": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 62}}, "19": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 59}}, "20": {"start": {"line": 33, "column": 39}, "end": {"line": 33, "column": 57}}, "21": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 26}}, "22": {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 99}}, "23": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 99}}, "24": {"start": {"line": 36, "column": 58}, "end": {"line": 36, "column": 98}}, "25": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 32}}, "26": {"start": {"line": 38, "column": 18}, "end": {"line": 46, "column": 1}}, "27": {"start": {"line": 39, "column": 2}, "end": {"line": 41, "column": 3}}, "28": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 23}}, "29": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 33}}, "30": {"start": {"line": 43, "column": 2}, "end": {"line": 45, "column": 4}}, "31": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 55}}, "32": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 30}}, "33": {"start": {"line": 50, "column": 22}, "end": {"line": 52, "column": 1}}, "34": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 43}}, "35": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 38}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 21}}, "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 87}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 14}}, "loc": {"start": {"line": 9, "column": 19}, "end": {"line": 12, "column": 3}}, "line": 9}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 23}}, "loc": {"start": {"line": 13, "column": 28}, "end": {"line": 16, "column": 3}}, "line": 13}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 20, "column": 3}}, "line": 17}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 17}}, "loc": {"start": {"line": 28, "column": 34}, "end": {"line": 34, "column": 1}}, "line": 28}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 63}, "end": {"line": 29, "column": 64}}, "loc": {"start": {"line": 29, "column": 80}, "end": {"line": 31, "column": 3}}, "line": 29}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 27}, "end": {"line": 33, "column": 28}}, "loc": {"start": {"line": 33, "column": 39}, "end": {"line": 33, "column": 57}}, "line": 33}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 20}}, "loc": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 99}}, "line": 36}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 36, "column": 50}, "end": {"line": 36, "column": 51}}, "loc": {"start": {"line": 36, "column": 58}, "end": {"line": 36, "column": 98}}, "line": 36}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 38, "column": 18}, "end": {"line": 38, "column": 19}}, "loc": {"start": {"line": 38, "column": 30}, "end": {"line": 46, "column": 1}}, "line": 38}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 44, "column": 16}, "end": {"line": 44, "column": 17}}, "loc": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 55}}, "line": 44}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": 23}}, "loc": {"start": {"line": 50, "column": 28}, "end": {"line": 52, "column": 1}}, "line": 50}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 11}, "end": {"line": 30, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 11}, "end": {"line": 30, "column": 44}}, {"start": {"line": 30, "column": 48}, "end": {"line": 30, "column": 74}}], "line": 30}, "1": {"loc": {"start": {"line": 39, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 39, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {"line": 39, "column": 2}, "end": {"line": 41, "column": 3}}], "line": 39}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 1, "22": 1, "23": 0, "24": 0, "25": 1, "26": 1, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 1, "33": 1, "34": 0, "35": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0], "1": [0, 0]}, "inputSourceMap": {"version": 3, "names": ["warnMessage", "impl", "console", "error", "defaultInstance", "translate", "filterByPartialKey", "tokenize", "implementationNames", "Object", "keys", "provideObservers", "Set", "instance", "exports", "provide", "implementation", "adaptedEntries", "entries", "filter", "key", "includes", "assign", "fromEntries", "for<PERSON>ach", "callback", "isProvided", "some", "onProvide", "add", "unregister", "delete", "clear<PERSON><PERSON><PERSON>"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/", "sources": ["provider.ts"], "sourcesContent": ["const warnMessage = (impl: string) => console.error(`i18n implementation \"${impl}\" not imported`)\n\ninterface TextToken {\n  type: 'text'\n  text: string\n}\n\ninterface ParameterToken {\n  type: 'parameter'\n  text: string\n  position: number\n  fallback: string\n  usingFallback: boolean\n}\n\ntype Token = TextToken | ParameterToken\n\ntype InstanceCallback = (instance: I18nInstance) => any\n\ninterface I18nInstance {\n  translate: (message: string, ...args: string[]) => string\n  tokenize: (message: string, ...args: string[]) => Token[]\n  filterByPartialKey: (partialMessage: string) => string[]\n}\n\nconst defaultInstance: I18nInstance = {\n  translate: () => { warnMessage('translate'); return '' },\n  filterByPartialKey: () => { warnMessage('filterByPartialKey'); return [] },\n  tokenize: () => { warnMessage('tokenize'); return [] }\n}\nconst implementationNames = Object.keys(defaultInstance)\n\nconst provideObservers: Set<InstanceCallback> = new Set()\nexport const instance = { ...defaultInstance }\nexport const provide = (implementation: I18nInstance) => {\n  const adaptedEntries = Object.entries(implementation).filter(([key, impl]) => {\n    return implementationNames.includes(key) && typeof impl === 'function'\n  })\n  Object.assign(instance, Object.fromEntries(adaptedEntries))\n  provideObservers.forEach(callback => callback(instance))\n}\nexport const isProvided = () => implementationNames.some(impl => instance[impl] !== defaultInstance[impl])\n\nexport const onProvide = (callback: InstanceCallback) => {\n  if (isProvided()) {\n    callback(instance)\n  }\n  provideObservers.add(callback)\n  return {\n    unregister: () => provideObservers.delete(callback)\n  }\n}\n\n// use only in tests\nexport const clearProvider = () => {\n  Object.assign(instance, defaultInstance)\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,WAAW,GAAIC,IAAY,IAAKC,OAAO,CAACC,KAAK,CAAE,wBAAuBF,IAAK,gBAAe,CAAC;AAyBjG,MAAMG,eAA6B,GAAG;EACpCC,SAAS,EAAEA,CAAA,KAAM;IAAEL,WAAW,CAAC,WAAW,CAAC;IAAE,OAAO,EAAE;EAAC,CAAC;EACxDM,kBAAkB,EAAEA,CAAA,KAAM;IAAEN,WAAW,CAAC,oBAAoB,CAAC;IAAE,OAAO,EAAE;EAAC,CAAC;EAC1EO,QAAQ,EAAEA,CAAA,KAAM;IAAEP,WAAW,CAAC,UAAU,CAAC;IAAE,OAAO,EAAE;EAAC;AACvD,CAAC;AACD,MAAMQ,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACN,eAAe,CAAC;AAExD,MAAMO,gBAAuC,GAAG,IAAIC,GAAG,CAAC,CAAC;AAClD,MAAMC,QAAQ,GAAG;EAAE,GAAGT;AAAgB,CAAC;AAAAU,OAAA,CAAAD,QAAA,GAAAA,QAAA;AACvC,MAAME,OAAO,GAAIC,cAA4B,IAAK;EACvD,MAAMC,cAAc,GAAGR,MAAM,CAACS,OAAO,CAACF,cAAc,CAAC,CAACG,MAAM,CAAC,CAAC,CAACC,GAAG,EAAEnB,IAAI,CAAC,KAAK;IAC5E,OAAOO,mBAAmB,CAACa,QAAQ,CAACD,GAAG,CAAC,IAAI,OAAOnB,IAAI,KAAK,UAAU;EACxE,CAAC,CAAC;EACFQ,MAAM,CAACa,MAAM,CAACT,QAAQ,EAAEJ,MAAM,CAACc,WAAW,CAACN,cAAc,CAAC,CAAC;EAC3DN,gBAAgB,CAACa,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACZ,QAAQ,CAAC,CAAC;AAC1D,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAA,OAAA;AACM,MAAMW,UAAU,GAAGA,CAAA,KAAMlB,mBAAmB,CAACmB,IAAI,CAAC1B,IAAI,IAAIY,QAAQ,CAACZ,IAAI,CAAC,KAAKG,eAAe,CAACH,IAAI,CAAC,CAAC;AAAAa,OAAA,CAAAY,UAAA,GAAAA,UAAA;AAEnG,MAAME,SAAS,GAAIH,QAA0B,IAAK;EACvD,IAAIC,UAAU,CAAC,CAAC,EAAE;IAChBD,QAAQ,CAACZ,QAAQ,CAAC;EACpB;EACAF,gBAAgB,CAACkB,GAAG,CAACJ,QAAQ,CAAC;EAC9B,OAAO;IACLK,UAAU,EAAEA,CAAA,KAAMnB,gBAAgB,CAACoB,MAAM,CAACN,QAAQ;EACpD,CAAC;AACH,CAAC;;AAED;AAAAX,OAAA,CAAAc,SAAA,GAAAA,SAAA;AACO,MAAMI,aAAa,GAAGA,CAAA,KAAM;EACjCvB,MAAM,CAACa,MAAM,CAACT,QAAQ,EAAET,eAAe,CAAC;AAC1C,CAAC;AAAAU,OAAA,CAAAkB,aAAA,GAAAA,aAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "51081e6fbbaf8630ad74479c057248ca2cd6d839", "contentHash": "c68eb3ecba66842b7dbac59373dbeee622091451f426a2deb4499a97c37bac1a"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/test-utils/i18n.test-util.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/test-utils/i18n.test-util.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 39}}, "2": {"start": {"line": 7, "column": 32}, "end": {"line": 7, "column": 46}}, "3": {"start": {"line": 8, "column": 34}, "end": {"line": 32, "column": 1}}, "4": {"start": {"line": 8, "column": 50}, "end": {"line": 32, "column": 1}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 3}}, "6": {"start": {"line": 10, "column": 4}, "end": {"line": 13, "column": 7}}, "7": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 39}}, "8": {"start": {"line": 16, "column": 2}, "end": {"line": 31, "column": 5}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 30, "column": 5}}, "10": {"start": {"line": 18, "column": 6}, "end": {"line": 21, "column": 8}}, "11": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 28}}, "12": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 34}}, "13": {"start": {"line": 25, "column": 6}, "end": {"line": 29, "column": 8}}, "14": {"start": {"line": 33, "column": 30}, "end": {"line": 49, "column": 1}}, "15": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 56}}, "16": {"start": {"line": 35, "column": 19}, "end": {"line": 42, "column": 4}}, "17": {"start": {"line": 35, "column": 37}, "end": {"line": 42, "column": 4}}, "18": {"start": {"line": 37, "column": 4}, "end": {"line": 41, "column": 6}}, "19": {"start": {"line": 43, "column": 15}, "end": {"line": 43, "column": 35}}, "20": {"start": {"line": 44, "column": 2}, "end": {"line": 48, "column": 4}}, "21": {"start": {"line": 46, "column": 33}, "end": {"line": 46, "column": 89}}, "22": {"start": {"line": 46, "column": 69}, "end": {"line": 46, "column": 79}}, "23": {"start": {"line": 47, "column": 38}, "end": {"line": 47, "column": 104}}, "24": {"start": {"line": 47, "column": 61}, "end": {"line": 47, "column": 89}}, "25": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 54}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 34}, "end": {"line": 8, "column": 35}}, "loc": {"start": {"line": 8, "column": 50}, "end": {"line": 32, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 50}, "end": {"line": 8, "column": 51}}, "loc": {"start": {"line": 8, "column": 57}, "end": {"line": 32, "column": 1}}, "line": 8}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 56}, "end": {"line": 16, "column": 57}}, "loc": {"start": {"line": 16, "column": 73}, "end": {"line": 31, "column": 3}}, "line": 16}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 30}, "end": {"line": 33, "column": 31}}, "loc": {"start": {"line": 33, "column": 41}, "end": {"line": 49, "column": 1}}, "line": 33}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 19}, "end": {"line": 35, "column": 20}}, "loc": {"start": {"line": 35, "column": 37}, "end": {"line": 42, "column": 4}}, "line": 35}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 35, "column": 58}, "end": {"line": 35, "column": 59}}, "loc": {"start": {"line": 35, "column": 67}, "end": {"line": 42, "column": 3}}, "line": 35}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 15}, "end": {"line": 46, "column": 16}}, "loc": {"start": {"line": 46, "column": 33}, "end": {"line": 46, "column": 89}}, "line": 46}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 46, "column": 60}, "end": {"line": 46, "column": 61}}, "loc": {"start": {"line": 46, "column": 69}, "end": {"line": 46, "column": 79}}, "line": 46}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 47, "column": 24}, "end": {"line": 47, "column": 25}}, "loc": {"start": {"line": 47, "column": 38}, "end": {"line": 47, "column": 104}}, "line": 47}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 47, "column": 50}, "end": {"line": 47, "column": 51}}, "loc": {"start": {"line": 47, "column": 61}, "end": {"line": 47, "column": 89}}, "line": 47}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 3}}, {"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 3}}], "line": 9}, "1": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 30, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 30, "column": 5}}, {"start": {"line": 17, "column": 4}, "end": {"line": 30, "column": 5}}], "line": 17}, "2": {"loc": {"start": {"line": 37, "column": 11}, "end": {"line": 41, "column": 5}}, "type": "cond-expr", "locations": [{"start": {"line": 37, "column": 40}, "end": {"line": 37, "column": 45}}, {"start": {"line": 37, "column": 48}, "end": {"line": 41, "column": 5}}], "line": 37}, "3": {"loc": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 141}}, "type": "cond-expr", "locations": [{"start": {"line": 39, "column": 104}, "end": {"line": 39, "column": 124}}, {"start": {"line": 39, "column": 127}, "end": {"line": 39, "column": 141}}], "line": 39}, "4": {"loc": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 101}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 66}}, {"start": {"line": 39, "column": 70}, "end": {"line": 39, "column": 101}}], "line": 39}, "5": {"loc": {"start": {"line": 47, "column": 38}, "end": {"line": 47, "column": 104}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 38}, "end": {"line": 47, "column": 90}}, {"start": {"line": 47, "column": 94}, "end": {"line": 47, "column": 104}}], "line": 47}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 1, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}, "inputSourceMap": {"version": 3, "names": ["parameterSeparatorRegex", "generateTokenizeKeyForMap", "translations", "key", "type", "translation", "split", "map", "text", "index", "position", "fallback", "implementationFromMap", "i18nMap", "tokenizeKey", "tokenize", "args", "token", "_args$token$position", "usingFallback", "keys", "Object", "translate", "join", "filterByPartialKey", "<PERSON><PERSON><PERSON>", "filter", "i18nKey", "includes", "exports"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/test-utils/", "sources": ["i18n.test-util.js"], "sourcesContent": ["const parameterSeparatorRegex = /{([0-9]+?)}/g\n\nconst generateTokenizeKeyForMap = (translations) => (key) => {\n  if (translations[key] == null) {\n    return [{ type: 'text', key }]\n  }\n  const translation = translations[key]\n  return translation.split(parameterSeparatorRegex).map((text, index) => {\n    if (index % 2 === 0) {\n      return { type: 'text', text }\n    } else {\n      const position = +text\n      const fallback = `{${text}}`\n      return {\n        type: 'parameter',\n        position,\n        fallback,\n      }\n    }\n  })\n}\n\nexport const implementationFromMap = (i18nMap) => {\n  const tokenizeKey = generateTokenizeKeyForMap(i18nMap)\n  const tokenize = (key, ...args) => tokenizeKey(key).map(token => token.type !== 'parameter' ? token : {\n    ...token,\n    text: args[token.position] ?? token.fallback,\n    usingFallback: args[token.position] == null\n\n  })\n  const keys = Object.keys(i18nMap)\n  return {\n    tokenize,\n    translate: (key, ...args) => tokenize(key, ...args).map(token => token.text).join(''),\n    filterByPartialKey: (partialKey) => keys.filter(i18nKey => i18nKey.includes(partialKey)) || partialKey\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,uBAAuB,GAAG,cAAc;AAE9C,MAAMC,yBAAyB,GAAIC,YAAY,IAAMC,GAAG,IAAK;EAC3D,IAAID,YAAY,CAACC,GAAG,CAAC,IAAI,IAAI,EAAE;IAC7B,OAAO,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAED;IAAI,CAAC,CAAC;EAChC;EACA,MAAME,WAAW,GAAGH,YAAY,CAACC,GAAG,CAAC;EACrC,OAAOE,WAAW,CAACC,KAAK,CAACN,uBAAuB,CAAC,CAACO,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IACrE,IAAIA,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEL,IAAI,EAAE,MAAM;QAAEI;MAAK,CAAC;IAC/B,CAAC,MAAM;MACL,MAAME,QAAQ,GAAG,CAACF,IAAI;MACtB,MAAMG,QAAQ,GAAI,IAAGH,IAAK,GAAE;MAC5B,OAAO;QACLJ,IAAI,EAAE,WAAW;QACjBM,QAAQ;QACRC;MACF,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AAEM,MAAMC,qBAAqB,GAAIC,OAAO,IAAK;EAChD,MAAMC,WAAW,GAAGb,yBAAyB,CAACY,OAAO,CAAC;EACtD,MAAME,QAAQ,GAAGA,CAACZ,GAAG,EAAE,GAAGa,IAAI,KAAKF,WAAW,CAACX,GAAG,CAAC,CAACI,GAAG,CAACU,KAAK;IAAA,IAAAC,oBAAA;IAAA,OAAID,KAAK,CAACb,IAAI,KAAK,WAAW,GAAGa,KAAK,GAAG;MACpG,GAAGA,KAAK;MACRT,IAAI,GAAAU,oBAAA,GAAEF,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC,cAAAQ,oBAAA,cAAAA,oBAAA,GAAID,KAAK,CAACN,QAAQ;MAC5CQ,aAAa,EAAEH,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC,IAAI;IAEzC,CAAC;EAAA,EAAC;EACF,MAAMU,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACP,OAAO,CAAC;EACjC,OAAO;IACLE,QAAQ;IACRO,SAAS,EAAEA,CAACnB,GAAG,EAAE,GAAGa,IAAI,KAAKD,QAAQ,CAACZ,GAAG,EAAE,GAAGa,IAAI,CAAC,CAACT,GAAG,CAACU,KAAK,IAAIA,KAAK,CAACT,IAAI,CAAC,CAACe,IAAI,CAAC,EAAE,CAAC;IACrFC,kBAAkB,EAAGC,UAAU,IAAKL,IAAI,CAACM,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,QAAQ,CAACH,UAAU,CAAC,CAAC,IAAIA;EAC9F,CAAC;AACH,CAAC;AAAAI,OAAA,CAAAjB,qBAAA,GAAAA,qBAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "70ce1e1c7a2703efcef56708cd87670324ce207a", "contentHash": "7182fc9e4d7c8279160cd316b28320a6dbb8a7ab70744684ce1cac37a13844bb"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/index.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/index.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 1983}}, "2": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 37}}, "3": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 22}}, "4": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 32}}, "5": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 60}}, "6": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 50}}, "7": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 44}}, "8": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 64}}, "9": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 78}}, "10": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 40}}, "11": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 40}}, "12": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 42}}, "13": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 42}}, "14": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 42}}, "15": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 42}}, "16": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 42}}, "17": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 42}}, "18": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 38}}, "19": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 38}}, "20": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 50}}, "21": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 46}}, "22": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 46}}, "23": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 50}}, "24": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 62}}, "25": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 52}}, "26": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 52}}, "27": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 74}}, "28": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 64}}, "29": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 70}}, "30": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 40}}, "31": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 40}}, "32": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 40}}, "33": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 36}}, "34": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 48}}, "35": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 44}}, "36": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 44}}, "37": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 48}}, "38": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 54}}, "39": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 60}}, "40": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 30}}, "41": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 30}}, "42": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 30}}, "43": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 26}}, "44": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 38}}, "45": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 34}}, "46": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 34}}, "47": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 38}}, "48": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 54}}, "49": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 60}}, "50": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 30}}, "51": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 30}}, "52": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 30}}, "53": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 26}}, "54": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 34}}, "55": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 34}}, "56": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 38}}, "57": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 38}}, "58": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 54}}, "59": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 48}}, "60": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 34}}, "61": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 76}}, "62": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 72}}, "63": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 50}}, "64": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 60}}, "65": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 38}}, "66": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 48}}, "67": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 38}}, "68": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 36}}, "69": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 38}}, "70": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 34}}, "71": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 40}}, "72": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 42}}, "73": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 42}}, "74": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 46}}, "75": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 46}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1}, "f": {}, "b": {}, "inputSourceMap": {"version": 3, "names": ["_provider", "require", "versionCreatePage", "versionSearchPage", "versionViewPage", "versionEditPage", "versionsSelect", "versionInfo", "versionCreate", "versionClone", "versionDelete", "versionStateChange", "versionUpdate", "versionSearchTableConfig", "versionStateMachine", "versionViewEntitiesTableConfig", "versionViewAttributesTableConfig", "entityNodes", "compositionDiagram", "newCompositionDiagram", "cfsSearchPage", "cfsCreatePage", "cfsViewPage", "cfsEditPage", "cfsInfo", "cfsCreate", "cfsUpdate", "cfsDelete", "cfsCloneWithoutRelations", "cfsCloneWithRelations", "rfsSearchPage", "rfsViewPage", "rfsEditPage", "rfsCreatePage", "rfsInfo", "rfsCreate", "rfsUpdate", "rfsDelete", "rfsCloneWithoutRelations", "rfsCloneWithRelations", "resourceSearchPage", "resourceViewPage", "resourceEditPage", "resourceCreatePage", "resourceInfo", "resourceCreate", "resourceUpdate", "resourceDelete", "resourceCloneWithoutRelations", "resourceCloneWithRelations", "entityViewAttributesTableConfig", "cfsSearchTableConfig", "rfsSearchTableConfig", "resourceSearchTableConfig", "attributeSearchPage", "attributeViewPage", "attributeEditPage", "attributeCreatePage", "attributeInfo", "componentInfo", "attributeCreate", "componentCreate", "attributeUpdate", "componentUpdate", "attributeDelete", "componentDelete", "attributeClone", "componentClone", "attributeViewComponentTableConfig", "attributeSearchTableConfig", "entityAttributes", "statesConfiguration", "attributesPrimitiveTypes", "selectTags", "instance", "exports"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/utils/routes/", "sources": ["index.js"], "sourcesContent": ["import { instance } from './provider'\n\nexport const {\n  // VERSION PAGES\n  versionCreatePage,\n  versionSearchPage,\n  versionViewPage,\n  versionEditPage,\n  // VERSION CRUD\n  versionsSelect, // Gets all versions\n  versionInfo, // Gets one version\n  versionCreate,\n  versionClone,\n  versionDelete,\n  versionStateChange,\n  versionUpdate,\n  // VERSION UTIL\n  versionSearchTableConfig,\n  versionStateMachine,\n  versionViewEntitiesTableConfig,\n  versionViewAttributesTableConfig,\n  // COMPOSITION DIAGRAM\n  entityNodes,\n  compositionDiagram,\n  newCompositionDiagram,\n  // CFS PAGES\n  cfsSearchPage,\n  cfsCreatePage,\n  cfsViewPage,\n  cfsEditPage,\n  // CFS CRUD\n  cfsInfo,\n  cfsCreate,\n  cfsUpdate,\n  cfsDelete,\n  cfsCloneWithoutRelations,\n  cfsCloneWithRelations,\n  // RFS PAGES\n  rfsSearchPage,\n  rfsViewPage,\n  rfsEditPage,\n  rfsCreatePage,\n  // RFS CRUD\n  rfsInfo,\n  rfsCreate,\n  rfsUpdate,\n  rfsDelete,\n  rfsCloneWithoutRelations,\n  rfsCloneWithRelations,\n  // RESOURCE PAGES\n  resourceSearchPage,\n  resourceViewPage,\n  resourceEditPage,\n  resourceCreatePage,\n  // RESOURCE CRUD\n  resourceInfo,\n  resourceCreate,\n  resourceUpdate,\n  resourceDelete,\n  resourceCloneWithoutRelations,\n  resourceCloneWithRelations,\n  // ENTITY UTIL\n  entityViewAttributesTableConfig,\n  cfsSearchTableConfig,\n  rfsSearchTableConfig,\n  resourceSearchTableConfig,\n  // ATTRIBUTE PAGES\n  attributeSearchPage,\n  attributeViewPage,\n  attributeEditPage,\n  attributeCreatePage,\n  // ATTRIBUTE CRUD\n  attributeInfo,\n  componentInfo,\n  attributeCreate,\n  componentCreate,\n  attributeUpdate,\n  componentUpdate,\n  attributeDelete,\n  componentDelete,\n  attributeClone,\n  componentClone,\n  // ATTRIBUTE UTILS\n  attributeViewComponentTableConfig,\n  attributeSearchTableConfig,\n  // UTIL ROUTES\n  entityAttributes, // Gets all attributes associated with a version, used in editable attribute table component\n  statesConfiguration, // Get version state associated permissions\n  attributesPrimitiveTypes, // Gets attribute primitive types, should be used in conjunction with attribute-primitive-types.util.js\n  selectTags, // Gets all tags\n\n} = instance\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEO,MAAM;EACX;EACAC,iBAAiB;EACjBC,iBAAiB;EACjBC,eAAe;EACfC,eAAe;EACf;EACAC,cAAc;EAAE;EAChBC,WAAW;EAAE;EACbC,aAAa;EACbC,YAAY;EACZC,aAAa;EACbC,kBAAkB;EAClBC,aAAa;EACb;EACAC,wBAAwB;EACxBC,mBAAmB;EACnBC,8BAA8B;EAC9BC,gCAAgC;EAChC;EACAC,WAAW;EACXC,kBAAkB;EAClBC,qBAAqB;EACrB;EACAC,aAAa;EACbC,aAAa;EACbC,WAAW;EACXC,WAAW;EACX;EACAC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,wBAAwB;EACxBC,qBAAqB;EACrB;EACAC,aAAa;EACbC,WAAW;EACXC,WAAW;EACXC,aAAa;EACb;EACAC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,wBAAwB;EACxBC,qBAAqB;EACrB;EACAC,kBAAkB;EAClBC,gBAAgB;EAChBC,gBAAgB;EAChBC,kBAAkB;EAClB;EACAC,YAAY;EACZC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,6BAA6B;EAC7BC,0BAA0B;EAC1B;EACAC,+BAA+B;EAC/BC,oBAAoB;EACpBC,oBAAoB;EACpBC,yBAAyB;EACzB;EACAC,mBAAmB;EACnBC,iBAAiB;EACjBC,iBAAiB;EACjBC,mBAAmB;EACnB;EACAC,aAAa;EACbC,aAAa;EACbC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,cAAc;EACd;EACAC,iCAAiC;EACjCC,0BAA0B;EAC1B;EACAC,gBAAgB;EAAE;EAClBC,mBAAmB;EAAE;EACrBC,wBAAwB;EAAE;EAC1BC,UAAU,CAAE;AAEd,CAAC,GAAGC,kBAAQ;AAAAC,OAAA,CAAAF,UAAA,GAAAA,UAAA;AAAAE,OAAA,CAAAH,wBAAA,GAAAA,wBAAA;AAAAG,OAAA,CAAAJ,mBAAA,GAAAA,mBAAA;AAAAI,OAAA,CAAAL,gBAAA,GAAAA,gBAAA;AAAAK,OAAA,CAAAN,0BAAA,GAAAA,0BAAA;AAAAM,OAAA,CAAAP,iCAAA,GAAAA,iCAAA;AAAAO,OAAA,CAAAR,cAAA,GAAAA,cAAA;AAAAQ,OAAA,CAAAT,cAAA,GAAAA,cAAA;AAAAS,OAAA,CAAAV,eAAA,GAAAA,eAAA;AAAAU,OAAA,CAAAX,eAAA,GAAAA,eAAA;AAAAW,OAAA,CAAAZ,eAAA,GAAAA,eAAA;AAAAY,OAAA,CAAAb,eAAA,GAAAA,eAAA;AAAAa,OAAA,CAAAd,eAAA,GAAAA,eAAA;AAAAc,OAAA,CAAAf,eAAA,GAAAA,eAAA;AAAAe,OAAA,CAAAhB,aAAA,GAAAA,aAAA;AAAAgB,OAAA,CAAAjB,aAAA,GAAAA,aAAA;AAAAiB,OAAA,CAAAlB,mBAAA,GAAAA,mBAAA;AAAAkB,OAAA,CAAAnB,iBAAA,GAAAA,iBAAA;AAAAmB,OAAA,CAAApB,iBAAA,GAAAA,iBAAA;AAAAoB,OAAA,CAAArB,mBAAA,GAAAA,mBAAA;AAAAqB,OAAA,CAAAtB,yBAAA,GAAAA,yBAAA;AAAAsB,OAAA,CAAAvB,oBAAA,GAAAA,oBAAA;AAAAuB,OAAA,CAAAxB,oBAAA,GAAAA,oBAAA;AAAAwB,OAAA,CAAAzB,+BAAA,GAAAA,+BAAA;AAAAyB,OAAA,CAAA1B,0BAAA,GAAAA,0BAAA;AAAA0B,OAAA,CAAA3B,6BAAA,GAAAA,6BAAA;AAAA2B,OAAA,CAAA5B,cAAA,GAAAA,cAAA;AAAA4B,OAAA,CAAA7B,cAAA,GAAAA,cAAA;AAAA6B,OAAA,CAAA9B,cAAA,GAAAA,cAAA;AAAA8B,OAAA,CAAA/B,YAAA,GAAAA,YAAA;AAAA+B,OAAA,CAAAhC,kBAAA,GAAAA,kBAAA;AAAAgC,OAAA,CAAAjC,gBAAA,GAAAA,gBAAA;AAAAiC,OAAA,CAAAlC,gBAAA,GAAAA,gBAAA;AAAAkC,OAAA,CAAAnC,kBAAA,GAAAA,kBAAA;AAAAmC,OAAA,CAAApC,qBAAA,GAAAA,qBAAA;AAAAoC,OAAA,CAAArC,wBAAA,GAAAA,wBAAA;AAAAqC,OAAA,CAAAtC,SAAA,GAAAA,SAAA;AAAAsC,OAAA,CAAAvC,SAAA,GAAAA,SAAA;AAAAuC,OAAA,CAAAxC,SAAA,GAAAA,SAAA;AAAAwC,OAAA,CAAAzC,OAAA,GAAAA,OAAA;AAAAyC,OAAA,CAAA1C,aAAA,GAAAA,aAAA;AAAA0C,OAAA,CAAA3C,WAAA,GAAAA,WAAA;AAAA2C,OAAA,CAAA5C,WAAA,GAAAA,WAAA;AAAA4C,OAAA,CAAA7C,aAAA,GAAAA,aAAA;AAAA6C,OAAA,CAAA9C,qBAAA,GAAAA,qBAAA;AAAA8C,OAAA,CAAA/C,wBAAA,GAAAA,wBAAA;AAAA+C,OAAA,CAAAhD,SAAA,GAAAA,SAAA;AAAAgD,OAAA,CAAAjD,SAAA,GAAAA,SAAA;AAAAiD,OAAA,CAAAlD,SAAA,GAAAA,SAAA;AAAAkD,OAAA,CAAAnD,OAAA,GAAAA,OAAA;AAAAmD,OAAA,CAAApD,WAAA,GAAAA,WAAA;AAAAoD,OAAA,CAAArD,WAAA,GAAAA,WAAA;AAAAqD,OAAA,CAAAtD,aAAA,GAAAA,aAAA;AAAAsD,OAAA,CAAAvD,aAAA,GAAAA,aAAA;AAAAuD,OAAA,CAAAxD,qBAAA,GAAAA,qBAAA;AAAAwD,OAAA,CAAAzD,kBAAA,GAAAA,kBAAA;AAAAyD,OAAA,CAAA1D,WAAA,GAAAA,WAAA;AAAA0D,OAAA,CAAA3D,gCAAA,GAAAA,gCAAA;AAAA2D,OAAA,CAAA5D,8BAAA,GAAAA,8BAAA;AAAA4D,OAAA,CAAA7D,mBAAA,GAAAA,mBAAA;AAAA6D,OAAA,CAAA9D,wBAAA,GAAAA,wBAAA;AAAA8D,OAAA,CAAA/D,aAAA,GAAAA,aAAA;AAAA+D,OAAA,CAAAhE,kBAAA,GAAAA,kBAAA;AAAAgE,OAAA,CAAAjE,aAAA,GAAAA,aAAA;AAAAiE,OAAA,CAAAlE,YAAA,GAAAA,YAAA;AAAAkE,OAAA,CAAAnE,aAAA,GAAAA,aAAA;AAAAmE,OAAA,CAAApE,WAAA,GAAAA,WAAA;AAAAoE,OAAA,CAAArE,cAAA,GAAAA,cAAA;AAAAqE,OAAA,CAAAtE,eAAA,GAAAA,eAAA;AAAAsE,OAAA,CAAAvE,eAAA,GAAAA,eAAA;AAAAuE,OAAA,CAAAxE,iBAAA,GAAAA,iBAAA;AAAAwE,OAAA,CAAAzE,iBAAA,GAAAA,iBAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "86071daa3e2846e43dcf8add7de01d81215408dd", "contentHash": "c14b3cc6e51c7ec1c98cb14be8cf55fe8cbc2faf6eb346af1ea2f0b1469c097e"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/components/composition-diagram-sidebar/composition-diagram-sidebar.element.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/components/composition-diagram-sidebar/composition-diagram-sidebar.element.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 68}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 41}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 61}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 51}}, "5": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 123}}, "6": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 43}}, "7": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 60}}, "8": {"start": {"line": 13, "column": 40}, "end": {"line": 13, "column": 117}}, "9": {"start": {"line": 14, "column": 35}, "end": {"line": 14, "column": 107}}, "10": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 59}}, "11": {"start": {"line": 16, "column": 26}, "end": {"line": 16, "column": 106}}, "12": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 43}}, "13": {"start": {"line": 18, "column": 39}, "end": {"line": 18, "column": 93}}, "14": {"start": {"line": 19, "column": 49}, "end": {"line": 19, "column": 96}}, "15": {"start": {"line": 19, "column": 84}, "end": {"line": 19, "column": 96}}, "16": {"start": {"line": 19, "column": 121}, "end": {"line": 19, "column": 134}}, "17": {"start": {"line": 19, "column": 159}, "end": {"line": 19, "column": 172}}, "18": {"start": {"line": 19, "column": 174}, "end": {"line": 19, "column": 309}}, "19": {"start": {"line": 19, "column": 234}, "end": {"line": 19, "column": 292}}, "20": {"start": {"line": 20, "column": 53}, "end": {"line": 20, "column": 111}}, "21": {"start": {"line": 20, "column": 98}, "end": {"line": 20, "column": 109}}, "22": {"start": {"line": 20, "column": 112}, "end": {"line": 20, "column": 214}}, "23": {"start": {"line": 20, "column": 188}, "end": {"line": 20, "column": 212}}, "24": {"start": {"line": 20, "column": 227}, "end": {"line": 20, "column": 264}}, "25": {"start": {"line": 20, "column": 266}, "end": {"line": 20, "column": 321}}, "26": {"start": {"line": 20, "column": 297}, "end": {"line": 20, "column": 319}}, "27": {"start": {"line": 20, "column": 335}, "end": {"line": 20, "column": 337}}, "28": {"start": {"line": 20, "column": 367}, "end": {"line": 20, "column": 423}}, "29": {"start": {"line": 20, "column": 425}, "end": {"line": 20, "column": 726}}, "30": {"start": {"line": 20, "column": 448}, "end": {"line": 20, "column": 724}}, "31": {"start": {"line": 20, "column": 534}, "end": {"line": 20, "column": 606}}, "32": {"start": {"line": 20, "column": 608}, "end": {"line": 20, "column": 722}}, "33": {"start": {"line": 20, "column": 646}, "end": {"line": 20, "column": 687}}, "34": {"start": {"line": 20, "column": 697}, "end": {"line": 20, "column": 720}}, "35": {"start": {"line": 20, "column": 727}, "end": {"line": 20, "column": 748}}, "36": {"start": {"line": 20, "column": 749}, "end": {"line": 20, "column": 787}}, "37": {"start": {"line": 20, "column": 762}, "end": {"line": 20, "column": 785}}, "38": {"start": {"line": 20, "column": 788}, "end": {"line": 20, "column": 802}}, "39": {"start": {"line": 21, "column": 30}, "end": {"line": 21, "column": 64}}, "40": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 71}}, "41": {"start": {"line": 23, "column": 17}, "end": {"line": 23, "column": 51}}, "42": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 63}}, "43": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 33}}, "44": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 12}}, "45": {"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 7}}, "46": {"start": {"line": 34, "column": 18}, "end": {"line": 34, "column": 61}}, "47": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 28}}, "48": {"start": {"line": 38, "column": 22}, "end": {"line": 38, "column": 79}}, "49": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 76}}, "50": {"start": {"line": 40, "column": 23}, "end": {"line": 40, "column": 55}}, "51": {"start": {"line": 41, "column": 24}, "end": {"line": 41, "column": 64}}, "52": {"start": {"line": 42, "column": 4}, "end": {"line": 44, "column": 5}}, "53": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 51}}, "54": {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": 5}}, "55": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 28}}, "56": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 37}}, "57": {"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": 7}}, "58": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 28}}, "59": {"start": {"line": 53, "column": 4}, "end": {"line": 56, "column": 7}}, "60": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 28}}, "61": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 61}}, "62": {"start": {"line": 55, "column": 33}, "end": {"line": 55, "column": 54}}, "63": {"start": {"line": 57, "column": 17}, "end": {"line": 57, "column": 38}}, "64": {"start": {"line": 58, "column": 4}, "end": {"line": 64, "column": 7}}, "65": {"start": {"line": 60, "column": 10}, "end": {"line": 60, "column": 29}}, "66": {"start": {"line": 61, "column": 6}, "end": {"line": 63, "column": 7}}, "67": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 30}}, "68": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 46}}, "69": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 44}}, "70": {"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 38}}, "71": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 139}}, "72": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 37}}, "73": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 50}}, "74": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, "75": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 37}}, "76": {"start": {"line": 85, "column": 24}, "end": {"line": 85, "column": 71}}, "77": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "78": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 13}}, "79": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 38}}, "80": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 64}}, "81": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 55}}, "82": {"start": {"line": 93, "column": 23}, "end": {"line": 93, "column": 64}}, "83": {"start": {"line": 94, "column": 4}, "end": {"line": 102, "column": 5}}, "84": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "85": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 31}}, "86": {"start": {"line": 97, "column": 8}, "end": {"line": 100, "column": 11}}, "87": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": 32}}, "88": {"start": {"line": 99, "column": 10}, "end": {"line": 99, "column": 34}}, "89": {"start": {"line": 105, "column": 22}, "end": {"line": 105, "column": 79}}, "90": {"start": {"line": 106, "column": 20}, "end": {"line": 106, "column": 49}}, "91": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 102}}, "92": {"start": {"line": 107, "column": 29}, "end": {"line": 107, "column": 51}}, "93": {"start": {"line": 107, "column": 71}, "end": {"line": 107, "column": 100}}, "94": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 67}}, "95": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 71}}, "96": {"start": {"line": 112, "column": 4}, "end": {"line": 114, "column": 5}}, "97": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 13}}, "98": {"start": {"line": 115, "column": 23}, "end": {"line": 115, "column": 55}}, "99": {"start": {"line": 116, "column": 24}, "end": {"line": 116, "column": 64}}, "100": {"start": {"line": 117, "column": 26}, "end": {"line": 117, "column": 63}}, "101": {"start": {"line": 118, "column": 29}, "end": {"line": 118, "column": 76}}, "102": {"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": 79}}, "103": {"start": {"line": 120, "column": 23}, "end": {"line": 126, "column": 5}}, "104": {"start": {"line": 127, "column": 4}, "end": {"line": 147, "column": 7}}, "105": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 93}}, "106": {"start": {"line": 133, "column": 41}, "end": {"line": 133, "column": 93}}, "107": {"start": {"line": 134, "column": 8}, "end": {"line": 144, "column": 11}}, "108": {"start": {"line": 135, "column": 37}, "end": {"line": 135, "column": 93}}, "109": {"start": {"line": 136, "column": 32}, "end": {"line": 136, "column": 94}}, "110": {"start": {"line": 137, "column": 36}, "end": {"line": 137, "column": 99}}, "111": {"start": {"line": 138, "column": 10}, "end": {"line": 138, "column": 56}}, "112": {"start": {"line": 139, "column": 10}, "end": {"line": 139, "column": 74}}, "113": {"start": {"line": 140, "column": 10}, "end": {"line": 140, "column": 44}}, "114": {"start": {"line": 141, "column": 10}, "end": {"line": 141, "column": 89}}, "115": {"start": {"line": 142, "column": 10}, "end": {"line": 142, "column": 71}}, "116": {"start": {"line": 143, "column": 10}, "end": {"line": 143, "column": 51}}, "117": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 62}}, "118": {"start": {"line": 152, "column": 2}, "end": {"line": 161, "column": 3}}, "119": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 40}}, "120": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 46}}, "121": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 33}}, "122": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 16}}, "123": {"start": {"line": 164, "column": 14}, "end": {"line": 167, "column": 3}}, "124": {"start": {"line": 168, "column": 14}, "end": {"line": 171, "column": 3}}, "125": {"start": {"line": 172, "column": 19}, "end": {"line": 175, "column": 3}}, "126": {"start": {"line": 176, "column": 2}, "end": {"line": 185, "column": 3}}, "127": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 34}}, "128": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 29}}, "129": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 24}}, "130": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 18}}, "131": {"start": {"line": 188, "column": 2}, "end": {"line": 197, "column": 3}}, "132": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 16}}, "133": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 21}}, "134": {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 28}}, "135": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 16}}, "136": {"start": {"line": 202, "column": 4}, "end": {"line": 202, "column": 75}}, "137": {"start": {"line": 204, "column": 2}, "end": {"line": 204, "column": 158}}, "138": {"start": {"line": 207, "column": 2}, "end": {"line": 209, "column": 4}}, "139": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 55}}, "140": {"start": {"line": 211, "column": 23}, "end": {"line": 211, "column": 58}}, "141": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 40}}, "142": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 65}}}, "fnMap": {"0": {"name": "_interopRequireDefault", "decl": {"start": {"line": 18, "column": 9}, "end": {"line": 18, "column": 31}}, "loc": {"start": {"line": 18, "column": 37}, "end": {"line": 18, "column": 95}}, "line": 18}, "1": {"name": "_getRequireWildcardCache", "decl": {"start": {"line": 19, "column": 9}, "end": {"line": 19, "column": 33}}, "loc": {"start": {"line": 19, "column": 47}, "end": {"line": 19, "column": 311}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 209}, "end": {"line": 19, "column": 210}}, "loc": {"start": {"line": 19, "column": 232}, "end": {"line": 19, "column": 294}}, "line": 19}, "3": {"name": "_interopRequireWildcard", "decl": {"start": {"line": 20, "column": 9}, "end": {"line": 20, "column": 32}}, "loc": {"start": {"line": 20, "column": 51}, "end": {"line": 20, "column": 804}}, "line": 20}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 16}, "end": {"line": 36, "column": 3}}, "line": 27}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 3}}, "loc": {"start": {"line": 37, "column": 22}, "end": {"line": 65, "column": 3}}, "line": 37}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 49, "column": 41}, "end": {"line": 49, "column": 42}}, "loc": {"start": {"line": 49, "column": 47}, "end": {"line": 51, "column": 5}}, "line": 49}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 53, "column": 47}, "end": {"line": 53, "column": 48}}, "loc": {"start": {"line": 53, "column": 53}, "end": {"line": 56, "column": 5}}, "line": 53}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 55, "column": 27}, "end": {"line": 55, "column": 28}}, "loc": {"start": {"line": 55, "column": 33}, "end": {"line": 55, "column": 54}}, "line": 55}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 58, "column": 66}, "end": {"line": 58, "column": 67}}, "loc": {"start": {"line": 60, "column": 10}, "end": {"line": 60, "column": 29}}, "line": 60}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 60, "column": 31}, "end": {"line": 60, "column": 32}}, "loc": {"start": {"line": 60, "column": 55}, "end": {"line": 64, "column": 5}}, "line": 60}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 3}}, "loc": {"start": {"line": 66, "column": 25}, "end": {"line": 68, "column": 3}}, "line": 66}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 3}}, "loc": {"start": {"line": 69, "column": 19}, "end": {"line": 71, "column": 3}}, "line": 69}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 3}}, "loc": {"start": {"line": 72, "column": 25}, "end": {"line": 77, "column": 3}}, "line": 72}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 3}}, "loc": {"start": {"line": 78, "column": 34}, "end": {"line": 80, "column": 3}}, "line": 78}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 3}}, "loc": {"start": {"line": 81, "column": 53}, "end": {"line": 103, "column": 3}}, "line": 81}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 97, "column": 30}, "end": {"line": 97, "column": 31}}, "loc": {"start": {"line": 97, "column": 36}, "end": {"line": 100, "column": 9}}, "line": 97}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 3}}, "loc": {"start": {"line": 104, "column": 28}, "end": {"line": 109, "column": 3}}, "line": 104}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 107, "column": 19}, "end": {"line": 107, "column": 20}}, "loc": {"start": {"line": 107, "column": 29}, "end": {"line": 107, "column": 51}}, "line": 107}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 107, "column": 61}, "end": {"line": 107, "column": 62}}, "loc": {"start": {"line": 107, "column": 71}, "end": {"line": 107, "column": 100}}, "line": 107}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 3}}, "loc": {"start": {"line": 110, "column": 19}, "end": {"line": 148, "column": 3}}, "line": 110}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 132, "column": 17}, "end": {"line": 132, "column": 18}}, "loc": {"start": {"line": 132, "column": 31}, "end": {"line": 145, "column": 7}}, "line": 132}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 134, "column": 27}, "end": {"line": 134, "column": 28}}, "loc": {"start": {"line": 134, "column": 34}, "end": {"line": 144, "column": 9}}, "line": 134}, "23": {"name": "selectTypesIcon", "decl": {"start": {"line": 151, "column": 9}, "end": {"line": 151, "column": 24}}, "loc": {"start": {"line": 151, "column": 37}, "end": {"line": 162, "column": 1}}, "line": 151}, "24": {"name": "selectTypes", "decl": {"start": {"line": 163, "column": 9}, "end": {"line": 163, "column": 20}}, "loc": {"start": {"line": 163, "column": 33}, "end": {"line": 186, "column": 1}}, "line": 163}, "25": {"name": "excludeTypes", "decl": {"start": {"line": 187, "column": 9}, "end": {"line": 187, "column": 21}}, "loc": {"start": {"line": 187, "column": 34}, "end": {"line": 198, "column": 1}}, "line": 187}, "26": {"name": "renderTypeOptions", "decl": {"start": {"line": 199, "column": 9}, "end": {"line": 199, "column": 26}}, "loc": {"start": {"line": 199, "column": 60}, "end": {"line": 205, "column": 1}}, "line": 199}, "27": {"name": "toTypeOption", "decl": {"start": {"line": 201, "column": 11}, "end": {"line": 201, "column": 23}}, "loc": {"start": {"line": 201, "column": 30}, "end": {"line": 203, "column": 3}}, "line": 201}, "28": {"name": "addToContainer", "decl": {"start": {"line": 206, "column": 9}, "end": {"line": 206, "column": 23}}, "loc": {"start": {"line": 206, "column": 35}, "end": {"line": 210, "column": 1}}, "line": 206}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 207, "column": 9}, "end": {"line": 207, "column": 10}}, "loc": {"start": {"line": 207, "column": 28}, "end": {"line": 209, "column": 3}}, "line": 207}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 46}, "end": {"line": 18, "column": 92}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 70}, "end": {"line": 18, "column": 73}}, {"start": {"line": 18, "column": 76}, "end": {"line": 18, "column": 92}}], "line": 18}, "1": {"loc": {"start": {"line": 18, "column": 46}, "end": {"line": 18, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 46}, "end": {"line": 18, "column": 49}}, {"start": {"line": 18, "column": 53}, "end": {"line": 18, "column": 67}}], "line": 18}, "2": {"loc": {"start": {"line": 19, "column": 49}, "end": {"line": 19, "column": 96}}, "type": "if", "locations": [{"start": {"line": 19, "column": 49}, "end": {"line": 19, "column": 96}}, {"start": {"line": 19, "column": 49}, "end": {"line": 19, "column": 96}}], "line": 19}, "3": {"loc": {"start": {"line": 19, "column": 241}, "end": {"line": 19, "column": 291}}, "type": "cond-expr", "locations": [{"start": {"line": 19, "column": 255}, "end": {"line": 19, "column": 271}}, {"start": {"line": 19, "column": 274}, "end": {"line": 19, "column": 291}}], "line": 19}, "4": {"loc": {"start": {"line": 20, "column": 53}, "end": {"line": 20, "column": 111}}, "type": "if", "locations": [{"start": {"line": 20, "column": 53}, "end": {"line": 20, "column": 111}}, {"start": {"line": 20, "column": 53}, "end": {"line": 20, "column": 111}}], "line": 20}, "5": {"loc": {"start": {"line": 20, "column": 57}, "end": {"line": 20, "column": 94}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 57}, "end": {"line": 20, "column": 69}}, {"start": {"line": 20, "column": 73}, "end": {"line": 20, "column": 76}}, {"start": {"line": 20, "column": 80}, "end": {"line": 20, "column": 94}}], "line": 20}, "6": {"loc": {"start": {"line": 20, "column": 112}, "end": {"line": 20, "column": 214}}, "type": "if", "locations": [{"start": {"line": 20, "column": 112}, "end": {"line": 20, "column": 214}}, {"start": {"line": 20, "column": 112}, "end": {"line": 20, "column": 214}}], "line": 20}, "7": {"loc": {"start": {"line": 20, "column": 116}, "end": {"line": 20, "column": 184}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 116}, "end": {"line": 20, "column": 128}}, {"start": {"line": 20, "column": 132}, "end": {"line": 20, "column": 155}}, {"start": {"line": 20, "column": 159}, "end": {"line": 20, "column": 184}}], "line": 20}, "8": {"loc": {"start": {"line": 20, "column": 266}, "end": {"line": 20, "column": 321}}, "type": "if", "locations": [{"start": {"line": 20, "column": 266}, "end": {"line": 20, "column": 321}}, {"start": {"line": 20, "column": 266}, "end": {"line": 20, "column": 321}}], "line": 20}, "9": {"loc": {"start": {"line": 20, "column": 270}, "end": {"line": 20, "column": 293}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 270}, "end": {"line": 20, "column": 275}}, {"start": {"line": 20, "column": 279}, "end": {"line": 20, "column": 293}}], "line": 20}, "10": {"loc": {"start": {"line": 20, "column": 367}, "end": {"line": 20, "column": 423}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 367}, "end": {"line": 20, "column": 388}}, {"start": {"line": 20, "column": 392}, "end": {"line": 20, "column": 423}}], "line": 20}, "11": {"loc": {"start": {"line": 20, "column": 448}, "end": {"line": 20, "column": 724}}, "type": "if", "locations": [{"start": {"line": 20, "column": 448}, "end": {"line": 20, "column": 724}}, {"start": {"line": 20, "column": 448}, "end": {"line": 20, "column": 724}}], "line": 20}, "12": {"loc": {"start": {"line": 20, "column": 452}, "end": {"line": 20, "column": 519}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 452}, "end": {"line": 20, "column": 469}}, {"start": {"line": 20, "column": 473}, "end": {"line": 20, "column": 519}}], "line": 20}, "13": {"loc": {"start": {"line": 20, "column": 534}, "end": {"line": 20, "column": 606}}, "type": "cond-expr", "locations": [{"start": {"line": 20, "column": 558}, "end": {"line": 20, "column": 599}}, {"start": {"line": 20, "column": 602}, "end": {"line": 20, "column": 606}}], "line": 20}, "14": {"loc": {"start": {"line": 20, "column": 608}, "end": {"line": 20, "column": 722}}, "type": "if", "locations": [{"start": {"line": 20, "column": 608}, "end": {"line": 20, "column": 722}}, {"start": {"line": 20, "column": 608}, "end": {"line": 20, "column": 722}}], "line": 20}, "15": {"loc": {"start": {"line": 20, "column": 612}, "end": {"line": 20, "column": 642}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 612}, "end": {"line": 20, "column": 616}}, {"start": {"line": 20, "column": 621}, "end": {"line": 20, "column": 629}}, {"start": {"line": 20, "column": 633}, "end": {"line": 20, "column": 641}}], "line": 20}, "16": {"loc": {"start": {"line": 20, "column": 749}, "end": {"line": 20, "column": 787}}, "type": "if", "locations": [{"start": {"line": 20, "column": 749}, "end": {"line": 20, "column": 787}}, {"start": {"line": 20, "column": 749}, "end": {"line": 20, "column": 787}}], "line": 20}, "17": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 44, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 44, "column": 5}}, {"start": {"line": 42, "column": 4}, "end": {"line": 44, "column": 5}}], "line": 42}, "18": {"loc": {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": 5}}, {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": 5}}], "line": 45}, "19": {"loc": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 18}}, {"start": {"line": 45, "column": 22}, "end": {"line": 45, "column": 76}}], "line": 45}, "20": {"loc": {"start": {"line": 61, "column": 6}, "end": {"line": 63, "column": 7}}, "type": "if", "locations": [{"start": {"line": 61, "column": 6}, "end": {"line": 63, "column": 7}}, {"start": {"line": 61, "column": 6}, "end": {"line": 63, "column": 7}}], "line": 61}, "21": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 138}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 64}}, {"start": {"line": 75, "column": 68}, "end": {"line": 75, "column": 100}}, {"start": {"line": 75, "column": 104}, "end": {"line": 75, "column": 138}}], "line": 75}, "22": {"loc": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, "type": "if", "locations": [{"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}, {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 5}}], "line": 82}, "23": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}], "line": 86}, "24": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 20}}, {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 40}}], "line": 86}, "25": {"loc": {"start": {"line": 93, "column": 23}, "end": {"line": 93, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 93, "column": 42}, "end": {"line": 93, "column": 46}}, {"start": {"line": 93, "column": 49}, "end": {"line": 93, "column": 64}}], "line": 93}, "26": {"loc": {"start": {"line": 94, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 94, "column": 4}, "end": {"line": 102, "column": 5}}, {"start": {"line": 94, "column": 4}, "end": {"line": 102, "column": 5}}], "line": 94}, "27": {"loc": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 143}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 26}}, {"start": {"line": 94, "column": 30}, "end": {"line": 94, "column": 60}}, {"start": {"line": 94, "column": 64}, "end": {"line": 94, "column": 74}}, {"start": {"line": 94, "column": 78}, "end": {"line": 94, "column": 97}}, {"start": {"line": 94, "column": 101}, "end": {"line": 94, "column": 123}}, {"start": {"line": 94, "column": 127}, "end": {"line": 94, "column": 143}}], "line": 94}, "28": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}], "line": 95}, "29": {"loc": {"start": {"line": 112, "column": 4}, "end": {"line": 114, "column": 5}}, "type": "if", "locations": [{"start": {"line": 112, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {"line": 112, "column": 4}, "end": {"line": 114, "column": 5}}], "line": 112}, "30": {"loc": {"start": {"line": 124, "column": 20}, "end": {"line": 124, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 124, "column": 34}, "end": {"line": 124, "column": 56}}, {"start": {"line": 124, "column": 59}, "end": {"line": 124, "column": 61}}], "line": 124}, "31": {"loc": {"start": {"line": 152, "column": 2}, "end": {"line": 161, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 153, "column": 4}, "end": {"line": 154, "column": 40}}, {"start": {"line": 155, "column": 4}, "end": {"line": 156, "column": 46}}, {"start": {"line": 157, "column": 4}, "end": {"line": 158, "column": 33}}, {"start": {"line": 159, "column": 4}, "end": {"line": 160, "column": 16}}], "line": 152}, "32": {"loc": {"start": {"line": 176, "column": 2}, "end": {"line": 185, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 178, "column": 34}}, {"start": {"line": 179, "column": 4}, "end": {"line": 180, "column": 29}}, {"start": {"line": 181, "column": 4}, "end": {"line": 182, "column": 24}}, {"start": {"line": 183, "column": 4}, "end": {"line": 184, "column": 18}}], "line": 176}, "33": {"loc": {"start": {"line": 188, "column": 2}, "end": {"line": 197, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 189, "column": 4}, "end": {"line": 190, "column": 16}}, {"start": {"line": 191, "column": 4}, "end": {"line": 192, "column": 21}}, {"start": {"line": 193, "column": 4}, "end": {"line": 194, "column": 28}}, {"start": {"line": 195, "column": 4}, "end": {"line": 196, "column": 16}}], "line": 188}, "34": {"loc": {"start": {"line": 204, "column": 2}, "end": {"line": 204, "column": 157}}, "type": "binary-expr", "locations": [{"start": {"line": 204, "column": 2}, "end": {"line": 204, "column": 51}}, {"start": {"line": 204, "column": 55}, "end": {"line": 204, "column": 78}}, {"start": {"line": 204, "column": 82}, "end": {"line": 204, "column": 157}}], "line": 204}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0, 0, 0, 0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0, 0, 0], "32": [0, 0, 0, 0], "33": [0, 0, 0, 0], "34": [0, 0, 0]}, "inputSourceMap": {"version": 3, "names": ["require", "_routes", "proxy", "_interopRequireWildcard", "_compositionDiagramSidebarElement", "_interopRequireDefault", "_draggableNodeEntityTemplate", "_i18n2", "_versionContextInfo", "_string", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "draggableNodeTemplate", "document", "createElement", "innerHTML", "draggableNodeHtml", "template", "containerHtml", "elementData", "CompositionDiagramSidebar", "HTMLElement", "constructor", "rootNodeId", "versionStoreObserver", "throttling", "clone", "importNode", "content", "append<PERSON><PERSON><PERSON>", "connectedCallback", "selectBox", "querySelector", "entityNameFilter", "entityType", "getAttribute", "excludedIds", "setAttribute", "versionStore", "getters", "versionName", "getEntityNodes", "reflectSelectBoxOptions", "addEventListener", "timeout", "clearTimeout", "setTimeout", "data", "react", "newValue", "oldValue", "hasAttribute", "nodeId", "disconnectedCallback", "_data$versionStoreObs", "unregister", "observedAttributes", "attributeChangedCallback", "name", "isConnected", "isToUpdate", "requestAnimationFrame", "options", "Array", "from", "filter", "option", "value", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "renderTypeOptions", "externalNodes", "dataToSend", "type", "toUpperCase", "rootId", "excludingIds", "split", "excludingCatIds", "excludeTypes", "request", "route", "entityNodes", "onSuccess", "serverData", "<PERSON><PERSON><PERSON><PERSON>", "cloneDraggableNode", "draggableNode", "draggableNodeIcon", "dataset", "toLowerCase", "id", "classList", "add", "selectTypesIcon", "htmlEscape", "exports", "selectTypes", "cfs", "text", "i18n", "rfs", "resource", "entityTypeSelectBox", "_selectTypes", "toTypeOption", "map", "addToContainer", "container", "element", "insertAdjacentHTML", "elementTagName", "customElements", "define"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/modules/tsc/components/composition-diagram-sidebar/", "sources": ["composition-diagram-sidebar.element.js"], "sourcesContent": ["import '~components/i18n/i18n.element'\nimport '~components/i18n-container/i18n-container.element'\nimport '~components/selectbox/selectbox.element'\nimport '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/shadow-scroll/shadow-scroll.element'\nimport { entityNodes } from '../../utils/routes'\nimport * as proxy from '~utils/proxy'\nimport containerHtml from './composition-diagram-sidebar.element.html'\nimport draggableNodeHtml from './draggable-node-entity.template.html'\nimport i18n from '~utils/i18n'\nimport versionStore from '~tsc-common-stores/version-context-info.store'\nimport { htmlEscape } from '~utils/string.util'\n\nconst draggableNodeTemplate = document.createElement('template')\ndraggableNodeTemplate.innerHTML = draggableNodeHtml\n\nconst template = document.createElement('template')\ntemplate.innerHTML = containerHtml\n\nconst elementData = new WeakMap()\n\nexport class CompositionDiagramSidebar extends HTMLElement {\n  constructor () {\n    super()\n    elementData.set(this, {\n      rootNodeId: null,\n      versionStoreObserver: null,\n      throttling: false\n    })\n    const clone = document.importNode(template.content, true)\n    this.appendChild(clone)\n  }\n\n  connectedCallback () {\n    const selectBox = this.querySelector('.select-box--tsc-entity-type-filter')\n    const entityNameFilter = this.querySelector('.entity-name-filter-input')\n    const entityType = this.getAttribute('entity-type')\n    const excludedIds = this.getAttribute('excluded-entity-ids')\n\n    if (excludedIds == null) {\n      this.setAttribute('excluded-entity-ids', '')\n    }\n\n    if (entityType && versionStore.getters.versionName !== '') {\n      this.getEntityNodes()\n      this.reflectSelectBoxOptions()\n    }\n\n    selectBox.addEventListener('change', () => {\n      this.getEntityNodes()\n    })\n\n    let timeout\n    entityNameFilter.addEventListener('input', () => {\n      clearTimeout(timeout)\n      timeout = setTimeout(() => this.getEntityNodes(), 300)\n    })\n\n    const data = elementData.get(this)\n\n    data.versionStoreObserver = versionStore.react(\n      ({ getters }) => getters.versionName,\n      (newValue, oldValue) => {\n        if (this.hasAttribute('entity-type')) {\n          this.getEntityNodes()\n        }\n      }\n    )\n  }\n\n  set rootNodeId (nodeId) {\n    elementData.get(this).rootNodeId = nodeId\n  }\n\n  get rootNodeId () {\n    return elementData.get(this).rootNodeId\n  }\n\n  disconnectedCallback () {\n    const data = elementData.get(this)\n    data.versionStoreObserver?.unregister()\n    data.versionStoreObserver = null\n  }\n\n  static get observedAttributes () { return ['excluded-entity-ids', 'entity-type'] }\n\n  attributeChangedCallback (name, oldValue, newValue) {\n    if (name === 'entity-type') {\n      this.reflectSelectBoxOptions()\n    }\n\n    const versionName = versionStore.getters.versionName\n    if (!versionName && this.isConnected) {\n      return\n    }\n\n    const data = elementData.get(this)\n    const excludedIds = this.getAttribute('excluded-entity-ids')\n    const entityType = this.getAttribute('entity-type')\n    // to ensure that is not to update when the attribute is created with value ''\n    const isToUpdate = oldValue != null ? true : newValue !== ''\n    if ((entityType != null && name === 'excluded-entity-ids' && isToUpdate) ||\n          (excludedIds != null && name === 'entity-type' && oldValue != null)) {\n      if (!data.throttling) {\n        data.throttling = true\n        requestAnimationFrame(() => {\n          this.getEntityNodes()\n          data.throttling = false\n        })\n      }\n    }\n  }\n\n  reflectSelectBoxOptions() {\n    const selectBox = this.querySelector('.select-box--tsc-entity-type-filter')\n    const options = Array.from(selectBox.options)\n    options.filter(option => option.value !== 'all').forEach(option => selectBox.removeChild(option))\n    renderTypeOptions(this.getAttribute('entity-type'), selectBox)\n  }\n\n  getEntityNodes () {\n    const versionName = versionStore.getters.versionName\n    if (!versionName) {\n      return\n    }\n    const entityType = this.getAttribute('entity-type')\n    const excludedIds = this.getAttribute('excluded-entity-ids')\n    const externalNodes = this.querySelector('.external-nodes')\n    const entityNameFilter = this.querySelector('.entity-name-filter-input')\n    const selectBox = this.querySelector('.select-box--tsc-entity-type-filter')\n    const dataToSend = {\n      type: selectBox.value.toUpperCase(),\n      name: entityNameFilter.value,\n      rootId: this.rootNodeId,\n      excludingIds: excludedIds ? excludedIds.split(',') : [],\n      excludingCatIds: excludeTypes(entityType)\n    }\n\n    proxy.request({\n      route: entityNodes({ entityType, versionName }),\n      onSuccess: (serverData) => {\n        while (externalNodes.firstChild) externalNodes.removeChild(externalNodes.firstChild)\n        serverData.forEach(obj => {\n          const cloneDraggableNode = document.importNode(draggableNodeTemplate.content, true)\n          const draggableNode = cloneDraggableNode.querySelector('.tsc-draggable-node-entity')\n          const draggableNodeIcon = draggableNode.querySelector('.tsc-draggable-node-entity--icon')\n          draggableNode.setAttribute('title', obj.name)\n          draggableNode.dataset.entityType = obj.entityType.toLowerCase()\n          draggableNode.dataset.id = obj.id\n          draggableNodeIcon.classList.add(selectTypesIcon(obj.entityType.toLowerCase()))\n          draggableNode.innerHTML += htmlEscape(obj.name)\n          externalNodes.appendChild(draggableNode)\n        })\n      },\n      data: dataToSend\n    })\n  }\n}\n\nfunction selectTypesIcon (entityType) {\n  switch (entityType) {\n    case 'cfs':\n      return 'fuxicons-client-services'\n    case 'rfs':\n      return 'fuxicons-network-configuration'\n    case 'resource':\n      return 'fuxicons-resource'\n    default:\n      return ''\n  }\n}\n\nfunction selectTypes (entityType) {\n  const cfs = { value: 'cfs', text: i18n('nossis.tsccatalog.cfs.name') }\n  const rfs = { value: 'rfs', text: i18n('nossis.tsccatalog.rfs.name') }\n  const resource = { value: 'resource', text: i18n('nossis.tsccatalog.resource.name') }\n\n  switch (entityType) {\n    case 'cfs':\n      return [cfs, rfs, resource]\n    case 'rfs':\n      return [rfs, resource]\n    case 'resource':\n      return [resource]\n    default:\n      return null\n  }\n}\n\nfunction excludeTypes (entityType) {\n  switch (entityType) {\n    case 'cfs':\n      return []\n    case 'rfs':\n      return ['CFS']\n    case 'resource':\n      return ['CFS', 'RFS']\n    default:\n      return []\n  }\n}\n\nfunction renderTypeOptions (entityType, entityTypeSelectBox) {\n  function toTypeOption (type) {\n    return '<option value=\"' + type.value + '\">' + type.text + '</option>'\n  }\n  selectTypes(entityType)?.map(toTypeOption).forEach(addToContainer(entityTypeSelectBox))\n}\n\nfunction addToContainer (container) {\n  return function (element) {\n    container.insertAdjacentHTML('beforeend', element)\n  }\n}\n\nexport const elementTagName = 'x-tsc-composition-diagram-sidebar'\ncustomElements.define(elementTagName, CompositionDiagramSidebar)\n"], "mappings": ";;;;;;AAAAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,iCAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,4BAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,MAAA,GAAAF,sBAAA,CAAAL,OAAA;AACA,IAAAQ,mBAAA,GAAAH,sBAAA,CAAAL,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;AAA+C,SAAAK,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAX,wBAAAO,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAE/C,MAAMW,qBAAqB,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;AAChEF,qBAAqB,CAACG,SAAS,GAAGC,oCAAiB;AAEnD,MAAMC,QAAQ,GAAGJ,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;AACnDG,QAAQ,CAACF,SAAS,GAAGG,yCAAa;AAElC,MAAMC,WAAW,GAAG,IAAIxB,OAAO,CAAC,CAAC;AAE1B,MAAMyB,yBAAyB,SAASC,WAAW,CAAC;EACzDC,WAAWA,CAAA,EAAI;IACb,KAAK,CAAC,CAAC;IACPH,WAAW,CAACR,GAAG,CAAC,IAAI,EAAE;MACpBY,UAAU,EAAE,IAAI;MAChBC,oBAAoB,EAAE,IAAI;MAC1BC,UAAU,EAAE;IACd,CAAC,CAAC;IACF,MAAMC,KAAK,GAAGb,QAAQ,CAACc,UAAU,CAACV,QAAQ,CAACW,OAAO,EAAE,IAAI,CAAC;IACzD,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC;EACzB;EAEAI,iBAAiBA,CAAA,EAAI;IACnB,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,qCAAqC,CAAC;IAC3E,MAAMC,gBAAgB,GAAG,IAAI,CAACD,aAAa,CAAC,2BAA2B,CAAC;IACxE,MAAME,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC,aAAa,CAAC;IACnD,MAAMC,WAAW,GAAG,IAAI,CAACD,YAAY,CAAC,qBAAqB,CAAC;IAE5D,IAAIC,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACC,YAAY,CAAC,qBAAqB,EAAE,EAAE,CAAC;IAC9C;IAEA,IAAIH,UAAU,IAAII,2BAAY,CAACC,OAAO,CAACC,WAAW,KAAK,EAAE,EAAE;MACzD,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC;IAEAX,SAAS,CAACY,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACzC,IAAI,CAACF,cAAc,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,IAAIG,OAAO;IACXX,gBAAgB,CAACU,gBAAgB,CAAC,OAAO,EAAE,MAAM;MAC/CE,YAAY,CAACD,OAAO,CAAC;MACrBA,OAAO,GAAGE,UAAU,CAAC,MAAM,IAAI,CAACL,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,CAAC,CAAC;IAEF,MAAMM,IAAI,GAAG5B,WAAW,CAACnB,GAAG,CAAC,IAAI,CAAC;IAElC+C,IAAI,CAACvB,oBAAoB,GAAGc,2BAAY,CAACU,KAAK,CAC5C,CAAC;MAAET;IAAQ,CAAC,KAAKA,OAAO,CAACC,WAAW,EACpC,CAACS,QAAQ,EAAEC,QAAQ,KAAK;MACtB,IAAI,IAAI,CAACC,YAAY,CAAC,aAAa,CAAC,EAAE;QACpC,IAAI,CAACV,cAAc,CAAC,CAAC;MACvB;IACF,CACF,CAAC;EACH;EAEA,IAAIlB,UAAUA,CAAE6B,MAAM,EAAE;IACtBjC,WAAW,CAACnB,GAAG,CAAC,IAAI,CAAC,CAACuB,UAAU,GAAG6B,MAAM;EAC3C;EAEA,IAAI7B,UAAUA,CAAA,EAAI;IAChB,OAAOJ,WAAW,CAACnB,GAAG,CAAC,IAAI,CAAC,CAACuB,UAAU;EACzC;EAEA8B,oBAAoBA,CAAA,EAAI;IAAA,IAAAC,qBAAA;IACtB,MAAMP,IAAI,GAAG5B,WAAW,CAACnB,GAAG,CAAC,IAAI,CAAC;IAClC,CAAAsD,qBAAA,GAAAP,IAAI,CAACvB,oBAAoB,cAAA8B,qBAAA,eAAzBA,qBAAA,CAA2BC,UAAU,CAAC,CAAC;IACvCR,IAAI,CAACvB,oBAAoB,GAAG,IAAI;EAClC;EAEA,WAAWgC,kBAAkBA,CAAA,EAAI;IAAE,OAAO,CAAC,qBAAqB,EAAE,aAAa,CAAC;EAAC;EAEjFC,wBAAwBA,CAAEC,IAAI,EAAER,QAAQ,EAAED,QAAQ,EAAE;IAClD,IAAIS,IAAI,KAAK,aAAa,EAAE;MAC1B,IAAI,CAAChB,uBAAuB,CAAC,CAAC;IAChC;IAEA,MAAMF,WAAW,GAAGF,2BAAY,CAACC,OAAO,CAACC,WAAW;IACpD,IAAI,CAACA,WAAW,IAAI,IAAI,CAACmB,WAAW,EAAE;MACpC;IACF;IAEA,MAAMZ,IAAI,GAAG5B,WAAW,CAACnB,GAAG,CAAC,IAAI,CAAC;IAClC,MAAMoC,WAAW,GAAG,IAAI,CAACD,YAAY,CAAC,qBAAqB,CAAC;IAC5D,MAAMD,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC,aAAa,CAAC;IACnD;IACA,MAAMyB,UAAU,GAAGV,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAGD,QAAQ,KAAK,EAAE;IAC5D,IAAKf,UAAU,IAAI,IAAI,IAAIwB,IAAI,KAAK,qBAAqB,IAAIE,UAAU,IAChExB,WAAW,IAAI,IAAI,IAAIsB,IAAI,KAAK,aAAa,IAAIR,QAAQ,IAAI,IAAK,EAAE;MACzE,IAAI,CAACH,IAAI,CAACtB,UAAU,EAAE;QACpBsB,IAAI,CAACtB,UAAU,GAAG,IAAI;QACtBoC,qBAAqB,CAAC,MAAM;UAC1B,IAAI,CAACpB,cAAc,CAAC,CAAC;UACrBM,IAAI,CAACtB,UAAU,GAAG,KAAK;QACzB,CAAC,CAAC;MACJ;IACF;EACF;EAEAiB,uBAAuBA,CAAA,EAAG;IACxB,MAAMX,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,qCAAqC,CAAC;IAC3E,MAAM8B,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACjC,SAAS,CAAC+B,OAAO,CAAC;IAC7CA,OAAO,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK,KAAK,CAAC,CAACC,OAAO,CAACF,MAAM,IAAInC,SAAS,CAACsC,WAAW,CAACH,MAAM,CAAC,CAAC;IACjGI,iBAAiB,CAAC,IAAI,CAACnC,YAAY,CAAC,aAAa,CAAC,EAAEJ,SAAS,CAAC;EAChE;EAEAU,cAAcA,CAAA,EAAI;IAChB,MAAMD,WAAW,GAAGF,2BAAY,CAACC,OAAO,CAACC,WAAW;IACpD,IAAI,CAACA,WAAW,EAAE;MAChB;IACF;IACA,MAAMN,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC,aAAa,CAAC;IACnD,MAAMC,WAAW,GAAG,IAAI,CAACD,YAAY,CAAC,qBAAqB,CAAC;IAC5D,MAAMoC,aAAa,GAAG,IAAI,CAACvC,aAAa,CAAC,iBAAiB,CAAC;IAC3D,MAAMC,gBAAgB,GAAG,IAAI,CAACD,aAAa,CAAC,2BAA2B,CAAC;IACxE,MAAMD,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,qCAAqC,CAAC;IAC3E,MAAMwC,UAAU,GAAG;MACjBC,IAAI,EAAE1C,SAAS,CAACoC,KAAK,CAACO,WAAW,CAAC,CAAC;MACnChB,IAAI,EAAEzB,gBAAgB,CAACkC,KAAK;MAC5BQ,MAAM,EAAE,IAAI,CAACpD,UAAU;MACvBqD,YAAY,EAAExC,WAAW,GAAGA,WAAW,CAACyC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;MACvDC,eAAe,EAAEC,YAAY,CAAC7C,UAAU;IAC1C,CAAC;IAEDpD,KAAK,CAACkG,OAAO,CAAC;MACZC,KAAK,EAAE,IAAAC,mBAAW,EAAC;QAAEhD,UAAU;QAAEM;MAAY,CAAC,CAAC;MAC/C2C,SAAS,EAAGC,UAAU,IAAK;QACzB,OAAOb,aAAa,CAACc,UAAU,EAAEd,aAAa,CAACF,WAAW,CAACE,aAAa,CAACc,UAAU,CAAC;QACpFD,UAAU,CAAChB,OAAO,CAAC9E,GAAG,IAAI;UACxB,MAAMgG,kBAAkB,GAAGzE,QAAQ,CAACc,UAAU,CAACf,qBAAqB,CAACgB,OAAO,EAAE,IAAI,CAAC;UACnF,MAAM2D,aAAa,GAAGD,kBAAkB,CAACtD,aAAa,CAAC,4BAA4B,CAAC;UACpF,MAAMwD,iBAAiB,GAAGD,aAAa,CAACvD,aAAa,CAAC,kCAAkC,CAAC;UACzFuD,aAAa,CAAClD,YAAY,CAAC,OAAO,EAAE/C,GAAG,CAACoE,IAAI,CAAC;UAC7C6B,aAAa,CAACE,OAAO,CAACvD,UAAU,GAAG5C,GAAG,CAAC4C,UAAU,CAACwD,WAAW,CAAC,CAAC;UAC/DH,aAAa,CAACE,OAAO,CAACE,EAAE,GAAGrG,GAAG,CAACqG,EAAE;UACjCH,iBAAiB,CAACI,SAAS,CAACC,GAAG,CAACC,eAAe,CAACxG,GAAG,CAAC4C,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,CAAC;UAC9EH,aAAa,CAACxE,SAAS,IAAI,IAAAgF,kBAAU,EAACzG,GAAG,CAACoE,IAAI,CAAC;UAC/Ca,aAAa,CAAC1C,WAAW,CAAC0D,aAAa,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDxC,IAAI,EAAEyB;IACR,CAAC,CAAC;EACJ;AACF;AAACwB,OAAA,CAAA5E,yBAAA,GAAAA,yBAAA;AAED,SAAS0E,eAAeA,CAAE5D,UAAU,EAAE;EACpC,QAAQA,UAAU;IAChB,KAAK,KAAK;MACR,OAAO,0BAA0B;IACnC,KAAK,KAAK;MACR,OAAO,gCAAgC;IACzC,KAAK,UAAU;MACb,OAAO,mBAAmB;IAC5B;MACE,OAAO,EAAE;EACb;AACF;AAEA,SAAS+D,WAAWA,CAAE/D,UAAU,EAAE;EAChC,MAAMgE,GAAG,GAAG;IAAE/B,KAAK,EAAE,KAAK;IAAEgC,IAAI,EAAE,IAAAC,cAAI,EAAC,4BAA4B;EAAE,CAAC;EACtE,MAAMC,GAAG,GAAG;IAAElC,KAAK,EAAE,KAAK;IAAEgC,IAAI,EAAE,IAAAC,cAAI,EAAC,4BAA4B;EAAE,CAAC;EACtE,MAAME,QAAQ,GAAG;IAAEnC,KAAK,EAAE,UAAU;IAAEgC,IAAI,EAAE,IAAAC,cAAI,EAAC,iCAAiC;EAAE,CAAC;EAErF,QAAQlE,UAAU;IAChB,KAAK,KAAK;MACR,OAAO,CAACgE,GAAG,EAAEG,GAAG,EAAEC,QAAQ,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,CAACD,GAAG,EAAEC,QAAQ,CAAC;IACxB,KAAK,UAAU;MACb,OAAO,CAACA,QAAQ,CAAC;IACnB;MACE,OAAO,IAAI;EACf;AACF;AAEA,SAASvB,YAAYA,CAAE7C,UAAU,EAAE;EACjC,QAAQA,UAAU;IAChB,KAAK,KAAK;MACR,OAAO,EAAE;IACX,KAAK,KAAK;MACR,OAAO,CAAC,KAAK,CAAC;IAChB,KAAK,UAAU;MACb,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;IACvB;MACE,OAAO,EAAE;EACb;AACF;AAEA,SAASoC,iBAAiBA,CAAEpC,UAAU,EAAEqE,mBAAmB,EAAE;EAAA,IAAAC,YAAA;EAC3D,SAASC,YAAYA,CAAEhC,IAAI,EAAE;IAC3B,OAAO,iBAAiB,GAAGA,IAAI,CAACN,KAAK,GAAG,IAAI,GAAGM,IAAI,CAAC0B,IAAI,GAAG,WAAW;EACxE;EACA,CAAAK,YAAA,GAAAP,WAAW,CAAC/D,UAAU,CAAC,cAAAsE,YAAA,eAAvBA,YAAA,CAAyBE,GAAG,CAACD,YAAY,CAAC,CAACrC,OAAO,CAACuC,cAAc,CAACJ,mBAAmB,CAAC,CAAC;AACzF;AAEA,SAASI,cAAcA,CAAEC,SAAS,EAAE;EAClC,OAAO,UAAUC,OAAO,EAAE;IACxBD,SAAS,CAACE,kBAAkB,CAAC,WAAW,EAAED,OAAO,CAAC;EACpD,CAAC;AACH;AAEO,MAAME,cAAc,GAAG,mCAAmC;AAAAf,OAAA,CAAAe,cAAA,GAAAA,cAAA;AACjEC,cAAc,CAACC,MAAM,CAACF,cAAc,EAAE3F,yBAAyB,CAAC"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "ec05f40f61476792c2020efa7fa0864ef54fe7c4", "contentHash": "b12380e73e14df08669bc14d54fac125e1d77b5870c69038eecb41660b93a63a"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n/i18n.element.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n/i18n.element.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 54}}, "2": {"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": 34}}, "3": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 47}}, "4": {"start": {"line": 9, "column": 33}, "end": {"line": 9, "column": 89}}, "5": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 38}}, "6": {"start": {"line": 11, "column": 36}, "end": {"line": 14, "column": 1}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 41}}, "8": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 89}}, "9": {"start": {"line": 13, "column": 63}, "end": {"line": 13, "column": 86}}, "10": {"start": {"line": 15, "column": 38}, "end": {"line": 20, "column": 1}}, "11": {"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, "12": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 47}}, "13": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 37}}, "14": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 12}}, "15": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 27}}, "16": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "17": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 13}}, "18": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 44}}, "19": {"start": {"line": 34, "column": 16}, "end": {"line": 34, "column": 140}}, "20": {"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 5}}, "21": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 26}}, "22": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 13}}, "23": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 50}}, "24": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": 67}}, "25": {"start": {"line": 44, "column": 32}, "end": {"line": 44, "column": 83}}, "26": {"start": {"line": 44, "column": 56}, "end": {"line": 44, "column": 82}}, "27": {"start": {"line": 45, "column": 4}, "end": {"line": 62, "column": 5}}, "28": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 70}}, "29": {"start": {"line": 47, "column": 11}, "end": {"line": 62, "column": 5}}, "30": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 26}}, "31": {"start": {"line": 49, "column": 6}, "end": {"line": 59, "column": 49}}, "32": {"start": {"line": 50, "column": 8}, "end": {"line": 57, "column": 9}}, "33": {"start": {"line": 51, "column": 23}, "end": {"line": 51, "column": 53}}, "34": {"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 50}}, "35": {"start": {"line": 53, "column": 10}, "end": {"line": 53, "column": 89}}, "36": {"start": {"line": 54, "column": 10}, "end": {"line": 54, "column": 71}}, "37": {"start": {"line": 55, "column": 10}, "end": {"line": 55, "column": 40}}, "38": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 22}}, "39": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 51}}, "40": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": 47}}, "41": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 72}}, "42": {"start": {"line": 65, "column": 4}, "end": {"line": 69, "column": 5}}, "43": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 29}}, "44": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 38}}, "45": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 40}}, "46": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 34}}, "47": {"start": {"line": 79, "column": 23}, "end": {"line": 79, "column": 31}}, "48": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 40}}, "49": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 51}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 37}}, "loc": {"start": {"line": 11, "column": 47}, "end": {"line": 14, "column": 1}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 57}, "end": {"line": 13, "column": 58}}, "loc": {"start": {"line": 13, "column": 63}, "end": {"line": 13, "column": 86}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 38}, "end": {"line": 15, "column": 39}}, "loc": {"start": {"line": 15, "column": 49}, "end": {"line": 20, "column": 1}}, "line": 15}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 22, "column": 16}, "end": {"line": 24, "column": 3}}, "line": 22}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 3}}, "loc": {"start": {"line": 25, "column": 34}, "end": {"line": 27, "column": 3}}, "line": 25}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 3}}, "loc": {"start": {"line": 28, "column": 18}, "end": {"line": 63, "column": 3}}, "line": 28}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 44, "column": 47}, "end": {"line": 44, "column": 48}}, "loc": {"start": {"line": 44, "column": 56}, "end": {"line": 44, "column": 82}}, "line": 44}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 21}}, "loc": {"start": {"line": 49, "column": 29}, "end": {"line": 59, "column": 7}}, "line": 49}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 59, "column": 17}, "end": {"line": 59, "column": 18}}, "loc": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": 47}}, "line": 59}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 3}}, "loc": {"start": {"line": 64, "column": 53}, "end": {"line": 70, "column": 3}}, "line": 64}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 3}}, "loc": {"start": {"line": 71, "column": 22}, "end": {"line": 73, "column": 3}}, "line": 71}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 3}}, "loc": {"start": {"line": 74, "column": 25}, "end": {"line": 76, "column": 3}}, "line": 74}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}], "line": 16}, "1": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}], "line": 30}, "2": {"loc": {"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 132}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 109}, "end": {"line": 34, "column": 127}}, {"start": {"line": 34, "column": 130}, "end": {"line": 34, "column": 132}}], "line": 34}, "3": {"loc": {"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 106}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 73}}, {"start": {"line": 34, "column": 77}, "end": {"line": 34, "column": 106}}], "line": 34}, "4": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 5}}, {"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 5}}], "line": 35}, "5": {"loc": {"start": {"line": 45, "column": 4}, "end": {"line": 62, "column": 5}}, "type": "if", "locations": [{"start": {"line": 45, "column": 4}, "end": {"line": 62, "column": 5}}, {"start": {"line": 45, "column": 4}, "end": {"line": 62, "column": 5}}], "line": 45}, "6": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 62, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 11}, "end": {"line": 62, "column": 5}}, {"start": {"line": 47, "column": 11}, "end": {"line": 62, "column": 5}}], "line": 47}, "7": {"loc": {"start": {"line": 50, "column": 8}, "end": {"line": 57, "column": 9}}, "type": "if", "locations": [{"start": {"line": 50, "column": 8}, "end": {"line": 57, "column": 9}}, {"start": {"line": 50, "column": 8}, "end": {"line": 57, "column": 9}}], "line": 50}, "8": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 69, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 17}}, {"start": {"line": 67, "column": 6}, "end": {"line": 68, "column": 29}}], "line": 65}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 1, "11": 0, "12": 0, "13": 0, "14": 0, "15": 1, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 1, "47": 1, "48": 1, "49": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}, "inputSourceMap": {"version": 3, "names": ["_i18n", "require", "_provider", "_stringWithParameterParser", "providerObserver", "WeakMap", "observeI18nProvideOnElement", "element", "unobserveI18nProvideOnElement", "set", "onProvide", "updateContent", "has", "get", "unregister", "delete", "I18nElement", "HTMLElement", "constructor", "observedAttributes", "_this$getAttribute", "isProvided", "isHtml", "hasAttribute", "key", "getAttribute", "trim", "innerHTML", "parsed<PERSON><PERSON>", "parameters", "parse", "tokenList", "tokenize", "tokensHasParameters", "find", "token", "type", "translate", "map", "span", "document", "createElement", "classList", "add", "toggle", "usingFallback", "setAttribute", "position", "textContent", "text", "createTextNode", "for<PERSON>ach", "node", "append<PERSON><PERSON><PERSON>", "attributeChangedCallback", "name", "oldValue", "newValue", "connectedCallback", "disconnectedCallback", "exports", "elementTagName", "customElements", "define"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n/", "sources": ["i18n.element.js"], "sourcesContent": ["import { translate, tokenize } from '~utils/i18n'\nimport { onProvide, isProvided } from '~utils/i18n/provider'\nimport { parse } from '~utils/i18n/string-with-parameter-parser.util'\n\nconst providerObserver = new WeakMap()\n\nconst observeI18nProvideOnElement = (element) => {\n  unobserveI18nProvideOnElement(element)\n  providerObserver.set(element, onProvide(() => element.updateContent()))\n}\n\nconst unobserveI18nProvideOnElement = (element) => {\n  if (providerObserver.has(element)) {\n    providerObserver.get(element).unregister()\n    providerObserver.delete(element)\n  }\n}\n\nexport class I18nElement extends HTMLElement {\n  constructor () {\n    super()\n  }\n\n  static get observedAttributes () { return ['key', 'html'] }\n\n  updateContent () {\n    if (!isProvided()) {\n      return\n    }\n    const isHtml = this.hasAttribute('html')\n    const key = (this.getAttribute('key') ?? '').trim()\n\n    if (key === '') {\n      this.innerHTML = ''\n      return\n    }\n\n    const { key: parsedKey, parameters } = parse(key)\n\n    const tokenList = tokenize(parsedKey, ...parameters)\n    const tokensHasParameters = tokenList.find(token => token.type === 'parameter')\n\n    if (isHtml) {\n      this.innerHTML = translate(parsedKey, ...parameters)\n    } else if (tokensHasParameters) {\n      this.innerHTML = ''\n      tokenList.map(token => {\n        if (token.type === 'parameter') {\n          const span = document.createElement('span')\n          span.classList.add('i18n-substitution')\n          span.classList.toggle('i18n-substitution--uses-fallback', token.usingFallback)\n          span.setAttribute('data-parameter-position', token.position)\n          span.textContent = token.text\n          return span\n        }\n        return document.createTextNode(token.text)\n      }).forEach(node => this.appendChild(node))\n    } else {\n      this.textContent = translate(parsedKey, ...parameters)\n    }\n  }\n\n  attributeChangedCallback (name, oldValue, newValue) {\n    switch (name) {\n      case 'key':\n      case 'html':\n        this.updateContent()\n    }\n  }\n\n  connectedCallback () {\n    observeI18nProvideOnElement(this)\n  }\n\n  disconnectedCallback () {\n    unobserveI18nProvideOnElement(this)\n  }\n}\n\nexport const elementTagName = 'x-i18n'\ncustomElements.define(elementTagName, I18nElement)\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEA,MAAMG,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAEtC,MAAMC,2BAA2B,GAAIC,OAAO,IAAK;EAC/CC,6BAA6B,CAACD,OAAO,CAAC;EACtCH,gBAAgB,CAACK,GAAG,CAACF,OAAO,EAAE,IAAAG,mBAAS,EAAC,MAAMH,OAAO,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,MAAMH,6BAA6B,GAAID,OAAO,IAAK;EACjD,IAAIH,gBAAgB,CAACQ,GAAG,CAACL,OAAO,CAAC,EAAE;IACjCH,gBAAgB,CAACS,GAAG,CAACN,OAAO,CAAC,CAACO,UAAU,CAAC,CAAC;IAC1CV,gBAAgB,CAACW,MAAM,CAACR,OAAO,CAAC;EAClC;AACF,CAAC;AAEM,MAAMS,WAAW,SAASC,WAAW,CAAC;EAC3CC,WAAWA,CAAA,EAAI;IACb,KAAK,CAAC,CAAC;EACT;EAEA,WAAWC,kBAAkBA,CAAA,EAAI;IAAE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EAAC;EAE1DR,aAAaA,CAAA,EAAI;IAAA,IAAAS,kBAAA;IACf,IAAI,CAAC,IAAAC,oBAAU,EAAC,CAAC,EAAE;MACjB;IACF;IACA,MAAMC,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC;IACxC,MAAMC,GAAG,GAAG,EAAAJ,kBAAA,GAAC,IAAI,CAACK,YAAY,CAAC,KAAK,CAAC,cAAAL,kBAAA,cAAAA,kBAAA,GAAI,EAAE,EAAEM,IAAI,CAAC,CAAC;IAEnD,IAAIF,GAAG,KAAK,EAAE,EAAE;MACd,IAAI,CAACG,SAAS,GAAG,EAAE;MACnB;IACF;IAEA,MAAM;MAAEH,GAAG,EAAEI,SAAS;MAAEC;IAAW,CAAC,GAAG,IAAAC,gCAAK,EAACN,GAAG,CAAC;IAEjD,MAAMO,SAAS,GAAG,IAAAC,cAAQ,EAACJ,SAAS,EAAE,GAAGC,UAAU,CAAC;IACpD,MAAMI,mBAAmB,GAAGF,SAAS,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAK,WAAW,CAAC;IAE/E,IAAId,MAAM,EAAE;MACV,IAAI,CAACK,SAAS,GAAG,IAAAU,eAAS,EAACT,SAAS,EAAE,GAAGC,UAAU,CAAC;IACtD,CAAC,MAAM,IAAII,mBAAmB,EAAE;MAC9B,IAAI,CAACN,SAAS,GAAG,EAAE;MACnBI,SAAS,CAACO,GAAG,CAACH,KAAK,IAAI;QACrB,IAAIA,KAAK,CAACC,IAAI,KAAK,WAAW,EAAE;UAC9B,MAAMG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAC3CF,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;UACvCJ,IAAI,CAACG,SAAS,CAACE,MAAM,CAAC,kCAAkC,EAAET,KAAK,CAACU,aAAa,CAAC;UAC9EN,IAAI,CAACO,YAAY,CAAC,yBAAyB,EAAEX,KAAK,CAACY,QAAQ,CAAC;UAC5DR,IAAI,CAACS,WAAW,GAAGb,KAAK,CAACc,IAAI;UAC7B,OAAOV,IAAI;QACb;QACA,OAAOC,QAAQ,CAACU,cAAc,CAACf,KAAK,CAACc,IAAI,CAAC;MAC5C,CAAC,CAAC,CAACE,OAAO,CAACC,IAAI,IAAI,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL,IAAI,CAACJ,WAAW,GAAG,IAAAX,eAAS,EAACT,SAAS,EAAE,GAAGC,UAAU,CAAC;IACxD;EACF;EAEAyB,wBAAwBA,CAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAClD,QAAQF,IAAI;MACV,KAAK,KAAK;MACV,KAAK,MAAM;QACT,IAAI,CAAC5C,aAAa,CAAC,CAAC;IACxB;EACF;EAEA+C,iBAAiBA,CAAA,EAAI;IACnBpD,2BAA2B,CAAC,IAAI,CAAC;EACnC;EAEAqD,oBAAoBA,CAAA,EAAI;IACtBnD,6BAA6B,CAAC,IAAI,CAAC;EACrC;AACF;AAACoD,OAAA,CAAA5C,WAAA,GAAAA,WAAA;AAEM,MAAM6C,cAAc,GAAG,QAAQ;AAAAD,OAAA,CAAAC,cAAA,GAAAA,cAAA;AACtCC,cAAc,CAACC,MAAM,CAACF,cAAc,EAAE7C,WAAW,CAAC"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "2a4c569043551c38b31d0e397e4556ea685e92eb", "contentHash": "325baf9d1cb818ac3c28eecd8ad0f334e2e99b33c072855e16ab453913288140"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/index.ts": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/index.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 141}}, "2": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 37}}, "3": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 86}}, "4": {"start": {"line": 8, "column": 40}, "end": {"line": 8, "column": 86}}, "5": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 30}}, "6": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 98}}, "7": {"start": {"line": 10, "column": 45}, "end": {"line": 10, "column": 98}}, "8": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 48}}, "9": {"start": {"line": 12, "column": 17}, "end": {"line": 12, "column": 84}}, "10": {"start": {"line": 12, "column": 39}, "end": {"line": 12, "column": 84}}, "11": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 28}}, "12": {"start": {"line": 14, "column": 31}, "end": {"line": 14, "column": 49}}, "13": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 56}}, "14": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 22}}, "15": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 20}}, "16": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 24}}, "17": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 19}}, "loc": {"start": {"line": 8, "column": 40}, "end": {"line": 8, "column": 86}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 28}}, "loc": {"start": {"line": 10, "column": 45}, "end": {"line": 10, "column": 98}}, "line": 10}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 17}, "end": {"line": 12, "column": 18}}, "loc": {"start": {"line": 12, "column": 39}, "end": {"line": 12, "column": 84}}, "line": 12}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0, "5": 1, "6": 1, "7": 0, "8": 1, "9": 1, "10": 0, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}, "inputSourceMap": {"version": 3, "names": ["_provider", "require", "translate", "message", "args", "instance", "exports", "filterByPartialKey", "partialMessage", "tokenize", "i18nFilterByPartialKey", "i18n", "_default", "default"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/", "sources": ["index.ts"], "sourcesContent": ["import { instance } from './provider'\n\nexport const translate = (message: string, ...args: string[]): string => instance.translate(message, ...args)\nexport const filterByPartialKey = (partialMessage: string): string | string[] => instance.filterByPartialKey(partialMessage)\nexport const tokenize = (message: string, ...args: string[]) => instance.tokenize(message, ...args)\n\nexport const i18nFilterByPartialKey = filterByPartialKey\nexport const i18n = translate\n\nexport default translate\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEO,MAAMC,SAAS,GAAGA,CAACC,OAAe,EAAE,GAAGC,IAAc,KAAaC,kBAAQ,CAACH,SAAS,CAACC,OAAO,EAAE,GAAGC,IAAI,CAAC;AAAAE,OAAA,CAAAJ,SAAA,GAAAA,SAAA;AACtG,MAAMK,kBAAkB,GAAIC,cAAsB,IAAwBH,kBAAQ,CAACE,kBAAkB,CAACC,cAAc,CAAC;AAAAF,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AACrH,MAAME,QAAQ,GAAGA,CAACN,OAAe,EAAE,GAAGC,IAAc,KAAKC,kBAAQ,CAACI,QAAQ,CAACN,OAAO,EAAE,GAAGC,IAAI,CAAC;AAAAE,OAAA,CAAAG,QAAA,GAAAA,QAAA;AAE5F,MAAMC,sBAAsB,GAAGH,kBAAkB;AAAAD,OAAA,CAAAI,sBAAA,GAAAA,sBAAA;AACjD,MAAMC,IAAI,GAAGT,SAAS;AAAAI,OAAA,CAAAK,IAAA,GAAAA,IAAA;AAAA,IAAAC,QAAA,GAEdV,SAAS;AAAAI,OAAA,CAAAO,OAAA,GAAAD,QAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "776aecdb7584483780bff5a7df8522adb500f041", "contentHash": "868b94d2a639928944aa09da7efceb21c3ec1e448e98182d3622580d9a116523"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/string-with-parameter-parser.util.ts": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/string-with-parameter-parser.util.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 23}}, "2": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 55}}, "3": {"start": {"line": 26, "column": 14}, "end": {"line": 55, "column": 1}}, "4": {"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 3}}, "5": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 81}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 6}}, "7": {"start": {"line": 35, "column": 21}, "end": {"line": 35, "column": 43}}, "8": {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 3}}, "9": {"start": {"line": 38, "column": 4}, "end": {"line": 42, "column": 6}}, "10": {"start": {"line": 44, "column": 2}, "end": {"line": 54, "column": 4}}, "11": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 23}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 50, "column": 7}}, "13": {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 32}}, "14": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 17}}, "15": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 14}, "end": {"line": 26, "column": 15}}, "loc": {"start": {"line": 26, "column": 27}, "end": {"line": 55, "column": 1}}, "line": 26}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 46, "column": 40}, "end": {"line": 46, "column": 41}}, "loc": {"start": {"line": 46, "column": 47}, "end": {"line": 52, "column": 5}}, "line": 46}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 3}}, "type": "if", "locations": [{"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 3}}, {"start": {"line": 27, "column": 2}, "end": {"line": 34, "column": 3}}], "line": 27}, "1": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 3}}], "line": 36}, "2": {"loc": {"start": {"line": 48, "column": 6}, "end": {"line": 50, "column": 7}}, "type": "if", "locations": [{"start": {"line": 48, "column": 6}, "end": {"line": 50, "column": 7}}, {"start": {"line": 48, "column": 6}, "end": {"line": 50, "column": 7}}], "line": 48}, "3": {"loc": {"start": {"line": 48, "column": 10}, "end": {"line": 48, "column": 94}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 10}, "end": {"line": 48, "column": 29}}, {"start": {"line": 48, "column": 33}, "end": {"line": 48, "column": 50}}, {"start": {"line": 48, "column": 54}, "end": {"line": 48, "column": 73}}, {"start": {"line": 48, "column": 77}, "end": {"line": 48, "column": 94}}], "line": 48}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 1}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0, 0, 0]}, "inputSourceMap": {"version": 3, "names": ["regex", "parse", "paramText", "console", "warn", "key", "parameters", "valid", "splitValue", "match", "trim", "slice", "map", "str", "startsWith", "endsWith", "exports"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/utils/i18n/", "sources": ["string-with-parameter-parser.util.ts"], "sourcesContent": ["interface ParseResult {\n  key: string\n  parameters: string[]\n  valid: boolean\n}\n\n/**\n  regex will match:\n\n     (\n        \"[^\"]*\"       double quotes + anything but double quotes + double quotes\n        |           OR\n        '[^']*'       quote + anything but quote + quote\n        |           OR\n        [^'\";]+    1 or more characters excl. quotes, double quotes or semicolon\n    )\n    (?=             FOLLOWED BY\n        \\s*;        0 or more empty spaces and a semicolon\n        |           OR\n        \\s*$        0 or more empty spaces and nothing else (end of string)\n    )\n\n It is used to split a string by unquoted semicolon\n*/\nconst regex = /(\"[^\"]*\"|'[^']*'|[^'\";]+)(?=\\s*;|\\s*$)/g\n\nexport const parse = (paramText: string): ParseResult => {\n  if (typeof paramText !== 'string') {\n    console.warn('i18n string with parameter parser: argument must be a string')\n    return {\n      key: '',\n      parameters: [],\n      valid: false\n    }\n  }\n  const splitValue = (paramText).match(regex)\n  if (splitValue == null) {\n    // on this block, paramText is empty or has only whitespace characters\n    return {\n      key: '',\n      parameters: [],\n      valid: true\n    }\n  }\n  return {\n    key: splitValue[0].trim(),\n    parameters: splitValue.slice(1).map(str => {\n      str = str.trim()\n      if ((str.startsWith(\"'\") && str.endsWith(\"'\")) || (str.startsWith('\"') && str.endsWith('\"'))) {\n        return str.slice(1, -1)\n      }\n      return str\n    }),\n    valid: true\n  }\n}\n"], "mappings": ";;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,KAAK,GAAG,yCAAyC;AAEhD,MAAMC,KAAK,GAAIC,SAAiB,IAAkB;EACvD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjCC,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC;IAC5E,OAAO;MACLC,GAAG,EAAE,EAAE;MACPC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE;IACT,CAAC;EACH;EACA,MAAMC,UAAU,GAAIN,SAAS,CAAEO,KAAK,CAACT,KAAK,CAAC;EAC3C,IAAIQ,UAAU,IAAI,IAAI,EAAE;IACtB;IACA,OAAO;MACLH,GAAG,EAAE,EAAE;MACPC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE;IACT,CAAC;EACH;EACA,OAAO;IACLF,GAAG,EAAEG,UAAU,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;IACzBJ,UAAU,EAAEE,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAACC,GAAG,IAAI;MACzCA,GAAG,GAAGA,GAAG,CAACH,IAAI,CAAC,CAAC;MAChB,IAAKG,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,GAAG,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAMF,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,GAAG,CAACE,QAAQ,CAAC,GAAG,CAAE,EAAE;QAC5F,OAAOF,GAAG,CAACF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACzB;MACA,OAAOE,GAAG;IACZ,CAAC,CAAC;IACFN,KAAK,EAAE;EACT,CAAC;AACH,CAAC;AAAAS,OAAA,CAAAf,KAAA,GAAAA,KAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "284928828c1ad3c27886f8c7020680406a8545a8", "contentHash": "2ba8dc2988df5e0ce4b1935dbe6bf586c422f84fd93ddbee7deada0ea0ee9e8b"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n-container/i18n-container.element.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n-container/i18n-container.element.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 63}}, "2": {"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": 34}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 40}}, "4": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 47}}, "5": {"start": {"line": 10, "column": 33}, "end": {"line": 10, "column": 89}}, "6": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 47}}, "7": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 105}}, "8": {"start": {"line": 12, "column": 41}, "end": {"line": 12, "column": 105}}, "9": {"start": {"line": 12, "column": 72}, "end": {"line": 12, "column": 104}}, "10": {"start": {"line": 13, "column": 35}, "end": {"line": 22, "column": 1}}, "11": {"start": {"line": 14, "column": 2}, "end": {"line": 21, "column": 3}}, "12": {"start": {"line": 14, "column": 15}, "end": {"line": 14, "column": 16}}, "13": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 23}}, "14": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 37}}, "15": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "16": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 52}}, "17": {"start": {"line": 23, "column": 35}, "end": {"line": 52, "column": 1}}, "18": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 27}}, "19": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 31}}, "20": {"start": {"line": 27, "column": 26}, "end": {"line": 27, "column": 77}}, "21": {"start": {"line": 28, "column": 2}, "end": {"line": 51, "column": 4}}, "22": {"start": {"line": 29, "column": 4}, "end": {"line": 38, "column": 7}}, "23": {"start": {"line": 30, "column": 6}, "end": {"line": 37, "column": 7}}, "24": {"start": {"line": 32, "column": 10}, "end": {"line": 32, "column": 113}}, "25": {"start": {"line": 32, "column": 53}, "end": {"line": 32, "column": 111}}, "26": {"start": {"line": 34, "column": 10}, "end": {"line": 36, "column": 11}}, "27": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 48}}, "28": {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, "29": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 13}}, "30": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 41}}, "31": {"start": {"line": 43, "column": 4}, "end": {"line": 50, "column": 7}}, "32": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 43}}, "33": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 53}}, "34": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 22}}, "35": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 26}}, "36": {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 68}}, "37": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 41}}, "38": {"start": {"line": 53, "column": 23}, "end": {"line": 57, "column": 1}}, "39": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 16}}, "40": {"start": {"line": 62, "column": 2}, "end": {"line": 78, "column": 3}}, "41": {"start": {"line": 62, "column": 15}, "end": {"line": 62, "column": 16}}, "42": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 18}}, "43": {"start": {"line": 65, "column": 22}, "end": {"line": 65, "column": 35}}, "44": {"start": {"line": 66, "column": 4}, "end": {"line": 77, "column": 5}}, "45": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 40}}, "46": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "47": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 17}}, "48": {"start": {"line": 74, "column": 10}, "end": {"line": 74, "column": 52}}, "49": {"start": {"line": 75, "column": 30}, "end": {"line": 75, "column": 72}}, "50": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 95}}, "51": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 28}}, "52": {"start": {"line": 82, "column": 2}, "end": {"line": 84, "column": 3}}, "53": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 33}}, "54": {"start": {"line": 86, "column": 28}, "end": {"line": 89, "column": 1}}, "55": {"start": {"line": 92, "column": 25}, "end": {"line": 92, "column": 38}}, "56": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 12}}, "57": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 7}}, "58": {"start": {"line": 101, "column": 42}, "end": {"line": 110, "column": 8}}, "59": {"start": {"line": 102, "column": 27}, "end": {"line": 102, "column": 45}}, "60": {"start": {"line": 103, "column": 6}, "end": {"line": 108, "column": 7}}, "61": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}, "62": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 22}}, "63": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 54}}, "64": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 19}}, "65": {"start": {"line": 112, "column": 4}, "end": {"line": 123, "column": 5}}, "66": {"start": {"line": 114, "column": 28}, "end": {"line": 114, "column": 54}}, "67": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 146}}, "68": {"start": {"line": 116, "column": 6}, "end": {"line": 122, "column": 9}}, "69": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 224}}, "70": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 52}}, "71": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 69}}, "72": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 40}}, "73": {"start": {"line": 127, "column": 26}, "end": {"line": 127, "column": 52}}, "74": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 147}}, "75": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 147}}, "76": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 42}}, "77": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 63}}, "78": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 52}}, "79": {"start": {"line": 137, "column": 23}, "end": {"line": 137, "column": 46}}, "80": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 40}}, "81": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 60}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 25}}, "loc": {"start": {"line": 12, "column": 41}, "end": {"line": 12, "column": 105}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 62}, "end": {"line": 12, "column": 63}}, "loc": {"start": {"line": 12, "column": 72}, "end": {"line": 12, "column": 104}}, "line": 12}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 36}}, "loc": {"start": {"line": 13, "column": 56}, "end": {"line": 22, "column": 1}}, "line": 13}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 35}, "end": {"line": 23, "column": 36}}, "loc": {"start": {"line": 23, "column": 46}, "end": {"line": 52, "column": 1}}, "line": 23}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 28, "column": 9}, "end": {"line": 28, "column": 10}}, "loc": {"start": {"line": 28, "column": 22}, "end": {"line": 51, "column": 3}}, "line": 28}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 22}, "end": {"line": 29, "column": 23}}, "loc": {"start": {"line": 29, "column": 42}, "end": {"line": 38, "column": 5}}, "line": 29}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 32, "column": 45}, "end": {"line": 32, "column": 46}}, "loc": {"start": {"line": 32, "column": 53}, "end": {"line": 32, "column": 111}}, "line": 32}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 43, "column": 43}, "end": {"line": 43, "column": 44}}, "loc": {"start": {"line": 43, "column": 49}, "end": {"line": 50, "column": 5}}, "line": 43}, "8": {"name": "updateI8nAttributes", "decl": {"start": {"line": 58, "column": 9}, "end": {"line": 58, "column": 28}}, "loc": {"start": {"line": 58, "column": 41}, "end": {"line": 79, "column": 1}}, "line": 58}, "9": {"name": "updateI8nAttributesOnTree", "decl": {"start": {"line": 80, "column": 9}, "end": {"line": 80, "column": 34}}, "loc": {"start": {"line": 80, "column": 41}, "end": {"line": 85, "column": 1}}, "line": 80}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 3}}, "loc": {"start": {"line": 94, "column": 16}, "end": {"line": 99, "column": 3}}, "line": 94}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 3}}, "loc": {"start": {"line": 100, "column": 22}, "end": {"line": 124, "column": 3}}, "line": 100}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 101, "column": 43}, "end": {"line": 101, "column": 44}}, "loc": {"start": {"line": 101, "column": 49}, "end": {"line": 110, "column": 5}}, "line": 101}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 116, "column": 64}, "end": {"line": 116, "column": 65}}, "loc": {"start": {"line": 116, "column": 70}, "end": {"line": 122, "column": 7}}, "line": 116}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 3}}, "loc": {"start": {"line": 125, "column": 25}, "end": {"line": 131, "column": 3}}, "line": 125}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 3}}, "loc": {"start": {"line": 132, "column": 27}, "end": {"line": 134, "column": 3}}, "line": 132}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}], "line": 18}, "1": {"loc": {"start": {"line": 30, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 31, "column": 8}, "end": {"line": 32, "column": 113}}, {"start": {"line": 33, "column": 8}, "end": {"line": 36, "column": 11}}], "line": 30}, "2": {"loc": {"start": {"line": 32, "column": 53}, "end": {"line": 32, "column": 111}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 53}, "end": {"line": 32, "column": 86}}, {"start": {"line": 32, "column": 90}, "end": {"line": 32, "column": 111}}], "line": 32}, "3": {"loc": {"start": {"line": 34, "column": 10}, "end": {"line": 36, "column": 11}}, "type": "if", "locations": [{"start": {"line": 34, "column": 10}, "end": {"line": 36, "column": 11}}, {"start": {"line": 34, "column": 10}, "end": {"line": 36, "column": 11}}], "line": 34}, "4": {"loc": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 53}}, {"start": {"line": 34, "column": 57}, "end": {"line": 34, "column": 80}}], "line": 34}, "5": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 5}}], "line": 39}, "6": {"loc": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 25}}, {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 50}}], "line": 39}, "7": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 77, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 77, "column": 5}}, {"start": {"line": 66, "column": 4}, "end": {"line": 77, "column": 5}}], "line": 66}, "8": {"loc": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "type": "if", "locations": [{"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}], "line": 68}, "9": {"loc": {"start": {"line": 103, "column": 13}, "end": {"line": 103, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 13}, "end": {"line": 103, "column": 35}}, {"start": {"line": 103, "column": 39}, "end": {"line": 103, "column": 71}}], "line": 103}, "10": {"loc": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}, "type": "if", "locations": [{"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}, {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}], "line": 104}, "11": {"loc": {"start": {"line": 112, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 112, "column": 4}, "end": {"line": 123, "column": 5}}, {"start": {"line": 112, "column": 4}, "end": {"line": 123, "column": 5}}], "line": 112}, "12": {"loc": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 145}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 71}}, {"start": {"line": 115, "column": 75}, "end": {"line": 115, "column": 107}}, {"start": {"line": 115, "column": 111}, "end": {"line": 115, "column": 145}}], "line": 115}, "13": {"loc": {"start": {"line": 118, "column": 41}, "end": {"line": 118, "column": 223}}, "type": "cond-expr", "locations": [{"start": {"line": 118, "column": 145}, "end": {"line": 118, "column": 166}}, {"start": {"line": 118, "column": 169}, "end": {"line": 118, "column": 223}}], "line": 118}, "14": {"loc": {"start": {"line": 118, "column": 41}, "end": {"line": 118, "column": 142}}, "type": "binary-expr", "locations": [{"start": {"line": 118, "column": 41}, "end": {"line": 118, "column": 106}}, {"start": {"line": 118, "column": 110}, "end": {"line": 118, "column": 142}}], "line": 118}, "15": {"loc": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 146}}, "type": "binary-expr", "locations": [{"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 70}}, {"start": {"line": 128, "column": 74}, "end": {"line": 128, "column": 107}}, {"start": {"line": 128, "column": 111}, "end": {"line": 128, "column": 146}}], "line": 128}, "16": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 146}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 70}}, {"start": {"line": 129, "column": 74}, "end": {"line": 129, "column": 107}}, {"start": {"line": 129, "column": 111}, "end": {"line": 129, "column": 146}}], "line": 129}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 0, "9": 0, "10": 1, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 1, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 1, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 1, "55": 1, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 1, "79": 1, "80": 1, "81": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0, 0], "16": [0, 0, 0]}, "inputSourceMap": {"version": 3, "names": ["_i18n", "require", "_provider", "_stringWithParameterParser", "validI18nPrefix", "isI18nAttribute", "attributeName", "some", "prefix", "startsWith", "getI18nAttributeNameTarget", "i18nAttributeName", "i", "length", "slice", "observerCallbackForElement", "element", "animationFrame", "targets", "Set", "treeTargets", "elementNodeType", "ownerDocument", "defaultView", "Node", "ELEMENT_NODE", "mutations", "for<PERSON>ach", "mutation", "type", "addedNodes", "node", "nodeType", "add", "target", "size", "cancelAnimationFrame", "requestAnimationFrame", "updateI8nAttributes", "updateI8nAttributesOnTree", "clear", "customEvent", "CustomEvent", "dispatchEvent", "observerConfig", "attributes", "subtree", "childList", "targetNode", "attribute", "name", "key", "value", "trim", "parsed<PERSON><PERSON>", "parameters", "parse", "targetAttribute", "setAttribute", "translate", "root", "querySelectorAll", "initialObserverInfo", "mutationObserver", "providerObserver", "observersInfoMap", "WeakMap", "I18nContainerElement", "HTMLElement", "constructor", "set", "connectedCallback", "hasParentI18nContainerElement", "currentElement", "parentElement", "document", "body", "_observersInfo$provid", "observersInfo", "get", "unregister", "onProvide", "_observersInfo$mutati", "MutationObserver", "disconnect", "observe", "disconnectedCallback", "_observersInfo$mutati2", "_observersInfo$provid2", "isObservingChanges", "exports", "elementTagName", "customElements", "define"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/i18n-container/", "sources": ["i18n-container.element.js"], "sourcesContent": ["import { translate } from '~utils/i18n'\nimport './i18n-container.element.css'\nimport { onProvide } from '~utils/i18n/provider'\nimport { parse } from '~utils/i18n/string-with-parameter-parser.util'\n\nconst validI18nPrefix = ['i18n-', 'data-i18n-']\nconst isI18nAttribute = (attributeName) => validI18nPrefix.some(prefix => attributeName.startsWith(prefix))\nconst getI18nAttributeNameTarget = (i18nAttributeName) => {\n  for (let i = 0, { length } = validI18nPrefix; i < length; i++) {\n    const prefix = validI18nPrefix[i]\n    if (i18nAttributeName.startsWith(prefix)) {\n      return i18nAttributeName.slice(prefix.length)\n    }\n  }\n}\n\nconst observerCallbackForElement = (element) => {\n  let animationFrame\n  const targets = new Set()\n  const treeTargets = new Set()\n  const elementNodeType = element.ownerDocument.defaultView.Node.ELEMENT_NODE\n\n  return (mutations) => {\n    mutations.forEach(function (mutation) {\n      switch (mutation.type) {\n        case 'childList':\n          return mutation.addedNodes.forEach(node => node.nodeType === elementNodeType && treeTargets.add(node))\n        case 'attributes':\n          if (isI18nAttribute(mutation.attributeName) && mutation.target != null) {\n            return targets.add(mutation.target)\n          }\n      }\n    })\n\n    if (targets.size <= 0 && treeTargets.size <= 0) {\n      return\n    }\n\n    cancelAnimationFrame(animationFrame)\n    animationFrame = requestAnimationFrame(() => {\n      targets.forEach(updateI8nAttributes)\n      treeTargets.forEach(updateI8nAttributesOnTree)\n      targets.clear()\n      treeTargets.clear()\n      const customEvent = new CustomEvent('i18n-attributes-updated')\n      element.dispatchEvent(customEvent)\n    })\n  }\n}\n\nconst observerConfig = {\n  attributes: true,\n  subtree: true,\n  childList: true,\n}\n\nfunction updateI8nAttributes (targetNode) {\n  const { attributes } = targetNode\n  for (let i = 0, { length } = attributes; i < length; i++) {\n    const attribute = attributes[i]\n    if (isI18nAttribute(attribute.name)) {\n      const key = attribute.value.trim()\n      if (key === '') {\n        continue\n      }\n\n      const { key: parsedKey, parameters } = parse(key)\n\n      const targetAttribute = getI18nAttributeNameTarget(attribute.name)\n      targetNode.setAttribute(targetAttribute, translate(parsedKey, ...parameters))\n    }\n  }\n}\n\nfunction updateI8nAttributesOnTree (root) {\n  updateI8nAttributes(root)\n  for (const element of root.querySelectorAll('*')) {\n    updateI8nAttributes(element)\n  }\n}\n\nconst initialObserverInfo = {\n  /** @type MutationObserver */ mutationObserver: null,\n  /** @type I18nProviderObserver */ providerObserver: null,\n}\n\n/** @type WeakMap <I18nContainerElement, typeof initialObserverInfo> */\nconst observersInfoMap = new WeakMap()\n\nexport class I18nContainerElement extends HTMLElement {\n  constructor () {\n    super()\n    observersInfoMap.set(this, { ...initialObserverInfo })\n  }\n\n  connectedCallback () {\n    const hasParentI18nContainerElement = (() => {\n      let currentElement = this.parentElement\n      while (currentElement != null && currentElement !== document.body) {\n        if (currentElement instanceof I18nContainerElement) {\n          return true\n        }\n        currentElement = currentElement.parentElement\n      }\n      return false\n    })()\n    // this if condition prevent running updateI8nAttributesOnTree multiple times\n    if (!hasParentI18nContainerElement) {\n      const observersInfo = observersInfoMap.get(this)\n      observersInfo.providerObserver?.unregister()\n      observersInfo.providerObserver = onProvide(() => {\n        observersInfo.mutationObserver = observersInfo.mutationObserver ?? new MutationObserver(observerCallbackForElement(this))\n        observersInfo.mutationObserver.disconnect()\n        observersInfo.mutationObserver.observe(this, observerConfig)\n        updateI8nAttributesOnTree(this)\n      })\n    }\n  }\n\n  disconnectedCallback () {\n    const observersInfo = observersInfoMap.get(this)\n    observersInfo.mutationObserver?.disconnect()\n    observersInfo.providerObserver?.unregister()\n    observersInfo.providerObserver = null\n  }\n\n  get isObservingChanges () {\n    return observersInfoMap.get(this).providerObserver != null\n  }\n}\n\nexport const elementTagName = 'x-i18n-attr-container'\ncustomElements.define(elementTagName, I18nContainerElement)\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACAA,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEA,MAAMG,eAAe,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;AAC/C,MAAMC,eAAe,GAAIC,aAAa,IAAKF,eAAe,CAACG,IAAI,CAACC,MAAM,IAAIF,aAAa,CAACG,UAAU,CAACD,MAAM,CAAC,CAAC;AAC3G,MAAME,0BAA0B,GAAIC,iBAAiB,IAAK;EACxD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAE;MAAEC;IAAO,CAAC,GAAGT,eAAe,EAAEQ,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;IAC7D,MAAMJ,MAAM,GAAGJ,eAAe,CAACQ,CAAC,CAAC;IACjC,IAAID,iBAAiB,CAACF,UAAU,CAACD,MAAM,CAAC,EAAE;MACxC,OAAOG,iBAAiB,CAACG,KAAK,CAACN,MAAM,CAACK,MAAM,CAAC;IAC/C;EACF;AACF,CAAC;AAED,MAAME,0BAA0B,GAAIC,OAAO,IAAK;EAC9C,IAAIC,cAAc;EAClB,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;EAC7B,MAAME,eAAe,GAAGL,OAAO,CAACM,aAAa,CAACC,WAAW,CAACC,IAAI,CAACC,YAAY;EAE3E,OAAQC,SAAS,IAAK;IACpBA,SAAS,CAACC,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpC,QAAQA,QAAQ,CAACC,IAAI;QACnB,KAAK,WAAW;UACd,OAAOD,QAAQ,CAACE,UAAU,CAACH,OAAO,CAACI,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKX,eAAe,IAAID,WAAW,CAACa,GAAG,CAACF,IAAI,CAAC,CAAC;QACxG,KAAK,YAAY;UACf,IAAI1B,eAAe,CAACuB,QAAQ,CAACtB,aAAa,CAAC,IAAIsB,QAAQ,CAACM,MAAM,IAAI,IAAI,EAAE;YACtE,OAAOhB,OAAO,CAACe,GAAG,CAACL,QAAQ,CAACM,MAAM,CAAC;UACrC;MACJ;IACF,CAAC,CAAC;IAEF,IAAIhB,OAAO,CAACiB,IAAI,IAAI,CAAC,IAAIf,WAAW,CAACe,IAAI,IAAI,CAAC,EAAE;MAC9C;IACF;IAEAC,oBAAoB,CAACnB,cAAc,CAAC;IACpCA,cAAc,GAAGoB,qBAAqB,CAAC,MAAM;MAC3CnB,OAAO,CAACS,OAAO,CAACW,mBAAmB,CAAC;MACpClB,WAAW,CAACO,OAAO,CAACY,yBAAyB,CAAC;MAC9CrB,OAAO,CAACsB,KAAK,CAAC,CAAC;MACfpB,WAAW,CAACoB,KAAK,CAAC,CAAC;MACnB,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,yBAAyB,CAAC;MAC9D1B,OAAO,CAAC2B,aAAa,CAACF,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AAED,MAAMG,cAAc,GAAG;EACrBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE;AACb,CAAC;AAED,SAAST,mBAAmBA,CAAEU,UAAU,EAAE;EACxC,MAAM;IAAEH;EAAW,CAAC,GAAGG,UAAU;EACjC,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAE;MAAEC;IAAO,CAAC,GAAGgC,UAAU,EAAEjC,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;IACxD,MAAMqC,SAAS,GAAGJ,UAAU,CAACjC,CAAC,CAAC;IAC/B,IAAIP,eAAe,CAAC4C,SAAS,CAACC,IAAI,CAAC,EAAE;MACnC,MAAMC,GAAG,GAAGF,SAAS,CAACG,KAAK,CAACC,IAAI,CAAC,CAAC;MAClC,IAAIF,GAAG,KAAK,EAAE,EAAE;QACd;MACF;MAEA,MAAM;QAAEA,GAAG,EAAEG,SAAS;QAAEC;MAAW,CAAC,GAAG,IAAAC,gCAAK,EAACL,GAAG,CAAC;MAEjD,MAAMM,eAAe,GAAG/C,0BAA0B,CAACuC,SAAS,CAACC,IAAI,CAAC;MAClEF,UAAU,CAACU,YAAY,CAACD,eAAe,EAAE,IAAAE,eAAS,EAACL,SAAS,EAAE,GAAGC,UAAU,CAAC,CAAC;IAC/E;EACF;AACF;AAEA,SAAShB,yBAAyBA,CAAEqB,IAAI,EAAE;EACxCtB,mBAAmB,CAACsB,IAAI,CAAC;EACzB,KAAK,MAAM5C,OAAO,IAAI4C,IAAI,CAACC,gBAAgB,CAAC,GAAG,CAAC,EAAE;IAChDvB,mBAAmB,CAACtB,OAAO,CAAC;EAC9B;AACF;AAEA,MAAM8C,mBAAmB,GAAG;EAC1B,6BAA8BC,gBAAgB,EAAE,IAAI;EACpD,iCAAkCC,gBAAgB,EAAE;AACtD,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAE/B,MAAMC,oBAAoB,SAASC,WAAW,CAAC;EACpDC,WAAWA,CAAA,EAAI;IACb,KAAK,CAAC,CAAC;IACPJ,gBAAgB,CAACK,GAAG,CAAC,IAAI,EAAE;MAAE,GAAGR;IAAoB,CAAC,CAAC;EACxD;EAEAS,iBAAiBA,CAAA,EAAI;IACnB,MAAMC,6BAA6B,GAAG,CAAC,MAAM;MAC3C,IAAIC,cAAc,GAAG,IAAI,CAACC,aAAa;MACvC,OAAOD,cAAc,IAAI,IAAI,IAAIA,cAAc,KAAKE,QAAQ,CAACC,IAAI,EAAE;QACjE,IAAIH,cAAc,YAAYN,oBAAoB,EAAE;UAClD,OAAO,IAAI;QACb;QACAM,cAAc,GAAGA,cAAc,CAACC,aAAa;MAC/C;MACA,OAAO,KAAK;IACd,CAAC,EAAE,CAAC;IACJ;IACA,IAAI,CAACF,6BAA6B,EAAE;MAAA,IAAAK,qBAAA;MAClC,MAAMC,aAAa,GAAGb,gBAAgB,CAACc,GAAG,CAAC,IAAI,CAAC;MAChD,CAAAF,qBAAA,GAAAC,aAAa,CAACd,gBAAgB,cAAAa,qBAAA,eAA9BA,qBAAA,CAAgCG,UAAU,CAAC,CAAC;MAC5CF,aAAa,CAACd,gBAAgB,GAAG,IAAAiB,mBAAS,EAAC,MAAM;QAAA,IAAAC,qBAAA;QAC/CJ,aAAa,CAACf,gBAAgB,IAAAmB,qBAAA,GAAGJ,aAAa,CAACf,gBAAgB,cAAAmB,qBAAA,cAAAA,qBAAA,GAAI,IAAIC,gBAAgB,CAACpE,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACzH+D,aAAa,CAACf,gBAAgB,CAACqB,UAAU,CAAC,CAAC;QAC3CN,aAAa,CAACf,gBAAgB,CAACsB,OAAO,CAAC,IAAI,EAAEzC,cAAc,CAAC;QAC5DL,yBAAyB,CAAC,IAAI,CAAC;MACjC,CAAC,CAAC;IACJ;EACF;EAEA+C,oBAAoBA,CAAA,EAAI;IAAA,IAAAC,sBAAA,EAAAC,sBAAA;IACtB,MAAMV,aAAa,GAAGb,gBAAgB,CAACc,GAAG,CAAC,IAAI,CAAC;IAChD,CAAAQ,sBAAA,GAAAT,aAAa,CAACf,gBAAgB,cAAAwB,sBAAA,eAA9BA,sBAAA,CAAgCH,UAAU,CAAC,CAAC;IAC5C,CAAAI,sBAAA,GAAAV,aAAa,CAACd,gBAAgB,cAAAwB,sBAAA,eAA9BA,sBAAA,CAAgCR,UAAU,CAAC,CAAC;IAC5CF,aAAa,CAACd,gBAAgB,GAAG,IAAI;EACvC;EAEA,IAAIyB,kBAAkBA,CAAA,EAAI;IACxB,OAAOxB,gBAAgB,CAACc,GAAG,CAAC,IAAI,CAAC,CAACf,gBAAgB,IAAI,IAAI;EAC5D;AACF;AAAC0B,OAAA,CAAAvB,oBAAA,GAAAA,oBAAA;AAEM,MAAMwB,cAAc,GAAG,uBAAuB;AAAAD,OAAA,CAAAC,cAAA,GAAAA,cAAA;AACrDC,cAAc,CAACC,MAAM,CAACF,cAAc,EAAExB,oBAAoB,CAAC"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "ade19af7628d902ae10dc9fb341ca5d30d1b0c14", "contentHash": "65bd741709f6911a70e52295cdca07664acec0c6a8b6d3ac91c73b2f39a88e96"}, "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/selectbox/selectbox.element.js": {"path": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/selectbox/selectbox.element.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 5, "column": 3}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 52}}, "2": {"start": {"line": 7, "column": 17}, "end": {"line": 7, "column": 131}}, "3": {"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 34}}, "4": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 69}}, "5": {"start": {"line": 14, "column": 47}, "end": {"line": 14, "column": 67}}, "6": {"start": {"line": 15, "column": 18}, "end": {"line": 15, "column": 38}}, "7": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 30}}, "8": {"start": {"line": 17, "column": 23}, "end": {"line": 17, "column": 48}}, "9": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 40}, "end": {"line": 14, "column": 41}}, "loc": {"start": {"line": 14, "column": 47}, "end": {"line": 14, "column": 67}}, "line": 14}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {}, "inputSourceMap": {"version": 3, "names": ["_selectbox", "require", "_i18n", "fullSelectBoxClass", "i18n", "setTranslator", "key", "SelectBox", "exports", "elementTagName", "tagName"], "sourceRoot": "/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/web/src/web-components/selectbox/", "sources": ["selectbox.element.js"], "sourcesContent": ["/**\n * @file this file imports selectbox from NOWC and configures the i18n, as to not\n * repeat the required configuration in all imports for selectbox component\n */\n\nimport { SelectBox as fullSelectBoxClass, elementTagName as tagName } from '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/selectbox/selectbox.element'\nimport { i18n } from '~utils/i18n'\n\nfullSelectBoxClass.i18n.setTranslator((key) => i18n(key))\n\nexport const SelectBox = fullSelectBoxClass\nexport const elementTagName = tagName\n"], "mappings": ";;;;;;AAKA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AANA;AACA;AACA;AACA;;AAKAE,oBAAkB,CAACC,IAAI,CAACC,aAAa,CAAEC,GAAG,IAAK,IAAAF,UAAI,EAACE,GAAG,CAAC,CAAC;AAElD,MAAMC,SAAS,GAAGJ,oBAAkB;AAAAK,OAAA,CAAAD,SAAA,GAAAA,SAAA;AACpC,MAAME,cAAc,GAAGC,yBAAO;AAAAF,OAAA,CAAAC,cAAA,GAAAA,cAAA"}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a23ffa89f52ea05fd40af7bfd2c275f64e869b92", "contentHash": "002db8736256c1c51d84874690c78546a4d0e210e42a4118867001aab32cd0b5"}}