const path = require('node:path')
const webpack = require('webpack')
const fs = require('node:fs')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const configParts = require('./webpack.config.parts.js')
registerRequireContext()

const menuGroups = {
  api: { label: 'APIs' },
  baseComponents: { label: 'Base Components' },
  contributing: { label: 'Contributing' },
}

const menus = {
  modal: { label: 'Modal', group: menuGroups.api },
  tables: { label: 'Tables', group: menuGroups.baseComponents },
  notifications: { label: 'Notifications', group: menuGroups.api },
  scrolls: { label: 'Scrolls', group: menuGroups.baseComponents },
  breadcrumb: { label: 'Breadcrumb', group: menuGroups.baseComponents },
  i18n: { label: 'I18n', group: menuGroups.baseComponents },
  tags: { label: 'Tags', group: menuGroups.baseComponents },
  graph: { label: 'Graphs', group: menuGroups.baseComponents },
  resumeSection: { label: 'Resume section', group: menuGroups.baseComponents },
  about: { label: 'About', group: menuGroups.baseComponents },
  collapsible: { label: 'Collapsible', group: menuGroups.baseComponents },
  tscComponents: { label: 'TSC Components' },
  tscPages: { label: 'TSC Pages' },
  tscApp: { label: 'TSC App' },

  contributingDevelopment: { label: 'Development', group: menuGroups.contributing },
  contributingArchitecture: { label: 'Architecture', group: menuGroups.contributing }
}

const tscApp = {
  entry: './src/modules/tsc/app/demo/tsc-app.demo.js',
  chunk: 'tsc-app/tsc-app',
  htmlTemplate: './src/modules/tsc/app/demo/tsc-app.demo.html',
  htmlOutput: 'tsc-app.html',
  title: 'TSC Demo',
  navBarLabel: 'tsc-app',
  menu: menus.tscApp,
  dependencies: {
    Bootstrap: { logic: true, styles: true },
    Fuxi: { styles: true }
  }
}

const pages = {
  ...require.context('./src', true, /\.demo\.config\.js$/).keys().reduce((acc, key) => {
    acc[key] = require(key)({ menus })
    return acc
  }, {}),
  tscApp,

  // TSC pages
  // Version
  versionSearchPage: {
    entry: './src/modules/tsc/pages/version-search/demo/version-search-page.demo.js',
    chunk: 'tsc/version-search-page',
    htmlTemplate: './src/modules/tsc/pages/version-search/demo/version-search-page.demo.html',
    htmlOutput: 'tsc-version-search-page.html',
    title: 'Version search page Demo',
    navBarLabel: 'Version search page',
    menu: menus.tscPages
  },

  versionViewPage: {
    entry: './src/modules/tsc/pages/version-view/demo/version-view-page.demo.js',
    chunk: 'tsc/version-view-page',
    htmlTemplate: './src/modules/tsc/pages/version-view/demo/version-view-page.demo.html',
    htmlOutput: 'tsc-version-view-page.html',
    title: 'Version view page Demo',
    navBarLabel: 'Version view page',
    menu: menus.tscPages
  },

  versionEditPage: {
    entry: './src/modules/tsc/pages/version-edit/demo/version-edit-page.demo.js',
    chunk: 'tsc/version-edit-page',
    htmlTemplate: './src/modules/tsc/pages/version-edit/demo/version-edit-page.demo.html',
    htmlOutput: 'tsc-version-edit-page.html',
    title: 'Version edit page Demo',
    navBarLabel: 'Version edit page',
    menu: menus.tscPages
  },

  versionCreatePage: {
    entry: './src/modules/tsc/pages/version-create/demo/version-create-page.demo.js',
    chunk: 'tsc/version-create-page',
    htmlTemplate: './src/modules/tsc/pages/version-create/demo/version-create-page.demo.html',
    htmlOutput: 'tsc-version-create-page.html',
    title: 'Version create page Demo',
    navBarLabel: 'Version create page',
    menu: menus.tscPages
  },

  // CFS

  cfsSearchPage: {
    entry: './src/modules/tsc/pages/entity-search/demo/cfs-search-page.demo.js',
    chunk: 'tsc/cfs-search-page',
    htmlTemplate: './src/modules/tsc/pages/entity-search/demo/entity-search-page.demo.html',
    htmlOutput: 'tsc-cfs-search-page.html',
    title: 'CFS search page Demo',
    navBarLabel: 'CFS search page',
    menu: menus.tscPages
  },

  cfsEditPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/cfs-edit-page.demo.js',
    chunk: 'tsc/cfs-edit-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-cfs-edit-page.html',
    title: 'CFS edit page Demo',
    navBarLabel: 'CFS edit page',
    menu: menus.tscPages
  },

  cfsViewPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/cfs-view-page.demo.js',
    chunk: 'tsc/cfs-view-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-cfs-view-page.html',
    title: 'CFS view page Demo',
    navBarLabel: 'CFS view page',
    menu: menus.tscPages
  },

  cfsCreatePage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/cfs-create-page.demo.js',
    chunk: 'tsc/cfs-create-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-cfs-create-page.html',
    title: 'CFS create page Demo',
    navBarLabel: 'CFS create page',
    menu: menus.tscPages
  },

  // RFS

  rfsSearchPage: {
    entry: './src/modules/tsc/pages/entity-search/demo/rfs-search-page.demo.js',
    chunk: 'tsc/rfs-search-page',
    htmlTemplate: './src/modules/tsc/pages/entity-search/demo/entity-search-page.demo.html',
    htmlOutput: 'tsc-rfs-search-page.html',
    title: 'RFS search page Demo',
    navBarLabel: 'RFS search page',
    menu: menus.tscPages
  },

  rfsEditPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/rfs-edit-page.demo.js',
    chunk: 'tsc/rfs-edit-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-rfs-edit-page.html',
    title: 'RFS edit page Demo',
    navBarLabel: 'RFS edit page',
    menu: menus.tscPages
  },

  rfsViewPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/rfs-view-page.demo.js',
    chunk: 'tsc/rfs-view-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-rfs-view-page.html',
    title: 'RFS view page Demo',
    navBarLabel: 'RFS view page',
    menu: menus.tscPages
  },

  rfsCreatePage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/rfs-create-page.demo.js',
    chunk: 'tsc/rfs-create-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-rfs-create-page.html',
    title: 'RFS create page Demo',
    navBarLabel: 'RFS create page',
    menu: menus.tscPages
  },

  // RESOURCE

  resourceSearchPage: {
    entry: './src/modules/tsc/pages/entity-search/demo/resource-search-page.demo.js',
    chunk: 'tsc/resource-search-page',
    htmlTemplate: './src/modules/tsc/pages/entity-search/demo/entity-search-page.demo.html',
    htmlOutput: 'tsc-resource-search-page.html',
    title: 'RESOURCE search page Demo',
    navBarLabel: 'RESOURCE search page',
    menu: menus.tscPages
  },

  resourceEditPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/resource-edit-page.demo.js',
    chunk: 'tsc/resource-edit-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-resource-edit-page.html',
    title: 'RESOURCE edit page Demo',
    navBarLabel: 'RESOURCE edit page',
    menu: menus.tscPages
  },

  resourceViewPage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/resource-view-page.demo.js',
    chunk: 'tsc/resource-view-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-resource-view-page.html',
    title: 'RESOURCE view page Demo',
    navBarLabel: 'RESOURCE view page',
    menu: menus.tscPages
  },

  resourceCreatePage: {
    entry: './src/modules/tsc/pages/entity-pages/demo/resource-create-page.demo.js',
    chunk: 'tsc/resource-create-page',
    htmlTemplate: './src/modules/tsc/pages/entity-pages/demo/entity-page.demo.html',
    htmlOutput: 'tsc-resource-create-page.html',
    title: 'RESOURCE create page Demo',
    navBarLabel: 'RESOURCE create page',
    menu: menus.tscPages
  },

  // ATTRIBUTE

  attributeSearchPage: {
    entry: './src/modules/tsc/pages/attribute-search/demo/attribute-search-page.demo.js',
    chunk: 'tsc/attribute-search-page',
    htmlTemplate: './src/modules/tsc/pages/attribute-search/demo/attribute-search-page.demo.html',
    htmlOutput: 'tsc-attribute-search-page.html',
    title: 'ATTRIBUTE search page Demo',
    navBarLabel: 'ATTRIBUTE search page',
    menu: menus.tscPages
  },

  attributeEditPage: {
    entry: './src/modules/tsc/pages/attribute-pages/demo/attribute-edit-page.demo.js',
    chunk: 'tsc/attribute-edit-page',
    htmlTemplate: './src/modules/tsc/pages/attribute-pages/demo/attribute-page.demo.html',
    htmlOutput: 'tsc-attribute-edit-page.html',
    title: 'ATTRIBUTE edit page Demo',
    navBarLabel: 'ATTRIBUTE edit page',
    menu: menus.tscPages
  },

  attributeViewPage: {
    entry: './src/modules/tsc/pages/attribute-pages/demo/attribute-view-page.demo.js',
    chunk: 'tsc/attribute-view-page',
    htmlTemplate: './src/modules/tsc/pages/attribute-pages/demo/attribute-page.demo.html',
    htmlOutput: 'tsc-attribute-view-page.html',
    title: 'ATTRIBUTE view page Demo',
    navBarLabel: 'ATTRIBUTE view page',
    menu: menus.tscPages
  },

  attributeCreatePage: {
    entry: './src/modules/tsc/pages/attribute-pages/demo/attribute-create-page.demo.js',
    chunk: 'tsc/attribute-create-page',
    htmlTemplate: './src/modules/tsc/pages/attribute-pages/demo/attribute-page.demo.html',
    htmlOutput: 'tsc-attribute-create-page.html',
    title: 'ATTRIBUTE create page Demo',
    navBarLabel: 'ATTRIBUTE create page',
    menu: menus.tscPages
  },

  // TSC components

  rangeInput: {
    entry: './src/modules/tsc/components/range-input/demo/range-input.demo.js',
    chunk: 'tsc/range-input',
    htmlTemplate: './src/modules/tsc/components/range-input/demo/range-input.demo.html',
    htmlOutput: 'range-input.html',
    title: 'Range input Demo',
    navBarLabel: 'Range input',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  entityCreateTable: {
    entry: './src/modules/tsc/components/editable-attribute-table/demo/editable-attribute-table.demo.js',
    chunk: 'tsc/entity-attribute-table',
    htmlTemplate: './src/modules/tsc/components/editable-attribute-table/demo/editable-attribute-table.demo.html',
    htmlOutput: 'entity-attribute-table.html',
    title: 'Editable attributes table Demo',
    navBarLabel: 'Editable attributes table',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  enumeratorTable: {
    entry: './src/modules/tsc/components/enumerator-table/demo/enumerator-table.demo.js',
    chunk: 'tsc/enumerator-table',
    htmlTemplate: './src/modules/tsc/components/enumerator-table/demo/enumerator-table.demo.html',
    htmlOutput: 'enumerator-table.html',
    title: 'enumerator table Demo',
    navBarLabel: 'enumerator table',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  versionSelectBox: {
    entry: './src/modules/tsc/components/version-select-box/demo/version-select-box.demo.js',
    chunk: 'tsc/version-select-box',
    htmlTemplate: './src/modules/tsc/components/version-select-box/demo/version-select-box.demo.html',
    htmlOutput: 'version-select-box.html',
    title: 'Version Select Box Demo',
    navBarLabel: 'Version Select Box',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  compositionDiagramSidebar: {
    entry: './src/modules/tsc/components/composition-diagram-sidebar/demo/composition-diagram-sidebar.demo.js',
    chunk: 'tsc/composition-diagram-sidebar',
    htmlTemplate: './src/modules/tsc/components/composition-diagram-sidebar/demo/composition-diagram-sidebar.demo.html',
    htmlOutput: 'composition-diagram-sidebar.html',
    title: 'Composition Diagram Sidebar Demo',
    navBarLabel: 'Composition Diagram Sidebar',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  compositionDiagram: {
    entry: './src/modules/tsc/components/composition-diagram/demo/composition-diagram.demo.js',
    chunk: 'tsc/composition-diagram',
    htmlTemplate: './src/modules/tsc/components/composition-diagram/demo/composition-diagram.demo.html',
    htmlOutput: 'composition-diagram.html',
    title: 'Composition Diagram Demo',
    navBarLabel: 'Composition Diagram',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  editCompositionDiagramTab: {
    entry: './src/modules/tsc/components/composition-diagram-tab/demo/edit-composition-diagram-tab.demo.js',
    chunk: 'tsc/edit-composition-diagram-tab',
    htmlTemplate: './src/modules/tsc/components/composition-diagram-tab/demo/edit-composition-diagram-tab.demo.html',
    htmlOutput: 'edit-composition-diagram-tab.html',
    title: 'Edit Composition Diagram Tab Demo',
    navBarLabel: 'Edit Composition Diagram Tab',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  createCompositionDiagramTab: {
    entry: './src/modules/tsc/components/composition-diagram-tab/demo/create-composition-diagram-tab.demo.js',
    chunk: 'tsc/create-composition-diagram-tab',
    htmlTemplate: './src/modules/tsc/components/composition-diagram-tab/demo/create-composition-diagram-tab.demo.html',
    htmlOutput: 'create-composition-diagram-tab.html',
    title: 'Create Composition Diagram Tab Demo',
    navBarLabel: 'Create Composition Diagram Tab',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  viewCompositionDiagramTab: {
    entry: './src/modules/tsc/components/composition-diagram-tab/demo/view-composition-diagram-tab.demo.js',
    chunk: 'tsc/view-composition-diagram-tab',
    htmlTemplate: './src/modules/tsc/components/composition-diagram-tab/demo/view-composition-diagram-tab.demo.html',
    htmlOutput: 'view-composition-diagram-tab.html',
    title: 'View Composition Diagram Tab Demo',
    navBarLabel: 'View Composition Diagram Tab',
    menu: menus.tscComponents,
    dependencies: {
      Bootstrap: { styles: true },
      Fuxi: { styles: true }
    }
  },

  sidebarSearch: {
    entry: './src/modules/tsc/components/sidebar-search/demo/sidebar-search.demo.js',
    chunk: 'tsc/sidebar-search',
    htmlTemplate: './src/modules/tsc/components/sidebar-search/demo/sidebar-search.demo.html',
    htmlOutput: 'sidebar-search.html',
    title: 'search sidebar Demo',
    navBarLabel: 'Search sidebar',
    menu: menus.tscComponents,
    dependencies: {
      fuxi: { styles: true }
    }
  },

  navigationSidebar: {
    entry: './src/modules/tsc/components/navigation-sidebar/demo/navigation-sidebar.demo.js',
    chunk: 'tsc/navigation-sidebar',
    htmlTemplate: './src/modules/tsc/components/navigation-sidebar/demo/navigation-sidebar.demo.html',
    htmlOutput: 'navigation-sidebar.html',
    title: 'Navigation sidebar Demo',
    navBarLabel: 'Navigation sidebar',
    menu: menus.tscComponents,
    dependencies: {
      Fuxi: { styles: true }
    }
  },

  viewCharacteristicsTab: {
    entry: './src/modules/tsc/components/version-characteristics-tab/demo/version-characteristics-tab.demo.js',
    chunk: 'tsc/view-characteristics-tab',
    htmlTemplate: './src/modules/tsc/components/version-characteristics-tab/demo/version-characteristics-tab.demo.html',
    htmlOutput: 'tsc-view-characteristics-tab.html',
    title: 'View Characteristics Tab Demo',
    navBarLabel: 'View Characteristics Tab',
    menu: menus.tscComponents,
    dependencies: {
      Fuxi: { styles: true }
    }
  },

  versionTitle: {
    entry: './src/modules/tsc/components/version-title/demo/version-title.demo.js',
    chunk: 'tsc/version-title',
    htmlTemplate: './src/modules/tsc/components/version-title/demo/version-title.demo.html',
    htmlOutput: 'tsc-version-title.html',
    title: 'Version Title Demo',
    navBarLabel: 'Version Title',
    menu: menus.tscComponents,
  },

  // Common components

  modal: {
    entry: './src/utils/modal/demo/demo.js',
    chunk: 'modals/modal',
    htmlTemplate: './src/utils/modal/demo/demo.html',
    htmlOutput: 'modal.html',
    title: 'Modals Demo',
    navBarLabel: 'Modals',
    menu: menus.modal,
    dependencies: {
      Bootstrap: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },
  toastNotification: {
    entry: './src/utils/toast-notification/demo/demo.js',
    chunk: 'toast/toast',
    htmlTemplate: './src/utils/toast-notification/demo/demo.html',
    htmlOutput: 'toast-notification.html',
    title: 'Toast Notifications Demo',
    navBarLabel: 'Toast',
    menu: menus.notifications,
    dependencies: {
      Toastr: { logic: true, styles: true },
      Fuxi: { styles: true }
    }
  },
  'element-loading-notification': {
    entry: './src/utils/element-notification/demo/demo.js',
    chunk: 'el-notify/el-notify',
    htmlTemplate: './src/utils/element-notification/demo/element-notification-demo.html',
    htmlOutput: 'element-loading-notification.html',
    title: 'Element Notification Demo',
    menu: menus.notifications,
    navBarLabel: 'Element Block',
    dependencies: {
      Bootstrap: { styles: true }
    }
  },
  'page-loading-notification': {
    entry: './src/utils/page-notification/demo/demo.js',
    chunk: 'page-notify/page-notify',
    htmlTemplate: './src/utils/page-notification/demo/demo.html',
    htmlOutput: 'page-loading-notification.html',
    title: 'Page Notification Demo',
    navBarLabel: 'Page Block',
    menu: menus.notifications,
    dependencies: {
      Bootstrap: { logic: true, styles: true }
    }
  },

  breadcrumb: {
    entry: './src/web-components/breadcrumb/demo/breadcrumb-demo.js',
    chunk: 'breadcrumb/breadcrumb',
    htmlTemplate: './src/web-components/breadcrumb/demo/breadcrumb-demo.html',
    htmlOutput: 'breadcrumb.html',
    title: 'Breadcrumb Demo',
    navBarLabel: 'Breadcrumb',
    menu: menus.breadcrumb,
    dependencies: {
      fuxi: { styles: true }
    }

  },

  tags: {
    entry: './src/web-components/tag/demo/tag-demo.js',
    chunk: 'tags/tags',
    htmlTemplate: './src/web-components/tag/demo/tag-demo.html',
    htmlOutput: 'tag.html',
    title: 'Tags Demo',
    navBarLabel: 'Tags',
    menu: menus.tags,
    dependencies: {}

  },
}

Object.values(pages).forEach((page) => {
  page.menu.subOptions = page.menu.subOptions || []
  page.menu.subOptions.push(page)
})

module.exports = env => {
  const mode = getMode(env)
  const outputDir = typeof env.outputDir === 'string' ? env.outputDir : 'demo'

  return {
    mode,
    entry: Object.values(pages)
      .reduce((acc, page) => {
        acc[page.chunk] = [].concat(page.entry, './src/docs/demo-common.js')
        return acc
      }, {
        demo: './src/docs/demo-common.js',
        demoIndex: './src/docs/demo-index.js',
        demoLogin: './src/docs/demo-login.js',
      }),
    output: {
      path: path.resolve(__dirname, outputDir),
      filename: outputFilenameNameByMode[mode]
    },
    module: {
      rules: [
        ...configParts.module.rules,
        {
          test: { or: [/\.(element|template)\.html$/, /\/implementation\.html$/] },
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
              (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'raw-loader',
          }
        }, {
          test: /\.css$/,
          exclude: /\.(raw|inline)\.css$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true } }
          ]
        }, {
          test: /\.(raw|inline)\.css$/,
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
              (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'css-loader',
          }
        }, {
          test: /\.s[ac]ss$/,
          exclude: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            MiniCssExtractPlugin.loader,
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        },
        {
          test: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: /\.(woff|woff2|eot|ttf|png|jpg|jpeg|gif|wav|mp3|svg)(\?v=\d+\.\d+\.\d+)?$/,
          include: /node_modules/,
          type: 'asset/resource',
          generator: {
            filename: 'nossis-tsc-assets-vendors/vendors-assets/[hash][ext][query]'
          }
        }, {
          test: /\.(png|jpeg)$/,
          include: /src\/web-components/,
          type: 'asset/resource',
          generator: {
            filename: 'na-ext/[name].[ext]'
          }
        }, {
          test: /\.(pt|en|fr)$/,
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: 'raw-loader',
          }
        },
        {
          test: /\.adoc$/,
          exclude: /(node_modules|bower_components)/,
          use: [{
            loader: 'raw-loader',
          }, {
            loader: path.resolve(__dirname, 'webpack-adoc-loader.js'),
            options: {
              safe: 'safe',
              to_file: false,
              standalone: false,
              attributes: { 'data-uri': true, 'allow-uri-read': true }
            }
          }]
        }]
    },
    resolve: {
      ...configParts.resolve,
      alias: {
        ...configParts.resolve.alias,
        '~basemodule-play-conf': path.resolve(__dirname, '../play/basemodule/conf'),
        '~tsc-catalog-play-conf': path.resolve(__dirname, '../play/tsc-catalog/conf'),
        '~portal-play-conf': path.resolve(__dirname, '../play/portal/conf'),
      }
    },
    optimization: {
      ...configParts.optimization,
      splitChunks: {
        // include all types of chunks
        cacheGroups: {

          ...configParts.optimization.splitChunks.cacheGroups,

          // Demo specific vendors, used to identify the css and js origins

          'vendors-other': {
            test: /[\\/]node_modules[\\/]/,
            name: 'nossis-tsc-assets-vendors/vendors-other',
            chunks: 'all',
            priority: 1
          },

          'vendors-fontsource': {
            test: /[\\/]node_modules[\\/]@fontsource[\\/]/,
            name: 'nossis-tsc-assets-vendors/vendors-fontsource',
            chunks: 'all',
            priority: 20,
            enforce: true
          },

        }
      }
    },

    plugins: [
      new JsonModuleDependenciesGraphPlugin(),
      new MiniCssExtractPlugin(MiniCssExtractPluginOptionsByMode[mode]),

      new HtmlWebpackPlugin({
        inject: false,
        chunks: ['demoIndex'],
        filename: 'index.html',
        template: './src/docs/demo-index.html',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets
          const noFOUCStyle = '<style class="no-fouc-style">html { visibility: hidden; opacity: 0;} </style>'
          return {
            js: js.map(js => `<script defer src="${js}"></script>`).join('\n'),
            css: noFOUCStyle + orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            navbar: navBarHtml(''),
            title: 'Nossis TSC Demos'
          }
        }
      }),

      new HtmlWebpackPlugin({
        inject: false,
        chunks: ['demoLogin'],
        filename: 'auth-page.html',
        template: './src/docs/demo-login.html',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets
          const noFOUCStyle = '<style class="no-fouc-style">html { display: none; visibility: hidden; opacity: 0;} </style>'
          return {
            js: js.map(js => `<script defer src="${js}"></script>`).join('\n'),
            css: noFOUCStyle + orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            navbar: navBarHtml(''),
            title: 'Nossis TSC Demos'
          }
        }
      }),

      webPackHooksPlugin({
        afterRun: () => {
          const nowcInputDir = path.join(__dirname, 'node_modules/@alticelabsprojects/nossis-orchestration-web-components/dist/docs')
          const nowcOutputDir = path.join(__dirname, outputDir, 'nowc')
          if (!fs.existsSync(nowcOutputDir)) {
            if (mode === DEVELOPMENT) {
              // no need to constantly copy the docs contents if it is just for development,
              // just a symlink is enough
              fs.symlinkSync(nowcInputDir, nowcOutputDir)
            } else {
              fs.cpSync(nowcInputDir, nowcOutputDir, { recursive: true })
            }
          }
        }
      }),
      ...Object.values(pages).map(page => new HtmlWebpackPlugin({
        chunks: [page.chunk],
        inject: false,
        filename: page.htmlOutput,
        template: page.htmlTemplate,
        minify: false,
        chunksSortMode: 'manual',
        templateParameters: (compilation, assets) => {
          const { js, css } = assets
          const noFOUCStyle = '<style class="no-fouc-style">html { visibility: hidden; opacity: 0;} </style>'
          return {
            js: js.map(js => `<script src="${js}"></script>`).join('\n'),
            css: noFOUCStyle + orderedCss(css).map(css => `<link rel="stylesheet" href="${css}">`).join('\n'),
            navbar: navBarHtml(page.htmlOutput),
            title: page.title,
            dependencyTable: dependencyTable(page.dependencies)
          }
        }
      })),
      new webpack.DefinePlugin({
        setImmediate: 'requestAnimationFrame'
      }),
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        process: 'process',
        'window.jQuery': 'jquery',
        Buffer: ['buffer', 'Buffer'],
      })
    ].concat(additionPluginsByMode[mode]),

    performance: {
      hints: mode === PRODUCTION ? 'warning' : false,
      maxEntrypointSize: 1024000
    },
  }
}

// constants
const DEVELOPMENT = 'development'
const PRODUCTION = 'production'

const outputFilenameNameByMode = {
  [DEVELOPMENT]: '[name].js',
  [PRODUCTION]: '[name].[contenthash].min.js'
}

const MiniCssExtractPluginOptionsByMode = {
  [DEVELOPMENT]: {
    filename: '[name].css',
    chunkFilename: '[name].css'
  },
  [PRODUCTION]: {
    filename: '[name].[contenthash].css',
    chunkFilename: '[name].[contenthash].css'
  }
}

const additionPluginsByMode = {
  [DEVELOPMENT]: [],
  // CompressionPlugin is most likely not needed
  [PRODUCTION]: [new CompressionPlugin({ test: /\.js(\?.*)?$/i })]
}

/*
 * Guarantees that the result is either "development" or "production"
 */
function getMode({ NODE_ENV }) {
  return typeof NODE_ENV === 'string' && NODE_ENV.toLowerCase() === PRODUCTION ? PRODUCTION : DEVELOPMENT
}

function dependencyTable(dependencyMap = {}) {
  const okSignHtml = '<i class="glyphicon glyphicon-ok"></i>'
  const dependencies = Object.keys(dependencyMap).map(name => ({ name: name.replace(/^([a-z])/g, $1 => $1.toUpperCase()), ...dependencyMap[name] }))
  if (dependencies.length <= 0) {
    return `<table class="table fx-table">
            <thead><tr><th>Dependency</th><th>Logic</th><th>Style</th></tr></thead>
            <tbody><tr><th colspan="3">No dependencies</th></tr></tbody>
        </table>`
  }
  dependencies.sort((a, b) => a.name.localeCompare(b.name))
  const rows = dependencies.map(dependency => {
    return `<tr><th>${dependency.name}</th><th>${dependency.logic === true ? okSignHtml : ''}</th><th>${dependency.styles === true ? okSignHtml : ''}</th></tr>`
  }).join('')

  return `<table class="table fx-table">
            <thead><tr><th>Dependency</th><th>Logic</th><th>Style</th></tr></thead>
            <tbody>${rows}</tbody>
        </table>`
}

function orderedCss(cssList) {
  const demoCss = cssList.filter(css => css.includes('demo-assets/'))
  const vendorsCss = cssList.filter(css => css.includes('nossis-tsc-assets-vendors/'))
  const vendorFuxiCss = vendorsCss.filter(css => css.includes('nossis-tsc-assets-vendors/vendors-fuxi'))
  const otherVendorsCss = vendorsCss.filter(css => !vendorFuxiCss.includes(css))
  const otherCss = cssList.filter(css => !vendorsCss.includes(css) && !demoCss.includes(css))
  return [...demoCss, ...otherVendorsCss, ...vendorFuxiCss, ...otherCss]
}

function navBarHtml(activePage) {
  const options = Object.values(menus).filter(option => option.subOptions != null)

  const menuGroupsInfo = options.reduce((acc, option) => {
    if (option.group && option.group.label) {
      acc[option.group.label] = {
        active: false,
        htmlList: []
      }
    }
    acc[option.label] = {
      active: false,
      htmlList: []
    }
    return acc
  }, {})

  options.forEach((option) => {
    const { group, subOptions } = option
    const isActive = subOptions.some(suboption => suboption.htmlOutput === activePage)
    const isInGroup = group && group.label != null
    const groupLabel = isInGroup ? group.label : option.label
    const hasDropdown = subOptions.length > 1

    const subOptionTemplate = (subOption) => {
      const isActive = subOption.htmlOutput === activePage
      if (isActive) {
        return `<li class="active"><a>${subOption.navBarLabel}</a></li>`
      } else {
        return `<li><a href="${subOption.htmlOutput}">${subOption.navBarLabel}</a></li>`
      }
    }

    const dropdownClass = isInGroup ? 'dropdown-submenu' : 'dropdown'
    const caret = isInGroup ? '' : '<span class="caret"></span>'

    const optionHtml = (() => {
      if (hasDropdown && isActive) {
        return `<li class="active ${dropdownClass}">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${option.label}${caret}</a>
            <ul class="dropdown-menu">
                ${subOptions.map(subOptionTemplate).join('')}
            </ul>
            </li>`
      }
      if (hasDropdown && !isActive) {
        return `<li class="${dropdownClass}">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${option.label}${caret}</a>
            <ul class="dropdown-menu">
                ${subOptions.map(subOptionTemplate).join('')}
            </ul>
            </li>`
      }
      if (!hasDropdown && isActive) {
        return `<li class="active"><a>${option.label}</a></li>`
      }
      if (!hasDropdown && !isActive) {
        return `<li><a href="${subOptions[0].htmlOutput}">${option.label}</a></li>`
      }
      return ''
    })()

    const groupInfo = menuGroupsInfo[groupLabel]
    groupInfo.active = groupInfo.active || isActive
    groupInfo.htmlList.push(optionHtml)
  })

  const menuGroupsHtml = Object.entries(menuGroupsInfo).reduce((acc, [key, value]) => {
    const { htmlList, active } = value
    if (htmlList.length <= 0) {
      return acc
    } else if (htmlList.length === 1) {
      return acc + htmlList[0]
    } else {
      const activeClass = active ? 'active' : ''
      return acc + `<li class=" ${activeClass} dropdown">
            <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">${key}<span class="caret"></span></a>
            <ul class="dropdown-menu">
                ${htmlList.join('')}
            </ul>
            </li>`
    }
  }, '')

  return `<nav class="fx-main-nav fx-main-nav-fixed navbar-default">
    <div class="navbar-header">
        <button type="button" data-target="#navbarCollapse" data-toggle="collapse" class="navbar-toggle">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
        <a href="./" class="navbar-brand"></a>
        <a href="./" class="navbar-brand" style="width: auto; background: none"><span>Nossis TSC Demos</span></a>
    </div>
    <div id="navbarCollapse" class="collapse navbar-collapse fx-classic-menu">
        <ul class="nav navbar-nav">${menuGroupsHtml}</ul>
    </div>
    <ul class="nav navbar-nav navbar-right fx-navbar-secondary">
        <li><a href="./nowc/">To NOWC Documentation</a></li>
        <li class="nav-item">
            <form class="navbar-form form-inline">
                <label for="app-theme-select"><span class="nav-link">Theme:</span></label>
                <select id="app-theme-select" class="form-control app-theme-select"><option>Fuxi</option><option>Nossis One</option></select>
            </form>
        </li>
    </ul>
</nav>`
}

function registerRequireContext() {
  // require.context polyfill for NodeJS environment
// This condition actually should detect if it's an Node environment
  if (typeof require.context === 'undefined') {
    const fs = require('node:fs')
    const path = require('node:path')

    require.context = (base = '.', scanSubDirectories = false, regularExpression = /\.js$/) => {
      const files = {}

      function readDirectory (directory) {
        fs.readdirSync(directory).forEach((file) => {
          const fullPath = path.resolve(directory, file)
          if (fs.statSync(fullPath).isDirectory()) {
            if (scanSubDirectories) readDirectory(fullPath)
            return
          }
          if (!regularExpression.test(fullPath)) return
          files[fullPath] = true
        })
      }
      readDirectory(path.resolve(__dirname, base))
      function Module (file) {
        return require(file)
      }
      Module.keys = () => Object.keys(files)
      return Module
    }
  }
}

class JsonModuleDependenciesGraphPlugin {
  constructor(options = {}) {
    const defaultOptions = {
      outputFile: 'module-graph.json',
    }
    this.options = { ...defaultOptions, ...options }
    this.moduleDependencyGraph = {}
    this.cache = Symbol('cache')
  }

  apply (compiler) {
    const outputPath = compiler.options.output.path

    compiler.hooks.compilation.tap('JsonModuleDependenciesGraphPlugin', compilation => {
      compilation.hooks.finishModules.tap('JsonModuleDependenciesGraphPlugin', modules => {
        const moduleGraph = compilation.moduleGraph
        const { moduleDependencyGraph } = this
        if (moduleDependencyGraph[this.cache] == null) {
          moduleDependencyGraph[this.cache] = { nodes: new Set() }
        }
        const cache = moduleDependencyGraph[this.cache]
        const nodes = moduleDependencyGraph.nodes || []
        const edges = moduleDependencyGraph.edges || []

        modules.forEach(module => {
          const { resource, dependencies, blocks } = module

          if (!resource) {
            return
          }

          const moduleFilePath = path.relative(__dirname, resource)
          const moduleFileName = path.basename(resource)
          if (cache.nodes.has(moduleFilePath)) {
            return
          }
          cache.nodes.add(moduleFilePath)
          nodes.push({
            file: moduleFileName,
            path: moduleFilePath
          })

          if (resource.includes('/node_modules/')) {
            return
          }

          const addEdge = (dependency, async) => {
            const { request } = dependency
            const edge = {
              src: moduleFilePath,
              request,
              async
            }
            if (request == null || request.includes('/node_modules/css-loader/') || edgesCache.has(request)) {
              return
            }
            edgesCache.add(request)
            edges.push(edge)
            const dependencyConnection = moduleGraph.getConnection(dependency)
            const dependencyResource = dependencyConnection && dependencyConnection.module && dependencyConnection.module.resource
            if (dependencyResource != null) {
              edge.dest = path.relative(__dirname, dependencyResource)
            }
          }

          const edgesCache = new Set()
          dependencies.forEach(dependency => addEdge(dependency, false))
          blocks.flatMap(block => block.dependencies).forEach(dependency => addEdge(dependency, true))
        })
        moduleDependencyGraph.nodes = nodes
        moduleDependencyGraph.edges = edges
      })
    })

    compiler.hooks.afterEmit.tap('JsonModuleDependenciesGraphPlugin', () => {
      const { moduleDependencyGraph, options } = this
      const outputFilePath = path.resolve(outputPath, options.outputFile)
      const { nodes, edges } = moduleDependencyGraph
      const edgesDests = new Set(edges.map(({ dest }) => dest))
      const moduleNodes = nodes
        .filter(({ path }) => !path.includes('node_modules/') || edgesDests.has(path))
        .map((node, index) => ({ id: index, ...node }))
      const nodePathIdMap = Object.fromEntries(moduleNodes.map(node => [node.path, node.id]))
      const content = moduleNodes.map(node => ({
        ...node,
        dependsOn: edges.filter(({ src, async }) => !async && src === node.path).map(edge => nodePathIdMap[edge.dest]),
        dependsOnAsync: edges.filter(({ src, async }) => async && src === node.path).map(edge => nodePathIdMap[edge.dest])
      }))

      const outputFileContent = JSON.stringify(content)
      this.moduleDependencyGraph = {}
      fs.writeFile(outputFilePath, outputFileContent, (err) => {
        if (err) throw err
        console.log(`${outputFilePath} module graph generated`)
      })
    })
  }
}

/*
 * Small plugin to add hooks to webpack compilation process.
 * Used to execute the hooks every time the build is run during watch mode
 */
function webPackHooksPlugin({ beforeRun, compile, afterRun }) {
  return {
    apply: (compiler) => {
      beforeRun && compiler.hooks.beforeRun.tap('beforeRunPlugin', beforeRun)
      compile && compiler.hooks.compile.tap('compilePlugin', compile)
      afterRun && compiler.hooks.afterEmit.tap('AfterEmitPlugin', afterRun)
    }
  }
}
