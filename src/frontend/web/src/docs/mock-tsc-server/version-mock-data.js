export default [{
  id: '50678',
  idCatEntity: 7,
  catEntityName: 'Vers<PERSON>',
  name: 'SELFNET',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-12-02T01:10:55.994+0000',
  userCreate: 'system',
  code: 'Version_50678',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-12-02T01:10:55.991+0000',
  updatedAt: '2020-12-02T01:10:55.994+0000'
}, {
  id: '56082',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-FINAL20cf747722b842c09eba697d1f3dcf49',
  description: 'Continuous Integration - Version',
  state: 4,
  createdAt: '2020-11-02T02:14:45.734+0000',
  userCreate: 'system',
  code: 'Version_56082',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:14:45.763+0000',
  updatedAt: '2020-11-02T02:14:45.764+0000'
}, {
  id: '56004',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-SELFNET20cf747722b842c09eba697d1f3dcf49',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-11-02T02:14:44.989+0000',
  userCreate: 'system',
  code: 'Version_56004',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:14:44.987+0000',
  updatedAt: '2020-11-02T02:14:44.989+0000'
}, {
  id: '50763',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-delet-dis',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:10:57.545+0000',
  userCreate: 'system',
  code: 'Version_50763',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.543+0000',
  updatedAt: '2020-11-02T01:10:57.545+0000'
}, {
  id: '50761',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-test',
  description: 'Continuous Integration - Version',
  state: 4,
  createdAt: '2020-11-02T01:10:57.507+0000',
  userCreate: 'system',
  code: 'Version_50761',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.505+0000',
  updatedAt: '2020-11-02T01:10:57.507+0000'
}, {
  id: '55065',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-testcb85139253f74ef598466c7ead755d36',
  description: 'CI generated version',
  state: 1,
  createdAt: '2020-11-02T01:59:39.327+0000',
  userCreate: 'system',
  code: 'Version_55065',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:59:39.325+0000',
  updatedAt: '2020-11-02T01:59:39.327+0000'
}, {
  id: '55064',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.0-testf269cf213a054e328937ea3aefb9e357',
  description: 'CI generated version',
  state: 1,
  createdAt: '2020-11-02T01:59:18.051+0000',
  userCreate: 'system',
  code: 'Version_55064',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:59:18.048+0000',
  updatedAt: '2020-11-02T01:59:18.051+0000'
}, {
  id: '50760',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.1-test',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:10:57.486+0000',
  userCreate: 'system',
  code: 'Version_50760',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.484+0000',
  updatedAt: '2020-11-02T01:10:57.486+0000'
}, {
  id: '55066',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '1.0.1-testcb85139253f74ef598466c7ead755d36',
  description: 'CI generated version 2',
  state: 1,
  createdAt: '2020-11-02T01:59:46.075+0000',
  userCreate: 'system',
  code: 'Version_55066',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:59:46.072+0000',
  updatedAt: '2020-11-02T01:59:46.075+0000'
}, {
  id: '50759',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '2.0.0-test',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:10:57.470+0000',
  userCreate: 'system',
  code: 'Version_50759',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.468+0000',
  updatedAt: '2020-11-02T01:10:57.470+0000'
}, {
  id: '56083',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '3.0.0-production',
  description: 'Continuous Integration - Version',
  canClone: true,
  canDelete: false,
  canEdit: false,
  state: 4,
  createdAt: '2020-11-02T01:10:57.355+0000',
  userCreate: 'system',
  code: 'Version_50757',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.419+0000',
  updatedAt: '2020-11-02T01:10:57.421+0000'
}, {
  id: '50758',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: '3.0.0-testing',
  description: 'Continuous Integration - Version',
  canClone: true,
  canDelete: false,
  canEdit: false,
  state: 2,
  createdAt: '2020-11-02T01:10:57.438+0000',
  userCreate: 'system',
  code: 'Version_50758',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.454+0000',
  updatedAt: '2020-11-02T01:10:57.455+0000'
}, {
  id: '50762',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'Beta',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:10:57.528+0000',
  userCreate: 'system',
  code: 'Version_50762',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.526+0000',
  updatedAt: '2020-11-02T01:10:57.528+0000'
}, {
  id: '55428',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'Clone_1.0.0-SELFNET326827f0a38b4254b42623b53e73516f',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-11-02T02:04:26.301+0000',
  userCreate: 'system',
  code: 'Version_55428',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:04:14.786+0000',
  updatedAt: '2020-11-02T02:04:26.301+0000'
}, {
  id: '54536',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'Clone_1.0.0-SELFNETc975293e7c6e4108a5a4ef784f0f9a3b',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-11-02T01:36:54.507+0000',
  userCreate: 'system',
  code: 'Version_54536',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:36:37.117+0000',
  updatedAt: '2020-11-02T01:36:54.507+0000'
}, {
  id: '55506',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'Clone_Clone_1.0.0-SELFNET326827f0a38b4254b42623b53e73516f',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-11-02T02:04:28.946+0000',
  userCreate: 'system',
  code: 'Version_55506',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:04:14.786+0000',
  updatedAt: '2020-11-02T02:04:28.946+0000'
}, {
  id: '54379',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'Clone_SELFNET87da857f8c684000ab904d5ce86012cd',
  description: 'version SELFNET description',
  state: 1,
  createdAt: '2020-11-02T01:36:30.977+0000',
  userCreate: 'system',
  code: 'Version_54379',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:36:15.685+0000',
  updatedAt: '2020-11-02T01:36:30.977+0000'
}, {
  id: '50764',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'empty-values',
  description: null,
  state: 1,
  createdAt: '2020-11-02T01:10:57.562+0000',
  userCreate: 'system',
  code: 'Version_50764',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:10:57.560+0000',
  updatedAt: '2020-11-02T01:10:57.562+0000'
}, {
  id: '55270',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 're-edited version test8df40fc42001427681bb29c6f91cb8a0',
  description: 'everything ok',
  state: 1,
  createdAt: '2020-11-02T02:03:51.686+0000',
  userCreate: 'system',
  code: 'Version_55270',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:03:51.684+0000',
  updatedAt: '2020-11-02T02:04:11.856+0000'
}, {
  id: '55723',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'teste13443cc8128c44bda89f18971a02e6bac',
  description: 'Version check 1',
  state: 1,
  createdAt: '2020-11-02T02:06:57.388+0000',
  userCreate: 'system',
  code: 'Version_55723',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:06:57.386+0000',
  updatedAt: '2020-11-02T02:06:57.388+0000'
}, {
  id: '55724',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'teste23443cc8128c44bda89f18971a02e6bac',
  description: 'Version check 2',
  state: 1,
  createdAt: '2020-11-02T02:07:02.227+0000',
  userCreate: 'system',
  code: 'Version_55724',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T02:07:02.225+0000',
  updatedAt: '2020-11-02T02:07:02.227+0000'
}, {
  id: '53472',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 0 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.600+0000',
  userCreate: 'system',
  code: 'Version_53472',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.598+0000',
  updatedAt: '2020-11-02T01:35:26.600+0000'
}, {
  id: '53473',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 1 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.614+0000',
  userCreate: 'system',
  code: 'Version_53473',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.611+0000',
  updatedAt: '2020-11-02T01:35:26.614+0000'
}, {
  id: '53482',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 10 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.725+0000',
  userCreate: 'system',
  code: 'Version_53482',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.723+0000',
  updatedAt: '2020-11-02T01:35:26.725+0000'
}, {
  id: '53483',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 11 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.739+0000',
  userCreate: 'system',
  code: 'Version_53483',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.737+0000',
  updatedAt: '2020-11-02T01:35:26.739+0000'
}, {
  id: '53484',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 12 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.750+0000',
  userCreate: 'system',
  code: 'Version_53484',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.749+0000',
  updatedAt: '2020-11-02T01:35:26.750+0000'
}, {
  id: '53485',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 13 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.761+0000',
  userCreate: 'system',
  code: 'Version_53485',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.760+0000',
  updatedAt: '2020-11-02T01:35:26.761+0000'
}, {
  id: '53486',
  idCatEntity: 7,
  catEntityName: 'Versão',
  name: 'version pagination test - version 14 d3ebcfe7158949cbb38310d8d861539a',
  description: 'Continuous Integration - Version',
  state: 1,
  createdAt: '2020-11-02T01:35:26.771+0000',
  userCreate: 'system',
  code: 'Version_53486',
  externalCode: '',
  userUpdate: 'system',
  stateDate: '2020-11-02T01:35:26.769+0000',
  updatedAt: '2020-11-02T01:35:26.771+0000'
}
]
