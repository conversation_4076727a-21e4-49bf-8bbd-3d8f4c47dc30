import versions from './version-mock-data'

// eslint-disable-next-line camelcase
const version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49 = versions.find(({ name }) => name === '1.0.0-SELFNET20cf747722b842c09eba697d1f3dcf49')

export default [{
  id: '56090',
  catEntityName: 'Atributos',
  name: 'alert',
  description: 'Alert text',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56090',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56086',
  catEntityName: 'Atributos',
  name: 'appType',
  description: 'Application Type to deploy',
  typeId: 2004,
  typeName: 'enumerador',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56086',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56093',
  catEntityName: 'Atributos',
  name: 'creation date',
  description: 'creation date (date test)',
  typeId: 2000,
  typeName: 'data',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56093',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56087',
  catEntityName: 'Atributos',
  name: 'host',
  description: 'host',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56087',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56092',
  catEntityName: 'Atributos',
  name: 'id',
  description: 'Id',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56092',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56084',
  catEntityName: 'Atributos',
  name: 'instanceName',
  description: 'instance name',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56084',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56088',
  catEntityName: 'Atributos',
  name: 'ipAddress',
  description: 'IPv4 or IPv6 Address',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56088',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56091',
  catEntityName: 'Atributos',
  name: 'location',
  description: 'deployment location',
  typeId: 2004,
  typeName: 'enumerador',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56091',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56085',
  catEntityName: 'Atributos',
  name: 'port',
  description: 'TCP/UDP Port',
  typeId: 2002,
  typeName: 'numérico',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56085',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56089',
  catEntityName: 'Atributos',
  name: 'size',
  description: 'payload size',
  typeId: 2003,
  typeName: 'float',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56089',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '56094',
  catEntityName: 'Atributos',
  name: 'uri',
  description: 'Uniform Resource Identifier',
  typeId: 2001,
  typeName: 'string',
  typeCatId: 9,
  tags: 'null',
  idCatEntity: 8,
  createdAt: '2020-11-06T02:27:05.259+0000',
  userCreate: 'system',
  code: 'AggregatedType_56094',
  externalCode: '',
  userUpdate: 'system',
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  updatedAt: '2020-11-06T02:27:05.259+0000'
}, {
  id: '50777',
  catEntityName: 'Atributos',
  name: 'attributeTypeDate',
  description: 'Continuous Integration - Attribute Type',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: '',
  typeId: 'data',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:17.329+0000',
  userCreate: 'system',
  code: 'AggregatedType_50777',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:17.329+0000'
}, {
  id: '50776',
  catEntityName: 'Atributos',
  name: 'attributeTypeNumber',
  description: 'Continuous Integration - Attribute Type',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: '',
  typeId: 'numérico',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:17.261+0000',
  userCreate: 'system',
  code: 'AggregatedType_50776',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:17.261+0000'
}, {
  id: '50771',
  catEntityName: 'Atributos',
  name: 'attributeWithoutTags',
  description: 'CI attribute without tags',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: '',
  typeId: 'string',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:16.979+0000',
  userCreate: 'system',
  code: 'AggregatedType_50771',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:16.979+0000'
}, {
  id: '50773',
  catEntityName: 'Atributos',
  name: 'attributeWithTagA',
  description: 'CI attribute with TagA',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: 'TagA',
  typeId: 'string',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:17.100+0000',
  userCreate: 'system',
  code: 'AggregatedType_50773',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:17.100+0000'
}, {
  id: '50774',
  catEntityName: 'Atributos',
  name: 'attributeWithTagB',
  description: 'CI attribute with TagB',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: 'TagB',
  typeId: 'string',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:17.152+0000',
  userCreate: 'system',
  code: 'AggregatedType_50774',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:17.152+0000'
}, {
  id: '50775',
  catEntityName: 'Atributos',
  name: 'attributeWithTags',
  description: 'CI attribute with many tags',
  clonePermission: true,
  deletePermission: true,
  editPermission: true,
  canClone: true,
  canDelete: true,
  canEdit: true,
  version: {
    id: '50761',
    idCatEntity: 7,
    catEntityName: null,
    name: '1.0.0-test',
    description: 'Continuous Integration - Version',
    state: 1,
    createdAt: '2020-11-10T01:12:16.358+0000',
    userCreate: 'system',
    code: 'Version_50761',
    externalCode: '',
    userUpdate: 'system',
    stateDate: '2020-11-10T01:12:16.349+0000',
    updatedAt: '2020-11-10T01:12:16.358+0000'
  },
  simpleType: true,
  typeCatId: 9,
  tags: 'TagA,TagC',
  typeId: 'string',
  idCatEntity: 8,
  createdAt: '2020-11-10T01:12:17.218+0000',
  userCreate: 'system',
  code: 'AggregatedType_50775',
  externalCode: '',
  userUpdate: 'system',
  updatedAt: '2020-11-10T01:12:17.218+0000'
}]
