/* eslint-disable @typescript-eslint/naming-convention */
import versions, { versionDto } from './versions'
import { attributeDto } from './attributes'
import { tags } from './tags'

const version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49 = versions.find(({ name }) => name === '1.0.0-SELFNET20cf747722b842c09eba697d1f3dcf49').id
const version__1_0_0_test = versions.find(({ name }) => name === '1.0.0-test').id
const version__SELFNET = versions.find(({ name }) => name === 'SELFNET').id
const version__2_0_0_test = versions.find(({ name }) => name === '2.0.0-test').id

export type attributeTypeDto = AttributeTypeDto | string

export interface AttributeTypeDto {
  id: string
  entityType: string
  name: string
  description: string
  tags: tags
  primitiveType?: string
  constraints?: ConstraintDto[]
  attributes?: attributeDto[]
  version: versionDto
}

export interface ConstraintDto {
  id: string
  entityType: string
  name: string
  description: string
  tags: tags
  type: string
  minValue?: number
  maxValue?: number
  regexp?: string
  valueList?: Array<{ value: string, position: number }>
}

const data: AttributeTypeDto[] = [{
  id: '56090',
  entityType: 'ATTRTYPE',
  name: 'alert',
  description: 'Alert text',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56086',
  entityType: 'ATTRTYPE',
  name: 'appType',
  description: 'Application Type to deploy',
  primitiveType: '2004',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56093',
  entityType: 'ATTRTYPE',
  name: 'creation date',
  description: 'creation date (date test)',
  primitiveType: '2000',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56087',
  entityType: 'ATTRTYPE',
  name: 'host',
  description: 'host',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56092',
  entityType: 'ATTRTYPE',
  name: 'id',
  description: 'Id',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56084',
  entityType: 'ATTRTYPE',
  name: 'instanceName',
  description: 'instance name',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56088',
  entityType: 'ATTRTYPE',
  name: 'ipAddress',
  description: 'IPv4 or IPv6 Address',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [{
    id: '40691',
    tags: [],
    entityType: 'CONSTRAINT',
    regexp: '.*',
    name: '3c6c20e5-45ba-405a-8a38-3a6385228e76',
    description: 'IPv4 or IPv6 Address Validator',
    type: 'REGEXP',
  }],
}, {
  id: '56091',
  entityType: 'ATTRTYPE',
  name: 'location',
  description: 'deployment location',
  primitiveType: '2004',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56085',
  entityType: 'ATTRTYPE',
  name: 'port',
  description: 'TCP/UDP Port',
  primitiveType: '2002',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56089',
  entityType: 'ATTRTYPE',
  name: 'size',
  description: 'payload size',
  primitiveType: '2003',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '56094',
  entityType: 'ATTRTYPE',
  name: 'uri',
  description: 'Uniform Resource Identifier',
  primitiveType: '2001',
  tags: [],
  version: version__1_0_0_SELFNET20cf747722b842c09eba697d1f3dcf49,
  constraints: [],
}, {
  id: '50777',
  entityType: 'ATTRTYPE',
  name: 'attributeTypeDate',
  description: 'Continuous Integration - Attribute Type',
  version: version__1_0_0_test,
  tags: [],
  primitiveType: '2000',
  constraints: [],
}, {
  id: '50776',
  entityType: 'ATTRTYPE',
  name: 'attributeTypeNumber',
  description: 'Continuous Integration - Attribute Type',
  version: version__1_0_0_test,
  tags: [],
  primitiveType: '2002',
  constraints: [],
}, {
  id: '50771',
  entityType: 'ATTRTYPE',
  name: 'attributeWithoutTags',
  description: 'CI attribute without tags',
  version: version__1_0_0_test,
  tags: [],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50773',
  entityType: 'ATTRTYPE',
  name: 'attributeWithTagA',
  description: 'CI attribute with TagA',
  version: version__1_0_0_test,
  tags: ['TagA'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50774',
  entityType: 'ATTRTYPE',
  name: 'attributeWithTagB',
  description: 'CI attribute with TagB',
  version: version__1_0_0_test,
  tags: ['TagB'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50775',
  entityType: 'ATTRTYPE',
  name: 'attributeWithTags',
  description: 'CI attribute with many tags',
  version: version__1_0_0_test,
  tags: ['TagA', 'TagC'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50681',
  entityType: 'ATTRTYPE',
  name: 'alert',
  description: 'Alert text',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50685',
  entityType: 'ATTRTYPE',
  name: 'appType',
  description: 'Application Type to deploy',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2004',
  constraints: [{
    name: 'e513ef26-0af1-4dc5-9cda-2ba0b2aa6b8f',
    description: null,
    entityType: 'CONSTRAINT',
    tags: [],
    id: '50686',
    type: 'ENUMC',
    valueList: [{
      position: 2,
      value: 'BOTNET',
    }, {
      position: 1,
      value: 'DPI',
    }]
  }],
}, {
  id: '50697',
  entityType: 'ATTRTYPE',
  name: 'creation date',
  description: 'creation date (date test)',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2000',
  constraints: [],
}, {
  id: '50689',
  entityType: 'ATTRTYPE',
  name: 'host',
  description: 'host',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50696',
  entityType: 'ATTRTYPE',
  name: 'id',
  description: 'Id',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50682',
  entityType: 'ATTRTYPE',
  name: 'instanceName',
  description: 'instance name',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '50690',
  entityType: 'ATTRTYPE',
  name: 'ipAddress',
  description: 'IPv4 or IPv6 Address',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [{
    entityType: 'CONSTRAINT',
    tags: [],
    regexp: '.*',
    name: '3c6c20e5-45ba-405a-8a38-3a6385228e76',
    description: 'IPv4 or IPv6 Address Validator',
    id: '50691',
    type: 'REGEXP',
  }],
}, {
  id: '50692',
  entityType: 'ATTRTYPE',
  name: 'location',
  description: 'deployment location',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2004',
  constraints: [],
}, {
  id: '50683',
  entityType: 'ATTRTYPE',
  name: 'port',
  description: 'TCP/UDP Port',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2002',
  constraints: [{
    id: '123',
    tags: [],
    entityType: 'CONSTRAINT.RANGECONSTRAINT',
    name: '0b36e69f-9eb7-4baa-bce1-03047d520b00',
    description: 'Port Range',
    type: 'RANGE',
    minValue: 0,
    maxValue: 65535
  }],
}, {
  id: '50680',
  entityType: 'ATTRTYPE',
  name: 'size',
  description: 'payload size',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2003',
  constraints: [{
    entityType: 'CONSTRAINT',
    tags: [],
    description: 'Port Range',
    minValue: 65535,
    type: 'RANGE',
    name: 'ffc20351-f078-4e6c-971e-e0b565d5814e',
    id: '50684',
    maxValue: 0,
  }],
}, {
  id: '50698',
  entityType: 'ATTRTYPE',
  name: 'uri',
  description: 'Uniform Resource Identifier',
  version: version__SELFNET,
  tags: ['selfnet'],
  primitiveType: '2001',
  constraints: [],
}, {
  id: '57453',
  entityType: 'ATTRTYPE',
  name: 'attr1',
  description: 'string attribute with logic constraint',
  version: version__2_0_0_test,
  tags: ['String', 'Logic'],
  primitiveType: '2001',
  constraints: [
    {
      type: 'SIZE',
      tags: [],
      id: '57454',
      entityType: null,
      name: 'Dimensão',
      description: null,
      minValue: 1,
      maxValue: 2,
    }
  ],
}, {
  id: '57456',
  entityType: 'ATTRTYPE',
  name: 'attr2',
  description: 'String Attribute with Regex Constraint',
  version: version__2_0_0_test,
  tags: ['String', 'Regex'],
  primitiveType: '2001',
  constraints: [
    {
      tags: [],
      type: 'REGEXP',
      id: '57457',
      entityType: null,
      name: 'Expressão regular',
      description: null,
      regexp: '[a-zA-Z]*',
    }
  ],
}, {
  id: '57459',
  entityType: 'ATTRTYPE',
  name: 'attr',
  description: 'Data Attribute',
  version: version__2_0_0_test,
  tags: ['Data'],
  primitiveType: '2000',
  constraints: [],
}, {
  id: '57461',
  entityType: 'ATTRTYPE',
  name: 'attr3',
  description: 'Numeric Attribute with Logic Constraint',
  version: version__2_0_0_test,
  tags: ['Numeric', 'Logic'],
  primitiveType: '2002',
  constraints: [
    {
      tags: [],
      type: 'RANGE',
      id: '57462',
      entityType: null,
      name: 'Intervalo',
      description: null,
      minValue: 0,
      maxValue: 8196,
    }
  ],
}, {
  id: '57464',
  entityType: 'ATTRTYPE',
  name: 'attr4',
  description: 'Float Attribute with Logic Constraint',
  version: version__2_0_0_test,
  tags: ['Float', 'Logic'],
  primitiveType: '2003',
  constraints: [
    {
      tags: [],
      type: 'FLOAT_RANGE',
      id: '57465',
      entityType: null,
      name: 'Float',
      description: null,
      minValue: 1.1,
      maxValue: 9,
    }
  ],
}, {
  id: '57467',
  entityType: 'ATTRTYPE',
  name: 'attr5',
  description: 'Enumerator Attribute',
  version: version__2_0_0_test,
  tags: ['Enumerator'],
  primitiveType: '2004',
  constraints: [
    {
      tags: [],
      type: 'ENUMC',
      id: '57468',
      entityType: null,
      name: 'Enumerado',
      description: null,
      valueList: [
        {
          value: 'constraint2',
          position: 2,
        },
        {
          value: 'constraint3',
          position: 1,
        },
        {
          value: 'constraint',
          position: 3,
        }
      ],
    }
  ],
}, {
  id: '57473',
  entityType: 'COMPONENT',
  name: 'attr6',
  description: 'Component Attribute',
  version: version__2_0_0_test,
  tags: ['Component'],
  attributes: [
    '51726',
    '51727',
    '51728',
  ],
  primitiveType: '2005',
}
]

export default data
