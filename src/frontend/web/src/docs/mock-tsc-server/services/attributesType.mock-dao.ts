import attributeTypes, { attributeTypeDto, AttributeTypeDto } from '../data/attribute-types'

import tableQuery from './table-query.util'
import { addAttribute, clearAttributes, preparedAttribute } from './attribute.mock-dao'
import { idGenerator } from './dao-services.utils'
import { joinVersion } from './version.mock-dao'
import { updateTags } from './tags.mock-dao'

const joinedMockData = () => attributeTypes.map(preparedAttributeType)

const generator = idGenerator(attributeTypes)

export function preparedAttributeType (attributeType: attributeTypeDto): AttributeTypeDto {
  if (typeof attributeType === 'string') {
    attributeType = attributeTypes.find(({ id }) => id === attributeType)
  }

  const newAttributeType = { ...attributeType }

  newAttributeType.version = joinVersion(newAttributeType.version)

  if (newAttributeType.entityType === 'COMPONENT') {
    newAttributeType.attributes = newAttributeType.attributes.map(preparedAttribute)
  }

  return newAttributeType
}

export function tableQueryAttributeTypes ({ requestData, onSuccess }) {
  tableQuery({
    mockData: joinedMockData(),
    parameterMap: {
      version: (data) => data.version.name
    },
    requestData,
    onSuccess,
  })
}

export function getAttributeTypesByVersion (versionId: string): AttributeTypeDto[] {
  return [...attributeTypes].filter(attributeType => attributeType.version === versionId).map(preparedAttributeType)
}

export function getAttributeTypeByIdOrNull (attributeId): AttributeTypeDto | null {
  const attribute = [...attributeTypes].find(({ id }) => id === attributeId)
  if (!attribute) {
    return null
  }
  return preparedAttributeType(attribute)
}

export function getAttributeTypeByNameOrNull (attributeName: string, versionId: string): AttributeTypeDto | null {
  const attribute = [...attributeTypes].find(({ name, version }) => name === attributeName && version === versionId)
  if (!attribute) {
    return null
  }
  return preparedAttributeType(attribute)
}

export function deleteAttributeType (attributeId) {
  const attributeIndex = attributeTypes.findIndex(({ id }) => id === attributeId)

  if (attributeIndex < 0) {
    return false
  }

  clearAttributes(attributeTypes[attributeIndex].attributes)

  attributeTypes.splice(attributeIndex, 1)
}

export function updateAttributeType (attributeType: AttributeTypeDto, attributeId: string): boolean {
  const persistedAttribute = attributeTypes.find(({ id }) => id === attributeId)

  if (!persistedAttribute) {
    return false
  }

  if (attributeType.entityType === 'COMPONENT') {
    clearAttributes(persistedAttribute.attributes)
    attributeType.attributes = attributeType.attributes.map(attribute => addAttribute(attribute))
  }

  updateTags(attributeType.tags)

  attributeType.id = persistedAttribute.id
  Object.assign(persistedAttribute, attributeType)

  return true
}

export function addNewAttributeType (attributeType: AttributeTypeDto): string {
  attributeType.id = generator.next().value
  if (attributeType.entityType === 'COMPONENT') {
    attributeType.attributes = attributeType.attributes.map(attribute => addAttribute(attribute))
  }

  updateTags(attributeType.tags)
  attributeTypes.push(attributeType)
  return attributeType.id
}
