<div class="doc-demo doc-demo--user-info container--user-info pull-right">
  <button class="btn btn-sm btn-default button--show-modal"> User info </button>
  <div tabindex="2" class="modal--user-info">
    <div class="doc-demo__authenticated">
      <div class="btn-group" data-toggle="buttons">
      <label class="auth__label btn btn-default active" for="user-authenticated">
        <input class="permission__checkbox" type="checkbox" checked id="user-authenticated">
        Authenticated
      </label>
      </div>
    </div>
    <!-- in nossis tsc frontend, we are not dealing with user name, only in nossis ui portal, this stays here to remind
    <div class="doc-demo__username">
      <label>
        Username
        <input type="text">
      </label>
    </div>
    -->
    <label>
      Permissions
    </label>
    <div class="doc-demo__permissions">
      <!-- dynamically add user permissions -->
    </div>
    <span>
      <label>Languages</label>
      <span class="doc-demo__i18n-langs">
        <!-- dynamically add languages -->
      </span>

    </span>
    <div class="doc-demo__i18n-timezone">
      <!-- in nossis tsc frontend we are not dealing with timezones yet, this stays here to remind -->
    </div>

  </div>
</div>