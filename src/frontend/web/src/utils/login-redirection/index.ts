import { instance, sessionHandlingOptions } from './provider'

export const redirectOnNextLogin = (enable: boolean = true): void => instance.redirectOnNextLogin(enable)
export const setLoginRedirectUrl = (url: string): void => instance.setLoginRedirectUrl(url)
export const shouldRedirectOnLogin = (): boolean => instance.shouldRedirectOnLogin()
export const applyLoginRedirection = (): void => instance.applyLoginRedirection()
export const handleSessionExpired = ({ redirectOnNextLogin }: sessionHandlingOptions) => instance.handleSessionExpired({ redirectOnNextLogin })
export const handleSessionAbsent = ({ redirectOnNextLogin }: sessionHandlingOptions) => instance.handleSessionAbsent({ redirectOnNextLogin })
