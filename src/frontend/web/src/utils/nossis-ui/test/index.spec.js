import { nextNossisUiUrlHash } from '../index'
import tape from 'tape'

/**
 * Generates an hash that is in accordance with nossis ui url pattern:
 *     ${url.origin}/portal#ref=${rng values}&target=${iframe url}
 *
 * @param {String} iframeUrl
 */
const nossisUiHashForIframeUrl = (iframeUrl) => `#ref=123&target=${iframeUrl}`

tape.test('Nossis UI util - nextNossisUiUrlHash - relative url wil update nossis hash with new iframe url', (t) => {
  // prepare
  const previousHash = nossisUiHashForIframeUrl('/nossis-tsc/dashboard?SessionId=456&Lang=pt#cfs')
  const portalOrigin = 'http://localhost'
  const iframeUrl = '/nossis-tsc/dashboard?SessionId=456&Lang=pt#rfs'
  t.plan(1)

  // act
  const nextHash = nextNossisUiUrlHash(previousHash, { iframeUrl, portalOrigin })

  // assert
  t.isEqual(nextHash, nossisUiHashForIframeUrl(iframeUrl))
})

tape.test('Nossis UI util - nextNossisUiUrlHash  - absolute url with same hash and origin returns the previous value', (t) => {
  // prepare
  const previousHash = nossisUiHashForIframeUrl('/nossis-tsc/dashboard?SessionId=456&Lang=pt#cfs')
  const portalOrigin = 'http://localhost'
  const iframeUrl = 'http://localhost/nossis-tsc/dashboard?SessionId=456&Lang=pt#cfs'
  t.plan(1)

  // act
  const nextHash = nextNossisUiUrlHash(previousHash, { iframeUrl, portalOrigin })

  // assert
  t.isEqual(nextHash, previousHash)
})

tape.test('Nossis UI util - nextNossisUiUrlHash  - absolute url with same origin will have the origin stripped', (t) => {
  // prepare
  const previousHash = nossisUiHashForIframeUrl('/nossis-tsc/dashboard?SessionId=456&Lang=pt#cfs')
  const portalOrigin = 'http://localhost'
  const iframeUrl = 'http://localhost/nossis-tsc/dashboard?SessionId=456&Lang=pt#rfs'
  t.plan(1)

  // act
  const nextHash = nextNossisUiUrlHash(previousHash, { iframeUrl, portalOrigin })

  // assert
  t.isEqual(nextHash, nossisUiHashForIframeUrl('/nossis-tsc/dashboard?SessionId=456&Lang=pt#rfs'))
})
