import { pageDataGetter } from './notifies-loaded-page-connected.mixin'
/**
 *
 * @param {typeof HTMLElement} elementClass
 * @param {string} initializeEventName
 *
 * @return {function(HTMLElement): ({new(): loadsPageWithTablesMixin, prototype: loadsPageWithTablesMixin}|*)}
 */
export function createLoadsPageWithAsyncLoadElementMixin ({ elementClass, initializeEventName }) {
  const loadsPageWithElementMixinSymbol = Symbol(`loads page with Async Load Element, ${elementClass.name}`)

  /**
   * @param {typeof HTMLElement} superClass
   * @return {{new(): loadsPageWithTablesMixin, prototype: loadsPageWithTablesMixin}|*}
   */
  function loadsPageWithElement(superClass) {
    if (loadsPageWithElement.hasMixin(superClass)) {
      return superClass
    }

    const elementData = new WeakMap()

    return class loadsPageWithTablesMixin extends superClass {
      constructor () {
        super()
        const data = {}
        data.currentInits = 0
        data.toResolve = false
        data.setMaxInits = (maxInits) => {
          data.maxInits = maxInits
          if (data.currentInits >= data.maxInits) {
            data.promiseResolve?.(true)
            data.toResolve = true
          }
        }
        data.incrementInits = () => {
          data.currentInits++
          if (data.maxInits != null && data.currentInits >= data.maxInits) {
            data.promiseResolve?.(true)
            data.toResolve = true
          }
        }
        data.pageLoadPromise = new Promise(resolve => {
          data.promiseResolve = resolve
          data.toResolve && resolve(true)
        })
        elementData.set(this, data)
        this.addEventListener(initializeEventName, (event) => {
          if (event.target instanceof elementClass) {
            data.incrementInits()
          }
        })
      }

      static get [loadsPageWithElementMixinSymbol]() {
        return true
      }

      // this mixin integrates with ./notifies-loaded-page-connected.mixin.js
      [pageDataGetter]() {
        return Promise.all([
          Promise.resolve(super[pageDataGetter]()),
          elementData.get(this).pageLoadPromise
        ]).then(([resultToChoose]) => resultToChoose)
      }

      connectedCallback() {
        super.connectedCallback?.()
        requestAnimationFrame(() => {
          let numOfElementClassInstances = 0
          this.querySelectorAll('*').forEach(elem => {
            if (elem instanceof elementClass) {
              numOfElementClassInstances++
            }
          })
          elementData.get(this).setMaxInits(numOfElementClassInstances)
        })
      }
    }
  }

  loadsPageWithElement.hasMixin = classObj => classObj[loadsPageWithElementMixinSymbol] === true

  return loadsPageWithElement
}
