import '@webcomponents/custom-elements'

import { SelectBox } from '~components/selectbox/selectbox.element'
import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'
import '~components/i18n-container/i18n-container.element'
import '~components/i18n/i18n.element'
import { request } from '~utils/proxy'
import versionStore from '~tsc-common-stores/version-context-info.store'
import { versionsSelect, versionInfo } from '~tsc-utils/routes'
import { getState } from '~tsc-utils/version-state-machine'
import { getBadgeClasses } from '~tsc-utils/version-state-badge-css'
import $ from 'jquery'
import { htmlEscape } from '~utils/string.util'

const cssLoad = CssStyleSheetLoader(() => import(/* webpackChunkName: "tsc-catalog/styles" */ './test.css'))

export class VersionSelectBox extends SelectBox {
  constructor () {
    super()

    this.setAttribute('full-width', '')
    this.setAttribute('adapter', 'tsc-version-state')
    this.setAttribute('template', 'tsc-version-state')
    this.setAttribute('data-i18n-placeholder', 'nossis.tsccatalog.version.select.box.placeholder')
    this.classList.add('version-select-box')

    this.setAttribute('url', this.getVersionsUrl())

    cssLoad.then(stylesheet => {
      this.shadowRoot.adoptedStyleSheets = [...super.shadowRoot.adoptedStyleSheets, stylesheet]
    })

    debugger

    const versionId = versionStore.getters.versionId

    if (versionId) {
      request({
        route: versionsSelect(),
        data: {
          value: versionId
        },
        onSuccess: (response) => {
          if (response.count <= 0 || response.entries.length <= 0) {
            this.initWithLatestVersion().catch(console.error)
          } else {
            this.initWithVersion(response.entries[0]).catch(console.error)
          }
        }
      })
    } else {
      this.initWithLatestVersion().catch(console.error)
    }

    this.addEventListener('change', (event) => {
      if (event.target !== this) {
        return
      }
      const optionData = this.selectedOptionsData[0]
      if (optionData == null) {
        return
      }
      updateVersionStore(this, optionData)
    })
  }

  getVersionsUrl () {
    return versionsSelect.url()
  }

  getVersionInfoUrl (versionId) {
    return versionInfo.url({ versionId })
  }

  initWithLatestVersion() {
    return new Promise((resolve, reject) => {
      this.queryTransportRequest({ type: 'GET', data: { q: '', page: 1 } }, (data) => {
        this.processAjaxResults(this.getAttribute('url'), '', true, data).then(processedAjaxResults => {
          const [firstVersion] = processedAjaxResults.results
          if (firstVersion) {
            this.value = firstVersion.id
            updateVersionStore(this, firstVersion)
            resolve(this)
          }
        })
      }, () => {
        reject(new Error('Error querying latest version'))
      })
    })
  }

  async initWithVersion(version) {
    const expandedVersion = await expandVersionData(version)
    this.value = expandedVersion.id
    updateVersionStore(this, version)
    return this
  }

  connectedCallback() {
    // Ensure parent connectedCallback is called if it exists
    if (super.connectedCallback) {
      super.connectedCallback()
    }

    // Apply styles once the element is connected and shadow root is available
    if (this.shadowRoot && !this._stylesApplied) {
      cssLoad.then(stylesheet => {
        this.shadowRoot.adoptedStyleSheets = [...this.shadowRoot.adoptedStyleSheets, stylesheet]
        this._stylesApplied = true
      })
    }
  }
}

function updateVersionStore(versionSelectBox, version) {
  if (versionSelectBox.hasAttribute('update-version-info-store')) {
    const { id: versionId, name: versionName, state: versionState } = version.data
    versionStore.setters.setVersionInfo({
      versionId,
      versionName,
      versionState
    })
  }
}

SelectBox.dataAdapter.define('tsc-version-state', {
  adaptOptionData: expandVersionData
})

async function expandVersionData(versionData) {
  const state = await getState(versionData.data.state)
  return {
    ...versionData,
    stateI18nKey: state.i18nKey,
    badgeClasses: getBadgeClasses(state.name),
  }
}

SelectBox.template.define('tsc-version-state', (params, type) => {
  const { badgeClasses, stateI18nKey, text } = params
  switch (type) {
    case 'dropdown':
    case 'selectedOption':
      return $('<div class ="version-select-box__dropdown version-select-box__dropdown--style">' +
                        `<span class="version-select-box__dropdown-items-text">${htmlEscape(text)}&emsp;</span><span class="${badgeClasses}"><x-i18n key="${stateI18nKey}"></x-i18n></span>` +
                    '</div>')
    case 'placeholder':
      return text || 'simple placeholder'
  }
})

export const elementTagName = 'x-tsc-version-select'
customElements.define(elementTagName, VersionSelectBox)
