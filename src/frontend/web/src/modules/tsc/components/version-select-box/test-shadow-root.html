<!DOCTYPE html>
<html>
<head>
    <title>Test VersionSelectBox Shadow Root</title>
</head>
<body>
    <h1>Test VersionSelectBox Shadow Root</h1>
    
    <div id="test-container">
        <!-- The version select box will be created here -->
    </div>

    <script type="module">
        // Simple test to verify shadow root creation
        import { VersionSelectBox } from './version-select-box.element.js';
        
        // Create an instance
        const versionSelectBox = new VersionSelectBox();
        
        // Add to DOM
        document.getElementById('test-container').appendChild(versionSelectBox);
        
        // Test shadow root
        setTimeout(() => {
            console.log('Shadow root exists:', !!versionSelectBox.shadowRoot);
            console.log('Adopted stylesheets length:', versionSelectBox.shadowRoot?.adoptedStyleSheets?.length || 0);
            
            if (versionSelectBox.shadowRoot) {
                console.log('✅ Shadow root created successfully');
                console.log('Adopted stylesheets:', versionSelectBox.shadowRoot.adoptedStyleSheets);
            } else {
                console.log('❌ Shadow root not created');
            }
        }, 1000);
    </script>
</body>
</html>
