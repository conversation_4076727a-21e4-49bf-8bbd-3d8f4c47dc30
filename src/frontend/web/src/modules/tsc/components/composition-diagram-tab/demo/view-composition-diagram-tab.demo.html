<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
    <%= css %></head>

<body>
<%= navbar %>
<div class='container container--tsc-composition-diagram-tab app'>
    <h1><%= title %></h1>

    <h3>Dependencies</h3>
    <%= dependencyTable %>

    <h3>Introduction</h3>
    This document contains information about the usages and configuration of the view composition diagram tab component.<br>
    The view composition diagram tab is only a super class of the <a href="composition-diagram.html">composition diagram </a>
    component (so it contains the same base behaviours). But there two main differences: <br/>
    The first one is that to receive the needed data about the composition diagram
    of the respective entity it sends a request to backend and then prepares the data to create
    the graph inside the component.<br/>
    The second one is that this component needs two attributes: <code>entity-id</code> and <code>entity-type</code>.
    Let's then see an example of this component and how to use it:

    <h3>Imports</h3>
    <pre><code class="code--import lang-js"></code></pre>

    <h3>View composition diagram tab with entity type as cfs </h3>
    <div class="demo-container demo-container--view-diagram">
        <x-tsc-view-composition-diagram-tab entity-id="276908" entity-type="rfs">
        </x-tsc-view-composition-diagram-tab>
    </div>
    <pre><code class="xml">&lt;x-tsc-view-composition-diagram-tab entity-id="276908" entity-type="rfs"&gt;&lt;/x-tsc-view-composition-diagram-tab&gt;</code></pre>
    <br />
    The attribute <code>entity-id</code> can than be dynamically changed and the changes will be reflected in the component.
    For the information to be updated, the attribute <code>entity-type</code> must be defined
    before changing the attribute mentioned above.<br>
    To test what was said about these attributes, two boxes are presented below. In the first one you can choose to
    remove or add the <code>entity-type</code> attribute, the next allows us to change the <code>entity-id</code>.
    You will notice that if you remove the first attribute and then set the second one with a new valid value
    (for example <code>276919</code>), the component will do nothing. Try it.
    <br /><br />
    <pre style=" display: inline-flex; width: 100%; align-items: center; flex-wrap: wrap;">
    <input type="radio" name="entityType" value="remove" style="margin-top: 0px;" />
    <code style="margin-top: 2px;"> body.querySelector('x-tsc-view-composition-diagram-tab').removeAttribute('entity-type')</code>
    <div class="break" style="flex-basis: 100%; height: 0;"></div> <!-- break to a new row -->
    <input type="radio" name="entityType" value="add" style="margin-top: 0px;" />
    <code style="margin-top: 2px;"> body.querySelector('x-tsc-view-composition-diagram-tab').setAttribute('entity-type','rfs')</code>
    </pre>
    <br/>
    <pre style=" display: inline-flex; width: 100%;">
      <code > body.querySelector('x-tsc-view-composition-diagram-tab').setAttribute('entity-id','</code>
      <input type="text" class="input-entity-id"/>
      <code>')</code>
    </pre>
    <br />

</div>
<%= js %></body>

</html>