<nav class="fx-page-main-menu fx-ico-nav side-nav-bar side-nav-bar--tsc-global">
  <ul class="list-unstyled side-nav-bar__item-list">
    <x-tsc-auth version="R">
      <li class="side-nav-bar__item side-nav-bar__item--versions">
      <a class="side-nav-bar__link" data-action="navigate-to-version-search-page">
        <i class="fuxicons fuxicons-catalogue"></i>
      </a>
      <div class="side-nav-bar__tooltip tooltip fade right">
        <div class="tooltip-arrow"></div>
        <div class="tooltip-inner"><x-i18n key="nossis.tsccatalog.navigation.versions"></x-i18n></div>
      </div>
    </li>
    </x-tsc-auth>
    <x-tsc-auth entity="R">
      <li class="side-nav-bar__item side-nav-bar__item--customer-services">
      <a class="side-nav-bar__link" data-action="go-to-cfs-search-page">
        <i class="fuxicons fuxicons-client-services"></i>
      </a>
      <div class="side-nav-bar__tooltip tooltip fade right">
        <div class="tooltip-arrow"></div>
        <div class="tooltip-inner"><x-i18n key="nossis.tsccatalog.navigation.customer.services"></x-i18n></div>
      </div>
    </li>
    <li class="side-nav-bar__item side-nav-bar__item--network-services">
      <a class="side-nav-bar__link" data-action="go-to-rfs-search-page">
        <i class="fuxicons fuxicons-network-configuration"></i>
      </a>
      <div class="side-nav-bar__tooltip tooltip fade right">
        <div class="tooltip-arrow"></div>
        <div class="tooltip-inner"><x-i18n key="nossis.tsccatalog.navigation.network.services"></x-i18n></div>
      </div>
    </li>
    <li class="side-nav-bar__item side-nav-bar__item--resources">
      <a class="side-nav-bar__link" data-action="go-to-resource-search-page">
        <i class="fuxicons fuxicons-resource"></i>
      </a>
      <div class="side-nav-bar__tooltip tooltip fade right">
        <div class="tooltip-arrow"></div>
        <div class="tooltip-inner"><x-i18n key="nossis.tsccatalog.navigation.resources"></x-i18n></div>
      </div>
    </li>
    <li class="side-nav-bar__item side-nav-bar__item--aggregatedtype">
      <a class="side-nav-bar__link" data-action="go-to-attribute-search-page">
        <i class="fuxicons fuxicons-attributes-library"></i>
      </a>
      <div class="side-nav-bar__tooltip tooltip fade right">
        <div class="tooltip-arrow"></div>
        <div class="tooltip-inner"><x-i18n key="nossis.tsccatalog.navigation.attributes"></x-i18n></div>
      </div>
    </li>
  </x-tsc-auth></ul>
</nav>