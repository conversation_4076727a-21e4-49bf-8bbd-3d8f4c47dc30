<tr>
    <td class="name-col">
      <x-input-container data-popover-error data-popover="top" full-width>
        <span>value</span>
        <input name="name" type="text"
               class="form-control input-sm hidden" data-enumerator-field="name"/>
      </x-input-container>
    </td>
    <td class="action-col">
        <div class="action-col__actions">
            <button class="btn btn-link btn-link-in-table add-button"
               data-i18n-title="nossis.tsccatalog.table.action.label.edit"
               data-action="edit-enumerator">
                <i class="glyphicon glyphicon-pencil"></i>
            </button>
            <button class="btn btn-link btn-link-in-table clear-button"
               data-i18n-title="nossis.tsccatalog.table.action.label.delete"
               data-action="delete-enumerator">
                <i class="glyphicon glyphicon-remove"></i>
            </button>
        </div>
        <div class="action-col__edit-actions hidden">
            <button class="btn btn-link btn-link-in-table add-button"
               data-i18n-title="nossis.tsccatalog.table.action.label.update"
               data-action="update-enumerator">
                <i class="glyphicon glyphicon-ok"></i>
            </button>
            <button class="btn btn-link btn-link-in-table clear-button"
               data-i18n-title="nossis.tsccatalog.table.action.label.rollback"
               data-action="rollback-enumerator">
                <i class="glyphicon glyphicon-remove"></i>
            </button>
        </div>
    </td>
</tr>


