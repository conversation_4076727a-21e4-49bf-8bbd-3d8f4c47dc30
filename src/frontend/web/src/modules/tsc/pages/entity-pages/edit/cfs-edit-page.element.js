import { cfsInfo, cfsUpdate } from '../../../utils/routes'
import { EditEntityPage } from './edit-entity-page.template'
import * as proxy from '~utils/proxy'

export class CfsEditPage extends EditEntityPage {
  constructor() {
    super()
  }

  connectedCallback() {
    super.connectedCallback()
    this.renderElements({
      titlePlaceholder: 'nossis.tsccatalog.entity.create.name.cfs',
      subtitlePlaceholder: 'nossis.tsccatalog.entity.create.description.cfs',
      iconClass: 'fuxicons-client-services',
      entityUrl: cfsInfo,
      entityType: 'cfs',
      navigationItem: 'customer-services'
    })
  }

  submitEntity(entity) {
    const params = {
      ...entity,
      tags: entity.tags || [],
      version: this.getAttribute('version-id')
    }

    proxy.put({
      route: cfsUpdate({ id: this.getAttribute('entity-id') }),
      data: params,
      onSuccess: () => {
        this.classList.add('page--tsc-entity-updated-successfully')
        const customEvent = new CustomEvent('go-to-cfs-search-page', { bubbles: true }, this.tagName)
        this.dispatchEvent(customEvent)
      }
    })
  }

  cancelAction(eventName) {
    super.cancelAction('go-to-cfs-search-page')
  }
}

export const elementTagName = 'x-tsc-cfs-edit-page'
customElements.define(elementTagName, CfsEditPage)
