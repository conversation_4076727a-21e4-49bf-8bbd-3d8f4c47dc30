import '~docs/demo-common-i18n'
import '~docs/demo-tsc-mock-server.proxy.provider'
import '~docs/demo-common-authorization'
import '~utils/modal/implementation'
import '~utils/toast-notification/implementation'
import './entity-page.demo.css'
import '../create/resource-create-page.element'
import versionStore from '~tsc-common-stores/version-context-info.store'

versionStore.setters.setVersionName('SELFNET')

$('.demo-container').append('<x-tsc-resource-create-page version-id="50678"></x-tsc-resource-view-page>')
