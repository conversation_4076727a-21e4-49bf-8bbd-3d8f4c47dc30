import { saveRenderer } from '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/table/table.renders-store'
import {
  link as linkUtil,
  menuLink as menuLinkUtil,
  menuButton,
  menuHeader,
  menuDivider
} from '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/table/table-render-utils'

function actionRenderer (context) {
  const link = linkUtil.withContext(context)
  const menuLink = menuLinkUtil.withContext(context)
  const { rowData, i18n } = context
  return '<span class="fx-table-actions">' +
    link({
      context,
      icon: 'fuxicons fuxicons-eye',
      action: 'view',
      title: i18n('nossis.tsccatalog.table.action.label.view')
    }) +
    link({
      context,
      icon: 'glyphicon glyphicon-pencil fx-icon',
      visible: rowData.editPermission === true,
      enabled: rowData.canEdit === true,
      action: 'edit',
      title: i18n('nossis.tsccatalog.table.action.label.edit')
    }) +
    menuButton({
      icon: 'glyphicon glyphicon-cog',
      title: i18n('nossis.tsccatalog.table.action.label.moreoptions'),
      enabled: rowData.canClone === true || rowData.canDelete === true,
      visible: rowData.clonePermission === true || rowData.deletePermission === true
    },
    menuHeader({
      icon: 'fuxicons fuxicons-clone fx-icon',
      visible: rowData.clonePermission === true,
      label: i18n('nossis.tsccatalog.table.action.label.clone')
    }) +
      menuLink({
        visible: rowData.clonePermission === true,
        enabled: rowData.canClone === true,
        action: 'cloneRelations',
        i18nTitle: 'nossis.tsccatalog.table.action.label.clone.with.relations',
        i18nLabel: 'nossis.tsccatalog.table.action.label.clone.with.relations'
      }) +
      menuLink({
        visible: rowData.clonePermission === true,
        enabled: rowData.canClone === true,
        action: 'clone',
        i18nTitle: 'nossis.tsccatalog.table.action.label.clone.with.no.relations',
        i18nLabel: 'nossis.tsccatalog.table.action.label.clone.with.no.relations'
      }) + menuDivider({
      visible: rowData.clonePermission === true && rowData.deletePermission === true
    }) +
      menuLink({
        icon: 'glyphicon glyphicon-remove fx-icon',
        visible: rowData.deletePermission === true,
        enabled: rowData.canDelete === true,
        action: 'delete',
        i18nTitle: 'nossis.tsccatalog.table.action.label.delete',
        i18nLabel: 'nossis.tsccatalog.table.action.label.delete'
      })
    ) +
    '</span>'
}

saveRenderer({
  key: 'tsc.datatableTemplates.catalog.actionRenderer',
  renderer: actionRenderer
})
