import { save<PERSON>enderer } from '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/table/table.renders-store'
import { htmlEscape } from '~utils/string.util'

const i18nElement = (constraint) => `<x-i18n key="nossis.tsccatalog.constraints.${constraint.type}.label"></x-i18n>`

const constraintMapping = {
  RANGE: (constraint) => `${i18nElement(constraint)} (${htmlEscape(constraint.minValue ?? '-∞')}, ${htmlEscape(constraint.maxValue ?? '∞')})`,
  REGEXP: (constraint) => `${i18nElement(constraint)} (${htmlEscape(constraint.regexp)})`,
  ENUMC: (constraint) => `${i18nElement(constraint)} (${constraint.valueList.map(e => htmlEscape(e.value)).join(', ')})`,
  FLOAT_RANGE: (constraint) => `${i18nElement(constraint)} (${htmlEscape(constraint.minValue ?? '-∞')}, ${htmlEscape(constraint.maxValue ?? '∞')})`,
  SIZE: (constraint) => `${i18nElement(constraint)} (${htmlEscape(constraint.minValue ?? '-∞')}, ${htmlEscape(constraint.maxValue ?? '∞')})`,
}

function renderConstraints(constraints) {
  if (!constraints) { // COMPONENT ATTRIBUTES HAVE NO CONSTRAINT FIELD
    return '--'
  }
  if (Array.isArray(constraints)) { // NORMAL ATTRIBUTES ALWAYS HAVE A CONSTRAINT FIELD, EMPTY IF WITH NO CONSTRAINTS
    return constraints.map(constraint => constraintMapping[constraint.type](constraint)).join(', ') || '--'
  }
}

saveRenderer({
  key: 'tsc.datatableTemplates.catalog.entityView.restrictionsRenderer',
  renderer: ({ cellData }) => {
    return `${renderConstraints(cellData)}`
  }
})
