import '@webcomponents/custom-elements'

import './attribute-search-table-action.renderer'
import './attribute-search-table-tag.renderer'

import { TableWithAllMixins } from '~components/table/table.element'
import { attributeSearchTableConfig } from '~tsc-utils/routes'
import { attributeStore } from './attribute-search-page.store'
import { store as versionInfoStore } from '../../commons-stores/version-context-info.store'
import { getAttributesPrimitiveTypes } from '~tsc-utils/attribute-primitive-type.util'
import { logsEventDispatch } from '../../common-element-mixins/logs-event-dispatch.mixin'
import { i18n } from '~utils/i18n'
import { getEntitiesPermissions } from '~tsc-utils/states-configuration'
import { store as authStore } from '../../commons-stores/authorization.store'

export class AttributeSearchTable extends logsEventDispatch(TableWithAllMixins) {
  constructor (...args) {
    super(...args)
    this.componentData.$tableElement.attr('id', 'attribute-search-table')

    const actions = {
      view: ({ rowData }) => {
        this.dispatchEvent(new CustomEvent('attribute-search-table-action', { bubbles: true, detail: { eventType: 'view', rowData } }))
      },
      edit: ({ rowData }) => {
        this.dispatchEvent(new CustomEvent('attribute-search-table-action', { bubbles: true, detail: { eventType: 'edit', rowData } }))
      },
      clone: ({ rowData }) => {
        this.dispatchEvent(new CustomEvent('attribute-search-table-action', { bubbles: true, detail: { eventType: 'clone', rowData } }))
      },
      delete: ({ rowData }) => {
        this.dispatchEvent(new CustomEvent('attribute-search-table-action', { bubbles: true, detail: { eventType: 'delete', rowData, table: this } }))
      }
    }

    this.shadowRoot.addEventListener('click', (event) => {
      const actionElement = event.target.closest('[data-action]')
      if (actionElement == null) {
        const dropDownElement = event.target.closest('[data-toggle]')
        if (dropDownElement == null) {
          return
        }
        this.shadowRoot.querySelector('.dropdown-menu').classList.toggle('show')
        return
      }
      const action = actionElement.dataset.action

      const tr = actionElement.closest('tr')
      const rowData = this.componentData.$tableElement.DataTable().row(tr).data()
      if (actions[action]) {
        actions[action]({ rowData, actionElement })
      }
    })

    this.addEventListener('draw', (event) => {
      const componentData = event.detail.componentData
      attributeStore.setters.updateTablePageLength(componentData.currentPageLength)
      attributeStore.setters.updateTablePageNumber(componentData.currentPageNumber)
    })
  }

  usesDatatable () {
    return true
  }

  usesAjaxLoader () {
    return true
  }

  usesI18nHeaders () {
    return true
  }

  paramsPost () {
    return {
      ...attributeStore.getters.searchValues,
      version: versionInfoStore.getters.versionName,
    }
  }

  getTableConfigUrl () {
    return attributeSearchTableConfig.url()
  }

  loadConfig () {
    return super.loadConfig().then(config => {
      config.pagination.pageLength = attributeStore.getters.tablePageLength
      config.pagination.pageNumber = attributeStore.getters.tablePageNumber
      return config
    })
  }

  reloadCallback(data, done) {
    super.reloadCallback(data, (result) => {
      getAttributesPrimitiveTypes().then(attributesPrimitiveTypes => {
        getEntitiesPermissions().then(({ entities: permissions }) => {
          const auth = authStore.getters.entity
          done({
            ...result,
            data: result.data.map(record => ({
              ...record,
              canClone: permissions.clonable.includes(record.version.state),
              canEdit: permissions.editable.includes(record.version.state),
              canDelete: permissions.removable.includes(record.version.state),
              clonePermission: auth.create,
              editPermission: auth.update,
              deletePermission: auth.delete,
              typeId: i18n('nossis.tsccatalog.attribute.primitivetypes.' + attributesPrimitiveTypes[record.primitiveType].name)
            }))
          })
        })
      })
    })
  }

  connectedCallback () {
    super.connectedCallback()
    this.observerSearchStore = attributeStore.react(
      ({ getters }) => getters.searchValues,
      () => this.reloadTable({ resetPagination: true }))

    this.observerVersionInfoStore = versionInfoStore.react(
      ({ getters }) => getters.versionName,
      () => this.reloadTable({ resetPagination: true }))
  }

  disconnectedCallback () {
    this.observerSearchStore.unregister()
    this.observerVersionInfoStore.unregister()
    super.disconnectedCallback()
  }
}

customElements.define('x-tsc-attribute-search-table', AttributeSearchTable)
