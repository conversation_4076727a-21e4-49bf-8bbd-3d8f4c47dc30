import '@webcomponents/custom-elements'

import '../../components/navigation-sidebar/navigation-sidebar.element'
import '../../components/sidebar-search/sidebar-search.element'
import '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/splitter/splitter.element'
import '~components/i18n/i18n.element'
import '~components/i18n-container/i18n-container.element'
import '~components/selectbox/selectbox.element'
import '~tsc-components/version-select-box/version-select-box.element'
import containerHtml from './attribute-search-page.element.html'
import './attribute-search-page.element.css'
import './attribute-search-table.element'

import { store as versionInfoStore } from '../../commons-stores/version-context-info.store'
import { attributeStore } from './attribute-search-page.store'
import { cloneAttribute, deleteAttribute } from '~tsc-utils/attribute-actions.util'
import { getAllPermissionsForEntityState } from '~tsc-utils/states-configuration'
import i18n from '~utils/i18n'
import { getAttributesPrimitiveTypesEntries } from '~tsc-utils/attribute-primitive-type.util'
import { hasBasicPageFeatures } from '../../common-element-mixins/has-basic-page-features.mixin'
import buildTagsMultiselect from '~tsc-utils/tags-multiselect.builder'

const template = document.createElement('template')
template.innerHTML = containerHtml

export class AttributeSearchPage extends hasBasicPageFeatures(HTMLElement) {
  constructor() {
    super()
    this.addEventListener('clear-sidebar-form', () => {
      const sideBarFormFields = Array.from(this.querySelectorAll('[data-field]'))
      sideBarFormFields.forEach(field => {
        field.value = ''
      })
    })

    this.addEventListener('submit-sidebar-form', () => {
      const sideBarFormFields = Array.from(this.querySelectorAll('[data-field]'))
      const searchValues = sideBarFormFields.reduce((acc, field) => {
        const fieldKey = field.getAttribute('data-field')
        acc[fieldKey] = field.value
        return acc
      }, {})
      attributeStore.setters.updateSearchValues(searchValues)
    })

    this.addEventListener('click', (event) => {
      if (event.target.closest('[data-action="create-attribute"]') != null) {
        const versionId = versionInfoStore.getters.versionId
        const customEvent = new CustomEvent('go-to-attribute-create-page', { bubbles: true, detail: { versionId } })
        this.dispatchEvent(customEvent)
      }
    })

    this.addEventListener('attribute-search-table-action', (event) => {
      const { detail: { rowData, table } } = event
      switch (event.detail.eventType) {
        case 'view':
          this.dispatchEvent(new CustomEvent('go-to-attribute-view-page', { bubbles: true, detail: { attributeId: rowData.id, entityType: rowData.entityType } }))
          break
        case 'edit':
          this.dispatchEvent(new CustomEvent('go-to-attribute-edit-page', { bubbles: true, detail: { attributeId: rowData.id, entityType: rowData.entityType } }))
          break
        case 'clone':
          cloneAttribute({ attributeId: rowData.id, entityType: rowData.entityType })
            .then(entity => {
              this.dispatchEvent(new CustomEvent('go-to-attribute-view-page', { bubbles: true, detail: { attributeId: entity.id, entityType: rowData.entityType } }, this.tagName))
            })
          break
        case 'delete':
          deleteAttribute({ attributeId: event.detail.rowData.id, attributeNameName: rowData.name, entityType: rowData.entityType })
            .then(() => {
              table.reloadTable({ resetPagination: false })
            })
      }
    })

    this.addEventListener('collapse-toggle', (event) => {
      const isCollapsed = event.target.isCollapsed
      attributeStore.setters.updateFormCollapse(isCollapsed)
    })
  }

  renderTagMultiselect() {
    const options = attributeStore.getters.searchValues.tags

    this.querySelector('.sidebar-search-form-item--tag-selector')
      .insertAdjacentHTML('beforeend', `<label for="tags" class="control-label">${i18n('nossis.tsccatalog.sidebarsearch.tag')}</label>
        ${buildTagsMultiselect({ options })}
        `)
  }

  renderTypeSelect() {
    const selected = attributeStore.getters.searchValues.type

    getAttributesPrimitiveTypesEntries().then((primitiveTypes) => {
      let options = '<option></option>'
      primitiveTypes.map(primitiveType => {
        options += `<option value="${primitiveType.id}" ${selected === primitiveType.id ? 'selected' : ''}>
                      ${i18n('nossis.tsccatalog.attribute.primitivetypes.' + primitiveType.name)}
                    </option>`
        return options
      })
      this.querySelector('.sidebar-search-form-item--type-selector').innerHTML = `
    <label for="type" class="control-label">
      ${i18n('nossis.tsccatalog.sidebarsearch.type')}
     </label>
     <x-select full-width data-field="typeId" id="type">
      ${options}
     </x-select>
    `
    })
  }

  assertPermissions() {
    const { versionState } = versionInfoStore.getters
    if (versionState < 0) {
      return
    }
    getAllPermissionsForEntityState(versionState).then(statePermissions => {
      this.querySelector('[data-action="create-attribute"]').disabled = !statePermissions.canEdit
    })
  }

  connectedCallback() {
    super.connectedCallback()
    this.innerHTML = ''
    const clone = document.importNode(template.content, true)
    clone.querySelector('x-splitter').toggleAttribute('collapsed', attributeStore.getters.isFormCollapsed)
    this.appendChild(clone)
    this.renderTagMultiselect()
    this.renderTypeSelect()

    this.assertPermissions()
    this.versionStoreObserver = versionInfoStore.reflect(
      ({ getters }) => getters.versionState,
      () => this.assertPermissions())

    if (!attributeStore.getters.isStateReset) {
      const searchValues = attributeStore.getters.searchValues
      this.querySelector('[data-field="name"]').value = searchValues.name || ''
      this.querySelector('[data-field="description"]').value = searchValues.description || ''
    }
  }

  disconnectedCallback() {
    this.versionStoreObserver.unregister()
    super.disconnectedCallback?.()
  }
}

export const elementTagName = 'x-tsc-attribute-search-page'
customElements.define(elementTagName, AttributeSearchPage)
