import '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/tabs/tab-container.element'
import '~components/i18n/i18n.element'
import '~components/i18n-container/i18n-container.element'
import '@alticelabsprojects/nossis-orchestration-web-components/dist/npm/components/multiselectbox/multiselect.element'
import containerHtml from './version-create-page.element.html'
import '../../components/navigation-sidebar/navigation-sidebar.element'
import '../../components/version-select-box/version-select-box.element'
import '~tsc-common-styles/header.css'
import './version-create-page.element.css'
import { hasFormBasicFeatures } from '../../common-element-mixins/has-basic-form-features.mixin'
import { versionCreate } from '~tsc-utils/routes'
import { request } from '~utils/proxy'
import { getInitialState } from '~tsc-utils/version-state-machine'
import { hasBasicPageFeatures } from '../../common-element-mixins/has-basic-page-features.mixin'
import versionStore from '~tsc-common-stores/version-context-info.store'

const template = document.createElement('template')
template.innerHTML = containerHtml

export class VersionCreatePage extends hasFormBasicFeatures(hasBasicPageFeatures(HTMLElement)) {
  constructor() {
    super()
    this.addEventListener('click', (event) => {
      if (event.target.closest('[data-action="cancel"]') != null) {
        const customEvent = new CustomEvent('version-create-page:cancel-click', { bubbles: true })
        this.dispatchEvent(customEvent)
      }
      if (event.target.closest('[data-action="submit"]') != null) {
        this.validateForm().showNotificationOnError().onValid((formData) => {
          Promise.all([submitVersion(formData), getInitialState()]).then((response) => {
            const [versionInfo, state] = response
            this.classList.add('page--tsc-version-created-successfully')
            const versionCreatedEvent = new CustomEvent('version-create-page:version-created', { bubbles: true })
            versionStore.setters.setVersionInfo({
              versionId: versionInfo.id,
              versionName: formData.name,
              versionState: state.id
            })
            this.dispatchEvent(versionCreatedEvent)
          })
        })
      }
    })
  }

  connectedCallback() {
    // prevent making the form dirty due to "from version" selectbox initialization
    this.addEventListener('change', (event) => {
      event.stopImmediatePropagation()
      event.preventDefault()
    }, { once: true })
    super.connectedCallback()
    this.innerHTML = ''
    const clone = document.importNode(template.content, true)
    this.appendChild(clone)
  }

  allowsNavigation () {
    return this.classList.contains('page--tsc-version-created-successfully') || super.allowsNavigation()
  }
}

function submitVersion (formData) {
  return getInitialState().then(initialState => new Promise((resolve) => {
    request({
      data: {
        ...formData,
        state: initialState.id
      },
      route: versionCreate(),
      onSuccess: resolve
    })
  }))
}

export const elementTagName = 'x-tsc-version-create-page'
customElements.define(elementTagName, VersionCreatePage)
