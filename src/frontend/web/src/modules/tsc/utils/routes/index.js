import { instance } from './provider'

export const {
  // VERSION PAGES
  versionCreatePage,
  versionSearchPage,
  versionViewPage,
  versionEditPage,
  // VERSION CRUD
  versionsSelect, // Gets all versions
  versionInfo, // Gets one version
  versionCreate,
  versionClone,
  versionDelete,
  versionStateChange,
  versionUpdate,
  // VERSION UTIL
  versionSearchTableConfig,
  versionStateMachine,
  versionViewEntitiesTableConfig,
  versionViewAttributesTableConfig,
  // COMPOSITION DIAGRAM
  entityNodes,
  compositionDiagram,
  newCompositionDiagram,
  // CFS PAGES
  cfsSearchPage,
  cfsCreatePage,
  cfsViewPage,
  cfsEditPage,
  // CFS CRUD
  cfsInfo,
  cfsCreate,
  cfsUpdate,
  cfsDelete,
  cfsCloneWithoutRelations,
  cfsCloneWithRelations,
  // RFS PAGES
  rfsSearchPage,
  rfsViewPage,
  rfsEditPage,
  rfsCreatePage,
  // RFS CRUD
  rfsInfo,
  rfsCreate,
  rfsUpdate,
  rfsDelete,
  rfsCloneWithoutRelations,
  rfsCloneWithRelations,
  // RESOURCE PAGES
  resourceSearchPage,
  resourceViewPage,
  resourceEditPage,
  resourceCreatePage,
  // RESOURCE CRUD
  resourceInfo,
  resourceCreate,
  resourceUpdate,
  resourceDelete,
  resourceCloneWithoutRelations,
  resourceCloneWithRelations,
  // ENTITY UTIL
  entityViewAttributesTableConfig,
  cfsSearchTableConfig,
  rfsSearchTableConfig,
  resourceSearchTableConfig,
  // ATTRIBUTE PAGES
  attributeSearchPage,
  attributeViewPage,
  attributeEditPage,
  attributeCreatePage,
  // ATTRIBUTE CRUD
  attributeInfo,
  componentInfo,
  attributeCreate,
  componentCreate,
  attributeUpdate,
  componentUpdate,
  attributeDelete,
  componentDelete,
  attributeClone,
  componentClone,
  // ATTRIBUTE UTILS
  attributeViewComponentTableConfig,
  attributeSearchTableConfig,
  // UTIL ROUTES
  entityAttributes, // Gets all attributes associated with a version, used in editable attribute table component
  statesConfiguration, // Get version state associated permissions
  attributesPrimitiveTypes, // Gets attribute primitive types, should be used in conjunction with attribute-primitive-types.util.js
  selectTags, // Gets all tags

} = instance
