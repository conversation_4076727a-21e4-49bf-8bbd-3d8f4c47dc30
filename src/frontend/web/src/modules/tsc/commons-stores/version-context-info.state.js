const initialStateObj = Object.freeze({
  versionName: '',
  versionId: '',
  versionState: -1,
})

export const initialState = () => initialStateObj

// getters

const getVersionName = (state) => state.versionName
const getVersionId = (state) => state.versionId
const getVersionState = (state) => state.versionState
const isStateReset = (state) => state === initialStateObj
const isValidVersionState = (state) => state.versionState > 0

export const getters = {
  versionName: getVersionName,
  versionId: getVersionId,
  versionState: getVersionState,
  isStateReset,
  isValidVersionState
}

// helpers

const isValidId = (versionId) => typeof versionId === 'string'
const isValidName = (versionName) => typeof versionName === 'string'
const isValidState = (versionState) => typeof versionState === 'number' && versionState > 0

// setters

function resetState () {
  return initialState()
}

function setVersionName (state, versionName) {
  if (state.versionName === versionName) {
    return state
  }

  if (!isValidName(versionName)) {
    return state
  }

  return {
    ...state,
    versionName: versionName
  }
}

function setVersionId (state, versionId) {
  if (state.versionId === versionId) {
    return state
  }

  if (!isValidId(versionId)) {
    return state
  }

  return {
    ...state,
    versionId: versionId
  }
}

function setVersionState (state, versionState) {
  if (state.versionState === versionState) {
    return state
  }

  if (!isValidState(versionState)) {
    return state
  }

  return {
    ...state,
    versionState
  }
}

function setVersionInfo (state, params) {
  if (params == null || typeof params !== 'object') {
    return state
  }

  const { versionId, versionName, versionState } = params

  if (!isValidId(versionId) ||
      !isValidName(versionName) ||
      !isValidState(versionState)) {
    return state
  }

  const state1 = setVersionId(state, versionId)
  const state2 = setVersionName(state1, versionName)
  return setVersionState(state2, versionState)
}

export const setters = {
  resetState: resetState,
  setVersionName: setVersionName,
  setVersionId: setVersionId,
  setVersionState: setVersionState,
  setVersionInfo: setVersionInfo
}
