// noinspection DuplicatedCode
import { commonActions } from '~test-utils/test-ui/common-driver-actions.util'
export { versionCreatePageActionsOfDriver } from '../../pages/version-create/test-ui/version-create.spec-ui-actions'

export function versionSearchPageActionsOfDriver(driver, test) {
  const { click, element } = commonActions(driver)

  const actions = {
    checkOnPage: async () => {
      await element('.page.page--tsc-version-search')
      test.pass('is on version search page')
      return actions
    },

    clickVersionCreateButton: async () => {
      await click('.page--tsc-version-search .button--create-version')
      return actions
    }
  }

  return actions
}
