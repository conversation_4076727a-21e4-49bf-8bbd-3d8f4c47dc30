html, body {
    /* why not use the one defined by the user? there is a reason why rem exits */
    font-size: 1rem;
}

.styling .i18n-substitution{
    font-weight: bold;
}

.styling .i18n-substitution[data-parameter-position="0"]{
    color: darkblue;
}

.example:after {
    content: "Example";
    position: absolute;
    top:  15px;
    left: 15px;
    font-size: 12px;
    font-weight: bold;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.example[data-example]:after {
    content: attr(data-example);
}

.example + .example {
    border-width: 0 1px 1px 1px;
    border-radius: 0;
}

.example > pre {
    border: none;
    padding: 0;
}

.example > pre:not(.hljs) {
    background: none;
}



.example {
    background-color: #fff;
    border-color: #ddd;
    border-width: 1px;
    border-radius: 4px 4px 0 0;
    -webkit-box-shadow: none;
    box-shadow: none;

    position: relative;
    padding: 45px 15px 15px;
    border-style: solid;
}
