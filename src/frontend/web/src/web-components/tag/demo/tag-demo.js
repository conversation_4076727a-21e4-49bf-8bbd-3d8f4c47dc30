import './demo-providers'
import css from './tag-demo.raw.css'
import '~components/tag/tag-container.element'
import '~docs/demo-common-module-graph.element'

const head = document.head || document.getElementsByTagName('head')[0]
const style = document.createElement('style')

head.appendChild(style)

style.type = 'text/css'
style.appendChild(document.createTextNode(css))

document.querySelectorAll('.code--import').forEach(el => { el.textContent = 'import "~components/tag/tag-container.element";' })
document.querySelectorAll('code--css').forEach(el => { el.textContent = css })

$('.code--css').text(css)
