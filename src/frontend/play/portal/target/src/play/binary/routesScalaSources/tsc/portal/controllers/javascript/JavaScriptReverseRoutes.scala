// @GENERATOR:play-routes-compiler
// @SOURCE:/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/portal/conf/tscportal.routes
// @DATE:Tue Jun 03 15:40:11 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:8
package tsc.portal.controllers.javascript {

  // @LINE:15
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:15
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "tsc.portal.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "portal/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:8
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:11
    def aboutExternalLibraries: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "tsc.portal.controllers.Application.aboutExternalLibraries",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "portal/about/externallibraries"})
        }
      """
    )
  
    // @LINE:8
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "tsc.portal.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "portal/resume"})
        }
      """
    )
  
  }


}
