2025-06-30T14:06:56.787 INFO  [play-dev-mode-akka.actor.default-dispatcher-4] [event.slf4j.Slf4jLogger] Slf4jLogger started
2025-06-30T14:06:57.216 INFO  [main] [core.server.AkkaHttpServer] Listening for HTTP on /0:0:0:0:0:0:0:0:9031
2025-06-30T14:08:31.276 INFO  [application-akka.actor.default-dispatcher-6] [event.slf4j.Slf4jLogger] Slf4jLogger started
2025-06-30T14:08:31.387 WARN  [play-dev-mode-akka.actor.default-dispatcher-9] [application] conf-dev/application.conf: 15: play.crypto.secret is deprecated, use play.http.secret.key instead
2025-06-30T14:08:31.626 INFO  [play-dev-mode-akka.actor.default-dispatcher-9] [internal.util.Version] HV000001: Hibernate Validator 5.4.3.Final
2025-06-30T14:08:31.637 WARN  [play-dev-mode-akka.actor.default-dispatcher-9] [validator.messageinterpolation.ParameterMessageInterpolator] HV000184: ParameterMessageInterpolator has been chosen, EL interpolation will not be supported
2025-06-30T14:08:31.685 INFO  [play-dev-mode-akka.actor.default-dispatcher-9] [api.http.EnabledFilters] Enabled Filters (see <https://www.playframework.com/documentation/latest/Filters>):


2025-06-30T14:08:31.688 INFO  [play-dev-mode-akka.actor.default-dispatcher-9] [play.api.Play] Application started (Dev)
2025-06-30T14:08:31.853 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Using provided configuration file: ../security/conf-dev/nossisaaapi.conf
2025-06-30T14:08:31.853 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Using nossis configuration file: /mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/portal/../security/conf-dev/nossisaaapi.conf
2025-06-30T14:08:31.902 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Looking for mappings on the classpath!
2025-06-30T14:08:31.914 INFO  [application-akka.actor.default-dispatcher-6] [core.config.InitializationService] Initializing OpenSAML using the Java Services API
2025-06-30T14:08:32.175 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.PlaySecurityConfig] Deprecated property 'nossisaaapi.provider.url-parameter-name' found in configuration. Please update to 'nossisaaapi.provider.sessionid-parameter-name'.
2025-06-30T14:08:32.187 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e14cf3b9-edc1-4c0e-bd37-05d67095d178]
2025-06-30T14:08:32.187 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:08:32.188 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with cookie: SCASessionId=YzMzMDI5MjgtYzg4Yi00NjI2LTlhMTctZGMzNmY2OGRmY2U1
2025-06-30T14:08:32.189 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:08:32.189 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:32.189 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6ac7127d
2025-06-30T14:08:32.190 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:08:32.512 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] user not authenticated
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@795794d8
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [security.auth.AbstractAuthStrategy] User session not found
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e14cf3b9-edc1-4c0e-bd37-05d67095d178]
2025-06-30T14:08:32.513 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:08:32.514 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with cookie: SCASessionId=YzMzMDI5MjgtYzg4Yi00NjI2LTlhMTctZGMzNmY2OGRmY2U1
2025-06-30T14:08:32.514 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:08:32.514 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:32.514 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5c8388b0
2025-06-30T14:08:32.514 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:08:32.573 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] user not authenticated
2025-06-30T14:08:32.573 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e14cf3b9-edc1-4c0e-bd37-05d67095d178]
2025-06-30T14:08:32.573 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:08:32.573 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with cookie: SCASessionId=YzMzMDI5MjgtYzg4Yi00NjI2LTlhMTctZGMzNmY2OGRmY2U1
2025-06-30T14:08:32.574 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:08:32.574 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:32.574 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@58dd66e
2025-06-30T14:08:32.574 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:08:32.637 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] user not authenticated
2025-06-30T14:08:32.692 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e14cf3b9-edc1-4c0e-bd37-05d67095d178]
2025-06-30T14:08:32.692 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:08:38.594 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:38.594 INFO  [application-akka.actor.default-dispatcher-8] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.UserPassCredentials@4a6243bf
2025-06-30T14:08:38.892 INFO  [application-akka.actor.default-dispatcher-8] [iam.tenant.TenantUtils] Finding tenant id for user: ADMIN_PORTAL
2025-06-30T14:08:38.914 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:08:37.892Z
2025-06-30T14:08:38.916 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:08:38.916 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:08:38.916 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:08:38.916 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:08:38.916 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:08:38.921 INFO  [application-akka.actor.default-dispatcher-8] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [dwAhRnadGVYsyPMNXTe6RF]
2025-06-30T14:08:38.921 INFO  [application-akka.actor.default-dispatcher-8] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.ServiceCredentials@10db3b97
2025-06-30T14:08:40.271 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:08:40.468 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:08:37.892Z
2025-06-30T14:08:40.468 INFO  [application-akka.actor.default-dispatcher-9] [portal.providers.ConfigurationFileProvider] getting menus of type: operational.view, found 2
2025-06-30T14:08:40.529 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:08:37.892Z
2025-06-30T14:08:40.530 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:08:40.530 INFO  [application-akka.actor.default-dispatcher-8] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:08:40.572 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:08:37.892Z
2025-06-30T14:10:54.459 INFO  [application-akka.actor.default-dispatcher-7] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [86a52f28-66b6-45a4-afba-72deaf5d85ea]
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying http Authentication
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7d279942
2025-06-30T14:10:54.460 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:10:54.723 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:10:54.723 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:10:54.723 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:10:54.723 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:10:54.723 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:10:56.011 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:11:08.040 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:11:08.134 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T14:11:08.135 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:11:08.135 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:11:08.135 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:11:08.135 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:08.135 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:11:09.656 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:11:10.178 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T14:11:10.179 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:11:10.179 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:11:10.179 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:11:10.179 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:10.179 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:11:11.108 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:11:11.344 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T14:11:11.345 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:11.345 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [92df6d5c-497b-439e-8fdb-1a5f3005a536]
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying http Authentication
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@51a60d86
2025-06-30T14:11:40.460 INFO  [application-akka.actor.default-dispatcher-10] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:11:40.763 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:11:40.763 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:11:40.763 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:11:40.763 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:40.763 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:11:42.179 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:11:47.284 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.761Z
2025-06-30T14:11:47.284 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:11:47.284 INFO  [application-akka.actor.default-dispatcher-7] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:12:51.959 INFO  [application-akka.actor.default-dispatcher-10] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [207987d2-b59b-4640-adbb-f274cab80e3a]
2025-06-30T14:12:51.959 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:12:51.959 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:12:51.960 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:12:51.960 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Trying http Authentication
2025-06-30T14:12:51.960 INFO  [application-akka.actor.default-dispatcher-10] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@93a17f0
2025-06-30T14:12:51.960 INFO  [application-akka.actor.default-dispatcher-10] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:12:52.216 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:12:52.216 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:12:52.216 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:12:52.216 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:12:52.216 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:12:53.260 INFO  [application-akka.actor.default-dispatcher-10] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:12:58.806 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.214Z
2025-06-30T14:12:58.806 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:12:58.806 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [ab182da5-432c-49ff-b600-e5835b6414df]
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying http Authentication
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1e6f1f5a
2025-06-30T14:13:33.966 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:13:34.318 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:13:34.318 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:13:34.318 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:13:34.318 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:13:34.318 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:13:35.551 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:13:36.550 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:27.315Z
2025-06-30T14:13:36.551 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:13:36.558 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [08ee4f7c-4218-4726-8b3b-e88299549dd9]
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@78f29e54
2025-06-30T14:13:57.808 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:13:57.982 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:13:57.982 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:13:57.982 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:13:57.982 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:13:57.983 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:13:59.089 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:13:59.294 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.973Z
2025-06-30T14:13:59.294 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:13:59.294 INFO  [application-akka.actor.default-dispatcher-6] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:14:48.772 INFO  [application-akka.actor.default-dispatcher-11] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [5f9da9bd-5c79-4992-9d5c-b43b7f3c8149]
2025-06-30T14:14:48.772 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:14:48.772 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:14:48.773 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:14:48.773 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying http Authentication
2025-06-30T14:14:48.773 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@327f1ccc
2025-06-30T14:14:48.773 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:14:48.989 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:14:48.989 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:14:48.989 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:14:48.989 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:14:48.989 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:14:49.920 INFO  [application-akka.actor.default-dispatcher-11] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:14:51.725 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:26.987Z
2025-06-30T14:14:51.726 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:14:51.726 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [ee868e00-17b3-421e-a22c-0c6d94db68cb]
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Trying http Authentication
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4330c11c
2025-06-30T14:15:12.476 INFO  [application-akka.actor.default-dispatcher-12] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:15:12.668 INFO  [application-akka.actor.default-dispatcher-12] [security.auth.AbstractAuthStrategy] user [ADMIN_PORTAL] already in session
2025-06-30T14:15:12.683 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:27.660Z
2025-06-30T14:15:12.683 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:15:12.683 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:15:12.683 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:15:12.683 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:15:12.683 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:15:13.999 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:15:52.894 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:27.660Z
2025-06-30T14:15:52.895 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:15:52.895 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:15:52.895 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:15:52.895 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:15:52.895 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:15:54.064 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:15:54.302 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:27.660Z
2025-06-30T14:15:54.302 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:15:54.302 INFO  [application-akka.actor.default-dispatcher-12] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [5287a685-97e2-458c-a3da-40f8e71ceb36]
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying http Authentication
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@309886d
2025-06-30T14:20:19.596 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:20:19.790 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:20:19.790 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:20:19.790 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:20:19.790 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:20:19.790 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:20:20.702 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:20:20.951 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:26.788Z
2025-06-30T14:20:20.951 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:20:20.951 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0080ce0d-431b-4a0d-8532-924e72fe3820]
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying http Authentication
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3734dc7f
2025-06-30T14:20:58.255 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:20:58.693 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:20:58.694 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:20:58.694 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:20:58.694 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:20:58.694 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:20:59.746 INFO  [application-akka.actor.default-dispatcher-16] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:21:03.898 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.691Z
2025-06-30T14:21:03.898 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:21:03.899 INFO  [application-akka.actor.default-dispatcher-14] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:24:15.240 INFO  [application-akka.actor.default-dispatcher-18] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0d8af2ce-9db4-411a-a1b9-c79dfcdc9c28]
2025-06-30T14:24:15.240 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:24:15.240 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:24:15.240 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:24:15.240 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying http Authentication
2025-06-30T14:24:15.241 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@661ab2ef
2025-06-30T14:24:15.241 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:24:15.530 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:24:15.530 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:24:15.530 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:24:15.530 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:24:15.530 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:24:16.463 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:24:16.689 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.528Z
2025-06-30T14:24:16.689 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:24:16.689 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4ec7e37f-f87a-4ee4-a953-508ee3bce0e3]
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying http Authentication
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@21112da9
2025-06-30T14:24:49.871 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:24:50.078 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:24:50.078 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:24:50.078 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:24:50.078 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:24:50.078 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:24:50.973 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:24:52.062 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.076Z
2025-06-30T14:24:52.063 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:24:52.063 INFO  [application-akka.actor.default-dispatcher-18] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [c8856db7-8d2f-4ded-94bc-1811da9b00b8]
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying http Authentication
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5840413c
2025-06-30T14:28:43.295 INFO  [application-akka.actor.default-dispatcher-22] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:28:43.533 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:28:43.534 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:28:43.534 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:28:43.534 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:28:43.534 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:28:44.511 INFO  [application-akka.actor.default-dispatcher-22] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:28:44.867 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.523Z
2025-06-30T14:28:44.868 INFO  [application-akka.actor.default-dispatcher-23] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:28:44.878 INFO  [application-akka.actor.default-dispatcher-23] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d4a6502a-a9bb-4667-a03d-d048f13b14b7]
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Trying http Authentication
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@51864a74
2025-06-30T14:34:55.185 INFO  [application-akka.actor.default-dispatcher-24] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:34:55.454 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:34:55.454 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:34:55.454 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:34:55.454 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:34:55.454 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:34:56.334 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:34:56.585 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.451Z
2025-06-30T14:34:56.589 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:34:56.597 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [81f8c958-c54c-4745-9384-a9d654317a2f]
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying http Authentication
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3e2b5659
2025-06-30T14:35:11.253 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:35:11.397 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:35:11.397 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:35:11.397 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:35:11.397 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:35:11.397 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:35:12.303 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [81f8c958-c54c-4745-9384-a9d654317a2f]
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying http Authentication
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@67b34da4
2025-06-30T14:35:12.543 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:35:12.701 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:35:12.701 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:35:12.701 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:35:12.701 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:35:12.701 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:35:13.555 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:35:13.783 WARN  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Session time: 2025-06-30T16:11:26.697Z
2025-06-30T14:35:13.783 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:35:13.783 INFO  [application-akka.actor.default-dispatcher-24] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [85367719-c5ef-4571-8a5b-4be9c88679ed]
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying http Authentication
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2564c99d
2025-06-30T14:36:18.214 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:36:18.462 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:36:18.462 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:36:18.462 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:36:18.462 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:36:18.462 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:36:19.824 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:36:20.090 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.459Z
2025-06-30T14:36:20.090 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:36:20.090 INFO  [application-akka.actor.default-dispatcher-25] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [1d4cb153-9e89-4489-b885-45101c44f998]
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Trying http Authentication
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1a71c4ce
2025-06-30T14:42:09.050 INFO  [application-akka.actor.default-dispatcher-29] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:42:09.289 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:42:09.289 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:42:09.289 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:42:09.289 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:42:09.289 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:42:10.158 INFO  [application-akka.actor.default-dispatcher-29] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:42:10.401 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:27.287Z
2025-06-30T14:42:10.401 INFO  [application-akka.actor.default-dispatcher-31] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:42:10.401 INFO  [application-akka.actor.default-dispatcher-31] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:44:19.228 INFO  [application-akka.actor.default-dispatcher-32] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [18d73f35-3c24-43f0-83a9-4a937f96259e]
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying http Authentication
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@15d98864
2025-06-30T14:44:19.229 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:44:19.430 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:44:19.430 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:44:19.430 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:44:19.430 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:44:19.430 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:44:20.322 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:44:20.577 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:27.427Z
2025-06-30T14:44:20.577 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:44:20.577 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:44:53.959 INFO  [application-akka.actor.default-dispatcher-33] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [da4aeb3b-6a26-4696-ab98-236362fa5cf8]
2025-06-30T14:44:53.959 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:44:53.959 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:44:53.960 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:44:53.960 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Trying http Authentication
2025-06-30T14:44:53.960 INFO  [application-akka.actor.default-dispatcher-33] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@555cd004
2025-06-30T14:44:53.960 INFO  [application-akka.actor.default-dispatcher-33] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:44:54.155 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:44:54.156 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:44:54.156 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:44:54.156 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:44:54.156 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:44:55.247 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:44:55.511 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:27.143Z
2025-06-30T14:44:55.511 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:44:55.511 INFO  [application-akka.actor.default-dispatcher-33] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:47:51.306 INFO  [application-akka.actor.default-dispatcher-32] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [765fb826-ca24-4d7c-8487-e15b884a0b55]
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying http Authentication
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@39a88661
2025-06-30T14:47:51.307 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:47:51.578 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:47:51.578 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:47:51.578 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:47:51.578 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:47:51.578 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:47:52.515 INFO  [application-akka.actor.default-dispatcher-32] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:47:52.716 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.575Z
2025-06-30T14:47:52.716 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:47:52.716 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3d77660c-aae2-48a8-8271-dbd099547e3b]
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying http Authentication
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7dff7ea1
2025-06-30T14:48:22.742 INFO  [application-akka.actor.default-dispatcher-36] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:48:23.017 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:48:23.017 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:48:23.017 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:48:23.017 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:48:23.017 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:48:23.929 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:48:24.151 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.015Z
2025-06-30T14:48:24.152 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:48:24.152 INFO  [application-akka.actor.default-dispatcher-36] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:55:31.844 INFO  [application-akka.actor.default-dispatcher-38] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [93be344f-24ad-49d2-b086-7df102e6b26d]
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying http Authentication
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@165a12cf
2025-06-30T14:55:31.845 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:55:32.079 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:55:32.079 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:55:32.079 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:55:32.079 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:55:32.079 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:55:33.009 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:55:33.240 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:27.075Z
2025-06-30T14:55:33.241 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:55:33.241 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:56:09.313 INFO  [application-akka.actor.default-dispatcher-39] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [107804f0-68a3-4cd8-aa9d-b25e5d6e91db]
2025-06-30T14:56:09.313 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:56:09.313 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:56:09.313 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:56:09.314 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying http Authentication
2025-06-30T14:56:09.314 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2e2f5a4d
2025-06-30T14:56:09.314 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:56:09.555 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:56:09.555 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:56:09.555 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:56:09.555 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:56:09.555 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:56:10.445 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:56:10.700 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:27.553Z
2025-06-30T14:56:10.700 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:56:10.700 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b972baa2-433d-45e6-873c-ffd74d0497f0]
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying http Authentication
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3bcc712f
2025-06-30T14:56:33.193 INFO  [application-akka.actor.default-dispatcher-40] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:56:33.352 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:56:33.352 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:56:33.352 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:56:33.352 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:56:33.352 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:56:34.262 INFO  [application-akka.actor.default-dispatcher-40] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:56:34.471 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:27.347Z
2025-06-30T14:56:34.472 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:56:34.472 INFO  [application-akka.actor.default-dispatcher-38] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d6e0b015-547f-4431-93ab-c752ddff0380]
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying http Authentication
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5ca4e75e
2025-06-30T14:59:03.840 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:59:04.024 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:59:04.024 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:59:04.024 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:59:04.024 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:59:04.024 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:59:04.895 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:59:05.146 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.022Z
2025-06-30T14:59:05.146 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:59:05.146 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [264ceffd-0cf1-4253-897d-ff914c9a79d2]
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying http Authentication
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@73dc0b0b
2025-06-30T14:59:46.729 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:59:46.967 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T14:59:46.967 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T14:59:46.967 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T14:59:46.967 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:59:46.967 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T14:59:47.884 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T14:59:48.094 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.955Z
2025-06-30T14:59:48.094 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T14:59:48.094 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [239497d1-d94c-4b1f-a776-c2e73ad79235]
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Trying http Authentication
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@554d71f0
2025-06-30T15:00:06.716 INFO  [application-akka.actor.default-dispatcher-39] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:00:06.859 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:00:06.859 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:00:06.859 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:00:06.859 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:00:06.859 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:00:07.759 INFO  [application-akka.actor.default-dispatcher-39] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:00:07.951 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:26.854Z
2025-06-30T15:00:07.951 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:00:07.951 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [2e9dbe0c-309f-420a-8275-9353397ce9a6]
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Trying http Authentication
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6f970521
2025-06-30T15:00:37.472 INFO  [application-akka.actor.default-dispatcher-42] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:00:37.669 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:00:37.669 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:00:37.669 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:00:37.669 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:00:37.669 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:00:38.567 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:00:38.773 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:26.663Z
2025-06-30T15:00:38.774 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:00:38.774 INFO  [application-akka.actor.default-dispatcher-42] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [28658699-b7dd-4810-9d2b-0cbc5494df02]
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying http Authentication
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4baf15d0
2025-06-30T15:03:35.317 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:03:35.542 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:03:35.542 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:03:35.542 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:03:35.542 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:03:35.542 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:03:36.422 INFO  [application-akka.actor.default-dispatcher-43] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:03:36.941 WARN  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Session time: 2025-06-30T16:11:27.540Z
2025-06-30T15:03:36.942 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:03:36.942 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [79ff1ac2-7864-42e8-a2eb-dd3b774d6efe]
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Trying http Authentication
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@279251e3
2025-06-30T15:04:14.404 INFO  [application-akka.actor.default-dispatcher-46] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:04:14.750 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:04:14.750 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:04:14.750 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:04:14.750 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:04:14.750 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:04:15.642 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:04:15.878 WARN  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Session time: 2025-06-30T16:11:26.747Z
2025-06-30T15:04:15.878 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:04:15.879 INFO  [application-akka.actor.default-dispatcher-46] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [998596ea-c2e0-4c69-9b35-1aee6b577f83]
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying http Authentication
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6433fec5
2025-06-30T15:08:21.241 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:08:21.521 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:08:21.521 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:08:21.521 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:08:21.521 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:08:21.521 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:08:22.405 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:08:22.641 WARN  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Session time: 2025-06-30T16:11:27.517Z
2025-06-30T15:08:22.641 INFO  [application-akka.actor.default-dispatcher-50] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:08:22.641 INFO  [application-akka.actor.default-dispatcher-50] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b53afa36-e75f-4493-bec2-70cba52dfbb6]
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying http Authentication
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@771c30a3
2025-06-30T15:09:22.301 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:09:22.542 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:09:22.543 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:09:22.543 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:09:22.543 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:09:22.543 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:09:23.488 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:09:23.720 WARN  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Session time: 2025-06-30T16:11:27.535Z
2025-06-30T15:09:23.720 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:09:23.720 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [5b22982c-ffd6-43ae-9a5a-2950555f411e]
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying http Authentication
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@70c3098b
2025-06-30T15:09:28.702 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:09:28.869 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:09:28.869 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:09:28.869 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:09:28.869 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:09:28.869 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:09:29.685 INFO  [application-akka.actor.default-dispatcher-48] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:09:29.908 WARN  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Session time: 2025-06-30T16:11:26.847Z
2025-06-30T15:09:29.908 INFO  [application-akka.actor.default-dispatcher-51] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:09:29.908 INFO  [application-akka.actor.default-dispatcher-51] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [6b4fb9fa-b36a-4373-b0fa-7e95ea7b178c]
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Trying http Authentication
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2b31dab2
2025-06-30T15:10:26.364 INFO  [application-akka.actor.default-dispatcher-52] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:10:26.633 INFO  [application-akka.actor.default-dispatcher-52] [security.auth.AbstractAuthStrategy] user [ADMIN_PORTAL] already in session
2025-06-30T15:10:26.640 WARN  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Session time: 2025-06-30T16:11:27.631Z
2025-06-30T15:10:26.640 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:10:26.640 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:10:26.640 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:10:26.640 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:10:26.640 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:10:27.527 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:10:27.831 WARN  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Session time: 2025-06-30T16:11:27.631Z
2025-06-30T15:10:27.831 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:10:27.831 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:10:27.876 WARN  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Session time: 2025-06-30T16:11:27.631Z
2025-06-30T15:10:27.876 INFO  [application-akka.actor.default-dispatcher-52] [portal.providers.ConfigurationFileProvider] getting menus of type: operational.view, found 2
2025-06-30T15:10:28.306 WARN  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Session time: 2025-06-30T16:11:27.631Z
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [bdd815b5-07a6-4263-8eab-ac5eabb30bd8]
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Trying http Authentication
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6bc17aef
2025-06-30T15:12:23.663 INFO  [application-akka.actor.default-dispatcher-49] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:12:24.011 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:12:24.011 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:12:24.011 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:12:24.012 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:12:24.012 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:12:25.082 INFO  [application-akka.actor.default-dispatcher-49] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:12:25.306 WARN  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Session time: 2025-06-30T16:11:27.005Z
2025-06-30T15:12:25.306 INFO  [application-akka.actor.default-dispatcher-54] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:12:25.306 INFO  [application-akka.actor.default-dispatcher-54] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [6924197b-e91c-4414-b9b9-c9dffb0c1c70]
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying http Authentication
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1c8eceac
2025-06-30T15:23:35.147 INFO  [application-akka.actor.default-dispatcher-56] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:23:35.381 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:23:35.381 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:23:35.381 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:23:35.381 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:23:35.381 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:23:36.357 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:23:36.593 WARN  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Session time: 2025-06-30T16:11:27.374Z
2025-06-30T15:23:36.593 INFO  [application-akka.actor.default-dispatcher-57] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:23:36.593 INFO  [application-akka.actor.default-dispatcher-57] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:25:17.105 INFO  [application-akka.actor.default-dispatcher-56] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [122f1e9c-0b40-4eda-a3af-3486b3edeaae]
2025-06-30T15:25:17.105 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:25:17.106 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:25:17.106 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:25:17.106 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Trying http Authentication
2025-06-30T15:25:17.106 INFO  [application-akka.actor.default-dispatcher-56] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@f5b8be9
2025-06-30T15:25:17.106 INFO  [application-akka.actor.default-dispatcher-56] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:25:17.363 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:25:17.363 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:25:17.363 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:25:17.364 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:25:17.364 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:25:18.287 INFO  [application-akka.actor.default-dispatcher-56] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:25:18.504 WARN  [application-akka.actor.default-dispatcher-61] [nossis.security.Security] Session time: 2025-06-30T16:11:27.356Z
2025-06-30T15:25:18.505 INFO  [application-akka.actor.default-dispatcher-61] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:25:18.505 INFO  [application-akka.actor.default-dispatcher-61] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [c92b0a84-de00-4911-8d6a-b955985736cf]
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying http Authentication
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5864ffd9
2025-06-30T15:26:05.251 INFO  [application-akka.actor.default-dispatcher-59] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:26:05.492 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:26:05.492 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:26:05.492 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:26:05.492 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:26:05.492 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:26:06.378 INFO  [application-akka.actor.default-dispatcher-59] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:26:06.586 WARN  [application-akka.actor.default-dispatcher-60] [nossis.security.Security] Session time: 2025-06-30T16:11:27.485Z
2025-06-30T15:26:06.586 INFO  [application-akka.actor.default-dispatcher-60] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:26:06.586 INFO  [application-akka.actor.default-dispatcher-60] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f7aaa497-0f3b-4f5a-b152-f37b4e39a36d]
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying http Authentication
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2c3a08d2
2025-06-30T15:29:33.543 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:29:33.796 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:29:33.796 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:29:33.796 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:29:33.796 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:29:33.796 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:29:34.772 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:29:35.002 WARN  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Session time: 2025-06-30T16:11:26.788Z
2025-06-30T15:29:35.002 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:29:35.002 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [90013981-d840-4f0a-b6e2-9c758500287a]
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying http Authentication
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@508050b5
2025-06-30T15:30:02.856 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:30:03.031 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:30:03.031 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:30:03.031 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:30:03.031 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:30:03.031 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:30:04.267 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:30:07.731 WARN  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Session time: 2025-06-30T16:11:27.024Z
2025-06-30T15:30:07.732 INFO  [application-akka.actor.default-dispatcher-66] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:30:07.732 INFO  [application-akka.actor.default-dispatcher-66] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3fb2bc28-0792-478c-a1fc-487b6b883846]
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying http Authentication
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7962198c
2025-06-30T15:31:29.333 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:31:29.779 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:31:29.779 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:31:29.779 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:31:29.779 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:31:29.779 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:31:30.769 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:31:30.985 WARN  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Session time: 2025-06-30T16:11:26.745Z
2025-06-30T15:31:30.986 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:31:30.986 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:31:39.888 INFO  [application-akka.actor.default-dispatcher-65] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f8dcc491-1528-42fa-9f59-c04e51dc1842]
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Trying http Authentication
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@686438d1
2025-06-30T15:31:39.889 INFO  [application-akka.actor.default-dispatcher-65] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:31:40.068 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:31:40.068 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:31:40.068 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:31:40.068 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:31:40.068 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:31:41.005 INFO  [application-akka.actor.default-dispatcher-65] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:31:41.235 WARN  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Session time: 2025-06-30T16:11:27.060Z
2025-06-30T15:31:41.235 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:31:41.235 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [5e60f608-f2f6-427f-81fa-76439f7817a7]
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Trying http Authentication
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@12b5e6ec
2025-06-30T15:31:59.784 INFO  [application-akka.actor.default-dispatcher-63] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:31:59.989 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:31:59.989 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:31:59.989 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:31:59.989 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:31:59.989 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:32:00.879 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:32:02.447 WARN  [application-akka.actor.default-dispatcher-63] [nossis.security.Security] Session time: 2025-06-30T16:11:26.982Z
2025-06-30T15:32:02.447 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:32:02.447 INFO  [application-akka.actor.default-dispatcher-63] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4083492d-6f32-4096-968b-9575709eb5f8]
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying http Authentication
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4b8fd541
2025-06-30T15:33:36.047 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:33:36.293 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:33:36.293 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:33:36.293 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:33:36.293 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:33:36.293 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:33:37.195 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:33:37.414 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.291Z
2025-06-30T15:33:37.414 INFO  [application-akka.actor.default-dispatcher-67] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:33:37.414 INFO  [application-akka.actor.default-dispatcher-67] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [2a8e0095-aa58-4485-8d2d-2823cea1649a]
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying http Authentication
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@52351fe3
2025-06-30T15:34:59.425 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:34:59.863 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:34:59.863 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:34:59.863 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:34:59.863 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:34:59.863 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:35:00.780 INFO  [application-akka.actor.default-dispatcher-64] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:35:01.580 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:26.856Z
2025-06-30T15:35:01.580 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:35:01.580 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:35:13.938 INFO  [application-akka.actor.default-dispatcher-69] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [8f3c0bf7-9681-4d36-8d9e-93cbba72c259]
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying http Authentication
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5676b26f
2025-06-30T15:35:13.939 INFO  [application-akka.actor.default-dispatcher-69] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:35:14.112 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:35:14.112 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:35:14.113 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:35:14.113 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:35:14.113 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:35:15.104 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:35:15.372 WARN  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Session time: 2025-06-30T16:11:27.105Z
2025-06-30T15:35:15.372 INFO  [application-akka.actor.default-dispatcher-70] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:35:15.372 INFO  [application-akka.actor.default-dispatcher-70] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [45d0fcd8-de80-40d5-8bad-5a4ee869c5c0]
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying http Authentication
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@773c2ce
2025-06-30T15:36:31.150 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:36:31.368 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:36:31.368 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:36:31.368 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:36:31.368 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:36:31.368 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:36:32.287 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:36:32.539 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.366Z
2025-06-30T15:36:32.540 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:36:32.540 INFO  [application-akka.actor.default-dispatcher-69] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b8369644-8e38-485d-921b-49e4594460c5]
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying http Authentication
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@292e6081
2025-06-30T15:38:01.887 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:38:02.227 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:38:02.227 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:38:02.227 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:38:02.227 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:38:02.227 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:38:03.840 INFO  [application-akka.actor.default-dispatcher-68] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:38:14.382 WARN  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Session time: 2025-06-30T16:11:27.225Z
2025-06-30T15:38:14.382 INFO  [application-akka.actor.default-dispatcher-72] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:38:14.382 INFO  [application-akka.actor.default-dispatcher-72] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [8a01938b-f1cc-4f44-ba7d-4e7de123222d]
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [nossis.security.Security] Trying http Authentication
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1d638428
2025-06-30T15:39:50.194 INFO  [application-akka.actor.default-dispatcher-71] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:39:50.458 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:39:50.458 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:39:50.458 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:39:50.458 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:39:50.458 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:39:51.397 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:39:51.626 WARN  [application-akka.actor.default-dispatcher-71] [nossis.security.Security] Session time: 2025-06-30T16:11:27.454Z
2025-06-30T15:39:51.626 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:39:51.626 INFO  [application-akka.actor.default-dispatcher-71] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:20.638 INFO  [application-akka.actor.default-dispatcher-73] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [7d5bc654-2458-4de2-adfb-15f5f58eebf1]
2025-06-30T15:42:20.638 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:20.639 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:20.639 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:20.639 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:20.639 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7a682843
2025-06-30T15:42:20.639 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:20.833 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:42:20.833 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:42:20.833 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:42:20.833 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:20.833 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:21.726 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:42:22.000 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:26.831Z
2025-06-30T15:42:22.000 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:22.000 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [593ad48c-18e7-44ab-bcd2-6760cd32f0b7]
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@8824771
2025-06-30T15:42:37.098 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:37.237 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:42:37.237 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:42:37.237 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:42:37.237 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:37.237 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:38.079 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:42:38.300 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.235Z
2025-06-30T15:42:38.301 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:38.301 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4166b773-f7e4-4c47-b91c-249324ae5adf]
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7b9a166a
2025-06-30T15:42:44.953 INFO  [application-akka.actor.default-dispatcher-76] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:45.092 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:42:45.092 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:42:45.092 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:42:45.092 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:45.092 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:45.873 INFO  [application-akka.actor.default-dispatcher-76] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:42:46.070 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.090Z
2025-06-30T15:42:46.070 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:46.070 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [68b97c67-51d1-4a82-8a98-d34d12da65c2]
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5ca46255
2025-06-30T15:42:50.955 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:51.193 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:42:51.193 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:42:51.193 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:42:51.193 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:51.193 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:42:52.056 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:42:52.285 WARN  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Session time: 2025-06-30T16:11:27.191Z
2025-06-30T15:42:52.285 INFO  [application-akka.actor.default-dispatcher-75] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:42:52.285 INFO  [application-akka.actor.default-dispatcher-75] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [8fbb57b2-e2fd-44c6-9d1b-9d6e38ad1781]
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying http Authentication
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4622bcb
2025-06-30T15:44:28.922 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:44:29.124 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:44:29.124 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:44:29.124 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:44:29.124 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:44:29.124 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:44:30.051 INFO  [application-akka.actor.default-dispatcher-74] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:44:41.087 WARN  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Session time: 2025-06-30T16:11:27.122Z
2025-06-30T15:44:41.087 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:44:41.087 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e393d82a-6011-4135-b5d2-b47e42b40166]
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying http Authentication
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@14349dbc
2025-06-30T15:44:50.896 INFO  [application-akka.actor.default-dispatcher-77] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:44:51.045 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:44:51.045 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:44:51.045 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:44:51.045 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:44:51.045 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:44:51.920 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:44:52.119 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.040Z
2025-06-30T15:44:52.120 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:44:52.120 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f7788007-ec3f-4b01-b413-d2fa63ad83b5]
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying http Authentication
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5c4374a8
2025-06-30T15:45:02.605 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:45:02.854 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:45:02.854 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:45:02.854 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:45:02.854 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:45:02.854 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:45:03.850 INFO  [application-akka.actor.default-dispatcher-73] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:45:04.079 WARN  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Session time: 2025-06-30T16:11:26.847Z
2025-06-30T15:45:04.079 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:45:04.079 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [62c1b6b3-b543-4507-99ab-8b10e2be5757]
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Trying http Authentication
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@47fd0006
2025-06-30T15:45:42.101 INFO  [application-akka.actor.default-dispatcher-77] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:45:42.423 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:45:42.424 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:45:42.424 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:45:42.424 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:45:42.424 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:45:43.339 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:45:55.053 WARN  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Session time: 2025-06-30T16:11:27.420Z
2025-06-30T15:45:55.053 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:45:55.053 INFO  [application-akka.actor.default-dispatcher-77] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [2b867fc7-8dc9-4736-9d95-0f3f0bdf2095]
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@57009d5
2025-06-30T15:49:22.742 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:23.013 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:49:23.013 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:49:23.013 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:49:23.013 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:23.013 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:24.978 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:49:25.369 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.006Z
2025-06-30T15:49:25.370 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:25.370 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [ed42c90e-3494-4750-b114-b478a2704b73]
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@186103cc
2025-06-30T15:49:28.332 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:28.493 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:49:28.493 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:49:28.493 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:49:28.493 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:28.493 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:29.401 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:49:29.620 WARN  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Session time: 2025-06-30T16:11:27.485Z
2025-06-30T15:49:29.621 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:29.621 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3cc8fe93-d8e5-498d-9d3c-1c65198c2a4e]
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@253d200d
2025-06-30T15:49:39.427 INFO  [application-akka.actor.default-dispatcher-81] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:39.588 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:49:39.588 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:49:39.588 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:49:39.588 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:39.588 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:49:40.579 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:49:40.796 WARN  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Session time: 2025-06-30T16:11:27.580Z
2025-06-30T15:49:40.797 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:49:40.797 INFO  [application-akka.actor.default-dispatcher-81] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f9dd9b9e-493c-4c60-b128-fcd9c391372e]
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying http Authentication
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@34cb9c09
2025-06-30T15:50:37.827 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:50:38.048 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:50:38.048 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:50:38.048 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:50:38.048 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:50:38.048 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:50:38.923 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:50:42.944 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.044Z
2025-06-30T15:50:42.944 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:50:42.944 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d61820f1-bf27-4ec1-b9bf-8d2fc58fc34f]
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying http Authentication
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6ed73f6f
2025-06-30T15:50:49.890 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:50:50.050 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:50:50.050 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:50:50.050 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:50:50.051 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:50:50.051 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:50:50.946 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:50:58.308 WARN  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Session time: 2025-06-30T16:11:27.044Z
2025-06-30T15:50:58.308 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:50:58.308 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [9979597a-88f5-4615-a334-200fb41616c4]
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying http Authentication
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@ff35980
2025-06-30T15:51:25.239 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:51:25.507 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:51:25.507 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:51:25.507 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:51:25.507 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:51:25.507 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:51:26.359 INFO  [application-akka.actor.default-dispatcher-78] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:51:32.098 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.505Z
2025-06-30T15:51:32.098 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:51:32.098 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:51:53.184 INFO  [application-akka.actor.default-dispatcher-80] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3944f50b-a872-43fd-87e4-d620f39f25da]
2025-06-30T15:51:53.184 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:51:53.184 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:51:53.185 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:51:53.185 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying http Authentication
2025-06-30T15:51:53.185 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1fd9ef9c
2025-06-30T15:51:53.185 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:51:53.353 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:51:53.353 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:51:53.353 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:51:53.353 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:51:53.353 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:51:54.308 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:51:54.546 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.346Z
2025-06-30T15:51:54.546 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:51:54.546 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [cf1271d6-87e1-4b38-baaf-9f89ab00d503]
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying http Authentication
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4bbc81a9
2025-06-30T15:52:05.479 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:52:05.678 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:52:05.678 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:52:05.678 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:52:05.678 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:52:05.678 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:52:06.533 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:52:08.564 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:26.676Z
2025-06-30T15:52:08.564 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:52:08.564 INFO  [application-akka.actor.default-dispatcher-80] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:54:51.383 INFO  [application-akka.actor.default-dispatcher-84] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3f08bb42-cba1-4786-ad1e-26ee5e1fcb55]
2025-06-30T15:54:51.383 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:54:51.383 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Trying with cookie: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:54:51.384 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:54:51.384 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Trying http Authentication
2025-06-30T15:54:51.384 INFO  [application-akka.actor.default-dispatcher-84] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7f85b78b
2025-06-30T15:54:51.384 INFO  [application-akka.actor.default-dispatcher-84] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:54:51.711 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: menu, found 1
2025-06-30T15:54:51.711 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: header.additional-menus, found 0
2025-06-30T15:54:51.711 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: header.user-menu-options, found 0
2025-06-30T15:54:51.711 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:54:51.711 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
2025-06-30T15:54:52.600 INFO  [application-akka.actor.default-dispatcher-84] [portal.providers.ConfigurationFileProvider] getting menus of type: footer.additional-footer-menu, found 1
2025-06-30T15:55:00.657 WARN  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Session time: 2025-06-30T16:11:26.709Z
2025-06-30T15:55:00.657 INFO  [application-akka.actor.default-dispatcher-85] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-modules, found 0
2025-06-30T15:55:00.657 INFO  [application-akka.actor.default-dispatcher-85] [portal.providers.ConfigurationFileProvider] getting menus of type: header.modules.other-systems, found 0
