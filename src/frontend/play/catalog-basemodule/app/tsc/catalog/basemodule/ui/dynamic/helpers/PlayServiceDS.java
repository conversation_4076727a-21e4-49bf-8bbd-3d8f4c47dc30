package tsc.catalog.basemodule.ui.dynamic.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import tsc.catalog.basemodule.services.CatalogServiceHelper;
import tsc.catalog.basemodule.exceptions.CatalogBaseModuleExceptionCodes;
import tsc.base.services.BaseResponseMapperWrapper;
import tsc.base.services.ws.Request;
import tsc.base.services.ws.RequestBuilder;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import java.security.InvalidParameterException;
import java.util.Map;
import java.util.concurrent.CompletionStage;

@SuppressWarnings("unchecked")
public final class PlayServiceDS implements DataSource<Map<String, String>> {

    private final play.Logger.ALogger LOGGER = play.Logger.of(PlayServiceDS.class);
    private final BaseResponseMapperWrapper baseResponseMapperWrapper;
    private final CatalogServiceHelper catalogServiceHelper;

    @Inject
    public PlayServiceDS(final BaseResponseMapperWrapper baseResponseMapperWrapper,
                         final CatalogServiceHelper catalogServiceHelper) {
        this.baseResponseMapperWrapper = baseResponseMapperWrapper;
        this.catalogServiceHelper = catalogServiceHelper;
    }

    @Override
    public Map<String, String> getResults(final Map<String, String> params) {

        final String route = getRoute(params);
        final String method = String.valueOf(params.get("method"));
        final String responseAdapterClassName = String.valueOf(params.get("responseAdapter"));
        final DataSourceResponseAdapter responseAdapter;

        try {
            responseAdapter = (DataSourceResponseAdapter) Class.forName(responseAdapterClassName).newInstance();
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
            throw new BusinessException(CatalogBaseModuleExceptionCodes.ERROR_LOADING_CLASS, e,
                    "implClassName", responseAdapterClassName,
                    "className", DataSourceResponseAdapter.class.getName());
        }

        final Request request = new RequestBuilder<>(JsonNode.class, baseResponseMapperWrapper)
                .setUrl(route)
                .responseAs(responseAdapter.getInputClass())
                .build();

        final CompletionStage client;

        switch (method.toUpperCase()) {
            case "GET":
                client = catalogServiceHelper.sendGet(request);
                break;
            case "POST":
                client = catalogServiceHelper.sendPost(request);
                break;
            case "PUT":
                client = catalogServiceHelper.sendPut(request);
                break;
            case "DELETE":
                client = catalogServiceHelper.sendDelete(request);
                break;
            default:
                final String message = "HTTP method " + method.toUpperCase() + " not supported!";
                LOGGER.error(message);
                throw new InvalidParameterException(message);
        }

        return (Map<String, String>) client.thenApply(responseAdapter::parse).toCompletableFuture().join();
    }

    private String getRoute(final Map<String, String> params) {
        return String.valueOf(params.get("route"));
    }
}