@(element: tsc.catalog.basemodule.ui.MultiSelect)

@import tsc.catalog.basemodule.ui.Action
@import _root_.tsc.base.utils.TemplateUtils
@import _root_.tsc.base.views.tags.i18n

@placeholder = @{
    if(element.getPlaceholder != null) {
        element.getPlaceholder.getTranslation
    }
}

@viewValue() = @{
    element.getValue.asInstanceOf[String]
}

@if(Action.VIEW.equals(element.getAction) && viewValue.isEmpty) {
    <span id="@element.getId">--</span>

} else if(Action.VIEW.equals(element.getAction)) {
    <x-tag-container id="@element.getId">
    @for(tagName <- viewValue.split(",")) {
        <x-tag>@tagName</x-tag>
    }
    </x-tag-container>
} else if(Action.SEARCH.equals(element.getAction)){
    <x-multiselect @TemplateUtils.dynamicElementsAttributes(element.getDOMAttributes)></x-multiselect>
} else {
    <x-multiselect @TemplateUtils.dynamicElementsAttributes(element.getDOMAttributes)>
    @if(viewValue != null && !viewValue.isEmpty) {
        @for(tagName <- viewValue.split(",")) {
            <option value="@tagName" selected>@tagName</option>
        }
    }
    </x-multiselect>
}
