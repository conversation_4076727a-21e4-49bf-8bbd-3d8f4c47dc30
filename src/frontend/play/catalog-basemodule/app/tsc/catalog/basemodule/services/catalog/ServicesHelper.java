package tsc.catalog.basemodule.services.catalog;

import com.typesafe.config.Config;
import tsc.base.services.i18n.Lang;
import tsc.catalog.basemodule.settings.CatalogBaseModuleConstants;
import tsc.base.settings.BaseConstants;
import play.libs.ws.WSClient;
import play.libs.ws.WSRequest;
import play.mvc.Controller;
import play.mvc.Http;
import pt.alticelabs.nossis.security.Security;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.time.Duration;
import java.util.Arrays;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static play.mvc.Controller.request;
import static play.mvc.Controller.response;

@Singleton
public final class ServicesHelper {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(ServicesHelper.class);

    private static final String USERNAME = "UserName";
    private static final String INVOKE_CONTEXT = "InvokeContext";
    private static final Pattern PATTERN = Pattern.compile("/(\\w+)/.*");

    private static final String INVALID_CACHE = "invalidCache";
    private static final String NO_CACHE = "no-cache";

    private final Config config;
    private final Lang lang;
    private final WSClient wsClient;

    @Inject
    public ServicesHelper(final Config moduleConfig,
                          final Lang lang,
                          final WSClient wsClient) {
        this.config = moduleConfig;
        this.lang = lang;
        this.wsClient = wsClient;
    }

    /**
     * retrieves a requestHolder instance with the service url already filled in.
     */
    WSRequest getRequestHolder(final String serviceUrl) {
        return getRequestHolder(serviceUrl, null);
    }

    /**
     * retrieves a requestHolder instance with the service url, query parameters and default header parameters
     * already filled in.
     */
    WSRequest getRequestHolder(final String serviceUrl,
                               final Map<String, String> queryParameters) {

        LOGGER.debug("getRequestHolder");
        final WSRequest request = wsClient.url(serviceUrl);

        // add query parameters
        if (queryParameters != null && !queryParameters.isEmpty()) {
            for (Map.Entry<String, String> entry : queryParameters.entrySet()) {
                request.addQueryParameter(entry.getKey(), entry.getValue());
            }
        }

        setCacheControl(request);

        // add language cookie
        final String cookieLang = lang.getLanguageFromCookie(request());
        if (cookieLang != null) {
            request.addHeader(Http.HeaderNames.ACCEPT_LANGUAGE, cookieLang);
        }

        // fixed header parameters
        final String username = Http.Context.current() == null ? "<no username>" : userNameHeaderParameter();
        final String invokeContext = Http.Context.current() == null ? "<no invokeContext>" : invokeContextHeaderParameter();
        request.addHeader(CatalogBaseModuleConstants.HEADER_USER_NAME, username);
        request.addHeader(CatalogBaseModuleConstants.HEADER_INVOKE_CONTEXT, invokeContext);

        return request;
    }

    // -----------------------------------------------------------------------------------------------------------------
    // --- Utils
    // -----------------------------------------------------------------------------------------------------------------

    private void setCacheControl(final WSRequest request) {

        final Http.Context context = Http.Context.current();
        final Map<String, Object> args = context.args;

        final String requestType;
        if (args.get("ROUTE_VERB") instanceof String) {
            requestType = args.get("ROUTE_VERB").toString();
        } else {
            requestType = context.request().method();
        }

        if (Arrays.asList(BaseConstants.REQUEST_TYPE_POST, BaseConstants.REQUEST_TYPE_PUT, BaseConstants.REQUEST_TYPE_PATCH, BaseConstants.REQUEST_TYPE_DELETE).contains(requestType)) {

            final int cacheTimeout;
            if (config.hasPath(CatalogBaseModuleConstants.CACHE_TIMEOUT)) {
                cacheTimeout = config.getInt(CatalogBaseModuleConstants.CACHE_TIMEOUT);
            } else {
                cacheTimeout = CatalogBaseModuleConstants.DEFAULT_CACHE_TIMEOUT;
            }

            final Http.Cookie cookie = Http.Cookie
                    .builder(INVALID_CACHE, "true")
                    .withMaxAge(Duration.ofSeconds(cacheTimeout))
                    .build();
            response().setCookie(cookie);
        } else if (request().cookie(INVALID_CACHE) != null && !request.getHeaders().containsKey(Http.HeaderNames.CACHE_CONTROL)) {
            request.addHeader(Http.HeaderNames.CACHE_CONTROL, NO_CACHE);
        }
    }

    /**
     * extracts the invocation Context through play.mvc.Controller.request uri and set it in the session.
     */
    private String invokeContextHeaderParameter() {

        if (Controller.session(INVOKE_CONTEXT) != null) {
            return Controller.session(INVOKE_CONTEXT);
        }

        final Matcher matcher;
        try {
            matcher = PATTERN.matcher(request().uri());
        } catch (Exception e) {
            return "";
        }

        if (matcher.matches()) {
            final String invokeContext = matcher.group(1);
            Controller.session(INVOKE_CONTEXT, invokeContext);
            return invokeContext;
        }

        return "";
    }

    /**
     * retrieves the username of the logged in user in the application through the SCA credentials and set it
     * in the session.
     *
     * @return String username
     */
    private String userNameHeaderParameter() {
        if (Controller.session(USERNAME) != null) {
            return Controller.session(USERNAME);
        }
        try {
            final String username = Security.getUser(Http.Context.current()).getDisplayName();
            Controller.session(USERNAME, username);
            return username;
        } catch (Exception e) {
            LOGGER.error("ERROR ON getting username from security");
        }
        return "";
    }
}