include 'nossis-tsc-frontend-basemodule'
include 'nossis-tsc-frontend-catalog-basemodule'
include 'nossis-tsc-frontend-security'
include 'nossis-tsc-frontend-catalog'
include 'nossis-tsc-frontend-portal'
include 'nossis-tsc-frontend-health-check'

rootProject.name = 'nossis-tsc-frontend'
project(':nossis-tsc-frontend-basemodule').projectDir = new File('basemodule')
project(':nossis-tsc-frontend-catalog-basemodule').projectDir = new File('catalog-basemodule')
project(':nossis-tsc-frontend-security').projectDir = new File('security')
project(':nossis-tsc-frontend-catalog').projectDir = new File('tsc-catalog')
project(':nossis-tsc-frontend-portal').projectDir = new File('portal')
project(':nossis-tsc-frontend-health-check').projectDir = new File('health-check')