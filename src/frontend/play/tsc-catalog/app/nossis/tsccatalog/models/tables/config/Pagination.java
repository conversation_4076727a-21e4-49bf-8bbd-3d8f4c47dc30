package nossis.tsccatalog.models.tables.config;

import com.google.common.collect.ImmutableList;

import java.util.List;

public class Pagination {
    public static final Pagination DISABLED = new Pagination(false,15, ImmutableList.of(15, 30, 60));
    public static final Pagination DEFAULT = new Pagination(true, 15,ImmutableList.of(15, 30, 60));

    private final boolean enabled;
    private final int pageLength;
    private final List<Integer> pageLenghts;

    public Pagination(boolean enabled, int pageLength, List<Integer> pageLenghts) {
        this.enabled = enabled;
        this.pageLength = pageLength;
        this.pageLenghts = pageLenghts;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public int getPageLength() {
        return pageLength;
    }

    public List<Integer> getPageLengths() {
        return pageLenghts;
    }

    public boolean isPageLengthEnabled() {
        return getPageLengths().size() > 1;
    }

    public static Pagination init(boolean enabled, int pageLength, List<Integer> pageLenghts) {
        return new Pagination(enabled, pageLength, pageLenghts);
    }

}
