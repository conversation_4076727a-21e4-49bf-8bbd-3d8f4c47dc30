package nossis.tsccatalog.models;

public enum PrimitiveTypes {
    DATE("2000"),
    STRING("2001"),
    NUMBER("2002"),
    FLOAT("2003"),
    ENUMERATION("2004"),
    COMPONENT("2005"),
    NONE("0000");

    private String value;

    PrimitiveTypes(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
    public Long getId() {
        return Long.parseLong(this.value);
    }

    public static PrimitiveTypes getPrimitiveTypeOf(String value) {
        for (PrimitiveTypes primitiveType : PrimitiveTypes.values()) {
            if (primitiveType.getValue().equals(value)) {
                return primitiveType;
            }
        }
        return PrimitiveTypes.NONE;
    }

}
