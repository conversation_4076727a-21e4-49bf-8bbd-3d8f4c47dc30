package nossis.tsccatalog.models.statemachine;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>, 21-12-2017.
 */
public class StateWithTransition extends State {
    private List<StateTransition> statetransitions;

    public StateWithTransition() {
    }

    public StateWithTransition(Long id, String name, String i18n, List<StateTransition> statetransitions) {
        super(id, name, i18n);
        this.statetransitions = statetransitions;
    }

    public List<StateTransition> getStatetransitions() {
        return statetransitions;
    }

    public void setStatetransitions(List<StateTransition> statetransitions) {
        this.statetransitions = statetransitions;
    }
}
