package nossis.tsccatalog.models.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import nossis.tsccatalog.models.EntityType;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

// This class is necessary due to a incompatibility between the Nosssis-inv Datatable implementation and the Model
public final class AggrTypeDTSearch extends TaggedAndVersionedEntity {

    private List<Tag> tags;
    private Long typeId;
    private String typeName;
    private Boolean isSimpleType;



    @JsonIgnore
    public List<Tag> getTags() {
        return tags;
    }

    @JsonProperty("tags")
    public void setTags(List<Tag> tags) {
        if (tags.stream().allMatch(tag -> tag.getName() != null)) {
            tags.sort(Comparator.comparing(Entity::getName));
        }
        this.tags = tags;
    }
    @JsonIgnore
    public Long getTypeId() {
        return typeId;
    }

    @JsonProperty("typeId")
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    @JsonProperty("typeId")
    public String getTypeName() {
        return typeName;
    }

    @JsonIgnore
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @JsonProperty("tagsAsString")
    public String getTagsAsString() {
        return (tags != null) ? tags.stream().map(Tag::getName).collect(Collectors.joining(",")) : "";
    }

    @JsonProperty("tags")
    public List<String> getTagsAsStringList() {
        return (tags != null) ? tags.stream().map(Tag::getName).collect(Collectors.toList()) : Collections.emptyList();
    }

    @Override
    @JsonProperty("typeCatId")
    public void setIdCatEntity(long idCatEntity) {
        super.setIdCatEntity(idCatEntity);
    }

    public Boolean getSimpleType() {
        return isSimpleType;
    }

    public void setSimpleType(Boolean simpleType) {
        isSimpleType = simpleType;
    }
}
