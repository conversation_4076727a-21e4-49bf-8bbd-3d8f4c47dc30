package nossis.tsccatalog.models.tables.config;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>, 21-02-2018.
 */
public class TableConfig {

    private String ajaxUrl;
    private String ajaxMethod;
    private List<Column> columns;
    private Pagination pagination;
    private boolean autoWidth = false;

    public TableConfig(String ajaxUrl, String ajaxMethod, List<Column> columns, Pagination pagination, boolean autoWidth) {
        this.ajaxUrl = ajaxUrl;
        this.ajaxMethod = ajaxMethod;
        this.columns = columns;
        this.pagination = pagination;
        this.autoWidth = autoWidth;
    }

    public String getAjaxUrl() {
        return ajaxUrl;
    }

    public void setAjaxUrl(String ajax) {
        this.ajaxUrl = ajax;
    }

    public String getAjaxMethod() {
        return ajaxMethod;
    }

    public void setAjaxMethod(String ajaxMethod) {
        this.ajaxMethod = ajaxMethod;
    }

    public void setColumns(List<Column> columns) {
        this.columns = columns;
    }

    public List<Column> getColumns() {
        return new ArrayList<>(columns);
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public boolean isAutoWidth() {
        return autoWidth;
    }

    public void setAutoWidth(boolean autoWidth) {
        this.autoWidth = autoWidth;
    }
}