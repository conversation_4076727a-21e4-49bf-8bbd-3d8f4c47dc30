package nossis.tsccatalog.models.entities;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Objects;

public final class Cfs extends EntityWithAttributes {

    private List<Relation> parentCfs;
    private List<Relation> childCfs;
    private List<Relation> childRfs;
    private List<Relation> childResources;


    public List<Relation> getParentCfs() {
        return parentCfs;
    }

    public List<Relation> getChildCfs() {
        return this.childCfs;
    }

    public List<Relation> getChildRfs() {
        return this.childRfs;
    }

    public List<Relation> getChildResources() {
        return this.childResources;
    }

    public void setParentCfs(List<Relation> parentCfs) {
        this.parentCfs = parentCfs;
    }

    public void setChildCfs(List<Relation> childCfs) {
        this.childCfs = childCfs;
    }

    public void setChildRfs(List<Relation> childRfs) {
        this.childRfs = childRfs;
    }

    public void setChildResources(List<Relation> childResources) {
        this.childResources = childResources;
    }

    @JsonProperty("CFS_VERSION_version")
    public void setVersionFromRelationshipField(String versionId) {
        this.setVersion(new Version(versionId));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        Cfs cfs = (Cfs) o;
        return Objects.equals(parentCfs, cfs.parentCfs) &&
                Objects.equals(childCfs, cfs.childCfs) &&
                Objects.equals(childRfs, cfs.childRfs) &&
                Objects.equals(childResources, cfs.childResources);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), parentCfs, childCfs, childRfs, childResources);
    }

    @Override
    public String toString() {
        return "Cfs{" +
                "id='" + this.getId() + '\'' +
                ", name='" + this.getName() + '\'' +
                ", description='" + this.getDescription() + '\'' +
                ", idCatEntity=" + this.getIdCatEntity() +
                ", catEntityName='" + this.getCatEntityName() + '\'' +
                ", version=" + this.getVersion() +
                ", tags=" + this.getTags() +
                ", attributes=" + this.getAttributes() +
                ", parentCfs=" + parentCfs +
                ", childCfs=" + childCfs +
                ", childRfs=" + childRfs +
                ", childResources=" + childResources +
                '}';
    }
}