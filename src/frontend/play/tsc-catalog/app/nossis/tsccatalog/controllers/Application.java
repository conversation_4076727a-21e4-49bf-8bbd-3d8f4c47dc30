package nossis.tsccatalog.controllers;

import nossis.tsccatalog.settings.AAAPIAccess;
import nossis.tsccatalog.settings.AAAPIResources;
import nossis.tsccatalog.settings.AppPage;
import nossis.tsccatalog.settings.ServicesSettings;
import play.mvc.Controller;
import play.mvc.Result;
import pt.ptinovacao.nossis.tsc.security.actions.AuthorizedOr;
import pt.ptinovacao.nossis.tsc.security.handler.PageRequestSecurityHandler;
import temp.actions.UpdatePermissions;

import javax.inject.Inject;

public class Application extends Controller {

    private final ServicesSettings servicesSettings;
    private final AppPage appPage;

    // private constructor prevents instantiation
    @Inject
    private Application(
            ServicesSettings servicesSettings,
            AppPage appPage
    ) {
        this.servicesSettings = servicesSettings;
        this.appPage = appPage;
    }

    public Result getStatesConfigurations() {
        response().setHeader(CONTENT_TYPE, "application/json; charset=UTF-8");
        return ok(servicesSettings.getStatesConfigurations());
    }

    @AuthorizedOr(
        value = {AAAPIResources.ENTITY + AAAPIAccess.R, AAAPIResources.VERSION + AAAPIAccess.R},
        handler = PageRequestSecurityHandler.class
    )
    @UpdatePermissions
    public Result catalogResume() {
        return ok(appPage.getResumePage());
    }

    @AuthorizedOr(
        value = {AAAPIResources.ENTITY + AAAPIAccess.R, AAAPIResources.VERSION + AAAPIAccess.R},
        handler = PageRequestSecurityHandler.class
    )
    @UpdatePermissions
    public Result appPage() {
        return ok(appPage.getPage());
    }

    /**
     * generation of jsroutes for the module nossis.tsccatalog.controllers
     */
    public Result javascriptRoutes() {
        return ok(play.routing.JavaScriptReverseRouter.create("tscCatalogJsRoutes",

                routes.javascript.Application.getStatesConfigurations(),

                routes.javascript.Assets.versioned(),

                temp.controllers.routes.javascript.IAttributeTypeDTController.attributeTypeTableConfig(),
                temp.controllers.routes.javascript.IAttributeTypeDTController.versionAttributeTableConfig(),
                temp.controllers.routes.javascript.IAttributeTypeDTController.componentAttributesTableConfig(),

                temp.controllers.routes.javascript.ITagController.getTags(),

                temp.controllers.routes.javascript.IAttributeTypeController.createSimpleAttribute(),
                temp.controllers.routes.javascript.IAttributeTypeController.cloneSimpleAttribute(),
                temp.controllers.routes.javascript.IAttributeTypeController.getSimpleAttributeById(),
                temp.controllers.routes.javascript.IAttributeTypeController.updateSimpleAttribute(),
                temp.controllers.routes.javascript.IAttributeTypeController.deleteSimpleAttribute(),

                temp.controllers.routes.javascript.IAttributeTypeController.createComponent(),
                temp.controllers.routes.javascript.IAttributeTypeController.cloneComponent(),
                temp.controllers.routes.javascript.IAttributeTypeController.getComponentById(),
                temp.controllers.routes.javascript.IAttributeTypeController.updateComponent(),
                temp.controllers.routes.javascript.IAttributeTypeController.deleteComponent(),


                temp.controllers.routes.javascript.IAttributeTypeController.getPrimitiveTypes(),
                temp.controllers.routes.javascript.IAttributeTypeController.getSelect2AttributeTypes(),

                temp.controllers.routes.javascript.ICfsController.getCfsById(),
                temp.controllers.routes.javascript.ICfsController.create(),
                temp.controllers.routes.javascript.ICfsController.cloneCfsWithRelations(),
                temp.controllers.routes.javascript.ICfsController.cloneCfsWithoutRelations(),
                temp.controllers.routes.javascript.ICfsController.update(),
                temp.controllers.routes.javascript.ICfsController.delete(),

                temp.controllers.routes.javascript.IRfsController.create(),
                temp.controllers.routes.javascript.IRfsController.update(),
                temp.controllers.routes.javascript.IRfsController.cloneRfsWithRelations(),
                temp.controllers.routes.javascript.IRfsController.cloneRfsWithoutRelations(),
                temp.controllers.routes.javascript.IRfsController.delete(),
                temp.controllers.routes.javascript.IRfsController.getRfsById(),

                temp.controllers.routes.javascript.IResourceController.create(),
                temp.controllers.routes.javascript.IResourceController.update(),
                temp.controllers.routes.javascript.IResourceController.delete(),
                temp.controllers.routes.javascript.IResourceController.cloneResourceWithRelations(),
                temp.controllers.routes.javascript.IResourceController.cloneResourceWithoutRelations(),
                temp.controllers.routes.javascript.IResourceController.getResourceById(),

                temp.controllers.routes.javascript.IVersionController.createVersion(),
                temp.controllers.routes.javascript.IVersionController.cloneVersion(),
                temp.controllers.routes.javascript.IVersionController.updateVersion(),
                temp.controllers.routes.javascript.IVersionController.deleteVersion(),
                temp.controllers.routes.javascript.IVersionController.getStateMachine(),
                temp.controllers.routes.javascript.IVersionController.changeVersionState(),
                temp.controllers.routes.javascript.IVersionController.select2GetAllVersions(),
                temp.controllers.routes.javascript.IVersionController.getVersionById(),

                temp.controllers.routes.javascript.IVersionDTController.versionTableConfig(),
                temp.controllers.routes.javascript.IVersionDTController.entitiesTableConfig(),

                temp.controllers.routes.javascript.IEntityDTController.cfsTableConfig(),
                temp.controllers.routes.javascript.IEntityDTController.rfsTableConfig(),
                temp.controllers.routes.javascript.IEntityDTController.resourceTableConfig(),
                temp.controllers.routes.javascript.IEntityDTController.viewAttributesTableConfig(),

                temp.controllers.routes.javascript.ICompDiagramController.getNewEntityCompositionDiagram(),
                temp.controllers.routes.javascript.ICompDiagramController.getEntityCompositionDiagram(),
                temp.controllers.routes.javascript.ICompDiagramSidebarController.getSideBarEntities()

        ));
    }
}