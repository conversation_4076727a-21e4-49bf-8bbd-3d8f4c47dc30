package nossis.tsccatalog.requests.backend.payloadElements;

import nossis.tsccatalog.requests.backend.SearchRequestPayload;

public abstract class Attribute {
    private final String name;
    private final String operator;

    public Attribute(String name, SearchRequestPayload.Operator operator) {
        this.name = name;
        this.operator = operator.getValue();
    }


    public String getName() {
        return name;
    }

    public String getOperator() {
        return operator;
    }
}