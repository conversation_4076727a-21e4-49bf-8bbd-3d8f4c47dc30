package nossis.tsccatalog.settings;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import controllers.Assets;
import nossis.tsccatalog.controllers.routes;
import play.mvc.Http;
import play.twirl.api.Html;

import javax.inject.Singleton;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static nossis.tsccatalog.controllers.routes.Assets;

/**
 * <AUTHOR> <<EMAIL>>, 02/02/21.
 */
@Singleton
public class AppPage {

    private static final play.Logger.ALogger LOGGER = play.Logger.of("catalog");

    private static final String PATH_JS = "js";
    private static final String PATH_CSS = "css";
    private static final String PATH_RESUME_JS = "resume.js";
    private static final String PATH_RESUME_CSS = "resume.css";

    private static final String PATH_CDN_URL = "cdn.url";

    private final Config config = ConfigFactory.load().getConfig(CatalogConstants.CONFIG_PREFIX_ASSETS);

    public Html getPage(){
        return Html.apply("<!DOCTYPE html>" +
            "<html lang=\""+ Http.Context.current().lang().code() +"\" data-app-theme=\"nossis-one\">" +
                "<head>" +
                    "<meta charset=\"UTF-8\">" +
                    String.join("", getCSSAsHtml()) +
                    String.join("", getJsScriptsAsHtml()) +
                "</head>" +
                "<body></body>" +
            "</html>"
        );
    }

    public Html getResumePage(){
        return Html.apply("<!DOCTYPE html>" +
            "<html lang=\""+ Http.Context.current().lang().code() +"\" data-app-theme=\"nossis-one\">" +
            "<head>" +
            "<meta charset=\"UTF-8\">" +
            String.join("", getResumeCSSAsHtml()) +
            String.join("", getResumeJsScriptsAsHtml()) +
            "</head>" +
            "<body>" +
            "<x-resume-section button-action-go-to-module=\"entities-catalog\" module=\"nossis.tsccatalog\"></x-resume-section>" +
            "</body>" +
            "</html>"
        );
    }

    public void validate(){
        List<String> errorList = new ArrayList<>();
        validateStringList(PATH_JS, errorList);
        validateStringList(PATH_CSS, errorList);
        if (!errorList.isEmpty()){
            String errors = String.join("\n  ", errorList);
            final String errorMessage = "error validating "+ this.getClass().getSimpleName() +": \n" + errors;
            LOGGER.error(errorMessage);
            throw new RuntimeException(errorMessage);
        }
    }

    private List<String> getJsScripts(){
        List<String> urls = new ArrayList<>();
        urls.add(routes.Application.javascriptRoutes().path());
        urls.add(tsc.base.controllers.routes.Application.javascriptRoutes().path());
        urls.addAll(getStrings(PATH_JS));
        return urls;

    }

    private List<String> getJsScriptsAsHtml(){
        return getJsScripts().stream().map(js -> "<script defer src=\""+js+"\"></script>").collect(Collectors.toList());
    }

    private List<String> getResumeJsScripts(){
        List<String> urls = new ArrayList<>();
        urls.add(tsc.base.controllers.routes.Application.javascriptRoutes().path());
        urls.addAll(getStrings(PATH_RESUME_JS));
        return urls;

    }

    private List<String> getResumeJsScriptsAsHtml(){
        return getResumeJsScripts().stream().map(js -> "<script defer src=\""+js+"\"></script>").collect(Collectors.toList());
    }

    private List<String> getCSS(){
        return getStrings(PATH_CSS);
    }
    private List<String> getCSSAsHtml(){
        return getCSS().stream().map(css -> "<link rel=\"stylesheet\" href=\""+css+"\"/>").collect(Collectors.toList());
    }

    private List<String> getResumeCSS(){
        return getStrings(PATH_RESUME_CSS);
    }
    private List<String> getResumeCSSAsHtml(){
        return getResumeCSS().stream().map(css -> "<link rel=\"stylesheet\" href=\""+css+"\"/>").collect(Collectors.toList());
    }


    private List<String> getStrings(String path) {
        final List<String> list = config.getStringList(path);
        if (config.hasPath(PATH_CDN_URL)){
            return list.stream().map(addPrefix(config.getString(PATH_CDN_URL))).collect(Collectors.toList());
        }
        return list.stream().map(cssSheet -> Assets.versioned(new Assets.Asset(cssSheet)).path()).collect(Collectors.toList());
    }

    private static Function<String, String> addPrefix(String prefix) {
        final String updatedPrefix = prefix.endsWith("/") ? prefix : prefix + "/";
        return (path) -> updatedPrefix + (path.startsWith("/") ? path.substring(1) : path);
    }


    private void validateStringList(String path, List<String> errorList){
        if(!config.hasPath(path)){
            errorList.add("path " + CatalogConstants.CONFIG_PREFIX_ASSETS + "."+ path + " is required");
        } else try {
            config.getStringList(path);
        } catch (Exception e){
            errorList.add("path " + CatalogConstants.CONFIG_PREFIX_ASSETS + "."+ path + " must be a string list");
        }

    }
}
