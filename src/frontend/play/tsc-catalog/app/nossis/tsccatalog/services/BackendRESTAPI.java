package nossis.tsccatalog.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.Entity;
import nossis.tsccatalog.services.ws.NossisTSCServicesHelper;
import tsc.base.services.ws.Request;
import tsc.base.services.ws.RequestBuilder;
import tsc.base.services.ServicesHelper;
import tsc.base.utils.RestAPIUtils;
import play.Logger;
import play.libs.Json;
import play.mvc.Http;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletionStage;

final class BackendRESTAPI {

    @Inject
    private NossisTSCServicesHelper ws;

    private static Logger.ALogger CATALOG_LOGGER = Logger.of("catalog");

    private static final String SERVICE_CATALOG_ENTITY = getServicesSuffix() +"/{entity}";
    private static final String SERVICE_CATALOG_NOSSIS_TSC_ENTITY = getServicesSuffix() + "/nossistsc/{entity}";
    private static final String SERVICE_CATALOG_NOSSIS_TSC_ENTITY_ANCESTORS = SERVICE_CATALOG_NOSSIS_TSC_ENTITY + "/{id}/ancestors";
    private static final String SERVICE_CATALOG_INSTANCE = SERVICE_CATALOG_ENTITY + "/{id}";
    private static final String SERVICE_CATALOG_TEMPLATE = SERVICE_CATALOG_ENTITY + "/template/{id}";
    private static final String SERVICE_CATALOG_INSTANCE_CLONE = SERVICE_CATALOG_NOSSIS_TSC_ENTITY + "/{id}/clone";
    private static final String SERVICE_CATALOG_INSTANCE_WITH_RELATIONS_CLONE = SERVICE_CATALOG_NOSSIS_TSC_ENTITY + "/{id}/clone/{cloneRelations}";

    private static final Map<EntityType, String> ENTITY_SEARCH_SERVICES;
    private static final Map<EntityType, String> ENTITY_INSTANCE_SERVICES;


    static {
        ENTITY_SEARCH_SERVICES = new HashMap<>();
        ENTITY_INSTANCE_SERVICES = new HashMap<>();

        ENTITY_SEARCH_SERVICES.put(EntityType.AGGREGATED_TYPE, "search/aggregatedTypes");
        ENTITY_SEARCH_SERVICES.put(EntityType.AGGREGATED_ENTITY, "search/aggregatedEntities");
        ENTITY_SEARCH_SERVICES.put(EntityType.CFS, "search/cfs");
        ENTITY_SEARCH_SERVICES.put(EntityType.RFS, "search/rfs");
        ENTITY_SEARCH_SERVICES.put(EntityType.RESOURCE, "search/resources");
        ENTITY_SEARCH_SERVICES.put(EntityType.VERSION, "versions");
        ENTITY_SEARCH_SERVICES.put(EntityType.TAGS, "tags");

        ENTITY_INSTANCE_SERVICES.put(EntityType.AGGREGATED_TYPE, "aggregatedTypes");
        ENTITY_INSTANCE_SERVICES.put(EntityType.SIMPLE_ATTRIBUTE, "attributeTypes");
        ENTITY_INSTANCE_SERVICES.put(EntityType.COMPONENT, "components");
        ENTITY_INSTANCE_SERVICES.put(EntityType.CFS, "cfs");
        ENTITY_INSTANCE_SERVICES.put(EntityType.RFS, "rfs");
        ENTITY_INSTANCE_SERVICES.put(EntityType.RESOURCE, "resources");
        ENTITY_INSTANCE_SERVICES.put(EntityType.VERSION, "versions");
        ENTITY_INSTANCE_SERVICES.put(EntityType.TAGS, "tags");
    }

    String parseUrl(String url, String entity, Long id){
        return parseUrl(url, entity, String.valueOf(id));
    }

    String parseUrl(String url, String entity, String id){
        return url.replace("{entity}", entity).replace("{id}", id);
    }

    String parseUrl(String url, String entity, String id, boolean cloneRelations){
        return url.replace("{entity}", entity).replace("{id}", id).replace("{cloneRelations}", String.valueOf(cloneRelations));
    }

    CompletionStage<ArrayNode> getAncestorsIds(final EntityType entityType, final Long id) {
        String urlService = parseUrl(SERVICE_CATALOG_NOSSIS_TSC_ENTITY_ANCESTORS, ENTITY_INSTANCE_SERVICES.get(entityType), id);

        CATALOG_LOGGER.debug("uriService: " + urlService);

        final Request<ArrayNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .responseAs(ArrayNode.class)
                .build();

        return ws.sendGet(request);
    }


    CompletionStage<JsonNode> createEntity(final Entity newEntity, final EntityType entityType) {
        String urlService = RestAPIUtils.parseUrlWithId(SERVICE_CATALOG_ENTITY, ENTITY_INSTANCE_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(Json.toJson(newEntity))
                .responseAs(JsonNode.class)
                .build();

        return ws.sendPost(request);

    }

    CompletionStage<JsonNode> updateEntity(final Long id, final Entity updatedEntity, final EntityType entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), id);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(Json.toJson(updatedEntity))
                .responseAs(JsonNode.class)
                .build();

        return ws.sendPut(request);
    }

    CompletionStage<JsonNode> cloneEntity(final Long id, final EntityType entityType) {
        return cloneEntity(id, entityType, null);
    }

    CompletionStage<JsonNode> cloneEntity(final Long id, final EntityType entityType, final Entity cloneEntity) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE_CLONE, ENTITY_INSTANCE_SERVICES.get(entityType), id);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(cloneEntity != null ? Json.toJson(cloneEntity) : null)
                .responseAs(JsonNode.class)
                .build();

        return ws.sendPost(request);
    }

    CompletionStage<JsonNode> cloneEntityWithRelations(final Long id, final EntityType entityType) {
        return cloneEntityWithRelations(id, entityType, true);
    }

    CompletionStage<JsonNode> cloneEntityWithRelations(final Long id, final EntityType entityType, final boolean cloneRelations) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE_WITH_RELATIONS_CLONE, ENTITY_INSTANCE_SERVICES.get(entityType), String.valueOf(id), cloneRelations);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addHeader(Http.HeaderNames.CONTENT_TYPE, Http.MimeTypes.JSON)
                .responseAs(JsonNode.class)
                .build();

        return ws.sendPost(request);
    }

    CompletionStage<Boolean> deleteEntity(final Long id, final EntityType entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), id);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .responseAs(JsonNode.class)
                .build();

        return ws.sendDelete(request);
    }

    CompletionStage<JsonNode> getEntityInstanceOfTypeById(final EntityType entityType, final long entityId) {
        return getEntityInstanceOfTypeById(entityType, entityId, new HashMap<>());
    }

    CompletionStage<JsonNode> getEntityInstanceOfTypeById(final EntityType entityType, final Long entityId, final Map<String, String> queryParameters) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), entityId);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(queryParameters)
                .build();

        return ws.sendGet(request);
    }

    CompletionStage<JsonNode> getEntitiesOfType(final EntityType entityType, final Map<String, String> queryParameters) {
        String urlService = RestAPIUtils.parseUrlWithId(SERVICE_CATALOG_ENTITY, ENTITY_SEARCH_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(queryParameters)
                .build();

        return ws.sendGet(request);
    }

    CompletionStage<JsonNode> getEntitiesOfTypeWithPayload(final EntityType<?> entityType, final Map<String, String> queryParameters, final JsonNode requestPayload) {
        String urlService = RestAPIUtils.parseUrl(SERVICE_CATALOG_ENTITY, ENTITY_SEARCH_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(queryParameters)
                .setPayloadAsJson(requestPayload)
                .build();

        return ws.sendPost(request);
    }

    <T extends Entity>  CompletionStage<T> getEntityTemplateById(final JsonNode jsonNode, final Long idCatEntity, final EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_TEMPLATE, ENTITY_INSTANCE_SERVICES.get(entityType), idCatEntity);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final RequestBuilder<T> builder = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(jsonNode)
                .responseAs(entityType.getEntityClass());

        return ws.sendPost(builder.build());
    }

    public static String getServicesSuffix() {
        // TODO Confirm this is the right tag context path
        final Config config = ConfigFactory.load();
        if (!config.hasPath("nossis.tsccatalog.context.path")) {
            return "/netwin";
        }
        final String suffix = config.getString("nossis.tsccatalog.context.path");
        if (!suffix.startsWith("/")) {
            return "/" + suffix;
        }
        return suffix;
    }
}
