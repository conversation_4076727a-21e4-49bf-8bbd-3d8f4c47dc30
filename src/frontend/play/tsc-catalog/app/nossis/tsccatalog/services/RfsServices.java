package nossis.tsccatalog.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import play.libs.concurrent.HttpExecutionContext;
import tsc.catalog.basemodule.models.nossisinv.attributes.UIContainer;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.diagram.EntityWithDiagram;
import nossis.tsccatalog.models.entities.Relation;
import nossis.tsccatalog.models.entities.Rfs;
import nossis.tsccatalog.models.responses.RfsResponse;
import tsc.base.models.tables.query.TableQuery;
import tsc.base.models.tables.response.TableResponse;
import nossis.tsccatalog.settings.CatalogConstants;
import tsc.base.utils.ODataQueryParametersBuilder;
import nossis.tsccatalog.utils.SearchPayLoadUtils;
import play.Logger;
import play.libs.Json;
import play.mvc.Http;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext;

import javax.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;

import static nossis.tsccatalog.settings.CatalogConstants.ResourceAttributes;

public final class RfsServices extends EntityServices<RfsResponse> implements EntityWithHeritage, SearchableService {

    private static final Logger.ALogger LOGGER = Logger.of("catalog");

    private final SearchPayLoadUtils searchPayLoadUtils;

    @Inject
    public RfsServices(BackendRESTAPI restApi,
                       HttpExecutionContext ec,
                       CatEntityAPIServices catEntityAPIServices,
                       StateServices stateServices,
                       AttributeTypeServices attributeTypeServices,
                       SearchPayLoadUtils searchPayLoadUtils) {
        super(restApi, ec, catEntityAPIServices, stateServices, attributeTypeServices);
        this.searchPayLoadUtils = searchPayLoadUtils;
    }

    @Override
    RfsResponse parseDynamicFields(RfsResponse rfsResponse) {
        final CatEntity catEntity = catEntityAPIServices.getCatEntityByName(EntityType.RFS);
        final String language = Http.Context.current().lang().language();
        final String catEntityName = catEntity.getI18n(language);

        rfsResponse.getEntries().forEach(rfs -> rfs.setCatEntityName(catEntityName));
        return rfsResponse;
    }

    @Override
    public CompletionStage<List<Long>> getAncestorsIds(Long id) {
        return this.getAncestorsIds(EntityType.RFS, id);
    }

    // --------------
    // --- Search ---
    // --------------

    public CompletionStage<TableResponse> search(TableQuery<JsonNode> tableQuery) {

        final ODataQueryParametersBuilder queryParametersBuilder = ODataQueryParametersBuilder
                .get()
                .top(tableQuery)
                .skip(tableQuery)
                .orderBy(tableQuery)
                .expand(ResourceAttributes.TAGS)
                .expand(ResourceAttributes.VERSION)
                .exclude(ResourceAttributes.ATTRIBUTES)
                .exclude(ResourceAttributes.PARENT_CFS)
                .exclude(ResourceAttributes.PARENT_RFS)
                .exclude(ResourceAttributes.CHILD_RFS)
                .exclude(ResourceAttributes.CHILD_RESOURCES);

        final ObjectNode payload = searchPayLoadUtils.buildSearchPayload(tableQuery.getParameters(), EntityType.RFS, false);

        return restApi.getEntitiesOfTypeWithPayload(EntityType.RFS, queryParametersBuilder.build(), payload)
                .thenApplyAsync(jsonResponse -> Json.fromJson(jsonResponse, RfsResponse.class), ec.current())
                .thenApplyAsync(this::parseDynamicFields, ec.current())
                .thenApply(rfsResponse -> TableResponse.fromQueryAndResponse(tableQuery,rfsResponse.getEntries(),rfsResponse.getCount()));
    }


    // --------------
    // --- Create ---
    // --------------

    public CompletionStage<JsonNode> create(JsonNode rfsNode) {
        final Rfs rfs = Json.fromJson(rfsNode, Rfs.class);
        return restApi.createEntity(rfs, EntityType.RFS);
    }

    // --------------
    // --- Update ---
    // --------------

    public CompletionStage<JsonNode> update(Long id, JsonNode rfsNode) {
        final Rfs rfs = Json.fromJson(rfsNode, Rfs.class);
        return restApi.updateEntity(id, rfs, EntityType.RFS);
    }

    // --------------
    // --- Clone ---
    // --------------

    public CompletionStage<JsonNode> clone(Long id, boolean cloneRelations) {
        return restApi.cloneEntityWithRelations(id, EntityType.RFS, cloneRelations);
    }

    // --------------
    // --- Delete ---
    // --------------

    public CompletionStage<Boolean> delete(Long id) {
        return restApi.deleteEntity(id, EntityType.RFS);
    }

    // -----------
    // --- Get ---
    // -----------

    public CompletionStage<Rfs> getRfsById(Long id, ODataQueryParametersBuilder oDataBuilder) {
        return restApi.getEntityInstanceOfTypeById(EntityType.RFS, id, oDataBuilder.build())
                .thenApplyAsync(jsonNode -> Json.fromJson(jsonNode, Rfs.class));
    }


    // -----------------------------------------------------------------------------------------------------------------
    // Composition Diagram
    // -----------------------------------------------------------------------------------------------------------------

    /**
     * build the list of a rfs parents
     */
    public CompletionStage<EntityWithDiagram<?>> navigateRfsParent(final EntityWithDiagram<?> diagram, Relation relation) {

        final String parentRfsId = relation.getParentEntity().getId();

        final ODataQueryParametersBuilder oDataBuilder = ODataQueryParametersBuilder
                .get()
                .exclude(CatalogConstants.ResourceAttributes.TAGS)
                .exclude(CatalogConstants.ResourceAttributes.VERSION)
                .exclude(CatalogConstants.ResourceAttributes.ATTRIBUTES)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_CFS)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_RFS)
                .exclude(CatalogConstants.ResourceAttributes.CHILD_RFS)
                .exclude(CatalogConstants.ResourceAttributes.CHILD_RESOURCES);

        return getRfsById(Long.parseLong(parentRfsId), oDataBuilder).thenApplyAsync(parentEntity -> {
            diagram.addRfs(parentEntity);
            return diagram;
        }, ec.current());
    }

    /**
     * navigate recursively through the rfs relations, building the child rfs diagram
     */
    public CompletionStage<Rfs> navigateRfsChild(EntityWithDiagram<?> diagram, Relation relation) {

        final String rfsId = relation.getChildEntity().getId();

        final ODataQueryParametersBuilder oDataBuilder = ODataQueryParametersBuilder
                .get()
                .exclude(CatalogConstants.ResourceAttributes.TAGS)
                .exclude(CatalogConstants.ResourceAttributes.VERSION)
                .exclude(CatalogConstants.ResourceAttributes.ATTRIBUTES)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_CFS)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_RFS)
                .expand(CatalogConstants.ResourceAttributes.CHILD_RFS)
                .expand(CatalogConstants.ResourceAttributes.CHILD_RESOURCES);

        return getRfsById(Long.parseLong(rfsId), oDataBuilder).thenApplyAsync(childEntity -> {
            diagram.addRfs(childEntity);
            return childEntity;
        }, ec.current());
    }


    public static class RfsUIContainer{
        private final Rfs rfs;
        private final UIContainer container;

        private RfsUIContainer(Rfs rfs, UIContainer container) {
            this.rfs = rfs;
            this.container = container;
        }

        public static RfsUIContainer of(Rfs rfs, UIContainer container){
            return new RfsUIContainer(rfs,container);
        }

        public Rfs getRfs() {
            return rfs;
        }

        public UIContainer getUIContainer() {
            return container;
        }
    }
}