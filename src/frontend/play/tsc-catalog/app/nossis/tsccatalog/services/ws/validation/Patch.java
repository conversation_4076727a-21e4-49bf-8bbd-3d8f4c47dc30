package nossis.tsccatalog.services.ws.validation;

import play.mvc.Http;

public class Patch implements ResponseValidation {

    private static final int[] SUCCESS = new int[]{ Http.Status.NO_CONTENT };

    public enum ErrorCode {
        UNAUTHORIZED(Http.Status.UNAUTHORIZED),
        FORBIDDEN(Http.Status.FORBIDDEN),
        METHOD_NOT_ALLOWED(Http.Status.METHOD_NOT_ALLOWED),
        NOT_ACCEPTABLE(Http.Status.NOT_ACCEPTABLE),
        CONFLICT(Http.Status.CONFLICT),
        PRECONDITION_FAILED(Http.Status.PRECONDITION_FAILED),
        UNSUPPORTED_MEDIA_TYPE(Http.Status.UNSUPPORTED_MEDIA_TYPE),
        INTERNAL_SERVER_ERROR(Http.Status.INTERNAL_SERVER_ERROR);

        private int status;

        ErrorCode(int status) {
            this.status = status;
        }

        public static boolean contains(final int code) {
            for (ErrorCode errorCode : ErrorCode.values()) {
                if (errorCode.status == code)
                    return true;
            }
            return false;
        }
    }


    @Override
    public boolean isExpectedSuccessStatusCode(int code) {
        for (int SUCCES : SUCCESS) {
            if (SUCCES == code) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public boolean isExpectedErrorStatusCode(int code) {
        return ErrorCode.contains(code);
    }

}
