package temp.services;

import com.google.inject.ImplementedBy;
import nossis.tsccatalog.models.entities.Version;
import nossis.tsccatalog.models.statemachine.StateMachine;
import temp.model.SelectResponse;
import temp.services.impl.VersionService;
import tsc.base.models.selectbox.SelectBoxQuery;

import java.util.concurrent.CompletionStage;

/**
 * Version services definitions
 */
@ImplementedBy(VersionService.class)
public interface IVersionService {

    /**
     * Gets a {@link Version} by an specific Id.
     *
     * @param versionId Id of the {@link Version}
     * @return A {@link CompletionStage} with a {@link Version} identified by the versionId
     */
    CompletionStage<Version> getById(Long versionId);

    /**
     * Creates and persists a new {@link Version} entity.
     *
     * @param version The {@link Version} data to be persisted
     * @return A {@link CompletionStage} containing the new {@link Version}
     */
    CompletionStage<Version> create(Version version);

    /**
     * Clones and persists a {@link Version} based on an existing version.
     * This method will create a new version based on a supplied version id
     * with an altered name (Clone_nameOfTheVersionBeingCloned)
     *
     * @param versionId Id of the {@link Version} used as base for the new version
     * @return A {@link CompletionStage} containing the new {@link Version}
     */
    CompletionStage<Version> clone(Long versionId);

    /**
     * Clones and persists a {@link Version} based on an existing version.
     * This method will create a new version based on a supplied version model
     * containing an already existing id. the version will inherit all characteristics
     * of the cloned version, but will have its base characteristics as supplied in the
     * model.
     *
     * @param version Model containing an altered persisted {@link Version}
     * @return A {@link CompletionStage} containing the new {@link Version}
     */
    CompletionStage<Version> clone(Version version);

    /**
     * Updates a specific {@link Version} based on the version Id.
     *
     * @param versionId Id of the {@link Version} to be updated
     * @param updatedVersion The {@link Version} data to be updated
     * @return A {@link CompletionStage} containing the updated {@link Version}
     */
    CompletionStage<Boolean> update(Long versionId, Version updatedVersion);

    /**
     * Deletes a specific {@link Version} based on the version Id.
     *
     * @param versionId Id of the {@link Version} to be updated
     * @return A {@link CompletionStage} containing a boolean indicating if the operation was successful
     */
    CompletionStage<Boolean> delete(Long versionId);

    /**
     * Gets a list with {@link Version Versions} based on a {@link SelectBoxQuery}.
     *
     * @param selectBoxQuery A <a href="https://select2.org/data-sources/ajax">Select2</a> {@link SelectBoxQuery}
     * @return A {@link CompletionStage} with a list of {@link Version Versions}
     */
    CompletionStage<SelectResponse<Version>> select2GetAll(SelectBoxQuery selectBoxQuery);

    /**
     * Updates a {@link Version} state, according to the {@link StateMachine}, the new state id and the version id.
     *
     * @param versionId Id of the {@link Version} to have its state be updated
     * @param stateId Id of the new state
     * @return A {@link CompletionStage} containing the updated {@link Version}
     */
    CompletionStage<Boolean> changeVersionState(Long versionId, Long stateId);

    /**
     * Gets a {@link StateMachine}.
     *
     * @return A {@link CompletionStage} containing the {@link StateMachine}
     */
    StateMachine getStateMachine();

}
