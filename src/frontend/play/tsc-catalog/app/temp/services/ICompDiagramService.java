package temp.services;

import com.google.inject.ImplementedBy;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.diagram.EntityWithDiagram;
import nossis.tsccatalog.models.entities.EntityWithAttributes;
import nossis.tsccatalog.models.entities.TaggedAndVersionedEntity;
import temp.services.impl.CompDiagramService;

import java.util.concurrent.CompletionStage;

@ImplementedBy(CompDiagramService.class)
public interface ICompDiagramService {
    /**
     * Obtains a new {@link EntityWithDiagram<>} to an entity type and version.
     *
     * @param entityType type of the entity, received trough path parameters
     * @param versionId id of the {@link nossis.tsccatalog.models.entities.Version}, received trough path parameters
     *
     * @return A {@link CompletionStage} containing the new (empty) {@link EntityWithDiagram<>}.
     */
    <T extends EntityWithAttributes> CompletionStage<EntityWithDiagram<?>> getEmptyCompositionDiagram(final EntityType<T> entityType, Long versionId);

    /**
     * Obtains a new {@link <T extends TaggedAndVersionedEntity>} to an entity type and version (by id).
     *
     * @param entityType type of the entity, received trough path parameters
     * @param versionId id of the {@link nossis.tsccatalog.models.entities.Version}, received trough path parameters
     *
     * @return A {@link CompletionStage} containing the new (empty) {@link T} .
     */
    <T extends TaggedAndVersionedEntity> CompletionStage<T> getEntityTemplateWithVersion(final EntityType<T> entityType, final Long versionId);

    /**
     * Obtains the {@link EntityWithDiagram<>} (composition diagram) to a existent entity.
     *
     * @param id id of the entity, received trough path parameters
     * @param entityType type of the entity, received trough path parameters
     *
     * @return A {@link CompletionStage} containing the required {@link EntityWithDiagram<>}.
     */
    <T extends EntityWithAttributes> CompletionStage<EntityWithDiagram<?>> getCompositionDiagram(Long id, final EntityType<T> entityType);
}
