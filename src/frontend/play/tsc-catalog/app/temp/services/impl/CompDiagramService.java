package temp.services.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.diagram.EntityWithDiagram;
import nossis.tsccatalog.models.entities.EntityWithAttributes;
import nossis.tsccatalog.models.entities.TaggedAndVersionedEntity;
import nossis.tsccatalog.models.entities.Version;
import nossis.tsccatalog.services.CatEntityAPIServices;
import nossis.tsccatalog.settings.CatalogConstants;
import play.libs.Json;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import temp.client.IBackendClient;
import temp.services.ICompDiagramService;
import tsc.base.utils.ODataQueryParametersBuilder;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.concurrent.CompletionStage;

@Singleton
public class CompDiagramService implements ICompDiagramService {


    private final CatEntityAPIServices catEntityAPIServices;
    private final FillDiagramWithCfsService fillDiagramWithCfsService;
    private final FillDiagramWithRfsService fillDiagramWithRfsService;
    private final FillDiagramWithResourceService fillDiagramWithResourceService;
    private final IBackendClient backendClient;

    @Inject
    public CompDiagramService(CatEntityAPIServices catEntityAPIServices, IBackendClient backendClient,
                              FillDiagramWithCfsService FillDiagramWithCfsService,
                              FillDiagramWithRfsService fillDiagramWithRfsService,
                              FillDiagramWithResourceService fillDiagramWithResourceService) {
        this.backendClient = backendClient;
        this.catEntityAPIServices = catEntityAPIServices;
        this.fillDiagramWithCfsService = FillDiagramWithCfsService;
        this.fillDiagramWithRfsService = fillDiagramWithRfsService;
        this.fillDiagramWithResourceService = fillDiagramWithResourceService;
    }

    @Override
    public <T extends EntityWithAttributes> CompletionStage<EntityWithDiagram<?>> getEmptyCompositionDiagram(final EntityType<T> entityType, Long versionId) {
        return getEntityTemplateWithVersion(entityType, versionId).thenApply(EntityWithDiagram::new);
    }

    @Override
    public <T extends TaggedAndVersionedEntity> CompletionStage<T> getEntityTemplateWithVersion(final EntityType<T> entityType, final Long versionId) {
        final CatEntity catEntity = catEntityAPIServices.getCatEntityByName(entityType);
        final ObjectNode jsonPayload = Json.newObject();

        final Version currentVersion = new Version();
        currentVersion.setId(Long.toString(versionId));

        return backendClient.getEntityTemplateById(jsonPayload, catEntity.getId(), entityType)
                .thenApply(entity -> {
                    entity.setVersion(currentVersion);
                    return entity;
                });
    }

    public <T extends EntityWithAttributes> CompletionStage<EntityWithDiagram<?>> getCompositionDiagram(Long id, final EntityType<T> entityType) {
        return backendClient.getEntityInstanceOfTypeById(id, setRelationOdataBuilder(entityType).build(), entityType)
                .thenApply(root -> {
                    final EntityWithDiagram<T> entityWithDiagram = new EntityWithDiagram<>(root);
                    fillDiagramWithCfsService.fillWithParents(root, entityWithDiagram);
                    fillDiagramWithRfsService.fillWithParents(root, entityWithDiagram);
                    fillDiagramWithResourceService.fillWithParents(root, entityWithDiagram);
                    fillDiagramWithCfsService.fillWithChildren(root, entityWithDiagram);
                    fillDiagramWithRfsService.fillWithChildren(root, entityWithDiagram);
                    fillDiagramWithResourceService.fillWithChildren(root, entityWithDiagram);
                    return entityWithDiagram;
                });
    }

    private ODataQueryParametersBuilder setRelationOdataBuilder(EntityType<?> entityType) {
        final  ODataQueryParametersBuilder oDataBuilder = ODataQueryParametersBuilder
                .get()
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTE_CONSTRAINTS_VALUE_LIST)
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTES_COMPONENT_TYPE);

        String entityName = entityType.getEntityName();
        switch (entityName) {
            case "CFS":
                oDataBuilder
                        .expand(CatalogConstants.ResourceAttributes.CFS_PARENT_CFS)
                        .expand(CatalogConstants.ResourceAttributes.CFS_CHILD_CFS)
                        .expand(CatalogConstants.ResourceAttributes.CFS_CHILD_RFS)
                        .expand(CatalogConstants.ResourceAttributes.CFS_CHILD_RESOURCES);
                break;
            case "RFS":
                oDataBuilder
                        .expand(CatalogConstants.ResourceAttributes.RFS_PARENT_CFS)
                        .expand(CatalogConstants.ResourceAttributes.RFS_PARENT_RFS)
                        .expand(CatalogConstants.ResourceAttributes.RFS_CHILD_RFS)
                        .expand(CatalogConstants.ResourceAttributes.CHILD_RESOURCES);
                break;
            case "RESOURCE":
                oDataBuilder
                        .expand(CatalogConstants.ResourceAttributes.RESOURCE_PARENT_CFS)
                        .expand(CatalogConstants.ResourceAttributes.RESOURCE_PARENT_RFS)
                        .expand(CatalogConstants.ResourceAttributes.RESOURCE_PARENT_RESOURCES)
                        .expand(CatalogConstants.ResourceAttributes.RESOURCE_CHILD_RESOURCES);
                break;
        }

        return oDataBuilder;
    }


}
