package temp.services;

import nossis.tsccatalog.models.diagram.EntityWithDiagram;
import nossis.tsccatalog.models.entities.Entity;
import nossis.tsccatalog.models.entities.EntityWithAttributes;
import nossis.tsccatalog.models.entities.Relation;

import java.util.List;
import java.util.concurrent.CompletionStage;

// Injected directly from implementations. No implementations are defined for this interface.
public interface IFillDiagramWithEntityService {
    /**
     * Fill diagram with {@link <T extends EntityWithAttributes>} entity parents.
     *
     * @param entity entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     * @param diagram  diagram of the entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     *
     */
    <T extends EntityWithAttributes> void fillWithParents(T entity, EntityWithDiagram<?> diagram);

    /**
     *  Method that will navigate trough the entity children of the root entity.
     *
     * @param relationList list of parent relations between entities and
     * the root entity {@link List<nossis.tsccatalog.models.entities.Relation>}
     * @param diagram  diagram of the entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     *
     */
    void navigateParent(List<Relation> relationList, EntityWithDiagram<?> diagram);

    /**
     * Fill diagram with {@link <T extends EntityWithAttributes>} entity children.
     *
     * @param entity entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     * @param diagram  diagram of the entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     *
     */
    <T extends EntityWithAttributes> void fillWithChildren(T entity, EntityWithDiagram<?> diagram);

    /**
     * Recursive method that will navigate trough the entity children of the root entity.
     *
     * @param relationList list of children relations between entities and
     * the root entity {@link List<nossis.tsccatalog.models.entities.Relation>}
     * @param diagram  diagram of the entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     *
     */
    void navigateChild(List<Relation> relationList, EntityWithDiagram<?> diagram);

    /**
     * Add to diagram an {@link <T extends EntityWithAttributes>} entity (that is either parent or children of
     * the root entity).
     *
     * @param diagram  diagram of the entity that extends {@link nossis.tsccatalog.models.entities.EntityWithAttributes}
     * @param entityToAdd The entity to add to the diagram  {@link nossis.tsccatalog.models.entities.Relation}
     *
     *   @return A {@link CompletionStage} containing the {@link ?} (the ? should be of type
     *   {@link <T extends EntityWithAttributes>}).
     */
    CompletionStage<?> addToDiagram(final EntityWithDiagram<?> diagram, Entity entityToAdd);
}
