package temp.actions;

import nossis.tsccatalog.settings.AAAPIResources;
import play.mvc.Action;
import play.mvc.Http;
import play.mvc.Result;
import pt.alticelabs.nossis.security.Security;
import pt.ptinovacao.nossis.security.aaapi.Authorization;
import pt.ptinovacao.nossis.security.aaapi.core.SecurityAAAPI;
import pt.ptinovacao.nossis.security.aaapi.exceptions.AuthenticationException;
import pt.ptinovacao.nossis.security.aaapi.exceptions.SecurityAAAPIException;
import pt.ptinovacao.nossis.security.aaapi.model.Permissions;
import pt.ptinovacao.nossis.security.aaapi.model.Resource;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>, 11/02/21.
 */

public class UpdatePermissionsAction extends Action<UpdatePermissions> {
    private static final String COOKIE_KEY = "tsc_permission";
    private static final String RESOURCE_SEPARATOR = ":";

    @Inject
    public UpdatePermissionsAction() {
    }

    public CompletionStage<Result> call(Http.Context context) {
        final Authorization authorization = SecurityAAAPI.getAuthorization();
        final String[] value = this.configuration.value();
        try {
            final Optional<Http.Cookie> currentCookie = context.response().cookie(COOKIE_KEY);
            if(currentCookie.isPresent() && value.length > 0){
                final Map<String, Resource> resources = authorization.getResources(Security.getSessionId(context), value);
                final String cookieValue = toCookieValue(resources, currentCookie.get().value());
                final Http.Cookie cookie = Http.Cookie.builder(COOKIE_KEY, cookieValue).build();
                context.response().setCookie(cookie);
            } else {
                final Map<String, Resource> resources = authorization.getResources(Security.getSessionId(context), AAAPIResources.ENTITY, AAAPIResources.VERSION);
                final String cookieValue = toCookieValue(resources);
                final Http.Cookie cookie = Http.Cookie.builder(COOKIE_KEY, cookieValue).build();
                context.response().setCookie(cookie);
            }
        } catch (SecurityAAAPIException e) {
            // unreachable code, since the @Authorized should handle it; however, in case @Authorized does not handle it,
            // we will handle it using nossis-ui-security-adapter
            return CompletableFuture.completedFuture(Security.getHandler().onAuthorizationFailure());
        } catch ( AuthenticationException e) {
            // also unreachable code, since the @Authorized should handle it; however, in case @Authorized does not handle it,
            // we will handle it using nossis-ui-security-adapter
            return CompletableFuture.completedFuture(Security.getHandler().onAuthenticationFailure());
        }
        return this.delegate.call(context);
    }

    private String resourceToString(Resource resource){
        StringBuilder permissions = new StringBuilder(resource.getName() + "|");
        if (resource.canCreate()) {
            permissions.append(Permissions.Create.value());
        }
        if (resource.canRead()) {
            permissions.append(Permissions.Read.value());
        }
        if (resource.canUpdate()) {
            permissions.append(Permissions.Update.value());
        }
        if (resource.canDelete()) {
            permissions.append(Permissions.Delete.value());
        }
        if (resource.canExecute()) {
            permissions.append(Permissions.Execute.value());
        }
        return permissions.toString();
    }

    private String toCookieValue(Map<String, Resource> resourceMap){
        return resourceMap.values().stream()
            .map(this::resourceToString)
            .collect(Collectors.joining(RESOURCE_SEPARATOR));
    }

    private String toCookieValue(Map<String, Resource> resourceMap, String previousValue){
        final Map<String, String> stringMap = Arrays.stream(previousValue.split(RESOURCE_SEPARATOR)).collect(Collectors.toMap(
            value -> value.split("\\|")[0],
            value -> value
        ));
        resourceMap.forEach((key, value) -> stringMap.put(key, resourceToString(value)));
        return String.join(RESOURCE_SEPARATOR, stringMap.values());
    }
}