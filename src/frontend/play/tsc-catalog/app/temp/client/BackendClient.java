package temp.client;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.Entity;
import nossis.tsccatalog.services.ws.NossisTSCServicesHelper;
import play.Logger;
import play.libs.Json;
import play.mvc.Http;
import pt.ptinovacao.nossis.exceptions.FaultException;
import temp.model.SelectResponse;
import tsc.base.services.ws.Request;
import tsc.base.services.ws.RequestBuilder;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;

import static nossis.tsccatalog.exceptions.ExceptionCodes.INVALID_MODEL;
import static temp.client.util.ClientDefinitions.*;

@Singleton
public class BackendClient implements IBackendClient {

    private final Logger.ALogger CATALOG_LOGGER = Logger.of(BackendClient.class.getSimpleName());

    private final NossisTSCServicesHelper ws;

    @Inject
    public BackendClient(NossisTSCServicesHelper ws) {
        this.ws = ws;
    }


    @Override
    public <T extends Entity> CompletionStage<T> getEntityInstanceOfTypeById(Long entityId, EntityType<T> entityType) {
        return getEntityInstanceOfTypeById(entityId, new HashMap<>(), entityType);
    }

    @Override
    public <T extends Entity> CompletionStage<T> getEntityInstanceOfTypeById(Long entityId, Map<String, String> queryParameters, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), entityId);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<T> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(queryParameters)
                .responseAs(entityType.getEntityClass())
                .build();

        return ws.sendGet(request);
    }

    @Override
    public <T extends Entity> CompletionStage<T> createEntity(T entity, EntityType<T> entityType) {
        return createEntity(entity, new HashMap<>(), entityType);
    }

    @Override
    public <T extends Entity> CompletionStage<T> createEntity(T entity, Map<String, String> queryParameters,EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_ENTITY, ENTITY_INSTANCE_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<T> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(Json.toJson(entity))
                .addQueryParameters(queryParameters)
                .responseAs(entityType.getEntityClass())
                .build();

        return ws.sendPost(request);
    }

    @Override
    public <T extends Entity> CompletionStage<T> cloneEntity(Long entityId, EntityType<T> entityType) {
        return cloneEntity(entityId, new HashMap<>(),null, entityType);
    }

    @Override
    public <T extends Entity> CompletionStage<T> cloneEntity(Long entityId, T entity, EntityType<T> entityType) {
        return cloneEntity(entityId, new HashMap<>(),entity, entityType);
    }

    @Override
    public <T extends Entity> CompletionStage<T> cloneEntity(Long entityId, Map<String, String> queryParameters, T entity, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE_CLONE, ENTITY_INSTANCE_SERVICES.get(entityType), entityId);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<T> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(entity != null ? Json.toJson(entity) : null)
                .addQueryParameters(queryParameters)
                .responseAs(entityType.getEntityClass())
                .build();

        return ws.sendPost(request);
    }

    @Override
    public <T extends Entity> CompletionStage<T> cloneEntity(final Long id, final EntityType<T> entityType, final boolean cloneRelations) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE_WITH_RELATIONS_CLONE,
                ENTITY_INSTANCE_SERVICES.get(entityType), String.valueOf(id), cloneRelations);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<T> request = RequestBuilder.start()
                .setUrl(urlService)
                .addHeader(Http.HeaderNames.CONTENT_TYPE, Http.MimeTypes.JSON)
                .responseAs(entityType.getEntityClass())
                .build();

        return ws.sendPost(request);
    }

    @Override
    public <T extends Entity> CompletionStage<Boolean> updateEntity(Long entityId, T entity, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), entityId);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<Boolean> request = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(Json.toJson(entity))
                .responseAs(Boolean.class)
                .build();

        return ws.sendPut(request);
    }

    @Override
    public <T extends Entity> CompletionStage<Boolean> deleteEntity(Long entityId, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_INSTANCE, ENTITY_INSTANCE_SERVICES.get(entityType), entityId);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .responseAs(JsonNode.class)
                .build();

        return ws.sendDelete(request);
    }

    public <T extends Entity> CompletionStage<SelectResponse<T>>  getEntitiesOfTypeWithPayload(EntityType<T> entityType, Map<String, String> parameters, JsonNode payload) {
        ObjectNode node = payload.deepCopy();
        return getEntitiesOfTypeWithPayload(parameters, node, entityType);
    }


    @Override
    public <T extends Entity> CompletionStage<SelectResponse<T>> getEntitiesOfTypeWithPayload(Map<String, String> parameters, ObjectNode payload, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_ENTITY, ENTITY_SEARCH_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(parameters)
                .setPayloadAsJson(payload)
                .responseAs(JsonNode.class)
                .build();

        return ws.sendPost(request).thenApply(node -> convertToSelectResponse(node, entityType));
    }

    @Override
    public <T extends Entity> CompletionStage<SelectResponse<T>> getEntitiesOfType(Map<String, String> parameters, EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_ENTITY, ENTITY_INSTANCE_SERVICES.get(entityType));
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final Request<JsonNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .addQueryParameters(parameters)
                .responseAs(JsonNode.class)
                .build();
        return ws.sendGet(request).thenApply(node -> convertToSelectResponse(node, entityType));
    }

    @Override
    public <T extends Entity>  CompletionStage<T> getEntityTemplateById(final JsonNode jsonNode, final Long idCatEntity, final EntityType<T> entityType) {
        String urlService = parseUrl(SERVICE_CATALOG_TEMPLATE, ENTITY_INSTANCE_SERVICES.get(entityType), idCatEntity);
        CATALOG_LOGGER.debug("serviceUri: " + urlService);

        final RequestBuilder<T> builder = RequestBuilder.start()
                .setUrl(urlService)
                .setPayloadAsJson(jsonNode)
                .responseAs(entityType.getEntityClass());

        return ws.sendPost(builder.build());
    }

    @Override
    public CompletionStage<ArrayNode> getAncestorsIds(final EntityType<?> entityType, final Long id) {
        String urlService = parseUrl(SERVICE_CATALOG_NOSSIS_TSC_ENTITY_ANCESTORS, ENTITY_INSTANCE_SERVICES.get(entityType), id);

        CATALOG_LOGGER.debug("uriService: " + urlService);

        final Request<ArrayNode> request = RequestBuilder.start()
                .setUrl(urlService)
                .responseAs(ArrayNode.class)
                .build();

        return ws.sendGet(request);
    }

    private String parseUrl(String url, String entity, Long id) {
        return parseUrl(url, entity, String.valueOf(id));
    }

    private String parseUrl(String url, String entity) {
        return url.replace("{entity}", entity);
    }

    private String parseUrl(String url, String entity, String id) {
        return url.replace("{entity}", entity).replace("{id}", id);
    }

    private String parseUrl(String url, String entity, String id, boolean cloneRelations){
        return url.replace("{entity}", entity).replace("{id}", id)
                .replace("{cloneRelations}", String.valueOf(cloneRelations));
    }

    private <T extends Entity> SelectResponse<T> convertToSelectResponse(JsonNode data, EntityType<T> entityType) {
        SelectResponse<T> selectResponse = new SelectResponse<>();
        JavaType type = Json.mapper().getTypeFactory().constructCollectionType(List.class, entityType.getEntityClass());
        ObjectReader reader = Json.mapper().readerFor(type);
        try{
            selectResponse.setOptions(reader.readValue(data.get(ENTITY_JSON_PROPERTY_SERVICES.get(entityType))));
            selectResponse.setCount(data.get("count").asInt());
        } catch (Exception ex) {
            throw new FaultException(INVALID_MODEL); //TODO change this error
        }
        return selectResponse;
    }
}
