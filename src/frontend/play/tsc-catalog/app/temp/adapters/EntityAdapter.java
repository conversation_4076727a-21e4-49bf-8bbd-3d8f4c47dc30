package temp.adapters;

import nossis.tsccatalog.exceptions.ExceptionCodes;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.Cfs;
import nossis.tsccatalog.models.entities.Entity;
import nossis.tsccatalog.models.entities.EntityWithAttributes;
import nossis.tsccatalog.models.entities.Resource;
import nossis.tsccatalog.models.entities.Rfs;
import nossis.tsccatalog.models.entities.TaggedAndVersionedEntity;
import nossis.tsccatalog.models.entities.Version;
import nossis.tsccatalog.services.CatEntityAPIServices;
import nossis.tsccatalog.settings.CatalogConstants;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import temp.dtos.EntityDto;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.List;
import java.util.stream.Collectors;

import static temp.adapters.AdapterHelper.preFilledBuilder;
import static temp.adapters.AdapterHelper.preFilledModel;
import static temp.adapters.AdapterHelper.tagAsStringFrom;
import static temp.adapters.AdapterHelper.tagFrom;

@Singleton
public class EntityAdapter {

    private final CatEntityAPIServices catServices;
    private final AttributeAdapter attributeAdapter;
    private final VersionAdapter versionAdapter;
    private final RelationAdapter relationAdapter;

    @Inject
    public EntityAdapter(CatEntityAPIServices catServices,
                         AttributeAdapter attributeAdapter,
                         VersionAdapter versionAdapter) {
        this.catServices = catServices;
        this.attributeAdapter = attributeAdapter;
        this.versionAdapter = versionAdapter;
        this.relationAdapter = new RelationAdapter(this);
    }

    public EntityAdapter(CatEntityAPIServices catServices, AttributeAdapter attributeAdapter, VersionAdapter versionAdapter, RelationAdapter relationAdapter) {
        this.catServices = catServices;
        this.attributeAdapter = attributeAdapter;
        this.versionAdapter = versionAdapter;
        this.relationAdapter = relationAdapter;
    }

    public EntityDto baseDtoFrom(Entity entity) {
        return preFilledBuilder(new EntityDto.Builder(), entity, catServices).build();
    }

    public EntityDto dtoWithRelationsFrom(Entity entity) {
        if(entity == null) return  null;
        switch (catServices.getCatEntityById(entity.getIdCatEntity()).getName()) {
            case CatalogConstants.Resources.CFS:
                return dtoFrom((Cfs) entity);
            case CatalogConstants.Resources.RFS:
                return dtoFrom((Rfs) entity);
            case CatalogConstants.Resources.RESOURCE:
                return dtoFrom((Resource) entity);
        }
        throw new BusinessException(ExceptionCodes.INVALID_MODEL);
    }

    public EntityDto dtoWithAttributesFrom(EntityWithAttributes entity) {
        if(entity == null) return  null;
        return preFilledEntityDtoBuilder(entity, false).build();
    }

    public List<EntityDto> simpleDtoFrom(List<? extends TaggedAndVersionedEntity> entities) {
        if(entities == null) return  null;
        return entities.stream().map(this::simpleDtoFrom).collect(Collectors.toList());
    }

    public Entity baseEntityFrom(EntityDto dto) {
        if(dto == null) return  null;
        Entity entity = preFilledModel(new Entity(), dto);
        switch(dto.getEntityType()) {
            case CatalogConstants.Resources.CFS:
                entity.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.CFS));
                break;
            case CatalogConstants.Resources.RFS:
                entity.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.RFS));
                break;
            case CatalogConstants.Resources.RESOURCE:
                entity.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.RESOURCE));
                break;
            default:
                throw new BusinessException(ExceptionCodes.INVALID_MODEL);
        }
        return entity;
    }

    private <T extends EntityWithAttributes> T preFilledEntity(T entity, EntityDto dto) {
        T model = preFilledModel(entity, dto);
        model.setVersion(versionAdapter.versionFrom(dto.getVersion()));
        model.setAttributes(attributeAdapter.attributeFrom(dto.getAttributes()));
        model.setTags(tagFrom(dto.getTags(), catServices));
        return model;
    }

    private <T extends EntityWithAttributes> EntityDto.Builder preFilledEntityDtoBuilder(T entity, Boolean ignoreFields) {
        Version version = entity.getVersion();
        if(version != null && version.getIdCatEntity() == null) {
            version.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.VERSION));
        }
        EntityDto.Builder builder = preFilledBuilder(new EntityDto.Builder(), entity, catServices)
                .setVersion(versionAdapter.dtoFrom(version));
        if (!ignoreFields) {
            builder.setAttributes(attributeAdapter.attributeDtoFrom(entity.getAttributes()))
                    .setTags(tagAsStringFrom(entity.getTags()));
        }
        return builder;
    }

    public Cfs cfsFrom(EntityDto dto) {
        if(dto == null) return null;
        Cfs cfs = preFilledEntity(new Cfs(), dto);
        cfs.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.CFS));
        cfs.setParentCfs(relationAdapter.relationFrom(dto.getParentCfs()));
        cfs.setChildCfs(relationAdapter.relationFrom(dto.getChildCfs()));
        cfs.setChildRfs(relationAdapter.relationFrom(dto.getChildRfs()));
        cfs.setChildResources(relationAdapter.relationFrom(dto.getChildResources()));
        return cfs;
    }

    private EntityDto simpleDtoFrom(TaggedAndVersionedEntity entity) {
        return preFilledBuilder(new EntityDto.Builder(), entity, catServices)
                .setTags(tagAsStringFrom(entity.getTags()))
                .setVersion(versionAdapter.dtoFrom(entity.getVersion()))
                .build();
    }

    private EntityDto dtoFrom(Cfs cfs) {
        if(cfs == null) return null;
        return preFilledEntityDtoBuilder(cfs, true)
                .setParentCfs(relationAdapter.relationDtoFrom(cfs.getParentCfs()))
                .setChildCfs(relationAdapter.relationDtoFrom(cfs.getChildCfs()))
                .setChildRfs(relationAdapter.relationDtoFrom(cfs.getChildRfs()))
                .setChildResources(relationAdapter.relationDtoFrom(cfs.getChildResources()))
                .build();
    }

    public Rfs rfsFrom(EntityDto dto) {
        if(dto == null) return null;
        Rfs rfs = preFilledEntity(new Rfs(), dto);
        rfs.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.RFS));
        rfs.setParentCfs(relationAdapter.relationFrom(dto.getParentCfs()));
        rfs.setParentRfs(relationAdapter.relationFrom(dto.getParentRfs()));
        rfs.setChildRfs(relationAdapter.relationFrom(dto.getChildRfs()));
        rfs.setChildResources(relationAdapter.relationFrom(dto.getChildResources()));
        return rfs;
    }

    private EntityDto dtoFrom(Rfs rfs) {
        if(rfs == null) return null;
        return preFilledEntityDtoBuilder(rfs, true)
                .setParentCfs(relationAdapter.relationDtoFrom(rfs.getParentCfs()))
                .setParentRfs(relationAdapter.relationDtoFrom(rfs.getParentRfs()))
                .setChildRfs(relationAdapter.relationDtoFrom(rfs.getChildRfs()))
                .setChildResources(relationAdapter.relationDtoFrom(rfs.getChildResources()))
                .build();
    }

    public Resource resourceFrom(EntityDto dto) {
        if(dto == null) return null;
        Resource resource = preFilledEntity(new Resource(), dto);
        resource.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.RESOURCE));
        resource.setChildResources(relationAdapter.relationFrom(dto.getChildResources()));
        return resource;
    }

    private EntityDto dtoFrom(Resource resource) {
        if(resource == null) return null;
        return preFilledEntityDtoBuilder(resource, true)
                .setParentCfs(relationAdapter.relationDtoFrom(resource.getParentCfs()))
                .setParentRfs(relationAdapter.relationDtoFrom(resource.getParentRfs()))
                .setParentResources(relationAdapter.relationDtoFrom(resource.getParentResources()))
                .setChildResources(relationAdapter.relationDtoFrom(resource.getChildResources()))
                .build();
    }
}
