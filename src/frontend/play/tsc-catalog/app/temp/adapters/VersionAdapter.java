package temp.adapters;

import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.Version;
import nossis.tsccatalog.services.CatEntityAPIServices;
import temp.dtos.VersionCreateDto;
import temp.dtos.VersionDto;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.List;
import java.util.stream.Collectors;

import static temp.adapters.AdapterHelper.preFilledBuilder;
import static temp.adapters.AdapterHelper.preFilledModel;

@Singleton
public class VersionAdapter {

    private final CatEntityAPIServices catServices;

    @Inject
    public VersionAdapter(CatEntityAPIServices catServices) {
        this.catServices = catServices;
    }

    public List<VersionDto> dtoFrom(List<Version> versions) {
        return versions.stream().map(this::dtoFrom).collect(Collectors.toList());
    }

    public VersionDto dtoFrom(Version version) {
        if(version == null) return null;

        return preFilledBuilder(new VersionDto.Builder(), version, catServices)
                .setState(version.getState())
                .build();
    }

    public List<Version> versionFrom(List<VersionDto> dtos) {
        return dtos.stream().map(this::versionFrom).collect(Collectors.toList());
    }

    public Version versionFrom(VersionDto dto) {
        if(dto == null) return null;
        Version version = preFilledModel(new Version(), dto);
        version.setState(dto.getState());
        version.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.VERSION));
        return version;
    }

    public Version versionFrom(VersionCreateDto dto) {
        if(dto == null) return null;
        Version version = preFilledModel(new Version(), dto);
        version.setState(dto.getState());
        if(dto.getCreationType() == VersionCreateDto.CreationType.FROM_ENTITY){
            version.setId(dto.getCloneId());
        }
        version.setIdCatEntity(catServices.getIdCatEntityOfType(EntityType.VERSION));
        return version;
    }
}
