package temp.controllers.impl;

import nossis.tsccatalog.models.selectbox.query.SelectBoxQuery;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import temp.controllers.ControllerHelper;
import temp.controllers.ITagController;
import temp.dtos.TagResponseDto;
import temp.services.ITagService;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

public class TagController extends Controller implements ITagController {

    private final ITagService service;

    @Inject
    public TagController(ITagService service) {
        this.service = service;
    }

    @Override
    public CompletionStage<Result> getTags() {
        final SelectBoxQuery selectBoxQuery = SelectBoxQuery.fromQueryString(request().queryString());

        CompletionStage<TagResponseDto> tagResponseDtoCompletionStage = service.getTagsByName(selectBoxQuery)
                .thenApply(TagResponseDto::new);

        return ControllerHelper.responseWithEtag(response(), tagResponseDtoCompletionStage, request());
    }
}
