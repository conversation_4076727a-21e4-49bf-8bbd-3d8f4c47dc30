package temp.controllers.impl;

import nossis.tsccatalog.models.entities.Cfs;
import play.Logger;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import play.mvc.Results;
import temp.adapters.EntityAdapter;
import temp.controllers.ControllerHelper;
import temp.controllers.ICfsController;
import temp.dtos.EntityDto;
import temp.services.IEntityService;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static temp.controllers.ControllerHelper.fromJson;

public class CfsController extends Controller implements ICfsController {

    private final Logger.ALogger LOGGER = Logger.of(this.getClass().getSimpleName());

    private final IEntityService<Cfs> cfsService;
    private final EntityAdapter adapter;
    private final HttpExecutionContext ec;

    @Inject
    public CfsController(IEntityService<Cfs> cfsService, EntityAdapter adapter, HttpExecutionContext ec) {

        this.cfsService = cfsService;
        this.adapter = adapter;
        this.ec = ec;
    }

    @Override
    public CompletionStage<Result> getCfsById(Long id) {
        LOGGER.info("Getting Cfs entity with id:" + id + ".");

        CompletionStage<EntityDto> entityDtoCompletionStage = cfsService
                .getEntityById(id)
                .thenApplyAsync(adapter::dtoWithAttributesFrom, ec.current());

        return ControllerHelper.responseWithEtag(response(), entityDtoCompletionStage, request());
    }


    @Override
    public CompletionStage<Result> create() {
        LOGGER.info("Creating new CFS entity.");
        Cfs cfs = adapter.cfsFrom(fromJson(request().body().asJson(), EntityDto.class));
        return cfsService
                .create(cfs)
                .thenApply(ControllerHelper::idResponse)
                .thenApply(Json::toJson)
                .thenApply(Results::created);
    }

    @Override
    public CompletionStage<Result> update(Long id) {
        LOGGER.info("Updating CFS entity with id:" + id + ".");
        Cfs cfs = adapter.cfsFrom(fromJson(request().body().asJson(), EntityDto.class));
        return cfsService
                .update(id, cfs)
                .thenApply(noContent -> Results.noContent());
    }

    @Override
    public CompletionStage<Result> cloneCfsWithRelations(Long id) {
        LOGGER.info("Cloning with relations CFS entity with id:" + id + ".");
        return cfsService
                .clone(id, true)
                .thenApply(ControllerHelper::idResponse)
                .thenApply(Json::toJson)
                .thenApply(Results::created);
    }

    @Override
    public CompletionStage<Result> cloneCfsWithoutRelations(Long id) {
        LOGGER.info("Cloning without relations CFS entity with id:" + id + ".");
        return cfsService
                .clone(id, false)
                .thenApply(ControllerHelper::idResponse)
                .thenApply(Json::toJson)
                .thenApply(Results::created);
    }

    @Override
    public CompletionStage<Result> delete(Long id) {
        LOGGER.info("Deleting CFS entity with id:" + id + ".");
        return cfsService
                .delete(id)
                .thenApply(deleted -> deleted ? Results.ok() : Results.noContent());
    }
}
