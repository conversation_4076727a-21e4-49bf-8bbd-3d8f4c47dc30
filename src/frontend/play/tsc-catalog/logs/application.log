2025-06-30T14:06:55.471 INFO  [play-dev-mode-akka.actor.default-dispatcher-4] [event.slf4j.Slf4jLogger] Slf4jLogger started
2025-06-30T14:06:56.024 INFO  [main] [core.server.AkkaHttpServer] Listening for HTTP on /0:0:0:0:0:0:0:0:9032
2025-06-30T14:08:40.916 INFO  [application-akka.actor.default-dispatcher-6] [event.slf4j.Slf4jLogger] Slf4jLogger started
2025-06-30T14:08:41.030 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [asf.componentsmanager.ComponentsManager] Created rewritable component 'NOSSIS-TSC-SERVICE-LOCATOR-COMPONENT' instance of 'pt.ptinovacao.nossistsc.cluster.ServiceLocator'.
2025-06-30T14:08:41.236 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperCluster$] null-1711203041 new : { hash: 1711203041, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}
2025-06-30T14:08:41.239 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperServices$] new : { hash: 1255872282, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}
2025-06-30T14:08:41.251 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperWatchDog] before start CuratorFramework: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: DISCONNECTED, observers: 0, connectionTimeoutMs: 15000)
2025-06-30T14:08:41.252 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [framework.imps.CuratorFrameworkImpl] Starting
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:zookeeper.version=3.8.4-9316c2a7a97e1666d8f4593f34dd6fc36ecc436c, built on 2024-02-12 22:16 UTC
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:host.name=andre-pc
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.version=1.8.0_452
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.vendor=Arch Linux
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.home=/usr/lib/jvm/java-8-openjdk/jre
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.class.path=
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.library.path=/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.io.tmpdir=/tmp
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:java.compiler=<NA>
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.name=Linux
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.arch=amd64
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.version=6.15.4-arch2-1
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:user.name=andre
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:user.home=/home/<USER>
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:user.dir=/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/tsc-catalog
2025-06-30T14:08:41.254 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.memory.free=433MB
2025-06-30T14:08:41.255 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.memory.max=488MB
2025-06-30T14:08:41.255 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Client environment:os.memory.total=488MB
2025-06-30T14:08:41.256 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ZooKeeper] Initiating client connection, connectString=nossistsc-ci.c.ptin.corppt.com:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@7e66fd3a
2025-06-30T14:08:41.257 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [zookeeper.common.X509Util] Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
2025-06-30T14:08:41.258 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ClientCnxnSocket] jute.maxbuffer value is 1048575 Bytes
2025-06-30T14:08:41.262 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [apache.zookeeper.ClientCnxn] zookeeper.request.timeout value is 0. feature enabled=false
2025-06-30T14:08:41.265 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [framework.imps.CuratorFrameworkImpl] Default schema
2025-06-30T14:08:41.393 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Opening socket connection to server nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181.
2025-06-30T14:08:41.394 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] SASL config status: Will not attempt to authenticate using SASL (unknown error)
2025-06-30T14:08:41.402 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Socket connection established, initiating session, client: /10.112.211.10:56248, server: nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181
2025-06-30T14:08:41.418 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Session establishment complete on server nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181, session id = 0x100000298ba0011, negotiated timeout = 40000
2025-06-30T14:08:41.428 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-EventThread] [framework.state.ConnectionStateManager] State change: CONNECTED
2025-06-30T14:08:41.432 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperWatchDog] after  start CuratorFramework: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: CONNECTED, observers: 0, connectionTimeoutMs: 15000)
2025-06-30T14:08:41.433 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 start begin: { hash: 1255872282, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}, curator state: STARTED, zookeeperAddress: nossistsc-ci.c.ptin.corppt.com:2181
2025-06-30T14:08:41.449 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-EventThread] [framework.imps.EnsembleTracker] New config event received: {}
2025-06-30T14:08:41.539 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 start end OK: { hash: 1255872282, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}, curator state: STARTED; zookeeper cli state: CONNECTED
2025-06-30T14:08:41.540 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperCluster$] null-1711203041 start begin: { hash: 1711203041, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}, curator state: STARTED, zookeeperAddress: nossistsc-ci.c.ptin.corppt.com:2181
2025-06-30T14:08:41.542 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service', stat=10,10,1751242068357,1751242068357,0,1,0,0,10,1,11
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.543 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest', stat=11,11,1751242068500,1751242068500,0,1,0,0,108,1,12
, data=[123, 34, 110, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 45, 116, 115, 99, 45, 114, 101, 115, 116, 34, 44, 34, 105, 112, 34, 58, 34, 34, 44, 34, 112, 111, 114, 116, 34, 58, 48, 44, 34, 102, 117, 108, 108, 65, 100, 100, 114, 101, 115, 115, 34, 58, 34, 34, 44, 34, 105, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 112, 114, 111, 116, 111, 99, 111, 108, 34, 58, 34, 78, 79, 78, 69, 34, 44, 34, 115, 116, 97, 116, 101, 34, 58, 34, 79, 75, 34, 125]}
2025-06-30T14:08:41.589 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest/instances', stat=12,12,1751242068518,1751242068518,0,3,0,0,10,1,43
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.589 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest/instances/nossistsc-ci.c.ptin.corppt.com:9015', stat=43,43,1751265451119,1751265451119,0,0,0,72057605190189063,272,0,43
, data=[123, 34, 115, 101, 114, 118, 105, 99, 101, 78, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 45, 116, 115, 99, 45, 114, 101, 115, 116, 34, 44, 34, 110, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 58, 57, 48, 49, 53, 34, 44, 34, 105, 112, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 34, 44, 34, 112, 111, 114, 116, 34, 58, 57, 48, 49, 53, 44, 34, 102, 117, 108, 108, 65, 100, 100, 114, 101, 115, 115, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 58, 57, 48, 49, 53, 34, 44, 34, 105, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 34, 58, 34, 78, 111, 115, 115, 105, 115, 32, 84, 115, 99, 32, 69, 110, 116, 105, 116, 105, 101, 115, 32, 67, 97, 116, 97, 108, 111, 103, 32, 83, 101, 114, 118, 105, 99, 101, 32, 73, 110, 115, 116, 97, 110, 99, 101, 34, 44, 34, 112, 114, 111, 116, 111, 99, 111, 108, 34, 58, 34, 72, 84, 84, 80, 34, 44, 34, 115, 116, 97, 116, 101, 34, 58, 34, 79, 75, 34, 125]}
2025-06-30T14:08:41.601 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1255872282 handler childEvent INITIALIZED -> null
2025-06-30T14:08:41.617 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [cluster.api.ZookeeperCluster$] null-1711203041 start end OK: { hash: 1711203041, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}, curator state: STARTED; zookeeper cli state: CONNECTED
2025-06-30T14:08:41.617 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [asf.componentsmanager.ComponentsManager] Component 'NOSSIS-TSC-SERVICE-LOCATOR-COMPONENT' instance of 'pt.ptinovacao.nossistsc.cluster.ServiceLocator' successfully initiated.
2025-06-30T14:08:41.618 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster', stat=15,15,1751242068558,1751242068558,0,3,0,0,10,3,20
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.617 INFO  [Curator-ConnectionStateManager-0] [cluster.api.ZookeeperWatchDog] stateChanged: CONNECTED, WatchDog: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: CONNECTED, observers: 0, connectionTimeoutMs: 15000), this._cf.hashCode: 588137958 client.hashCode: 588137958
2025-06-30T14:08:41.619 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster', stat=17,17,1751242068611,1751242068611,0,1,0,0,0,1,18
, data=[]}
2025-06-30T14:08:41.619 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/configuration', stat=19,19,1751242068649,1751242068649,0,0,0,0,10,0,19
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.620 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/nodesConfiguration', stat=20,20,1751242068657,1751242068657,0,1,0,0,10,1,21
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.620 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes', stat=18,18,1751242068626,1751242068626,0,18,0,0,10,2,78
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.620 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/nodesConfiguration/null', stat=21,21,1751242068671,1751242068671,0,0,0,0,10,0,21
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T14:08:41.621 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000002', stat=42,42,1751265451103,1751265451103,0,0,0,72057605190189063,67,0,42
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T14:08:41.628 INFO  [play-dev-mode-akka.actor.default-dispatcher-10-EventThread] [framework.imps.EnsembleTracker] New config event received: {}
2025-06-30T14:08:41.641 WARN  [play-dev-mode-akka.actor.default-dispatcher-10] [application] conf-dev/application.conf: 19: play.crypto.secret is deprecated, use play.http.secret.key instead
2025-06-30T14:08:41.642 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000009', stat=77,77,1751291246832,1751291246832,0,0,0,72057605190189072,44,0,77
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T14:08:41.642 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent INITIALIZED -> null
2025-06-30T14:08:41.643 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000010', stat=83,83,1751292521607,1751292521607,0,0,0,72057605190189073,44,0,83
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T14:08:41.823 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [internal.util.Version] HV000001: Hibernate Validator 5.4.3.Final
2025-06-30T14:08:41.835 WARN  [play-dev-mode-akka.actor.default-dispatcher-10] [validator.messageinterpolation.ParameterMessageInterpolator] HV000184: ParameterMessageInterpolator has been chosen, EL interpolation will not be supported
2025-06-30T14:08:41.869 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [api.http.EnabledFilters] Enabled Filters (see <https://www.playframework.com/documentation/latest/Filters>):


2025-06-30T14:08:41.872 INFO  [play-dev-mode-akka.actor.default-dispatcher-10] [play.api.Play] Application started (Dev)
2025-06-30T14:08:42.043 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Using provided configuration file: ../security/conf-dev/modules-nossisaaapi.conf
2025-06-30T14:08:42.043 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Using nossis configuration file: /mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/tsc-catalog/../security/conf-dev/modules-nossisaaapi.conf
2025-06-30T14:08:42.112 INFO  [application-akka.actor.default-dispatcher-6] [aaapi.config.ConfigurationLoader] Looking for mappings on the classpath!
2025-06-30T14:08:42.149 INFO  [application-akka.actor.default-dispatcher-6] [core.config.InitializationService] Initializing OpenSAML using the Java Services API
2025-06-30T14:08:42.395 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.PlaySecurityConfig] Deprecated property 'nossisaaapi.provider.url-parameter-name' found in configuration. Please update to 'nossisaaapi.provider.sessionid-parameter-name'.
2025-06-30T14:08:42.397 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:08:42.398 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:08:42.398 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [83c5fd42-b32d-4426-a7d1-70ec25607713]
2025-06-30T14:08:42.399 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:08:42.400 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:08:42.401 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:08:42.401 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T14:08:42.401 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2780c6
2025-06-30T14:08:42.401 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:08:42.858 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [dwAhRnadGVYsyPMNXTe6RF]
2025-06-30T14:08:42.859 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.ServiceCredentials@410725a9
2025-06-30T14:08:43.215 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.844Z
2025-06-30T14:08:43.357 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.844Z
2025-06-30T14:08:43.507 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:08:43.507 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:08:43.508 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:26.844Z
2025-06-30T14:08:43.510 WARN  [play-dev-mode-akka.actor.default-dispatcher-13] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:08:43.511 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:08:43.511 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:08:43.511 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.844Z
2025-06-30T14:08:43.753 INFO  [application-akka.actor.default-dispatcher-12] [VersionController] Getting version state machine.
2025-06-30T14:08:43.753 INFO  [application-akka.actor.default-dispatcher-13] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:08:43.885 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:08:43.885 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:08:43.885 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.844Z
2025-06-30T14:08:43.886 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = VERSION
2025-06-30T14:08:43.898 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T14:08:43.910 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ROOT.WITH_ATTRIBUTES
2025-06-30T14:08:43.923 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ROOT
2025-06-30T14:08:43.925 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ROOT
2025-06-30T14:08:43.925 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ROOT.WITH_ATTRIBUTES
2025-06-30T14:08:43.925 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T14:08:43.956 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T14:08:43.974 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = TAG
2025-06-30T14:08:43.989 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ATTRTYPE
2025-06-30T14:08:44.002 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T14:08:44.012 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT
2025-06-30T14:08:44.012 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT
2025-06-30T14:08:44.012 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T14:08:44.024 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T14:08:44.035 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = VALUE
2025-06-30T14:08:44.035 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = VALUE
2025-06-30T14:08:44.035 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T14:08:44.046 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T14:08:44.046 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T14:08:44.058 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ATTRIBUTE
2025-06-30T14:08:44.070 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = COMPONENT
2025-06-30T14:08:44.073 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = COMPONENT
2025-06-30T14:08:44.086 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CFS
2025-06-30T14:08:44.097 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T14:08:44.107 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T14:08:44.107 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T14:08:44.107 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T14:08:44.117 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T14:08:44.129 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = RESOURCE
2025-06-30T14:08:44.140 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RESOURCE_RESOURCE_RELATION
2025-06-30T14:08:44.140 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RESOURCE_RESOURCE_RELATION
2025-06-30T14:08:44.150 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T14:08:44.162 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = RFS
2025-06-30T14:08:44.172 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T14:08:44.173 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T14:08:44.184 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T14:08:44.184 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T14:08:44.185 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = RFS
2025-06-30T14:08:44.185 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T14:08:44.185 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = RESOURCE
2025-06-30T14:08:44.185 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T14:08:44.186 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CFS
2025-06-30T14:08:44.186 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ATTRIBUTE
2025-06-30T14:08:44.200 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T14:08:44.200 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T14:08:44.212 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T14:08:44.213 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T14:08:44.213 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ATTRTYPE
2025-06-30T14:08:44.225 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T14:08:44.225 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T14:08:44.225 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = TAG
2025-06-30T14:08:44.225 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T14:08:44.225 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = VERSION
2025-06-30T14:08:44.235 INFO  [application-akka.actor.default-dispatcher-13] [EntityDTController] Fetching cfs search data.
2025-06-30T14:08:44.313 WARN  [application-akka.actor.default-dispatcher-13] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [8782ff4a-ca20-4939-8664-a9efe339cb9f]
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying http Authentication
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@698e5a83
2025-06-30T14:11:08.041 INFO  [application-akka.actor.default-dispatcher-9] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [8782ff4a-ca20-4939-8664-a9efe339cb9f]
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Trying http Authentication
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@a8a6869
2025-06-30T14:11:11.378 INFO  [application-akka.actor.default-dispatcher-8] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:11:11.764 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:11.947 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.384 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:12.384 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:12.384 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.384 WARN  [play-dev-mode-akka.actor.default-dispatcher-58] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:11:12.388 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:12.389 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:12.389 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.396 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:12.396 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:12.396 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.538 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting all versions.
2025-06-30T14:11:12.544 INFO  [application-akka.actor.default-dispatcher-8] [VersionController] Getting version state machine.
2025-06-30T14:11:12.554 INFO  [application-akka.actor.default-dispatcher-7] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:11:12.722 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:12.722 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:12.723 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.726 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:12.726 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:12.726 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:12.856 INFO  [application-akka.actor.default-dispatcher-16] [EntityDTController] Fetching cfs search data.
2025-06-30T14:11:12.863 INFO  [application-akka.actor.default-dispatcher-7] [EntityDTController] Fetching cfs search data.
2025-06-30T14:11:12.874 WARN  [application-akka.actor.default-dispatcher-7] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:11:12.892 WARN  [application-akka.actor.default-dispatcher-7] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:11:27.273 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:27.273 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:27.273 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.534Z
2025-06-30T14:11:27.589 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting all versions.
2025-06-30T14:11:47.308 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4c78358b-495f-464b-8fe0-ddf37c74d231]
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying http Authentication
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@752149e1
2025-06-30T14:11:47.309 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:11:47.948 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.086 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.265 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:48.265 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:48.265 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.266 WARN  [play-dev-mode-akka.actor.default-dispatcher-10] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:11:48.266 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:48.266 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:48.266 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.269 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:48.270 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:48.270 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.280 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:48.280 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:48.280 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.452 INFO  [application-akka.actor.default-dispatcher-7] [VersionController] Getting version state machine.
2025-06-30T14:11:48.456 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T14:11:48.456 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting version with the id: 273853.
2025-06-30T14:11:48.464 INFO  [application-akka.actor.default-dispatcher-8] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:11:48.615 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:11:48.615 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:11:48.615 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:11:48.779 INFO  [application-akka.actor.default-dispatcher-14] [EntityDTController] Fetching cfs search data.
2025-06-30T14:11:48.802 WARN  [application-akka.actor.default-dispatcher-14] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:12:04.778 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:04.778 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:04.778 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:12:06.032 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting all versions.
2025-06-30T14:12:08.080 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:08.080 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:08.081 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:12:08.238 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting all versions.
2025-06-30T14:12:09.244 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:09.244 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:09.244 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.595Z
2025-06-30T14:12:09.383 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0fb1b21f-2e7d-49c1-bb6f-3bffd03dd629]
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Trying http Authentication
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1a6a14e2
2025-06-30T14:12:58.840 INFO  [application-akka.actor.default-dispatcher-16] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:12:59.467 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.585 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.775 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:59.775 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:59.775 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.775 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:59.775 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:59.776 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.776 WARN  [play-dev-mode-akka.actor.default-dispatcher-40] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:12:59.780 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:59.780 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:59.780 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.789 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:12:59.789 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:12:59.789 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:12:59.949 INFO  [application-akka.actor.default-dispatcher-8] [VersionController] Getting version state machine.
2025-06-30T14:12:59.953 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T14:12:59.955 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting version with the id: 273853.
2025-06-30T14:12:59.959 INFO  [application-akka.actor.default-dispatcher-11] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:13:00.174 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:00.174 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:00.174 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:00.302 INFO  [application-akka.actor.default-dispatcher-15] [EntityDTController] Fetching cfs search data.
2025-06-30T14:13:00.321 WARN  [application-akka.actor.default-dispatcher-15] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:13:01.279 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:01.279 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:01.279 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:01.422 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T14:13:03.141 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:03.141 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:03.141 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:03.295 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T14:13:06.040 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:06.040 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:06.040 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:06.178 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T14:13:16.670 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:16.670 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:16.670 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:16.892 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T14:13:18.205 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:18.205 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:18.205 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.109Z
2025-06-30T14:13:18.439 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0cb164fe-4824-4510-95c1-7f2e70773f73]
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying http Authentication
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@43de55f6
2025-06-30T14:13:36.586 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:13:37.111 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.254 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.432 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:37.432 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:37.432 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:37.432 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:37.432 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.432 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.433 WARN  [play-dev-mode-akka.actor.default-dispatcher-41] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:13:37.438 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:37.438 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:37.438 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.458 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:37.458 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:37.458 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.605 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T14:13:37.607 INFO  [application-akka.actor.default-dispatcher-8] [VersionController] Getting version state machine.
2025-06-30T14:13:37.612 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:13:37.613 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting version with the id: 273853.
2025-06-30T14:13:37.808 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:37.809 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:37.809 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:37.966 INFO  [application-akka.actor.default-dispatcher-17] [EntityDTController] Fetching cfs search data.
2025-06-30T14:13:37.984 WARN  [application-akka.actor.default-dispatcher-17] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:13:38.569 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:38.569 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:38.569 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:38.746 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T14:13:40.381 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:40.381 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:40.381 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:26.776Z
2025-06-30T14:13:40.520 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T14:13:59.324 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f6742874-637d-4540-b9b4-5af07f7599d2]
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Trying http Authentication
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@57b0c639
2025-06-30T14:13:59.325 INFO  [application-akka.actor.default-dispatcher-17] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:13:59.703 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:13:59.813 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.001 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:00.001 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:00.001 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.001 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:00.001 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:00.002 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.002 WARN  [play-dev-mode-akka.actor.default-dispatcher-116] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:14:00.006 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:00.006 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:00.006 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.011 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:00.011 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:00.011 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.165 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version state machine.
2025-06-30T14:14:00.172 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting version with the id: 273853.
2025-06-30T14:14:00.175 INFO  [application-akka.actor.default-dispatcher-7] [VersionController] Getting all versions.
2025-06-30T14:14:00.179 INFO  [application-akka.actor.default-dispatcher-9] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:14:00.377 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:00.377 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:00.377 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:00.510 INFO  [application-akka.actor.default-dispatcher-9] [EntityDTController] Fetching cfs search data.
2025-06-30T14:14:00.526 WARN  [application-akka.actor.default-dispatcher-9] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:14:01.600 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:01.600 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:01.600 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.475Z
2025-06-30T14:14:01.787 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting all versions.
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0c6d8db5-4092-4760-952a-6165a1d679b9]
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying http Authentication
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5e7f28b5
2025-06-30T14:14:51.761 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:14:52.209 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.950Z
2025-06-30T14:14:52.396 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:26.950Z
2025-06-30T14:14:52.591 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:52.591 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:52.591 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.950Z
2025-06-30T14:14:52.591 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:14:52.591 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:14:52.591 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.950Z
2025-06-30T14:14:52.739 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version with the id: 273853.
2025-06-30T14:14:52.745 INFO  [application-akka.actor.default-dispatcher-7] [VersionController] Getting version state machine.
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [2deaad18-bfb7-4896-8c74-6f17e9530de4]
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Trying http Authentication
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3f23a1cf
2025-06-30T14:15:54.336 INFO  [application-akka.actor.default-dispatcher-7] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:15:54.765 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:54.891 WARN  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.078 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:55.078 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:55.078 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:55.078 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:55.078 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.078 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.079 WARN  [play-dev-mode-akka.actor.default-dispatcher-40] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:15:55.083 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:55.083 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:55.083 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.086 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:55.086 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:55.086 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.248 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting version state machine.
2025-06-30T14:15:55.253 INFO  [application-akka.actor.default-dispatcher-8] [VersionController] Getting version with the id: 273853.
2025-06-30T14:15:55.258 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T14:15:55.261 INFO  [application-akka.actor.default-dispatcher-7] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:15:55.464 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:15:55.464 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:15:55.464 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:27.549Z
2025-06-30T14:15:55.592 INFO  [application-akka.actor.default-dispatcher-8] [EntityDTController] Fetching cfs search data.
2025-06-30T14:15:55.606 WARN  [application-akka.actor.default-dispatcher-8] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [98c82768-ac16-4b82-b39a-cea4764bc4f5]
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying http Authentication
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@76cce08
2025-06-30T14:20:20.997 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:20:21.431 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:21.628 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:21.806 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:21.806 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:21.806 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:21.806 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:21.806 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:21.806 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:21.807 WARN  [play-dev-mode-akka.actor.default-dispatcher-198] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:20:21.810 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:21.811 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:21.811 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:21.816 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:21.816 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:21.816 WARN  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:22.017 INFO  [application-akka.actor.default-dispatcher-23] [VersionController] Getting version with the id: 273853.
2025-06-30T14:20:22.029 INFO  [application-akka.actor.default-dispatcher-29] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:20:22.030 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting version state machine.
2025-06-30T14:20:22.036 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T14:20:22.135 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:20:22.135 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:20:22.135 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.170Z
2025-06-30T14:20:22.274 INFO  [application-akka.actor.default-dispatcher-28] [EntityDTController] Fetching cfs search data.
2025-06-30T14:20:22.293 WARN  [application-akka.actor.default-dispatcher-28] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [7e4a7700-e2ee-44b0-b831-516a130487d2]
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Trying http Authentication
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1e7cdd8f
2025-06-30T14:21:03.934 INFO  [application-akka.actor.default-dispatcher-23] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:21:04.668 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.141Z
2025-06-30T14:21:04.813 WARN  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Session time: 2025-06-30T16:11:27.141Z
2025-06-30T14:21:04.992 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:21:04.992 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:21:04.992 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:21:04.992 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:21:04.992 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.141Z
2025-06-30T14:21:04.992 WARN  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Session time: 2025-06-30T16:11:27.141Z
2025-06-30T14:21:04.993 WARN  [play-dev-mode-akka.actor.default-dispatcher-197] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:21:04.997 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:21:04.997 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:21:04.997 WARN  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Session time: 2025-06-30T16:11:27.141Z
2025-06-30T14:21:05.166 INFO  [application-akka.actor.default-dispatcher-23] [VersionController] Getting version state machine.
2025-06-30T14:21:05.167 INFO  [application-akka.actor.default-dispatcher-29] [VersionController] Getting version with the id: 273853.
2025-06-30T14:21:05.176 INFO  [application-akka.actor.default-dispatcher-24] [VersionController] Getting all versions.
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [78c61a28-0860-4835-acc5-34e58225dff3]
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Trying http Authentication
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7e65474f
2025-06-30T14:24:16.732 INFO  [application-akka.actor.default-dispatcher-22] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:24:17.213 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:17.353 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:17.529 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:17.529 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:17.529 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:17.529 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:17.529 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:17.529 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:17.530 WARN  [play-dev-mode-akka.actor.default-dispatcher-175] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:24:17.534 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:17.538 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:17.538 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:17.667 INFO  [application-akka.actor.default-dispatcher-33] [VersionController] Getting all versions.
2025-06-30T14:24:18.706 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting version with the id: 273853.
2025-06-30T14:24:18.723 INFO  [application-akka.actor.default-dispatcher-32] [VersionController] Getting version state machine.
2025-06-30T14:24:37.852 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:37.852 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:37.853 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:38.081 INFO  [application-akka.actor.default-dispatcher-26] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:24:38.188 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:38.188 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:38.188 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:26.933Z
2025-06-30T14:24:38.338 INFO  [application-akka.actor.default-dispatcher-26] [EntityDTController] Fetching cfs search data.
2025-06-30T14:24:38.358 WARN  [application-akka.actor.default-dispatcher-26] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [1edcb581-7c96-459b-8c9d-4d792d1aece5]
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Trying http Authentication
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@419b3b0b
2025-06-30T14:24:52.103 INFO  [application-akka.actor.default-dispatcher-25] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:24:52.560 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:52.698 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:52.874 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:52.874 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:52.874 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:52.874 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:52.874 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:52.874 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:52.874 WARN  [play-dev-mode-akka.actor.default-dispatcher-192] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:24:52.878 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:52.878 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:52.878 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:53.009 INFO  [application-akka.actor.default-dispatcher-32] [VersionController] Getting version state machine.
2025-06-30T14:24:53.016 INFO  [application-akka.actor.default-dispatcher-27] [VersionController] Getting version with the id: 273853.
2025-06-30T14:24:53.023 INFO  [application-akka.actor.default-dispatcher-25] [VersionController] Getting all versions.
2025-06-30T14:24:55.916 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:55.916 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:55.917 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:56.045 INFO  [application-akka.actor.default-dispatcher-27] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:24:56.245 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:24:56.245 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:24:56.245 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:24:56.366 INFO  [application-akka.actor.default-dispatcher-27] [EntityDTController] Fetching cfs search data.
2025-06-30T14:24:56.385 WARN  [application-akka.actor.default-dispatcher-27] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:26:10.841 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:26:10.841 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:26:10.841 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:27.341Z
2025-06-30T14:26:11.058 INFO  [application-akka.actor.default-dispatcher-33] [VersionController] Getting all versions.
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [7d0685ba-2080-4e65-bb6f-7b49a8a98cac]
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying http Authentication
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@71003fc
2025-06-30T14:28:44.900 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:28:45.336 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.562 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.736 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:45.736 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:45.736 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:45.736 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:45.736 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.736 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.737 WARN  [play-dev-mode-akka.actor.default-dispatcher-287] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:28:45.747 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:45.747 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:45.747 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.750 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:45.751 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:45.751 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:45.914 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting version with the id: 273853.
2025-06-30T14:28:45.915 INFO  [application-akka.actor.default-dispatcher-35] [VersionController] Getting all versions.
2025-06-30T14:28:45.921 INFO  [application-akka.actor.default-dispatcher-37] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:28:46.112 INFO  [application-akka.actor.default-dispatcher-31] [VersionController] Getting version state machine.
2025-06-30T14:28:46.112 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:46.112 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:46.112 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:46.248 INFO  [application-akka.actor.default-dispatcher-35] [EntityDTController] Fetching cfs search data.
2025-06-30T14:28:46.267 WARN  [application-akka.actor.default-dispatcher-35] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:28:52.978 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:28:52.978 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:28:52.978 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:27.135Z
2025-06-30T14:28:53.117 INFO  [application-akka.actor.default-dispatcher-35] [VersionController] Getting all versions.
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [bdf367ac-2f1f-4cc6-b613-20a51309826b]
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying http Authentication
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@436334df
2025-06-30T14:34:56.627 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:34:57.025 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:57.185 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.256 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:59.256 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:59.256 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:59.256 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:59.256 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.256 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.256 WARN  [play-dev-mode-akka.actor.default-dispatcher-323] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:34:59.261 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:59.261 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:59.261 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.266 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:59.266 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:59.266 WARN  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.423 INFO  [application-akka.actor.default-dispatcher-41] [VersionController] Getting version state machine.
2025-06-30T14:34:59.433 INFO  [application-akka.actor.default-dispatcher-39] [VersionController] Getting all versions.
2025-06-30T14:34:59.435 INFO  [application-akka.actor.default-dispatcher-40] [VersionController] Getting version with the id: 273853.
2025-06-30T14:34:59.446 INFO  [application-akka.actor.default-dispatcher-45] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:34:59.611 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:34:59.611 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:34:59.611 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:26.800Z
2025-06-30T14:34:59.736 INFO  [application-akka.actor.default-dispatcher-40] [EntityDTController] Fetching cfs search data.
2025-06-30T14:34:59.759 WARN  [application-akka.actor.default-dispatcher-40] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:35:13.820 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:13.820 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [035fa2c8-d4ab-446a-a252-148dfee8d5da]
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Trying http Authentication
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5ee471f0
2025-06-30T14:35:13.821 INFO  [application-akka.actor.default-dispatcher-40] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:35:14.174 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.331 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.810 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:14.810 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:14.810 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:14.810 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.811 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:14.811 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.811 WARN  [play-dev-mode-akka.actor.default-dispatcher-325] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:35:14.815 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:14.815 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:14.815 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.827 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:14.827 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:14.827 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:14.990 INFO  [application-akka.actor.default-dispatcher-43] [VersionController] Getting version state machine.
2025-06-30T14:35:15.006 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting all versions.
2025-06-30T14:35:15.008 INFO  [application-akka.actor.default-dispatcher-38] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:35:15.010 INFO  [application-akka.actor.default-dispatcher-41] [VersionController] Getting version with the id: 273853.
2025-06-30T14:35:15.024 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:15.024 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:15.024 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:15.153 INFO  [application-akka.actor.default-dispatcher-41] [EntityDTController] Fetching cfs search data.
2025-06-30T14:35:15.168 WARN  [application-akka.actor.default-dispatcher-41] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:35:57.007 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:35:57.007 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:35:57.008 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:26.957Z
2025-06-30T14:35:57.295 INFO  [application-akka.actor.default-dispatcher-38] [VersionController] Getting all versions.
2025-06-30T14:36:20.116 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:20.116 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [57b309c1-3d7e-4a8a-9eb0-1b8638d6df8f]
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Trying http Authentication
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6fe21aed
2025-06-30T14:36:20.117 INFO  [application-akka.actor.default-dispatcher-38] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:36:20.492 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:20.662 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:20.874 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:20.874 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:20.874 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:20.874 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:20.874 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:20.874 WARN  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:20.874 WARN  [play-dev-mode-akka.actor.default-dispatcher-323] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:36:20.879 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:20.879 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:20.879 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:20.885 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:20.885 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:20.885 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:21.054 INFO  [application-akka.actor.default-dispatcher-40] [VersionController] Getting version state machine.
2025-06-30T14:36:21.056 INFO  [application-akka.actor.default-dispatcher-46] [VersionController] Getting version with the id: 273853.
2025-06-30T14:36:21.058 INFO  [application-akka.actor.default-dispatcher-38] [VersionController] Getting all versions.
2025-06-30T14:36:21.065 INFO  [application-akka.actor.default-dispatcher-43] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:36:21.283 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:36:21.283 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:36:21.283 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:36:21.407 INFO  [application-akka.actor.default-dispatcher-43] [EntityDTController] Fetching cfs search data.
2025-06-30T14:36:21.426 WARN  [application-akka.actor.default-dispatcher-43] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:39:07.005 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:39:07.005 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:39:07.005 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:39:07.299 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting all versions.
2025-06-30T14:41:00.686 INFO  [application-akka.actor.default-dispatcher-47] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:41:00.686 INFO  [application-akka.actor.default-dispatcher-47] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:41:00.686 WARN  [application-akka.actor.default-dispatcher-47] [nossis.security.Security] Session time: 2025-06-30T16:11:27.254Z
2025-06-30T14:41:01.000 INFO  [application-akka.actor.default-dispatcher-47] [VersionController] Getting all versions.
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d8b8eecf-904d-4d8b-a7f6-ff35296995e3]
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Trying http Authentication
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@2486c47b
2025-06-30T14:42:10.425 INFO  [application-akka.actor.default-dispatcher-45] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:42:10.897 WARN  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.054 WARN  [application-akka.actor.default-dispatcher-52] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.457 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:11.457 INFO  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:11.457 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:11.457 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:11.457 WARN  [application-akka.actor.default-dispatcher-45] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.457 WARN  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.457 WARN  [play-dev-mode-akka.actor.default-dispatcher-292] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:42:11.463 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:11.463 INFO  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:11.463 WARN  [application-akka.actor.default-dispatcher-49] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.467 INFO  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:11.467 INFO  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:11.467 WARN  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.627 INFO  [application-akka.actor.default-dispatcher-45] [VersionController] Getting version with the id: 273853.
2025-06-30T14:42:11.639 INFO  [application-akka.actor.default-dispatcher-50] [VersionController] Getting version state machine.
2025-06-30T14:42:11.639 INFO  [application-akka.actor.default-dispatcher-49] [VersionController] Getting all versions.
2025-06-30T14:42:11.656 INFO  [application-akka.actor.default-dispatcher-53] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:42:11.671 INFO  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:42:11.671 INFO  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:42:11.671 WARN  [application-akka.actor.default-dispatcher-53] [nossis.security.Security] Session time: 2025-06-30T16:11:27.644Z
2025-06-30T14:42:11.801 INFO  [application-akka.actor.default-dispatcher-53] [EntityDTController] Fetching cfs search data.
2025-06-30T14:42:11.818 WARN  [application-akka.actor.default-dispatcher-53] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e34cff10-1bf0-4b85-8e95-2a5ae98a2fb8]
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Trying http Authentication
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@26cd6374
2025-06-30T14:44:20.603 INFO  [application-akka.actor.default-dispatcher-50] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:44:21.020 WARN  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.148 WARN  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.318 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:21.318 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:21.318 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:21.318 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:21.318 WARN  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.318 WARN  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.319 WARN  [play-dev-mode-akka.actor.default-dispatcher-362] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:44:21.322 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:21.322 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:21.322 WARN  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.332 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:21.332 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:21.332 WARN  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.495 INFO  [application-akka.actor.default-dispatcher-48] [VersionController] Getting version with the id: 273853.
2025-06-30T14:44:21.505 INFO  [application-akka.actor.default-dispatcher-51] [VersionController] Getting version state machine.
2025-06-30T14:44:21.509 INFO  [application-akka.actor.default-dispatcher-50] [VersionController] Getting all versions.
2025-06-30T14:44:21.509 INFO  [application-akka.actor.default-dispatcher-57] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:44:21.699 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:21.699 INFO  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:21.699 WARN  [application-akka.actor.default-dispatcher-50] [nossis.security.Security] Session time: 2025-06-30T16:11:26.821Z
2025-06-30T14:44:21.826 INFO  [application-akka.actor.default-dispatcher-50] [EntityDTController] Fetching cfs search data.
2025-06-30T14:44:21.845 WARN  [application-akka.actor.default-dispatcher-50] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:44:55.539 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [45e8d40d-09f1-48b9-8ad5-9c83db7f1252]
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Trying http Authentication
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@549b11a0
2025-06-30T14:44:55.540 INFO  [application-akka.actor.default-dispatcher-48] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:44:55.954 WARN  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.077 WARN  [application-akka.actor.default-dispatcher-55] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.266 INFO  [application-akka.actor.default-dispatcher-55] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:56.266 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:56.266 INFO  [application-akka.actor.default-dispatcher-55] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:56.266 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:56.266 WARN  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.266 WARN  [application-akka.actor.default-dispatcher-55] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.267 WARN  [play-dev-mode-akka.actor.default-dispatcher-347] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:44:56.271 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:56.271 INFO  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:56.272 WARN  [application-akka.actor.default-dispatcher-48] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.275 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:56.276 INFO  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:56.276 WARN  [application-akka.actor.default-dispatcher-56] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.441 INFO  [application-akka.actor.default-dispatcher-57] [VersionController] Getting version with the id: 273853.
2025-06-30T14:44:56.450 INFO  [application-akka.actor.default-dispatcher-55] [VersionController] Getting version state machine.
2025-06-30T14:44:56.453 INFO  [application-akka.actor.default-dispatcher-48] [VersionController] Getting all versions.
2025-06-30T14:44:56.456 INFO  [application-akka.actor.default-dispatcher-56] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:44:56.635 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:44:56.635 INFO  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:44:56.635 WARN  [application-akka.actor.default-dispatcher-57] [nossis.security.Security] Session time: 2025-06-30T16:11:26.730Z
2025-06-30T14:44:56.762 INFO  [application-akka.actor.default-dispatcher-57] [EntityDTController] Fetching cfs search data.
2025-06-30T14:44:56.779 WARN  [application-akka.actor.default-dispatcher-57] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [81aabdae-6967-455c-9d2d-928420acdaa3]
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Trying http Authentication
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@69703028
2025-06-30T14:47:52.746 INFO  [application-akka.actor.default-dispatcher-44] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:47:53.393 WARN  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:47:53.539 WARN  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:47:53.710 INFO  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:47:53.710 INFO  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:47:53.710 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:47:53.710 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:47:53.710 WARN  [application-akka.actor.default-dispatcher-54] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:47:53.710 WARN  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:47:53.842 INFO  [application-akka.actor.default-dispatcher-58] [VersionController] Getting version state machine.
2025-06-30T14:47:53.848 INFO  [application-akka.actor.default-dispatcher-54] [VersionController] Getting version with the id: 273853.
2025-06-30T14:48:03.140 WARN  [play-dev-mode-akka.actor.default-dispatcher-365] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:48:03.147 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:03.148 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:03.148 WARN  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:48:03.160 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:03.160 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:03.161 WARN  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:48:03.284 INFO  [application-akka.actor.default-dispatcher-58] [VersionController] Getting all versions.
2025-06-30T14:48:03.289 INFO  [application-akka.actor.default-dispatcher-59] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:48:03.490 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:03.490 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:03.490 WARN  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Session time: 2025-06-30T16:11:27.165Z
2025-06-30T14:48:03.686 INFO  [application-akka.actor.default-dispatcher-59] [EntityDTController] Fetching cfs search data.
2025-06-30T14:48:03.701 WARN  [application-akka.actor.default-dispatcher-59] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [49052682-1052-4347-86f8-1f4ebe3cc8cc]
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Trying http Authentication
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3201c085
2025-06-30T14:48:24.180 INFO  [application-akka.actor.default-dispatcher-59] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:48:24.618 WARN  [application-akka.actor.default-dispatcher-59] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:48:24.729 WARN  [application-akka.actor.default-dispatcher-61] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:48:24.901 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:24.901 INFO  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:24.901 INFO  [application-akka.actor.default-dispatcher-60] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:48:24.901 INFO  [application-akka.actor.default-dispatcher-60] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:48:24.901 WARN  [application-akka.actor.default-dispatcher-58] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:48:24.901 WARN  [application-akka.actor.default-dispatcher-60] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:48:25.037 INFO  [application-akka.actor.default-dispatcher-58] [VersionController] Getting version with the id: 273853.
2025-06-30T14:48:25.046 INFO  [application-akka.actor.default-dispatcher-60] [VersionController] Getting version state machine.
2025-06-30T14:50:29.810 WARN  [play-dev-mode-akka.actor.default-dispatcher-367] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:50:29.823 INFO  [application-akka.actor.default-dispatcher-61] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:50:29.824 INFO  [application-akka.actor.default-dispatcher-61] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:50:29.824 WARN  [application-akka.actor.default-dispatcher-61] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:50:29.842 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:50:29.843 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:50:29.843 WARN  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:50:30.110 INFO  [application-akka.actor.default-dispatcher-51] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:50:30.123 INFO  [application-akka.actor.default-dispatcher-61] [VersionController] Getting all versions.
2025-06-30T14:50:30.202 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:50:30.202 INFO  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:50:30.202 WARN  [application-akka.actor.default-dispatcher-51] [nossis.security.Security] Session time: 2025-06-30T16:11:27.418Z
2025-06-30T14:50:30.334 INFO  [application-akka.actor.default-dispatcher-51] [EntityDTController] Fetching cfs search data.
2025-06-30T14:50:30.352 WARN  [application-akka.actor.default-dispatcher-51] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [addf9dc8-160e-43d4-b6d6-3f871ea5bfa5]
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Trying http Authentication
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@32e68749
2025-06-30T14:55:33.279 INFO  [application-akka.actor.default-dispatcher-64] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:55:33.780 WARN  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:33.903 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:34.085 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:34.085 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:34.085 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:34.085 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:34.085 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:34.085 WARN  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:34.228 INFO  [application-akka.actor.default-dispatcher-68] [VersionController] Getting version state machine.
2025-06-30T14:55:34.231 INFO  [application-akka.actor.default-dispatcher-70] [VersionController] Getting version with the id: 273853.
2025-06-30T14:55:53.984 WARN  [play-dev-mode-akka.actor.default-dispatcher-379] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:55:53.989 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:53.989 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:53.990 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:54.007 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:54.007 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:54.008 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:54.190 INFO  [application-akka.actor.default-dispatcher-67] [VersionController] Getting all versions.
2025-06-30T14:55:54.197 INFO  [application-akka.actor.default-dispatcher-68] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:55:54.386 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:55:54.387 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:55:54.387 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:27.569Z
2025-06-30T14:55:54.513 INFO  [application-akka.actor.default-dispatcher-68] [EntityDTController] Fetching cfs search data.
2025-06-30T14:55:54.537 WARN  [application-akka.actor.default-dispatcher-68] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [1dff05f2-a6e4-49a7-ad42-b991e717f586]
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Trying http Authentication
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@630dffa9
2025-06-30T14:56:10.734 INFO  [application-akka.actor.default-dispatcher-68] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:56:11.195 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:11.346 WARN  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:11.530 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:11.530 INFO  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:11.531 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:11.531 WARN  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:11.531 INFO  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:11.531 WARN  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:11.667 INFO  [application-akka.actor.default-dispatcher-70] [VersionController] Getting version state machine.
2025-06-30T14:56:11.674 INFO  [application-akka.actor.default-dispatcher-66] [VersionController] Getting version with the id: 273853.
2025-06-30T14:56:30.658 WARN  [play-dev-mode-akka.actor.default-dispatcher-374] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:56:30.662 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:30.662 INFO  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:30.662 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:30.673 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:30.673 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:30.673 WARN  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:30.866 INFO  [application-akka.actor.default-dispatcher-68] [VersionController] Getting all versions.
2025-06-30T14:56:30.872 INFO  [application-akka.actor.default-dispatcher-70] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:56:31.023 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:31.024 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:31.024 WARN  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Session time: 2025-06-30T16:11:26.983Z
2025-06-30T14:56:31.161 INFO  [application-akka.actor.default-dispatcher-70] [EntityDTController] Fetching cfs search data.
2025-06-30T14:56:31.175 WARN  [application-akka.actor.default-dispatcher-70] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f1bd5474-bc8b-44b1-a202-fe713bb4e5f3]
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [nossis.security.Security] Trying http Authentication
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@7f64c524
2025-06-30T14:56:34.498 INFO  [application-akka.actor.default-dispatcher-70] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:56:34.817 WARN  [application-akka.actor.default-dispatcher-68] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T14:56:35.056 WARN  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T14:56:35.208 INFO  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:35.208 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:56:35.208 INFO  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:35.208 INFO  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:56:35.208 WARN  [application-akka.actor.default-dispatcher-66] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T14:56:35.209 WARN  [application-akka.actor.default-dispatcher-65] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T14:56:35.339 INFO  [application-akka.actor.default-dispatcher-65] [VersionController] Getting version state machine.
2025-06-30T14:56:35.346 INFO  [application-akka.actor.default-dispatcher-66] [VersionController] Getting version with the id: 273853.
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [1e1cb84f-1920-4a4e-8e26-4bcea058614a]
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Trying http Authentication
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@547bd455
2025-06-30T14:59:05.176 INFO  [application-akka.actor.default-dispatcher-69] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:59:05.763 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:05.912 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:06.101 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:06.101 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:06.101 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:06.101 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:06.101 WARN  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:06.101 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:06.227 INFO  [application-akka.actor.default-dispatcher-75] [VersionController] Getting version with the id: 273853.
2025-06-30T14:59:06.234 INFO  [application-akka.actor.default-dispatcher-69] [VersionController] Getting version state machine.
2025-06-30T14:59:14.126 WARN  [play-dev-mode-akka.actor.default-dispatcher-380] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:59:14.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:14.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:14.130 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:14.145 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:14.145 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:14.145 WARN  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:14.270 INFO  [application-akka.actor.default-dispatcher-73] [VersionController] Getting all versions.
2025-06-30T14:59:14.276 INFO  [application-akka.actor.default-dispatcher-75] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:59:14.483 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:14.483 INFO  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:14.483 WARN  [application-akka.actor.default-dispatcher-75] [nossis.security.Security] Session time: 2025-06-30T16:11:27.406Z
2025-06-30T14:59:14.626 INFO  [application-akka.actor.default-dispatcher-75] [EntityDTController] Fetching cfs search data.
2025-06-30T14:59:14.674 WARN  [application-akka.actor.default-dispatcher-75] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3a180980-8b08-40cc-9962-8c49cd489440]
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying authentication with credential
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Trying http Authentication
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3f1269eb
2025-06-30T14:59:48.130 INFO  [application-akka.actor.default-dispatcher-73] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T14:59:48.564 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:48.693 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:48.869 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:48.869 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:48.869 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:48.869 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:48.869 WARN  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:48.869 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:48.994 INFO  [application-akka.actor.default-dispatcher-64] [VersionController] Getting version state machine.
2025-06-30T14:59:49.014 INFO  [application-akka.actor.default-dispatcher-74] [VersionController] Getting version with the id: 273853.
2025-06-30T14:59:54.463 WARN  [play-dev-mode-akka.actor.default-dispatcher-387] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T14:59:54.474 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:54.474 INFO  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:54.474 WARN  [application-akka.actor.default-dispatcher-64] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:54.488 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:54.488 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:54.488 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:54.607 INFO  [application-akka.actor.default-dispatcher-64] [VersionController] Getting all versions.
2025-06-30T14:59:54.615 INFO  [application-akka.actor.default-dispatcher-74] [EntityDTController] Fetching cfs search table config json.
2025-06-30T14:59:54.817 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T14:59:54.817 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T14:59:54.817 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.325Z
2025-06-30T14:59:54.942 INFO  [application-akka.actor.default-dispatcher-74] [EntityDTController] Fetching cfs search data.
2025-06-30T14:59:54.956 WARN  [application-akka.actor.default-dispatcher-74] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:00:07.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:07.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:07.985 INFO  [application-akka.actor.default-dispatcher-74] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [a5191d8a-23f0-4104-b5b0-5f15d9f57ef2]
2025-06-30T15:00:07.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:00:07.986 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:00:07.986 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:00:07.986 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying http Authentication
2025-06-30T15:00:07.986 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@31778912
2025-06-30T15:00:07.986 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:00:08.345 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.130Z
2025-06-30T15:00:08.469 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.130Z
2025-06-30T15:00:08.656 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:08.656 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:08.656 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:08.656 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:08.656 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.130Z
2025-06-30T15:00:08.656 WARN  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Session time: 2025-06-30T16:11:27.130Z
2025-06-30T15:00:08.787 INFO  [application-akka.actor.default-dispatcher-74] [VersionController] Getting version with the id: 273853.
2025-06-30T15:00:08.791 INFO  [application-akka.actor.default-dispatcher-72] [VersionController] Getting version state machine.
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [bd36018b-b70a-4cca-a9c0-6c6df5fac1e0]
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [nossis.security.Security] Trying http Authentication
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5bf521ae
2025-06-30T15:00:38.806 INFO  [application-akka.actor.default-dispatcher-72] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:00:39.214 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:39.346 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:39.525 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:39.525 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:39.525 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:39.525 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:39.525 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:39.525 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:39.651 INFO  [application-akka.actor.default-dispatcher-76] [VersionController] Getting version with the id: 273853.
2025-06-30T15:00:39.657 INFO  [application-akka.actor.default-dispatcher-73] [VersionController] Getting version state machine.
2025-06-30T15:00:46.291 WARN  [play-dev-mode-akka.actor.default-dispatcher-380] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:00:46.294 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:46.294 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:46.294 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:46.303 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:00:46.303 INFO  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:00:46.303 WARN  [application-akka.actor.default-dispatcher-69] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:00:46.420 INFO  [application-akka.actor.default-dispatcher-73] [VersionController] Getting all versions.
2025-06-30T15:00:46.459 INFO  [application-akka.actor.default-dispatcher-69] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:01:23.355 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:01:23.355 INFO  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:01:23.355 WARN  [application-akka.actor.default-dispatcher-73] [nossis.security.Security] Session time: 2025-06-30T16:11:27.016Z
2025-06-30T15:01:23.680 INFO  [application-akka.actor.default-dispatcher-73] [EntityDTController] Fetching cfs search data.
2025-06-30T15:01:23.826 WARN  [application-akka.actor.default-dispatcher-73] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:03:36.984 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:36.984 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:36.984 INFO  [application-akka.actor.default-dispatcher-74] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [a7d0303f-9c48-4bc0-a7fd-412ea3ccc754]
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Trying http Authentication
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@10010847
2025-06-30T15:03:36.985 INFO  [application-akka.actor.default-dispatcher-74] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:03:37.428 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:37.555 WARN  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:37.732 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:37.732 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:37.732 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:37.732 INFO  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:37.732 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:37.732 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:37.858 INFO  [application-akka.actor.default-dispatcher-76] [VersionController] Getting version state machine.
2025-06-30T15:03:37.868 INFO  [application-akka.actor.default-dispatcher-67] [VersionController] Getting version with the id: 273853.
2025-06-30T15:03:42.970 WARN  [play-dev-mode-akka.actor.default-dispatcher-376] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:03:42.989 INFO  [application-akka.actor.default-dispatcher-79] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:42.990 INFO  [application-akka.actor.default-dispatcher-79] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:42.990 WARN  [application-akka.actor.default-dispatcher-79] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:43.012 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:43.013 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:43.013 WARN  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:43.133 INFO  [application-akka.actor.default-dispatcher-79] [VersionController] Getting all versions.
2025-06-30T15:03:43.143 INFO  [application-akka.actor.default-dispatcher-78] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:03:58.061 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:03:58.061 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:03:58.061 WARN  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Session time: 2025-06-30T16:11:27.215Z
2025-06-30T15:03:58.257 INFO  [application-akka.actor.default-dispatcher-78] [EntityDTController] Fetching cfs search data.
2025-06-30T15:03:58.278 WARN  [application-akka.actor.default-dispatcher-78] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [57c2dc59-f52c-4610-b0cd-a3b0f18b4576]
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Trying http Authentication
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@91b6e07
2025-06-30T15:04:15.918 INFO  [application-akka.actor.default-dispatcher-78] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:04:16.311 WARN  [application-akka.actor.default-dispatcher-78] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:16.450 WARN  [application-akka.actor.default-dispatcher-76] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:16.636 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:04:16.636 INFO  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:04:16.637 INFO  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:04:16.637 INFO  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:04:16.637 WARN  [application-akka.actor.default-dispatcher-81] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:16.637 WARN  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:16.773 INFO  [application-akka.actor.default-dispatcher-81] [VersionController] Getting version with the id: 273853.
2025-06-30T15:04:16.781 INFO  [application-akka.actor.default-dispatcher-83] [VersionController] Getting version state machine.
2025-06-30T15:04:19.627 WARN  [play-dev-mode-akka.actor.default-dispatcher-398] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:04:19.656 INFO  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:04:19.656 INFO  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:04:19.656 WARN  [application-akka.actor.default-dispatcher-83] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:19.671 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:04:19.672 INFO  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:04:19.672 WARN  [application-akka.actor.default-dispatcher-77] [nossis.security.Security] Session time: 2025-06-30T16:11:27.108Z
2025-06-30T15:04:19.793 INFO  [application-akka.actor.default-dispatcher-83] [VersionController] Getting all versions.
2025-06-30T15:04:19.801 INFO  [application-akka.actor.default-dispatcher-77] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e2145a2d-472e-40a2-8b53-bf9bf9bab383]
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Trying http Authentication
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@130b4771
2025-06-30T15:08:22.672 INFO  [application-akka.actor.default-dispatcher-82] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:08:23.144 WARN  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:23.272 WARN  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:23.456 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:23.456 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:23.456 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:23.456 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:23.456 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:23.456 WARN  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:23.583 INFO  [application-akka.actor.default-dispatcher-80] [VersionController] Getting version with the id: 273853.
2025-06-30T15:08:23.590 INFO  [application-akka.actor.default-dispatcher-84] [VersionController] Getting version state machine.
2025-06-30T15:08:26.786 WARN  [play-dev-mode-akka.actor.default-dispatcher-401] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:08:26.803 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:26.804 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:26.805 WARN  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:26.814 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:26.814 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:26.814 WARN  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:26.940 INFO  [application-akka.actor.default-dispatcher-89] [VersionController] Getting all versions.
2025-06-30T15:08:26.947 INFO  [application-akka.actor.default-dispatcher-84] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:08:27.157 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:27.157 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:27.157 WARN  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:27.286 INFO  [application-akka.actor.default-dispatcher-84] [EntityDTController] Fetching cfs search data.
2025-06-30T15:08:27.307 WARN  [application-akka.actor.default-dispatcher-84] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:08:43.937 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:08:43.937 INFO  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:08:43.937 WARN  [application-akka.actor.default-dispatcher-84] [nossis.security.Security] Session time: 2025-06-30T16:11:26.948Z
2025-06-30T15:08:44.139 INFO  [application-akka.actor.default-dispatcher-84] [VersionController] Getting all versions.
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [f6e8d0c0-5768-4245-bcef-b5b2431133a8]
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying http Authentication
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6fb3fe5b
2025-06-30T15:09:23.758 INFO  [application-akka.actor.default-dispatcher-89] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:09:24.148 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.284 WARN  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.463 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:24.463 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:24.463 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:24.463 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:24.463 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.463 WARN  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.463 WARN  [play-dev-mode-akka.actor.default-dispatcher-400] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:09:24.467 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:24.467 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:24.467 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.475 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:24.475 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:24.475 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.623 INFO  [application-akka.actor.default-dispatcher-88] [VersionController] Getting version with the id: 273853.
2025-06-30T15:09:24.632 INFO  [application-akka.actor.default-dispatcher-74] [VersionController] Getting all versions.
2025-06-30T15:09:24.634 INFO  [application-akka.actor.default-dispatcher-87] [VersionController] Getting version state machine.
2025-06-30T15:09:24.639 INFO  [application-akka.actor.default-dispatcher-67] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:09:24.840 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:24.840 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:24.840 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:26.934Z
2025-06-30T15:09:24.981 INFO  [application-akka.actor.default-dispatcher-88] [EntityDTController] Fetching cfs search data.
2025-06-30T15:09:24.996 WARN  [application-akka.actor.default-dispatcher-88] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d852d976-841a-4ae7-bb30-03ba0df4b55b]
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Trying http Authentication
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@68bd2563
2025-06-30T15:09:29.941 INFO  [application-akka.actor.default-dispatcher-88] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:09:30.267 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:30.464 WARN  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:30.641 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:30.641 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:30.641 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:30.641 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:30.641 WARN  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:30.641 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:30.765 INFO  [application-akka.actor.default-dispatcher-89] [VersionController] Getting version with the id: 273853.
2025-06-30T15:09:30.777 INFO  [application-akka.actor.default-dispatcher-80] [VersionController] Getting version state machine.
2025-06-30T15:09:38.495 WARN  [play-dev-mode-akka.actor.default-dispatcher-373] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:09:38.507 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:38.507 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:38.507 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:38.520 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:38.520 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:38.520 WARN  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:38.705 INFO  [application-akka.actor.default-dispatcher-80] [VersionController] Getting all versions.
2025-06-30T15:09:38.710 INFO  [application-akka.actor.default-dispatcher-87] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:09:38.851 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:09:38.851 INFO  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:09:38.851 WARN  [application-akka.actor.default-dispatcher-87] [nossis.security.Security] Session time: 2025-06-30T16:11:27.091Z
2025-06-30T15:09:40.079 INFO  [application-akka.actor.default-dispatcher-87] [EntityDTController] Fetching cfs search data.
2025-06-30T15:09:40.135 WARN  [application-akka.actor.default-dispatcher-87] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b0d849d6-032a-46db-8644-83eac503ed80]
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Trying http Authentication
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4082c2b0
2025-06-30T15:10:28.425 INFO  [application-akka.actor.default-dispatcher-80] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:10:28.813 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:10:28.943 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:10:29.130 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:10:29.130 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:10:29.130 WARN  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:10:29.253 INFO  [application-akka.actor.default-dispatcher-89] [VersionController] Getting version state machine.
2025-06-30T15:10:35.548 WARN  [play-dev-mode-akka.actor.default-dispatcher-426] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:10:43.619 INFO  [application-akka.actor.default-dispatcher-86] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:10:43.619 INFO  [application-akka.actor.default-dispatcher-86] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:10:43.619 WARN  [application-akka.actor.default-dispatcher-86] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:10:43.637 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:10:43.637 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:10:43.637 WARN  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:10:43.831 INFO  [application-akka.actor.default-dispatcher-86] [VersionController] Getting all versions.
2025-06-30T15:10:43.835 INFO  [application-akka.actor.default-dispatcher-89] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:11:37.484 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:11:37.484 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:11:37.484 INFO  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:11:37.484 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:11:37.484 WARN  [application-akka.actor.default-dispatcher-74] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:11:37.484 WARN  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Session time: 2025-06-30T16:11:27.603Z
2025-06-30T15:11:37.960 INFO  [application-akka.actor.default-dispatcher-89] [EntityDTController] Fetching cfs search data.
2025-06-30T15:11:37.967 INFO  [application-akka.actor.default-dispatcher-74] [EntityDTController] Fetching cfs search data.
2025-06-30T15:11:38.123 WARN  [application-akka.actor.default-dispatcher-89] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:11:38.123 WARN  [application-akka.actor.default-dispatcher-74] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [ec5651a5-b2d1-4d9e-9f8f-4b3447c557ca]
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [nossis.security.Security] Trying http Authentication
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@581e683a
2025-06-30T15:12:25.339 INFO  [application-akka.actor.default-dispatcher-89] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:12:25.782 WARN  [application-akka.actor.default-dispatcher-80] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:25.903 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.100 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:26.100 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:26.100 INFO  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:26.100 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:26.100 WARN  [application-akka.actor.default-dispatcher-67] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.100 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.101 WARN  [play-dev-mode-akka.actor.default-dispatcher-429] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:12:26.105 INFO  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:26.106 INFO  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:26.106 WARN  [application-akka.actor.default-dispatcher-85] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.109 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:26.109 INFO  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:26.109 WARN  [application-akka.actor.default-dispatcher-82] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.268 INFO  [application-akka.actor.default-dispatcher-88] [VersionController] Getting version with the id: 273853.
2025-06-30T15:12:26.285 INFO  [application-akka.actor.default-dispatcher-85] [VersionController] Getting all versions.
2025-06-30T15:12:26.286 INFO  [application-akka.actor.default-dispatcher-67] [VersionController] Getting version state machine.
2025-06-30T15:12:26.291 INFO  [application-akka.actor.default-dispatcher-82] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:12:26.519 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:12:26.519 INFO  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:12:26.519 WARN  [application-akka.actor.default-dispatcher-88] [nossis.security.Security] Session time: 2025-06-30T16:11:27.581Z
2025-06-30T15:12:26.727 INFO  [application-akka.actor.default-dispatcher-88] [EntityDTController] Fetching cfs search data.
2025-06-30T15:12:26.757 WARN  [application-akka.actor.default-dispatcher-88] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [d2c685d0-a1b2-43dd-ac6e-8600acffaf86]
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Trying http Authentication
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@38dd53fd
2025-06-30T15:23:36.623 INFO  [application-akka.actor.default-dispatcher-92] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:23:37.283 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.410 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.611 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.611 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.611 INFO  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.611 INFO  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.611 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.611 WARN  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.611 WARN  [play-dev-mode-akka.actor.default-dispatcher-484] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:23:37.616 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.616 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.616 WARN  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.620 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.620 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.620 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.782 INFO  [application-akka.actor.default-dispatcher-94] [VersionController] Getting version with the id: 273853.
2025-06-30T15:23:37.793 INFO  [application-akka.actor.default-dispatcher-100] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:23:37.796 INFO  [application-akka.actor.default-dispatcher-93] [VersionController] Getting version state machine.
2025-06-30T15:23:37.798 INFO  [application-akka.actor.default-dispatcher-96] [VersionController] Getting all versions.
2025-06-30T15:23:37.943 INFO  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.943 INFO  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.943 WARN  [application-akka.actor.default-dispatcher-94] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:37.982 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:37.982 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:37.982 WARN  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:38.087 INFO  [application-akka.actor.default-dispatcher-94] [EntityDTController] Fetching cfs search data.
2025-06-30T15:23:38.105 WARN  [application-akka.actor.default-dispatcher-94] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:23:38.115 INFO  [application-akka.actor.default-dispatcher-96] [VersionController] Getting all versions.
2025-06-30T15:23:39.619 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:23:39.619 INFO  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:23:39.619 WARN  [application-akka.actor.default-dispatcher-96] [nossis.security.Security] Session time: 2025-06-30T16:11:27.057Z
2025-06-30T15:23:39.763 INFO  [application-akka.actor.default-dispatcher-96] [VersionController] Getting all versions.
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [80bee5b0-e597-41db-8083-7032726e2891]
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying http Authentication
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1305bca2
2025-06-30T15:25:18.535 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:25:19.012 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.161 WARN  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.346 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.346 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.346 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.346 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.346 WARN  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.346 WARN  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.346 WARN  [play-dev-mode-akka.actor.default-dispatcher-501] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:25:19.350 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.350 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.350 WARN  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.356 INFO  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.356 INFO  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.356 WARN  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.519 INFO  [application-akka.actor.default-dispatcher-95] [VersionController] Getting all versions.
2025-06-30T15:25:19.526 INFO  [application-akka.actor.default-dispatcher-92] [VersionController] Getting version with the id: 273853.
2025-06-30T15:25:19.529 INFO  [application-akka.actor.default-dispatcher-99] [VersionController] Getting version state machine.
2025-06-30T15:25:19.541 INFO  [application-akka.actor.default-dispatcher-98] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:25:19.725 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.725 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.725 WARN  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.735 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:19.735 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:19.735 WARN  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:19.865 INFO  [application-akka.actor.default-dispatcher-92] [EntityDTController] Fetching cfs search data.
2025-06-30T15:25:19.867 INFO  [application-akka.actor.default-dispatcher-95] [VersionController] Getting all versions.
2025-06-30T15:25:19.881 WARN  [application-akka.actor.default-dispatcher-95] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:25:23.998 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:25:23.998 INFO  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:25:23.998 WARN  [application-akka.actor.default-dispatcher-92] [nossis.security.Security] Session time: 2025-06-30T16:11:26.798Z
2025-06-30T15:25:24.159 INFO  [application-akka.actor.default-dispatcher-92] [VersionController] Getting all versions.
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [a161b6a3-b996-41a4-bd09-79876b07c44e]
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Trying http Authentication
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@59f5ac75
2025-06-30T15:26:06.621 INFO  [application-akka.actor.default-dispatcher-95] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:26:07.242 WARN  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.393 WARN  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.570 INFO  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.570 INFO  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.570 INFO  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.570 INFO  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.570 WARN  [application-akka.actor.default-dispatcher-98] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.570 WARN  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.570 WARN  [play-dev-mode-akka.actor.default-dispatcher-466] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:26:07.573 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.573 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.573 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.578 INFO  [application-akka.actor.default-dispatcher-101] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.578 INFO  [application-akka.actor.default-dispatcher-101] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.578 WARN  [application-akka.actor.default-dispatcher-101] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.737 INFO  [application-akka.actor.default-dispatcher-100] [VersionController] Getting all versions.
2025-06-30T15:26:07.745 INFO  [application-akka.actor.default-dispatcher-97] [VersionController] Getting version with the id: 273853.
2025-06-30T15:26:07.747 INFO  [application-akka.actor.default-dispatcher-98] [VersionController] Getting version state machine.
2025-06-30T15:26:07.751 INFO  [application-akka.actor.default-dispatcher-101] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:26:07.924 INFO  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.924 INFO  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.924 WARN  [application-akka.actor.default-dispatcher-97] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:07.932 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:07.932 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:07.932 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:08.047 INFO  [application-akka.actor.default-dispatcher-97] [EntityDTController] Fetching cfs search data.
2025-06-30T15:26:08.055 INFO  [application-akka.actor.default-dispatcher-100] [VersionController] Getting all versions.
2025-06-30T15:26:08.069 WARN  [application-akka.actor.default-dispatcher-100] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:26:08.978 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:26:08.978 INFO  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:26:08.978 WARN  [application-akka.actor.default-dispatcher-100] [nossis.security.Security] Session time: 2025-06-30T16:11:26.827Z
2025-06-30T15:26:09.113 INFO  [application-akka.actor.default-dispatcher-100] [VersionController] Getting all versions.
2025-06-30T15:28:37.452 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_REMOVED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000009', stat=77,77,1751291246832,1751291246832,0,0,0,72057605190189072,44,0,77
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [afadc464-3dff-4c52-ba1f-20ca965dda57]
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying http Authentication
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6c622b0
2025-06-30T15:29:35.037 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:29:35.489 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.604 WARN  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.790 INFO  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:35.790 INFO  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:35.790 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:35.790 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:35.790 WARN  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.790 WARN  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.790 WARN  [play-dev-mode-akka.actor.default-dispatcher-527] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:29:35.794 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:35.794 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:35.794 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.798 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:35.798 INFO  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:35.798 WARN  [application-akka.actor.default-dispatcher-95] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:35.963 INFO  [application-akka.actor.default-dispatcher-93] [VersionController] Getting all versions.
2025-06-30T15:29:35.967 INFO  [application-akka.actor.default-dispatcher-107] [VersionController] Getting version with the id: 273853.
2025-06-30T15:29:35.974 INFO  [application-akka.actor.default-dispatcher-108] [VersionController] Getting version state machine.
2025-06-30T15:29:35.976 INFO  [application-akka.actor.default-dispatcher-95] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:29:36.153 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:36.153 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:36.153 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:36.156 INFO  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:36.156 INFO  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:36.156 WARN  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:36.280 INFO  [application-akka.actor.default-dispatcher-93] [EntityDTController] Fetching cfs search data.
2025-06-30T15:29:36.287 INFO  [application-akka.actor.default-dispatcher-107] [VersionController] Getting all versions.
2025-06-30T15:29:36.304 WARN  [application-akka.actor.default-dispatcher-107] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:29:37.029 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:37.029 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:37.029 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:37.169 INFO  [application-akka.actor.default-dispatcher-93] [VersionController] Getting all versions.
2025-06-30T15:29:38.105 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:29:38.105 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:29:38.105 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.234Z
2025-06-30T15:29:38.247 INFO  [application-akka.actor.default-dispatcher-93] [VersionController] Getting all versions.
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [01a7a07b-7aa2-4154-87e4-ca2c659353a8]
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Trying http Authentication
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6cebdb4b
2025-06-30T15:30:07.780 INFO  [application-akka.actor.default-dispatcher-93] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:30:08.262 WARN  [application-akka.actor.default-dispatcher-107] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.390 WARN  [application-akka.actor.default-dispatcher-105] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.593 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.593 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.593 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.593 INFO  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.593 WARN  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.593 WARN  [application-akka.actor.default-dispatcher-93] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.594 WARN  [play-dev-mode-akka.actor.default-dispatcher-549] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:30:08.598 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.598 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.598 WARN  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.605 INFO  [application-akka.actor.default-dispatcher-104] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.606 INFO  [application-akka.actor.default-dispatcher-104] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.606 WARN  [application-akka.actor.default-dispatcher-104] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.752 INFO  [application-akka.actor.default-dispatcher-93] [VersionController] Getting version state machine.
2025-06-30T15:30:08.763 INFO  [application-akka.actor.default-dispatcher-108] [VersionController] Getting version with the id: 273853.
2025-06-30T15:30:08.763 INFO  [application-akka.actor.default-dispatcher-99] [VersionController] Getting all versions.
2025-06-30T15:30:08.771 INFO  [application-akka.actor.default-dispatcher-104] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:30:08.968 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.968 INFO  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.968 WARN  [application-akka.actor.default-dispatcher-99] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:08.978 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:08.978 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:08.978 WARN  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:09.099 INFO  [application-akka.actor.default-dispatcher-99] [EntityDTController] Fetching cfs search data.
2025-06-30T15:30:09.107 INFO  [application-akka.actor.default-dispatcher-108] [VersionController] Getting all versions.
2025-06-30T15:30:09.115 WARN  [application-akka.actor.default-dispatcher-108] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:30:10.569 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:30:10.569 INFO  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:30:10.569 WARN  [application-akka.actor.default-dispatcher-108] [nossis.security.Security] Session time: 2025-06-30T16:11:27.054Z
2025-06-30T15:30:10.705 INFO  [application-akka.actor.default-dispatcher-108] [VersionController] Getting all versions.
2025-06-30T15:31:31.160 INFO  [application-akka.actor.default-dispatcher-104] [akka.actor.CoordinatedShutdown] Running CoordinatedShutdown with reason [ApplicationShutdownReason]
2025-06-30T15:31:31.271 INFO  [application-akka.actor.default-dispatcher-6] [event.slf4j.Slf4jLogger] Slf4jLogger started
2025-06-30T15:31:31.329 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [asf.componentsmanager.ComponentsManager] Update component 'NOSSIS-TSC-SERVICE-LOCATOR-COMPONENT' with an instance of 'pt.ptinovacao.nossistsc.cluster.ServiceLocator' and set is rewritable value to 'true'.
2025-06-30T15:31:31.331 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperCluster$] null-683706896 new : { hash: 683706896, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}
2025-06-30T15:31:31.331 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperServices$] new : { hash: 1298288445, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperWatchDog] before start CuratorFramework: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: DISCONNECTED, observers: 0, connectionTimeoutMs: 15000)
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [framework.imps.CuratorFrameworkImpl] Starting
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [apache.zookeeper.ZooKeeper] Initiating client connection, connectString=nossistsc-ci.c.ptin.corppt.com:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@784f5909
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [apache.zookeeper.ClientCnxnSocket] jute.maxbuffer value is 1048575 Bytes
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [apache.zookeeper.ClientCnxn] zookeeper.request.timeout value is 0. feature enabled=false
2025-06-30T15:31:31.332 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [framework.imps.CuratorFrameworkImpl] Default schema
2025-06-30T15:31:31.486 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Opening socket connection to server nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181.
2025-06-30T15:31:31.486 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] SASL config status: Will not attempt to authenticate using SASL (unknown error)
2025-06-30T15:31:31.494 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Socket connection established, initiating session, client: /10.112.211.10:45256, server: nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181
2025-06-30T15:31:31.506 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-SendThread(nossistsc-ci.c.ptin.corppt.com:2181)] [apache.zookeeper.ClientCnxn] Session establishment complete on server nossistsc-ci.c.ptin.corppt.com/10.113.138.45:2181, session id = 0x100000298ba0012, negotiated timeout = 40000
2025-06-30T15:31:31.507 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-EventThread] [framework.state.ConnectionStateManager] State change: CONNECTED
2025-06-30T15:31:31.507 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperWatchDog] after  start CuratorFramework: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: CONNECTED, observers: 0, connectionTimeoutMs: 15000)
2025-06-30T15:31:31.507 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 start begin: { hash: 1298288445, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}, curator state: STARTED, zookeeperAddress: nossistsc-ci.c.ptin.corppt.com:2181
2025-06-30T15:31:31.516 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-EventThread] [framework.imps.EnsembleTracker] New config event received: {}
2025-06-30T15:31:31.516 INFO  [play-dev-mode-akka.actor.default-dispatcher-546-EventThread] [framework.imps.EnsembleTracker] New config event received: {}
2025-06-30T15:31:31.534 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 start end OK: { hash: 1298288445, ZookeeperServices {root: nossis-tsc-root, serviceGroup: nossis-tsc-rest-backend-service}}, curator state: STARTED; zookeeper cli state: CONNECTED
2025-06-30T15:31:31.534 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperCluster$] null-683706896 start begin: { hash: 683706896, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}, curator state: STARTED, zookeeperAddress: nossistsc-ci.c.ptin.corppt.com:2181
2025-06-30T15:31:31.534 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service', stat=10,10,1751242068357,1751242068357,0,1,0,0,10,1,11
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.534 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest', stat=11,11,1751242068500,1751242068500,0,1,0,0,108,1,12
, data=[123, 34, 110, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 45, 116, 115, 99, 45, 114, 101, 115, 116, 34, 44, 34, 105, 112, 34, 58, 34, 34, 44, 34, 112, 111, 114, 116, 34, 58, 48, 44, 34, 102, 117, 108, 108, 65, 100, 100, 114, 101, 115, 115, 34, 58, 34, 34, 44, 34, 105, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 112, 114, 111, 116, 111, 99, 111, 108, 34, 58, 34, 78, 79, 78, 69, 34, 44, 34, 115, 116, 97, 116, 101, 34, 58, 34, 79, 75, 34, 125]}
2025-06-30T15:31:31.541 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest/instances', stat=12,12,1751242068518,1751242068518,0,3,0,0,10,1,43
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.552 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/services/nossis-tsc-rest-backend-service/nossis-tsc-rest/instances/nossistsc-ci.c.ptin.corppt.com:9015', stat=43,43,1751265451119,1751265451119,0,0,0,72057605190189063,272,0,43
, data=[123, 34, 115, 101, 114, 118, 105, 99, 101, 78, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 45, 116, 115, 99, 45, 114, 101, 115, 116, 34, 44, 34, 110, 97, 109, 101, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 58, 57, 48, 49, 53, 34, 44, 34, 105, 112, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 34, 44, 34, 112, 111, 114, 116, 34, 58, 57, 48, 49, 53, 44, 34, 102, 117, 108, 108, 65, 100, 100, 114, 101, 115, 115, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 58, 57, 48, 49, 53, 34, 44, 34, 105, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 34, 58, 34, 78, 111, 115, 115, 105, 115, 32, 84, 115, 99, 32, 69, 110, 116, 105, 116, 105, 101, 115, 32, 67, 97, 116, 97, 108, 111, 103, 32, 83, 101, 114, 118, 105, 99, 101, 32, 73, 110, 115, 116, 97, 110, 99, 101, 34, 44, 34, 112, 114, 111, 116, 111, 99, 111, 108, 34, 58, 34, 72, 84, 84, 80, 34, 44, 34, 115, 116, 97, 116, 101, 34, 58, 34, 79, 75, 34, 125]}
2025-06-30T15:31:31.552 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperServices$] nossis-tsc-rest-backend-service-1298288445 handler childEvent INITIALIZED -> null
2025-06-30T15:31:31.592 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [cluster.api.ZookeeperCluster$] null-683706896 start end OK: { hash: 683706896, ZookeeperCluster {root: nossis-tsc-root, clusterId: nossis-tsc-backend-cluster, thisNode: ClusterNode(null,web-dev,null), isMaster: false, masterNode: ClusterNode(,,null), nodes: [] }}, curator state: STARTED; zookeeper cli state: CONNECTED
2025-06-30T15:31:31.592 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [asf.componentsmanager.ComponentsManager] Component 'NOSSIS-TSC-SERVICE-LOCATOR-COMPONENT' instance of 'pt.ptinovacao.nossistsc.cluster.ServiceLocator' successfully initiated.
2025-06-30T15:31:31.592 INFO  [Curator-ConnectionStateManager-0] [cluster.api.ZookeeperWatchDog] stateChanged: CONNECTED, WatchDog: ZookeeperWatchDog(connectionString: nossistsc-ci.c.ptin.corppt.com:2181, closed: false, connectionState: CONNECTED, observers: 0, connectionTimeoutMs: 15000), this._cf.hashCode: 1739886235 client.hashCode: 1739886235
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster', stat=15,15,1751242068558,1751242068558,0,3,0,0,10,3,20
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster', stat=17,17,1751242068611,1751242068611,0,1,0,0,0,1,18
, data=[]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/configuration', stat=19,19,1751242068649,1751242068649,0,0,0,0,10,0,19
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/nodesConfiguration', stat=20,20,1751242068657,1751242068657,0,1,0,0,10,1,21
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes', stat=18,18,1751242068626,1751242068626,0,20,0,0,10,2,84
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/nodesConfiguration/null', stat=21,21,1751242068671,1751242068671,0,0,0,0,10,0,21
, data=[49, 55, 50, 46, 49, 55, 46, 48, 46, 51]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000002', stat=42,42,1751265451103,1751265451103,0,0,0,72057605190189063,67,0,42
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 110, 111, 115, 115, 105, 115, 116, 115, 99, 45, 99, 105, 46, 99, 46, 112, 116, 105, 110, 46, 99, 111, 114, 112, 112, 116, 46, 99, 111, 109, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000010', stat=83,83,1751292521607,1751292521607,0,0,0,72057605190189073,44,0,83
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:31:31.593 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent INITIALIZED -> null
2025-06-30T15:31:31.593 WARN  [play-dev-mode-akka.actor.default-dispatcher-546] [application] conf-dev/application.conf: 19: play.crypto.secret is deprecated, use play.http.secret.key instead
2025-06-30T15:31:31.611 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000011', stat=89,89,1751297491584,1751297491584,0,0,0,72057605190189074,44,0,89
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:31:31.612 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000011', stat=89,89,1751297491584,1751297491584,0,0,0,72057605190189074,44,0,89
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:31:31.673 WARN  [play-dev-mode-akka.actor.default-dispatcher-546] [validator.messageinterpolation.ParameterMessageInterpolator] HV000184: ParameterMessageInterpolator has been chosen, EL interpolation will not be supported
2025-06-30T15:31:31.676 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [api.http.EnabledFilters] Enabled Filters (see <https://www.playframework.com/documentation/latest/Filters>):


2025-06-30T15:31:31.676 INFO  [play-dev-mode-akka.actor.default-dispatcher-546] [play.api.Play] Application started (Dev)
2025-06-30T15:31:31.685 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:31.685 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [1755ed4c-def7-4a1a-a5b0-408757104d92]
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Trying http Authentication
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5069e313
2025-06-30T15:31:31.686 INFO  [application-akka.actor.default-dispatcher-6] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:31:33.044 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.171 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.353 WARN  [play-dev-mode-akka.actor.default-dispatcher-514] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:31:33.356 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:33.356 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:33.356 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:33.356 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:33.357 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.357 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.357 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:33.357 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:33.357 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.366 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:33.366 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:33.366 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.535 INFO  [application-akka.actor.default-dispatcher-10] [VersionController] Getting version state machine.
2025-06-30T15:31:33.544 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T15:31:33.547 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting version with the id: 273853.
2025-06-30T15:31:33.548 INFO  [application-akka.actor.default-dispatcher-12] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:31:33.564 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = VERSION
2025-06-30T15:31:33.583 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.595 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ROOT.WITH_ATTRIBUTES
2025-06-30T15:31:33.604 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = VERSION
2025-06-30T15:31:33.605 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ROOT
2025-06-30T15:31:33.605 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = VERSION
2025-06-30T15:31:33.606 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ROOT
2025-06-30T15:31:33.606 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ROOT.WITH_ATTRIBUTES
2025-06-30T15:31:33.606 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.614 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.614 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.615 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.615 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ROOT.RESOURCE
2025-06-30T15:31:33.629 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T15:31:33.629 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T15:31:33.630 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T15:31:33.640 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = TAG
2025-06-30T15:31:33.643 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = TAG
2025-06-30T15:31:33.644 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = TAG
2025-06-30T15:31:33.654 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ATTRTYPE
2025-06-30T15:31:33.656 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ATTRTYPE
2025-06-30T15:31:33.657 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ATTRTYPE
2025-06-30T15:31:33.667 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.667 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.668 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.668 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:33.668 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:33.668 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT
2025-06-30T15:31:33.679 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.REGEXCONSTRAINT
2025-06-30T15:31:33.692 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.693 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.694 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.704 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.705 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = VALUE
2025-06-30T15:31:33.705 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.ENUMCONSTRAINT
2025-06-30T15:31:33.716 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.716 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.716 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.716 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.717 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.717 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.SIZECONSTRAINT
2025-06-30T15:31:33.728 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = ATTRIBUTE
2025-06-30T15:31:33.728 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ATTRIBUTE
2025-06-30T15:31:33.730 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ATTRIBUTE
2025-06-30T15:31:33.742 INFO  [application-akka.actor.default-dispatcher-12] [catalog-base-module] begin instantiateCatEntity, name = COMPONENT
2025-06-30T15:31:33.742 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = COMPONENT
2025-06-30T15:31:33.742 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = COMPONENT
2025-06-30T15:31:33.743 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = COMPONENT
2025-06-30T15:31:33.743 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = COMPONENT
2025-06-30T15:31:33.748 ERROR [application-akka.actor.default-dispatcher-12] [application] onServerError 
java.util.concurrent.CompletionException: java.lang.NullPointerException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273) ~[?:1.8.0_452]
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280) ~[?:1.8.0_452]
	at java.util.concurrent.CompletableFuture.uniApply(CompletableFuture.java:618) ~[?:1.8.0_452]
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:591) ~[?:1.8.0_452]
	at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:456) ~[?:1.8.0_452]
	at play.core.j.HttpExecutionContext$$anon$2.run(HttpExecutionContext.scala:65) ~[play_2.12-2.6.25.jar:2.6.25]
	at akka.dispatch.TaskInvocation.run(AbstractDispatcher.scala:49) [akka-actor_2.12-2.6.21.jar:2.6.21]
	at akka.dispatch.ForkJoinExecutorConfigurator$AkkaForkJoinTask.exec(ForkJoinExecutorConfigurator.scala:48) [akka-actor_2.12-2.6.21.jar:2.6.21]
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289) [?:1.8.0_452]
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056) [?:1.8.0_452]
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692) [?:1.8.0_452]
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175) [?:1.8.0_452]
Caused by: java.lang.NullPointerException
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.lambda$instantiateUIElements$9(CatalogImpl.java:804) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateUIElements(CatalogImpl.java:771) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateUISubGroup(CatalogImpl.java:763) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateUISubGroups(CatalogImpl.java:751) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateUIGroups(CatalogImpl.java:739) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateUIContexts(CatalogImpl.java:711) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:433) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationship(CatalogImpl.java:969) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationships(CatalogImpl.java:930) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:431) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationship(CatalogImpl.java:969) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationships(CatalogImpl.java:930) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:431) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationship(CatalogImpl.java:969) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationships(CatalogImpl.java:930) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:431) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationship(CatalogImpl.java:969) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationships(CatalogImpl.java:930) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:431) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationship(CatalogImpl.java:969) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateRelationships(CatalogImpl.java:930) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.instantiateCatEntity(CatalogImpl.java:431) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.matchCatEntity(CatalogImpl.java:386) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntity(CatalogImpl.java:364) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityHolderFromCacheOrInstantiate(CatalogImpl.java:331) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.catalog.CatalogImpl.getCatEntityById(CatalogImpl.java:75) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at tsc.catalog.basemodule.services.CatEntityAPIServices.getCatEntityById(CatEntityAPIServices.java:25) ~[nossis-tsc-frontend-catalog-basemodule.jar:?]
	at nossis.tsccatalog.services.CatEntityAPIServices.getCatEntityById(CatEntityAPIServices.java:23) ~[nossis-tsc-frontend-catalog.jar:?]
	at temp.adapters.AdapterHelper.preFilledBuilder(AdapterHelper.java:52) ~[nossis-tsc-frontend-catalog.jar:?]
	at temp.adapters.VersionAdapter.dtoFrom(VersionAdapter.java:34) ~[nossis-tsc-frontend-catalog.jar:?]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at temp.adapters.VersionAdapter.dtoFrom(VersionAdapter.java:28) ~[nossis-tsc-frontend-catalog.jar:?]
	at temp.controllers.impl.VersionController.lambda$select2GetAllVersions$2(VersionController.java:109) ~[nossis-tsc-frontend-catalog.jar:?]
	at java.util.concurrent.CompletableFuture.uniApply(CompletableFuture.java:616) ~[?:1.8.0_452]
	... 9 more
2025-06-30T15:31:33.755 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CFS
2025-06-30T15:31:33.756 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CFS
2025-06-30T15:31:33.769 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T15:31:33.769 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T15:31:33.779 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T15:31:33.779 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T15:31:33.779 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T15:31:33.796 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.797 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T15:31:33.797 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION
2025-06-30T15:31:33.797 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_CFS_RELATION
2025-06-30T15:31:33.798 INFO  [application-akka.actor.default-dispatcher-6] [EntityDTController] Fetching cfs search data.
2025-06-30T15:31:33.809 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.810 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.817 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = CFS
2025-06-30T15:31:33.821 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RESOURCE_RESOURCE_RELATION
2025-06-30T15:31:33.821 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RESOURCE_RESOURCE_RELATION
2025-06-30T15:31:33.824 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.827 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.832 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.833 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.839 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.845 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = RFS
2025-06-30T15:31:33.846 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = RFS
2025-06-30T15:31:33.850 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.855 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T15:31:33.856 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T15:31:33.860 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T15:31:33.860 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RFS_RELATION
2025-06-30T15:31:33.863 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = RFS
2025-06-30T15:31:33.867 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.867 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = RFS
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CFS
2025-06-30T15:31:33.868 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ATTRIBUTE
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = RFS
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CFS
2025-06-30T15:31:33.870 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ATTRIBUTE
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] begin instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RFS_RELATION
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = RFS
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.RFS_RESOURCE_RELATION
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = RESOURCE
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = ENTITY_RELATION.CFS_RESOURCE_RELATION
2025-06-30T15:31:33.875 INFO  [application-akka.actor.default-dispatcher-6] [catalog-base-module] end instantiateCatEntity, name = CFS
2025-06-30T15:31:33.880 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T15:31:33.880 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T15:31:33.884 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T15:31:33.884 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.RANGECONSTRAINT
2025-06-30T15:31:33.891 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T15:31:33.891 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T15:31:33.891 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = ATTRTYPE
2025-06-30T15:31:33.897 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T15:31:33.897 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = CONSTRAINT.FLOATRANGECONSTRAINT
2025-06-30T15:31:33.897 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = ATTRTYPE
2025-06-30T15:31:33.902 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T15:31:33.902 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T15:31:33.902 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = TAG
2025-06-30T15:31:33.902 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T15:31:33.903 INFO  [application-akka.actor.default-dispatcher-10] [catalog-base-module] end instantiateCatEntity, name = VERSION
2025-06-30T15:31:33.907 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] begin instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T15:31:33.907 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDENTITY
2025-06-30T15:31:33.907 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = TAG
2025-06-30T15:31:33.907 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = AGGREGATEDTYPE
2025-06-30T15:31:33.907 INFO  [application-akka.actor.default-dispatcher-9] [catalog-base-module] end instantiateCatEntity, name = VERSION
2025-06-30T15:31:33.919 WARN  [application-akka.actor.default-dispatcher-9] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:31:37.826 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:37.826 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:37.826 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:26.999Z
2025-06-30T15:31:37.968 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting all versions.
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b7348d02-d30b-4284-b73a-8839bba754c7]
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Trying http Authentication
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@388a5593
2025-06-30T15:31:41.290 INFO  [application-akka.actor.default-dispatcher-9] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:31:41.617 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:41.825 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.030 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.030 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.030 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.030 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.030 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.030 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.031 WARN  [play-dev-mode-akka.actor.default-dispatcher-526] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:31:42.035 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.035 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.035 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.042 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.042 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.042 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.308 INFO  [application-akka.actor.default-dispatcher-7] [VersionController] Getting version with the id: 273853.
2025-06-30T15:31:42.309 INFO  [application-akka.actor.default-dispatcher-11] [VersionController] Getting version state machine.
2025-06-30T15:31:42.314 INFO  [application-akka.actor.default-dispatcher-14] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:31:42.317 INFO  [application-akka.actor.default-dispatcher-13] [VersionController] Getting all versions.
2025-06-30T15:31:42.396 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.396 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.396 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.401 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:42.401 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:42.401 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:42.542 INFO  [application-akka.actor.default-dispatcher-13] [EntityDTController] Fetching cfs search data.
2025-06-30T15:31:42.550 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T15:31:42.559 WARN  [application-akka.actor.default-dispatcher-14] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:31:43.964 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:43.964 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:43.964 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:44.105 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T15:31:45.467 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:31:45.467 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:31:45.468 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.471Z
2025-06-30T15:31:45.628 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [9ac22e7e-a316-4a71-ae46-febd25c7ffd2]
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying http Authentication
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5282f4e1
2025-06-30T15:32:02.478 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:32:02.940 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.078 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.249 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.249 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.249 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.249 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.249 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.249 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.250 WARN  [play-dev-mode-akka.actor.default-dispatcher-514] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:32:03.255 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.255 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.255 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.260 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.260 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.260 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.427 INFO  [application-akka.actor.default-dispatcher-13] [VersionController] Getting version with the id: 273853.
2025-06-30T15:32:03.429 INFO  [application-akka.actor.default-dispatcher-8] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:32:03.432 INFO  [application-akka.actor.default-dispatcher-14] [VersionController] Getting all versions.
2025-06-30T15:32:03.584 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.584 INFO  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.584 WARN  [application-akka.actor.default-dispatcher-8] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.639 INFO  [application-akka.actor.default-dispatcher-6] [VersionController] Getting version state machine.
2025-06-30T15:32:03.649 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:32:03.649 INFO  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:32:03.649 WARN  [application-akka.actor.default-dispatcher-6] [nossis.security.Security] Session time: 2025-06-30T16:11:26.720Z
2025-06-30T15:32:03.714 INFO  [application-akka.actor.default-dispatcher-8] [EntityDTController] Fetching cfs search data.
2025-06-30T15:32:03.730 WARN  [application-akka.actor.default-dispatcher-8] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:32:03.781 INFO  [application-akka.actor.default-dispatcher-6] [VersionController] Getting all versions.
2025-06-30T15:33:31.936 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000012', stat=92,92,1751297611893,1751297611893,0,0,0,72057605190189075,44,0,92
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:33:32.158 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000012', stat=92,92,1751297611893,1751297611893,0,0,0,72057605190189075,44,0,92
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [9e152c3c-5da6-45e9-a0e9-00e6de494812]
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying http Authentication
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3d0be8ad
2025-06-30T15:33:37.444 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:33:37.885 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:38.870 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.041 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.041 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.041 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.041 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.041 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.041 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.041 WARN  [play-dev-mode-akka.actor.default-dispatcher-527] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:33:39.046 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.046 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.046 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.054 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.054 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.054 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.206 INFO  [application-akka.actor.default-dispatcher-12] [VersionController] Getting version with the id: 273853.
2025-06-30T15:33:39.212 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting all versions.
2025-06-30T15:33:39.214 INFO  [application-akka.actor.default-dispatcher-10] [VersionController] Getting version state machine.
2025-06-30T15:33:39.222 INFO  [application-akka.actor.default-dispatcher-13] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:33:39.428 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.428 INFO  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.428 WARN  [application-akka.actor.default-dispatcher-12] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.432 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:39.432 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:39.432 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:39.602 INFO  [application-akka.actor.default-dispatcher-12] [EntityDTController] Fetching cfs search data.
2025-06-30T15:33:39.611 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting all versions.
2025-06-30T15:33:39.621 WARN  [application-akka.actor.default-dispatcher-9] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:33:40.582 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:33:40.582 INFO  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:33:40.582 WARN  [application-akka.actor.default-dispatcher-9] [nossis.security.Security] Session time: 2025-06-30T16:11:27.645Z
2025-06-30T15:33:40.724 INFO  [application-akka.actor.default-dispatcher-9] [VersionController] Getting all versions.
2025-06-30T15:35:01.609 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:01.609 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [a66a6965-67e4-49d9-998b-26864d506fc9]
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Trying http Authentication
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@20816a07
2025-06-30T15:35:01.610 INFO  [application-akka.actor.default-dispatcher-13] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:35:02.137 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.282 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.481 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.481 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.481 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.481 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.481 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.481 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.481 WARN  [play-dev-mode-akka.actor.default-dispatcher-526] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:35:02.486 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.486 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.486 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.491 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.491 INFO  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.491 WARN  [application-akka.actor.default-dispatcher-13] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.689 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version with the id: 273853.
2025-06-30T15:35:02.690 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:35:02.691 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting version state machine.
2025-06-30T15:35:02.701 INFO  [application-akka.actor.default-dispatcher-13] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:35:02.870 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.870 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.870 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:02.873 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:02.873 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:02.873 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:26.923Z
2025-06-30T15:35:03.004 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search data.
2025-06-30T15:35:03.016 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:35:03.020 WARN  [application-akka.actor.default-dispatcher-19] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [b2f811cf-bff2-4409-9fc8-3ab45984f824]
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying http Authentication
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1974c502
2025-06-30T15:35:15.624 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:35:15.986 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:26.767Z
2025-06-30T15:35:16.112 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.767Z
2025-06-30T15:35:16.292 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:16.292 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:35:16.292 INFO  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:16.292 INFO  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:35:16.292 WARN  [application-akka.actor.default-dispatcher-16] [nossis.security.Security] Session time: 2025-06-30T16:11:26.767Z
2025-06-30T15:35:16.292 WARN  [application-akka.actor.default-dispatcher-7] [nossis.security.Security] Session time: 2025-06-30T16:11:26.767Z
2025-06-30T15:35:16.419 INFO  [application-akka.actor.default-dispatcher-16] [VersionController] Getting version with the id: 273853.
2025-06-30T15:35:16.427 INFO  [application-akka.actor.default-dispatcher-7] [VersionController] Getting version state machine.
2025-06-30T15:35:23.174 WARN  [play-dev-mode-akka.actor.default-dispatcher-549] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [6f1efb15-18ed-48ce-ad29-91695f646ac8]
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Trying http Authentication
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@db699f5
2025-06-30T15:36:32.572 INFO  [application-akka.actor.default-dispatcher-14] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:36:32.948 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:36:33.069 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:36:33.238 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:36:33.238 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:36:33.238 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:36:33.238 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:36:33.238 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:36:33.238 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:36:33.238 WARN  [play-dev-mode-akka.actor.default-dispatcher-456] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:36:33.373 INFO  [application-akka.actor.default-dispatcher-11] [VersionController] Getting version with the id: 273853.
2025-06-30T15:36:33.384 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting version state machine.
2025-06-30T15:37:00.565 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:37:00.565 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:37:00.565 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:37:00.577 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:37:00.577 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:37:00.577 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:37:00.775 INFO  [application-akka.actor.default-dispatcher-11] [VersionController] Getting all versions.
2025-06-30T15:37:00.783 INFO  [application-akka.actor.default-dispatcher-10] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:37:00.889 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:37:00.889 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:37:00.889 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:37:01.016 INFO  [application-akka.actor.default-dispatcher-10] [EntityDTController] Fetching cfs search data.
2025-06-30T15:37:01.030 WARN  [application-akka.actor.default-dispatcher-10] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:37:04.452 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:37:04.452 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:37:04.452 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:37:04.700 INFO  [application-akka.actor.default-dispatcher-10] [VersionController] Getting all versions.
2025-06-30T15:37:10.047 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:37:10.047 INFO  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:37:10.047 WARN  [application-akka.actor.default-dispatcher-10] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:37:10.191 INFO  [application-akka.actor.default-dispatcher-10] [VersionController] Getting all versions.
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0987be2d-74d0-4a9b-9424-e29e32019299]
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Trying http Authentication
2025-06-30T15:38:14.423 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@dbddb35
2025-06-30T15:38:14.424 INFO  [application-akka.actor.default-dispatcher-11] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:38:15.387 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:15.613 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:15.757 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:15.757 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:15.757 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:15.758 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:15.758 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:15.758 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:15.762 WARN  [play-dev-mode-akka.actor.default-dispatcher-660] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:38:15.894 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version state machine.
2025-06-30T15:38:15.900 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting version with the id: 273853.
2025-06-30T15:38:22.428 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_REMOVED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000012', stat=92,92,1751297611893,1751297611893,0,0,0,72057605190189075,44,0,92
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:38:22.428 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_REMOVED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000012', stat=92,92,1751297611893,1751297611893,0,0,0,72057605190189075,44,0,92
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:38:30.464 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:30.464 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:30.464 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:30.469 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:30.470 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:30.470 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:30.661 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T15:38:30.665 INFO  [application-akka.actor.default-dispatcher-14] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:38:31.642 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:31.642 INFO  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:31.642 WARN  [application-akka.actor.default-dispatcher-14] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:31.648 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:38:31.648 INFO  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:38:31.648 WARN  [application-akka.actor.default-dispatcher-17] [nossis.security.Security] Session time: 2025-06-30T16:11:27.629Z
2025-06-30T15:38:31.788 INFO  [application-akka.actor.default-dispatcher-14] [EntityDTController] Fetching cfs search data.
2025-06-30T15:38:31.798 INFO  [application-akka.actor.default-dispatcher-17] [VersionController] Getting all versions.
2025-06-30T15:38:31.804 WARN  [application-akka.actor.default-dispatcher-17] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [79f08038-810b-4a93-b354-d863f9cd26b8]
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying http Authentication
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5c5a34e7
2025-06-30T15:39:51.681 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:39:52.114 WARN  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:52.227 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:52.403 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:39:52.403 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:39:52.403 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:39:52.403 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:39:52.403 WARN  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:52.403 WARN  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:52.403 WARN  [play-dev-mode-akka.actor.default-dispatcher-449] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:39:52.533 INFO  [application-akka.actor.default-dispatcher-21] [VersionController] Getting version state machine.
2025-06-30T15:39:52.542 INFO  [application-akka.actor.default-dispatcher-20] [VersionController] Getting version with the id: 273853.
2025-06-30T15:39:55.376 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:39:55.376 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:39:55.376 WARN  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:55.393 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:39:55.393 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:39:55.393 WARN  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:39:55.654 INFO  [application-akka.actor.default-dispatcher-20] [VersionController] Getting all versions.
2025-06-30T15:39:55.671 INFO  [application-akka.actor.default-dispatcher-21] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:40:25.700 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:40:25.700 INFO  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:40:25.700 WARN  [application-akka.actor.default-dispatcher-21] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:40:25.710 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:40:25.710 INFO  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:40:25.710 WARN  [application-akka.actor.default-dispatcher-20] [nossis.security.Security] Session time: 2025-06-30T16:11:26.886Z
2025-06-30T15:40:26.024 INFO  [application-akka.actor.default-dispatcher-20] [EntityDTController] Fetching cfs search data.
2025-06-30T15:40:26.030 INFO  [application-akka.actor.default-dispatcher-21] [VersionController] Getting all versions.
2025-06-30T15:40:26.193 WARN  [application-akka.actor.default-dispatcher-21] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:41:24.349 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-683706896 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000013', stat=98,98,1751298084291,1751298084291,0,0,0,72057605190189076,44,0,98
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:41:24.350 INFO  [Curator-TreeCache-0] [cluster.api.ZookeeperCluster$] null-1711203041 handler childEvent NODE_ADDED -> ChildData{path='/nossis-tsc-root/clusters/nossis-tsc-backend-cluster/cluster/nodes/node_0000000013', stat=98,98,1751298084291,1751298084291,0,0,0,72057605190189076,44,0,98
, data=[123, 34, 105, 100, 34, 58, 110, 117, 108, 108, 44, 34, 105, 112, 79, 114, 72, 111, 115, 116, 34, 58, 34, 119, 101, 98, 45, 100, 101, 118, 34, 44, 34, 105, 110, 102, 111, 34, 58, 110, 117, 108, 108, 125]}
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [54e0717e-ce8d-4acf-bac0-c9902f10eb1b]
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@13c550d9
2025-06-30T15:42:22.038 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:22.558 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:22.702 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:22.896 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:22.896 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:22.896 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:22.896 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:22.896 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:22.896 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:22.896 WARN  [play-dev-mode-akka.actor.default-dispatcher-660] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:42:22.900 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:22.900 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:22.900 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:22.907 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:22.907 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:22.907 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:23.065 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version with the id: 273853.
2025-06-30T15:42:23.073 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:42:23.074 INFO  [application-akka.actor.default-dispatcher-11] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:42:23.079 INFO  [application-akka.actor.default-dispatcher-25] [VersionController] Getting version state machine.
2025-06-30T15:42:23.268 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:23.268 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:23.268 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:23.304 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:23.304 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:23.304 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.313Z
2025-06-30T15:42:23.399 INFO  [application-akka.actor.default-dispatcher-19] [EntityDTController] Fetching cfs search data.
2025-06-30T15:42:23.414 WARN  [application-akka.actor.default-dispatcher-19] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:42:23.429 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting all versions.
2025-06-30T15:42:38.327 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:38.327 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:38.327 INFO  [application-akka.actor.default-dispatcher-18] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3dd6c1b0-0d93-43c1-8542-bf00619a861f]
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@c45c99b
2025-06-30T15:42:38.328 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:38.698 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:38.793 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.032 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.032 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.032 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.032 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.032 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.032 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.032 WARN  [play-dev-mode-akka.actor.default-dispatcher-659] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:42:39.037 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.037 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.037 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.045 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.045 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.045 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.199 INFO  [application-akka.actor.default-dispatcher-22] [VersionController] Getting version with the id: 273853.
2025-06-30T15:42:39.206 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting version state machine.
2025-06-30T15:42:39.207 INFO  [application-akka.actor.default-dispatcher-23] [VersionController] Getting all versions.
2025-06-30T15:42:39.218 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:42:39.429 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.429 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.429 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.431 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:39.431 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:39.432 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.462Z
2025-06-30T15:42:39.556 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search data.
2025-06-30T15:42:39.565 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T15:42:39.573 WARN  [application-akka.actor.default-dispatcher-15] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3f53f734-9724-4518-bee3-12324464b123]
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6180fe04
2025-06-30T15:42:46.082 INFO  [application-akka.actor.default-dispatcher-15] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:46.368 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.474 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.559 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.559 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.559 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.560 WARN  [play-dev-mode-akka.actor.default-dispatcher-456] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:42:46.564 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.564 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.564 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.567 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.567 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.567 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.721 INFO  [application-akka.actor.default-dispatcher-25] [VersionController] Getting all versions.
2025-06-30T15:42:46.721 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version state machine.
2025-06-30T15:42:46.727 INFO  [application-akka.actor.default-dispatcher-19] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:42:46.730 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.730 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.730 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.738 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.738 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.738 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.749 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.749 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.749 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.860 INFO  [application-akka.actor.default-dispatcher-19] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:42:46.868 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search data.
2025-06-30T15:42:46.869 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:46.869 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:46.869 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.233Z
2025-06-30T15:42:46.878 INFO  [application-akka.actor.default-dispatcher-25] [VersionController] Getting all versions.
2025-06-30T15:42:46.884 WARN  [application-akka.actor.default-dispatcher-25] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:42:46.991 INFO  [application-akka.actor.default-dispatcher-19] [EntityDTController] Fetching cfs search data.
2025-06-30T15:42:47.008 WARN  [application-akka.actor.default-dispatcher-19] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:42:52.315 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:52.315 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:52.315 INFO  [application-akka.actor.default-dispatcher-19] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0901b4fa-2b24-47bd-be84-596a8b919f5c]
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying http Authentication
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@47b7a098
2025-06-30T15:42:52.316 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:42:52.726 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:52.831 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.011 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.011 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.011 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.011 INFO  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.011 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.011 WARN  [application-akka.actor.default-dispatcher-25] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.011 WARN  [play-dev-mode-akka.actor.default-dispatcher-663] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:42:53.016 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.016 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.016 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.020 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.020 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.020 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.185 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting version with the id: 273853.
2025-06-30T15:42:53.185 INFO  [application-akka.actor.default-dispatcher-25] [VersionController] Getting version state machine.
2025-06-30T15:42:53.190 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T15:42:53.195 INFO  [application-akka.actor.default-dispatcher-11] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:42:53.370 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.370 INFO  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.370 WARN  [application-akka.actor.default-dispatcher-11] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.375 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:42:53.375 INFO  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:42:53.376 WARN  [application-akka.actor.default-dispatcher-15] [nossis.security.Security] Session time: 2025-06-30T16:11:27.508Z
2025-06-30T15:42:53.499 INFO  [application-akka.actor.default-dispatcher-11] [EntityDTController] Fetching cfs search data.
2025-06-30T15:42:53.517 INFO  [application-akka.actor.default-dispatcher-15] [VersionController] Getting all versions.
2025-06-30T15:42:53.522 WARN  [application-akka.actor.default-dispatcher-15] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [9017ce69-8d89-45b5-aed5-e3f3d20776f2]
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying http Authentication
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@22b6835a
2025-06-30T15:44:41.119 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:44:42.081 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.260 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.672 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:42.672 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:42.672 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:42.672 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:42.672 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.672 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.672 WARN  [play-dev-mode-akka.actor.default-dispatcher-623] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:44:42.677 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:42.677 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:42.677 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.684 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:42.685 INFO  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:42.685 WARN  [application-akka.actor.default-dispatcher-24] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:42.848 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting version state machine.
2025-06-30T15:44:42.852 INFO  [application-akka.actor.default-dispatcher-27] [VersionController] Getting version with the id: 273853.
2025-06-30T15:44:42.855 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting all versions.
2025-06-30T15:44:42.863 INFO  [application-akka.actor.default-dispatcher-24] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:44:43.024 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:43.024 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:43.024 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:43.028 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:43.028 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:43.028 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.331Z
2025-06-30T15:44:43.154 INFO  [application-akka.actor.default-dispatcher-27] [EntityDTController] Fetching cfs search data.
2025-06-30T15:44:43.161 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:44:43.168 WARN  [application-akka.actor.default-dispatcher-28] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [0fe3da8d-7644-4522-9766-4f51adeb1c44]
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying http Authentication
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@38b3846e
2025-06-30T15:44:52.150 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:44:52.488 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.619 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.804 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:52.804 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:52.804 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:52.804 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:52.804 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.804 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.805 WARN  [play-dev-mode-akka.actor.default-dispatcher-683] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:44:52.809 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:52.809 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:52.809 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.817 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:52.817 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:52.817 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:52.967 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting version state machine.
2025-06-30T15:44:52.975 INFO  [application-akka.actor.default-dispatcher-23] [VersionController] Getting version with the id: 273853.
2025-06-30T15:44:52.975 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:44:52.980 INFO  [application-akka.actor.default-dispatcher-28] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:44:53.161 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:53.161 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:53.161 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:53.165 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:44:53.165 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:44:53.165 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.286Z
2025-06-30T15:44:53.289 INFO  [application-akka.actor.default-dispatcher-28] [EntityDTController] Fetching cfs search data.
2025-06-30T15:44:53.297 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:44:53.308 WARN  [application-akka.actor.default-dispatcher-19] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4a397c84-d270-426c-a36f-8b17322a0f7f]
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Trying http Authentication
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@184b0174
2025-06-30T15:45:04.106 INFO  [application-akka.actor.default-dispatcher-19] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:45:04.450 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.563 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.752 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:04.752 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:04.752 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.752 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:04.752 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:04.752 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.752 WARN  [play-dev-mode-akka.actor.default-dispatcher-719] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:45:04.758 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:04.758 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:04.758 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.770 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:04.770 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:04.770 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:04.919 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting version with the id: 273853.
2025-06-30T15:45:04.919 INFO  [application-akka.actor.default-dispatcher-22] [VersionController] Getting all versions.
2025-06-30T15:45:04.938 INFO  [application-akka.actor.default-dispatcher-23] [VersionController] Getting version state machine.
2025-06-30T15:45:04.947 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:45:05.103 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:05.103 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:05.103 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:05.110 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:05.110 INFO  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:05.110 WARN  [application-akka.actor.default-dispatcher-23] [nossis.security.Security] Session time: 2025-06-30T16:11:27.248Z
2025-06-30T15:45:05.239 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting all versions.
2025-06-30T15:45:05.246 INFO  [application-akka.actor.default-dispatcher-23] [EntityDTController] Fetching cfs search data.
2025-06-30T15:45:05.262 WARN  [application-akka.actor.default-dispatcher-23] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [cbe1abf6-b6c7-4031-b773-c08629a85adc]
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying http Authentication
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@518cab97
2025-06-30T15:45:55.088 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:45:55.850 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:55.967 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.406 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.406 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.406 INFO  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.406 INFO  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.406 WARN  [application-akka.actor.default-dispatcher-27] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.406 WARN  [application-akka.actor.default-dispatcher-26] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.406 WARN  [play-dev-mode-akka.actor.default-dispatcher-623] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:45:56.410 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.410 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.410 WARN  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.420 INFO  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.420 INFO  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.421 WARN  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.579 INFO  [application-akka.actor.default-dispatcher-27] [VersionController] Getting version state machine.
2025-06-30T15:45:56.589 INFO  [application-akka.actor.default-dispatcher-29] [VersionController] Getting all versions.
2025-06-30T15:45:56.590 INFO  [application-akka.actor.default-dispatcher-26] [VersionController] Getting version with the id: 273853.
2025-06-30T15:45:56.597 INFO  [application-akka.actor.default-dispatcher-30] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:45:56.609 INFO  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.609 INFO  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.609 WARN  [application-akka.actor.default-dispatcher-30] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.728 INFO  [application-akka.actor.default-dispatcher-30] [EntityDTController] Fetching cfs search data.
2025-06-30T15:45:56.803 WARN  [application-akka.actor.default-dispatcher-29] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:45:56.815 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:45:56.815 INFO  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:45:56.815 WARN  [application-akka.actor.default-dispatcher-29] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:45:56.946 INFO  [application-akka.actor.default-dispatcher-29] [VersionController] Getting all versions.
2025-06-30T15:49:22.741 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:22.741 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:22.741 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.633Z
2025-06-30T15:49:23.043 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting all versions.
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [3c8dd253-0c87-46df-9c9e-df622709da60]
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@d5130a9
2025-06-30T15:49:25.399 INFO  [application-akka.actor.default-dispatcher-18] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:25.732 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:25.861 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.033 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.033 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.033 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.033 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.033 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.033 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.034 WARN  [play-dev-mode-akka.actor.default-dispatcher-768] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:49:26.039 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.039 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.039 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.051 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.051 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.051 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.186 INFO  [application-akka.actor.default-dispatcher-36] [VersionController] Getting version state machine.
2025-06-30T15:49:26.188 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting version with the id: 273853.
2025-06-30T15:49:26.199 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:49:26.201 INFO  [application-akka.actor.default-dispatcher-28] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:49:26.389 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.389 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.389 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.391 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:26.391 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:26.391 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.538Z
2025-06-30T15:49:26.520 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:49:26.527 INFO  [application-akka.actor.default-dispatcher-28] [EntityDTController] Fetching cfs search data.
2025-06-30T15:49:26.551 WARN  [application-akka.actor.default-dispatcher-28] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:49:29.650 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:29.650 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [08ac41f6-a1f3-41ce-993f-26c2c82ca69f]
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@1535e8ac
2025-06-30T15:49:29.651 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:29.979 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.116 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.304 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.304 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.304 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.304 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.304 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.305 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.305 WARN  [play-dev-mode-akka.actor.default-dispatcher-765] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:49:30.309 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.309 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.309 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.317 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.317 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.317 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.483 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version with the id: 273853.
2025-06-30T15:49:30.488 INFO  [application-akka.actor.default-dispatcher-33] [VersionController] Getting all versions.
2025-06-30T15:49:30.489 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting version state machine.
2025-06-30T15:49:30.493 INFO  [application-akka.actor.default-dispatcher-37] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:49:30.656 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.656 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.656 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.659 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:30.659 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:30.659 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:30.787 INFO  [application-akka.actor.default-dispatcher-37] [EntityDTController] Fetching cfs search data.
2025-06-30T15:49:30.797 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:49:30.802 WARN  [application-akka.actor.default-dispatcher-28] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:49:31.740 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:31.740 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:31.740 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.795Z
2025-06-30T15:49:31.871 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:49:40.826 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [97c6b02b-3bf0-47db-8fcb-cfc2b6e7d4e8]
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying http Authentication
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@59d00678
2025-06-30T15:49:40.827 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:49:41.164 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.286 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.485 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.485 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.485 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.485 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.485 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.485 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.487 WARN  [play-dev-mode-akka.actor.default-dispatcher-744] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:49:41.491 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.491 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.491 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.502 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.502 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.502 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.653 INFO  [application-akka.actor.default-dispatcher-32] [VersionController] Getting version state machine.
2025-06-30T15:49:41.662 INFO  [application-akka.actor.default-dispatcher-35] [VersionController] Getting version with the id: 273853.
2025-06-30T15:49:41.663 INFO  [application-akka.actor.default-dispatcher-33] [VersionController] Getting all versions.
2025-06-30T15:49:41.665 INFO  [application-akka.actor.default-dispatcher-22] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:49:41.752 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.752 INFO  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.752 WARN  [application-akka.actor.default-dispatcher-22] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.754 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:49:41.754 INFO  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:49:41.754 WARN  [application-akka.actor.default-dispatcher-33] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:49:41.884 INFO  [application-akka.actor.default-dispatcher-22] [EntityDTController] Fetching cfs search data.
2025-06-30T15:49:41.892 INFO  [application-akka.actor.default-dispatcher-33] [VersionController] Getting all versions.
2025-06-30T15:49:41.899 WARN  [application-akka.actor.default-dispatcher-33] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [bb10edb9-709a-4d3e-b0e3-e6651b8ad457]
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Trying http Authentication
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@397ab00e
2025-06-30T15:50:42.979 INFO  [application-akka.actor.default-dispatcher-35] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:50:43.383 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:43.505 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:43.705 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:43.705 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:43.705 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:43.705 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:43.705 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:43.705 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:43.706 WARN  [play-dev-mode-akka.actor.default-dispatcher-744] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:50:43.833 INFO  [application-akka.actor.default-dispatcher-36] [VersionController] Getting version state machine.
2025-06-30T15:50:43.840 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting version with the id: 273853.
2025-06-30T15:50:45.313 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:45.313 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:45.313 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:45.334 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:45.334 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:45.334 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:45.455 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:50:45.461 INFO  [application-akka.actor.default-dispatcher-32] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:50:45.658 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:45.658 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:45.658 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:45.693 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:45.693 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:45.693 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:45.783 INFO  [application-akka.actor.default-dispatcher-32] [EntityDTController] Fetching cfs search data.
2025-06-30T15:50:45.797 WARN  [application-akka.actor.default-dispatcher-32] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:50:45.817 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:50:46.802 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:46.803 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:46.803 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.181Z
2025-06-30T15:50:46.930 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting all versions.
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [2ec555bf-e1eb-4240-b194-9b3b1f4796ba]
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:50:58.337 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying http Authentication
2025-06-30T15:50:58.338 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@573fb839
2025-06-30T15:50:58.338 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:50:58.678 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:27.470Z
2025-06-30T15:50:58.820 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:27.470Z
2025-06-30T15:50:59.009 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:59.009 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:50:59.009 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:59.009 INFO  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:50:59.009 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.470Z
2025-06-30T15:50:59.009 WARN  [application-akka.actor.default-dispatcher-35] [nossis.security.Security] Session time: 2025-06-30T16:11:27.470Z
2025-06-30T15:50:59.009 WARN  [play-dev-mode-akka.actor.default-dispatcher-835] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:50:59.134 INFO  [application-akka.actor.default-dispatcher-32] [VersionController] Getting version state machine.
2025-06-30T15:50:59.142 INFO  [application-akka.actor.default-dispatcher-35] [VersionController] Getting version with the id: 273853.
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [175276da-4b17-4209-bfe2-7ed6cb4f846b]
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying http Authentication
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@3514a940
2025-06-30T15:51:32.145 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:51:32.581 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.377Z
2025-06-30T15:51:32.706 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.377Z
2025-06-30T15:51:32.888 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:32.888 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:32.888 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:32.888 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:32.888 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:27.377Z
2025-06-30T15:51:32.888 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:27.377Z
2025-06-30T15:51:32.888 WARN  [play-dev-mode-akka.actor.default-dispatcher-767] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:51:32.893 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:32.893 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:32.893 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.377Z
2025-06-30T15:51:33.030 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting version state machine.
2025-06-30T15:51:33.031 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting version with the id: 273853.
2025-06-30T15:51:33.041 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4e3e3769-5526-4d3d-9c10-0d40bfb42715]
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying http Authentication
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@29cf4a9b
2025-06-30T15:51:54.586 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:51:54.974 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:51:55.141 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:51:55.543 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:55.543 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:55.543 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:55.543 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:55.543 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:51:55.543 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:51:55.544 WARN  [play-dev-mode-akka.actor.default-dispatcher-769] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:51:55.548 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:51:55.548 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:51:55.548 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.749Z
2025-06-30T15:51:55.692 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting version with the id: 273853.
2025-06-30T15:51:55.692 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting version state machine.
2025-06-30T15:51:55.715 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [735093e9-e1c4-4864-b429-247434233494]
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Trying http Authentication
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@43772f26
2025-06-30T15:52:08.603 INFO  [application-akka.actor.default-dispatcher-28] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:52:09.031 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:09.150 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:09.323 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:09.323 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:09.323 INFO  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:09.323 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:09.324 WARN  [application-akka.actor.default-dispatcher-28] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:09.324 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:09.324 WARN  [play-dev-mode-akka.actor.default-dispatcher-659] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:52:09.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:09.328 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:09.328 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:09.469 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting version with the id: 273853.
2025-06-30T15:52:09.470 INFO  [application-akka.actor.default-dispatcher-28] [VersionController] Getting version state machine.
2025-06-30T15:52:09.476 INFO  [application-akka.actor.default-dispatcher-18] [VersionController] Getting all versions.
2025-06-30T15:52:33.606 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:33.606 INFO  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:33.606 WARN  [application-akka.actor.default-dispatcher-18] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:33.649 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:33.650 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:33.650 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:33.819 INFO  [application-akka.actor.default-dispatcher-18] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:52:33.876 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting all versions.
2025-06-30T15:52:40.404 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:40.404 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:40.404 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:40.715 INFO  [application-akka.actor.default-dispatcher-34] [EntityDTController] Fetching cfs search data.
2025-06-30T15:52:40.735 WARN  [application-akka.actor.default-dispatcher-34] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:52:41.734 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:52:41.734 INFO  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:52:41.734 WARN  [application-akka.actor.default-dispatcher-34] [nossis.security.Security] Session time: 2025-06-30T16:11:26.824Z
2025-06-30T15:52:41.863 INFO  [application-akka.actor.default-dispatcher-34] [VersionController] Getting all versions.
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [4fc200c5-342a-46f5-a1da-7ac8a0eb54ff]
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying http Authentication
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5e51de0
2025-06-30T15:55:00.695 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:55:01.170 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.292 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.467 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.467 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.467 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.467 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.467 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.467 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.467 WARN  [play-dev-mode-akka.actor.default-dispatcher-949] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:55:01.472 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.472 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.472 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.482 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.482 INFO  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.482 WARN  [application-akka.actor.default-dispatcher-40] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.647 INFO  [application-akka.actor.default-dispatcher-39] [VersionController] Getting version with the id: 273853.
2025-06-30T15:55:01.648 INFO  [application-akka.actor.default-dispatcher-31] [VersionController] Getting version state machine.
2025-06-30T15:55:01.649 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:55:01.656 INFO  [application-akka.actor.default-dispatcher-40] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:55:01.823 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.823 INFO  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.823 WARN  [application-akka.actor.default-dispatcher-39] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.831 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:01.831 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:01.831 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:01.976 INFO  [application-akka.actor.default-dispatcher-39] [EntityDTController] Fetching cfs search data.
2025-06-30T15:55:01.984 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:55:01.991 WARN  [application-akka.actor.default-dispatcher-37] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T15:55:02.758 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:55:02.758 INFO  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:55:02.758 WARN  [application-akka.actor.default-dispatcher-37] [nossis.security.Security] Session time: 2025-06-30T16:11:26.960Z
2025-06-30T15:55:03.110 INFO  [application-akka.actor.default-dispatcher-37] [VersionController] Getting all versions.
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [94077d52-379b-48ea-a1ab-e9eb566d0f5f]
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Trying http Authentication
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@5727d861
2025-06-30T15:57:31.107 INFO  [application-akka.actor.default-dispatcher-31] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:57:31.561 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:27.351Z
2025-06-30T15:57:31.723 WARN  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Session time: 2025-06-30T16:11:27.351Z
2025-06-30T15:57:32.164 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:32.164 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:32.164 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:32.164 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:32.164 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.351Z
2025-06-30T15:57:32.164 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.351Z
2025-06-30T15:57:32.165 WARN  [play-dev-mode-akka.actor.default-dispatcher-972] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:57:32.169 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:32.169 INFO  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:32.169 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.351Z
2025-06-30T15:57:32.320 INFO  [application-akka.actor.default-dispatcher-43] [VersionController] Getting version with the id: 273853.
2025-06-30T15:57:32.322 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting version state machine.
2025-06-30T15:57:32.328 INFO  [application-akka.actor.default-dispatcher-19] [VersionController] Getting all versions.
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [e1668008-f668-4b4f-9912-9d7c2bcea0f4]
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Trying http Authentication
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@4c23c810
2025-06-30T15:57:55.497 INFO  [application-akka.actor.default-dispatcher-32] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:57:55.891 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T15:57:56.015 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T15:57:56.198 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:56.198 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:56.198 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T15:57:56.198 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:56.198 INFO  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:56.198 WARN  [application-akka.actor.default-dispatcher-38] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T15:57:56.198 WARN  [play-dev-mode-akka.actor.default-dispatcher-767] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:57:56.203 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:57:56.203 INFO  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:57:56.203 WARN  [application-akka.actor.default-dispatcher-32] [nossis.security.Security] Session time: 2025-06-30T16:11:27.639Z
2025-06-30T15:57:56.364 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting version with the id: 273853.
2025-06-30T15:57:56.365 INFO  [application-akka.actor.default-dispatcher-38] [VersionController] Getting version state machine.
2025-06-30T15:57:56.370 INFO  [application-akka.actor.default-dispatcher-32] [VersionController] Getting all versions.
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [66780692-724b-487d-8ff6-4d7c467dd420]
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying authentication with credential
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Trying http Authentication
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@712f35c6
2025-06-30T15:58:24.265 INFO  [application-akka.actor.default-dispatcher-36] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T15:58:24.679 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:24.810 WARN  [application-akka.actor.default-dispatcher-19] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:24.976 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:24.976 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:24.976 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:24.976 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:24.976 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:24.976 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:24.976 WARN  [play-dev-mode-akka.actor.default-dispatcher-456] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T15:58:24.981 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:24.981 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:24.981 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:25.116 INFO  [application-akka.actor.default-dispatcher-36] [VersionController] Getting version with the id: 273853.
2025-06-30T15:58:25.117 INFO  [application-akka.actor.default-dispatcher-43] [VersionController] Getting version state machine.
2025-06-30T15:58:25.128 INFO  [application-akka.actor.default-dispatcher-41] [VersionController] Getting all versions.
2025-06-30T15:58:26.440 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:26.440 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:26.440 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:26.479 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:58:26.479 INFO  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:58:26.479 WARN  [application-akka.actor.default-dispatcher-36] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:58:26.572 INFO  [application-akka.actor.default-dispatcher-41] [EntityDTController] Fetching cfs search table config json.
2025-06-30T15:58:26.603 INFO  [application-akka.actor.default-dispatcher-36] [VersionController] Getting all versions.
2025-06-30T15:59:22.862 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T15:59:22.862 INFO  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T15:59:22.862 WARN  [application-akka.actor.default-dispatcher-41] [nossis.security.Security] Session time: 2025-06-30T16:11:27.480Z
2025-06-30T15:59:23.098 INFO  [application-akka.actor.default-dispatcher-41] [EntityDTController] Fetching cfs search data.
2025-06-30T15:59:23.181 WARN  [application-akka.actor.default-dispatcher-41] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [security.aaapi.SecurityContext] Internal Cache: Not Found Session [9102e686-8acd-44b2-8fbc-e04f5af5581c]
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] NO session object found, trying alternatives
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying with URL Parameter: SCASessionId=MjA4NjM2ODQtMjczNS00ZDFhLTgxMWQtZmRhZTI3NjQ5NDNl
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying authentication with credential
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Trying http Authentication
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Received an object: pt.ptinovacao.nossis.security.aaapi.auth.HttpCredentials@6e79c634
2025-06-30T16:00:26.105 INFO  [application-akka.actor.default-dispatcher-43] [provider.iam.IAMAuthentication] Is an Http Credentials Object
2025-06-30T16:00:26.505 WARN  [application-akka.actor.default-dispatcher-43] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:26.649 WARN  [application-akka.actor.default-dispatcher-31] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:26.863 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:26.863 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:26.863 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:26.863 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:26.863 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:26.863 WARN  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:26.864 WARN  [play-dev-mode-akka.actor.default-dispatcher-972] [akka.actor.ActorSystemImpl] Explicitly set HTTP header 'Content-Type: application/json' is ignored, explicit `Content-Type` header is not allowed. Set `HttpResponse.entity.contentType` instead.
2025-06-30T16:00:26.868 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:26.868 INFO  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:26.868 WARN  [application-akka.actor.default-dispatcher-44] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:27.000 INFO  [application-akka.actor.default-dispatcher-46] [VersionController] Getting version with the id: 273853.
2025-06-30T16:00:27.007 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting version state machine.
2025-06-30T16:00:27.013 INFO  [application-akka.actor.default-dispatcher-44] [VersionController] Getting all versions.
2025-06-30T16:00:29.104 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:29.104 INFO  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:29.104 WARN  [application-akka.actor.default-dispatcher-46] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:29.135 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:29.135 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:29.135 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:29.257 INFO  [application-akka.actor.default-dispatcher-46] [EntityDTController] Fetching cfs search table config json.
2025-06-30T16:00:29.264 INFO  [application-akka.actor.default-dispatcher-42] [VersionController] Getting all versions.
2025-06-30T16:00:31.492 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Handler: PageRequestSecurityHandler
2025-06-30T16:00:31.492 INFO  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Security Application: Nossis Application
2025-06-30T16:00:31.492 WARN  [application-akka.actor.default-dispatcher-42] [nossis.security.Security] Session time: 2025-06-30T16:11:27.310Z
2025-06-30T16:00:31.629 INFO  [application-akka.actor.default-dispatcher-42] [EntityDTController] Fetching cfs search data.
2025-06-30T16:00:31.643 WARN  [application-akka.actor.default-dispatcher-42] [play] You are using status code '200' with flashing, which should only be used with a redirect status!
