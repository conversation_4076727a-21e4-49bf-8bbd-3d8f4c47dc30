package temp.controllers.impl;

import com.fasterxml.jackson.databind.JsonNode;
import nossis.tsccatalog.models.entities.Resource;
import org.junit.Before;
import org.junit.Test;
import play.libs.Json;
import play.mvc.Http;
import play.mvc.Result;
import temp.controllers.AbstractControllerTest;
import temp.dtos.EntityDto;
import temp.adapters.EntityAdapter;
import temp.services.impl.ResourceService;

import java.io.IOException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static temp.CompletionStageTestHelper.complete;
import static temp.CompletionStageTestHelper.createCompletionStage;
import static temp.TestUtils.postResponse;
import static temp.model.EntityTestModels.simpleResource;
import static temp.model.EntityTestModels.simpleResourceDto;

public class ResourceControllerTest extends AbstractControllerTest {


    private ResourceController controller;
    private ResourceService service;
    private EntityAdapter adapter;

    @Before @Override
    public void setUp() {
        super.setUp();
        adapter = mock(EntityAdapter.class);
        service = mock(ResourceService.class);
        controller = new ResourceController(service, adapter, ec);
    }

    @Test
    public void getResourceById_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException, IOException {
        //Arrange
        final long idParam = 1;
        EntityDto dto = simpleResourceDto();
        Resource model = simpleResource();
        JsonNode expectedBody = Json.toJson(dto);
        when(service.getEntityById(idParam)).thenReturn(createCompletionStage(model));
        when(adapter.dtoWithAttributesFrom(model)).thenReturn(dto);
        //Act
        CompletionStage<Result> completionStage = controller.getResourceById(idParam);
        //Assert
        Result result = complete(completionStage);
        verify(mockResponse).setHeader(eq(Http.HeaderNames.ETAG), anyString());
        assertEquals(expectedBody.toString(), getResultPayloadAsJson(result).toString());
        assertEquals(Http.Status.OK, result.status());
    }

    @Test
    public void create_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException, IOException {
        //Arrange
        EntityDto dto = simpleResourceDto();
        Resource model = simpleResource();
        Http.RequestBody requestBody = new Http.RequestBody(Json.toJson(dto));
        JsonNode expectedBody = Json.toJson(postResponse(model));
        when(mockRequest.body()).thenReturn(requestBody);
        when(adapter.resourceFrom(any(EntityDto.class))).thenReturn(model);
        when(service.create(model)).thenReturn(createCompletionStage(model));
        //Act
        CompletionStage<Result> completionStage = controller.create();
        //Assert
        Result result = complete(completionStage);
        assertEquals(expectedBody.toString(), getResultPayloadAsJson(result).toString());
        assertEquals(Http.Status.CREATED, result.status());
    }

    @Test
    public void update_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException {
        //Arrange
        final long idParam = 1;
        EntityDto dto = simpleResourceDto();
        Resource model = simpleResource();
        Http.RequestBody requestBody = new Http.RequestBody(Json.toJson(dto));
        when(mockRequest.body()).thenReturn(requestBody);
        when(adapter.resourceFrom(any(EntityDto.class))).thenReturn(model);
        when(service.update(idParam, model)).thenReturn(createCompletionStage(true));
        //Act
        CompletionStage<Result> completionStage = controller.update(idParam);
        //Assert
        Result result = complete(completionStage);
        assertTrue(result.body().isKnownEmpty());
        assertEquals(Http.Status.NO_CONTENT, result.status());
    }

    @Test
    public void cloneResourceWithRelations_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException, IOException {
        //Arrange
        final long idParam = 1;
        Resource model = simpleResource();
        JsonNode expectedBody = Json.toJson(postResponse(model));
        when(service.clone(idParam, true)).thenReturn(createCompletionStage(model));
        //Act
        CompletionStage<Result> completionStage = controller.cloneResourceWithRelations(idParam);
        //Assert
        Result result = complete(completionStage);
        verify(service).clone(idParam, true);
        assertEquals(expectedBody.toString(), getResultPayloadAsJson(result).toString());
        assertEquals(Http.Status.CREATED, result.status());
    }

    @Test
    public void cloneResourceWithoutRelations_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException, IOException {
        //Arrange
        final long idParam = 1;
        Resource model = simpleResource();
        JsonNode expectedBody = Json.toJson(postResponse(model));
        when(service.clone(idParam, false)).thenReturn(createCompletionStage(model));
        //Act
        CompletionStage<Result> completionStage = controller.cloneResourceWithoutRelations(idParam);
        //Assert
        Result result = complete(completionStage);
        verify(service).clone(idParam, false);
        assertEquals(expectedBody.toString(), getResultPayloadAsJson(result).toString());
        assertEquals(Http.Status.CREATED, result.status());
    }

    @Test
    public void delete_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException {
        //Arrange
        final long idParam = 1;
        when(service.delete(idParam)).thenReturn(createCompletionStage(true));
        //Act
        CompletionStage<Result> completionStage = controller.delete(idParam);
        //Assert
        Result result = complete(completionStage);
        assertTrue(result.body().isKnownEmpty());
        assertEquals(Http.Status.OK, result.status());
    }
}