package temp.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.AggregatedEntity;
import nossis.tsccatalog.models.entities.Cfs;
import nossis.tsccatalog.services.CatEntityAPIServices;
import nossis.tsccatalog.settings.CatalogConstants;
import nossis.tsccatalog.settings.ServicesSettings;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;

import play.libs.Json;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import temp.client.BackendClient;
import temp.client.IBackendClient;
import tsc.base.utils.ODataQueryParametersBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static temp.CompletionStageTestHelper.complete;
import static temp.CompletionStageTestHelper.createCompletionStage;
import static temp.TestUtils.newSidebarSelectResponse;
import static temp.TestUtils.payloadBuilder;
import static temp.model.EntityTestModels.aggregatedEntity;
import static temp.model.EntityTestModels.aggregatedEntityType;
import static temp.model.VersionTestModels.versionCatId;
import static temp.model.VersionTestModels.versionName;


public class CompDiagramSidebarServiceTest {

    private CatEntityAPIServices catApi;
    private IBackendClient client;
    private ServicesSettings servicesSettings;
    @InjectMocks
    @Spy
    private CompDiagramSidebarService service;

    @Before
    public void setUp() throws Exception {
        client = mock(BackendClient.class);
        catApi = mock(CatEntityAPIServices.class);
        servicesSettings = mock(ServicesSettings.class);
        service = new CompDiagramSidebarService(catApi, client, servicesSettings);
    }

    @Test
    public void getAncestorsIds_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException {
        //ARRANGE
        final long idParam = 1;
        CompletionStage<List<Long>> expectedResult = createCompletionStage(new ArrayList<>());
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode arrayNode = mapper.createArrayNode();
        when(client.getAncestorsIds(any(), any())).thenReturn(createCompletionStage(arrayNode));
        EntityType<Cfs> entityType = EntityType.CFS;

        //ACT
        CompletionStage<List<Long>> result = service.getAncestorsIds(entityType, idParam);

        //ASSERT
        verify(client).getAncestorsIds(entityType, idParam);
        assertEquals(complete(expectedResult), complete(result));
    }


    @Test
    public void getAncestorsIds_invalidScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException {
        //ARRANGE
        final long idParam = 1;
        CompletionStage<List<Long>> expectedResult = createCompletionStage(new ArrayList<>());
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode arrayNode = mapper.createArrayNode();
        when(client.getAncestorsIds(any(), any())).thenReturn(createCompletionStage(arrayNode));
        EntityType<Cfs> entityType = EntityType.CFS;

        //ACT
        CompletionStage<List<Long>> result = service.getAncestorsIds(entityType, null);

        //ASSERT
        verify(client, never()).getAncestorsIds(entityType, idParam);
        assertEquals(complete(expectedResult), complete(result));
    }


    @Test
    public void search_successScenario_expectedResult() throws InterruptedException, ExecutionException, TimeoutException {
        //ARRANGE
        final CatEntity cfsCatEntityMock = mock(CatEntity.class);
        List<AggregatedEntity> expectedResult = new ArrayList<AggregatedEntity>() {{
            add(aggregatedEntity("ae1"));
        }};
        Set<Long> emptySet = Collections.emptySet();
        Set<EntityType<?>> emptyEntityTypeSet = Collections.emptySet();

        when(catApi.getIdCatEntityOfType(any())).thenReturn(versionCatId);
        when(servicesSettings.getDiagramEntityListsMaxLimitSearch()).thenReturn(25);
        when(client.getEntitiesOfTypeWithPayload(EntityType.AGGREGATED_ENTITY, parametersBuilder().build(), Json.toJson(payloadBuilder().build())))
                .thenReturn(createCompletionStage(newSidebarSelectResponse()));
        when(catApi.getCatEntityById(anyLong())).thenReturn(cfsCatEntityMock);
        when(cfsCatEntityMock.getName()).thenReturn(aggregatedEntityType);

        //ACT
        CompletionStage<List<AggregatedEntity>> completionStage = service.search(versionName,"", emptySet, emptyEntityTypeSet, java.util.Optional.empty());
        List<AggregatedEntity> result = complete(completionStage);

        //ASSERT
        verify(catApi).getIdCatEntityOfType(any());
        verify(catApi).getCatEntityById(anyLong());
        verify(servicesSettings).getDiagramEntityListsMaxLimitSearch();
        verify(client).getEntitiesOfTypeWithPayload(EntityType.AGGREGATED_ENTITY, parametersBuilder().build(), Json.toJson(payloadBuilder().build()));
        assertEquals(expectedResult, result);
    }

    private ODataQueryParametersBuilder parametersBuilder() {
        int top = 25;
        return ODataQueryParametersBuilder.get()
                .orderBy("name asc")
                .exclude(CatalogConstants.ResourceAttributes.VERSION)
                .exclude(CatalogConstants.ResourceAttributes.TAGS)
                .top(top)
                .skip(0);
    }

}
