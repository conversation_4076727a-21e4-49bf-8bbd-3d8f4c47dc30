package temp.services.impl;

import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.diagram.EntityWithDiagram;
import nossis.tsccatalog.models.entities.EntityWithAttributes;
import nossis.tsccatalog.models.entities.Relation;
import nossis.tsccatalog.models.entities.Resource;
import nossis.tsccatalog.models.entities.Rfs;
import nossis.tsccatalog.settings.CatalogConstants;
import org.junit.Before;
import org.junit.Test;
import temp.client.IBackendClient;
import tsc.base.utils.ODataQueryParametersBuilder;

import java.util.ArrayList;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static temp.CompletionStageTestHelper.complete;
import static temp.CompletionStageTestHelper.createCompletionStage;
import static temp.model.AttributeTypeTestModels.componentType;
import static temp.model.EntityTestModels.resourceWithRelations;
import static temp.model.EntityTestModels.rfsWithRelations;
import static temp.model.EntityTestModels.simpleRfs;
import static temp.model.RelationTestModels.rfsRfsRelation;

public class FillDiagramWithRfsServiceTest {

    private FillDiagramWithResourceService fillDiagramWithResourceService;
    private FillDiagramWithRfsService fillDiagramWithRfsService;
    private IBackendClient backendClient;

    @Before
    public void setUp() {
        fillDiagramWithResourceService = mock(FillDiagramWithResourceService.class);
        backendClient = mock(IBackendClient.class);
        fillDiagramWithRfsService = new FillDiagramWithRfsService(backendClient, fillDiagramWithResourceService);
    }

    @Test
    public void addToDiagram_aNewEntity_addedToDiagram() throws InterruptedException, ExecutionException, TimeoutException {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Rfs expectedRfs = simpleRfs();
        expectedRfs.setId(relation.getChildEntity().getId());
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();
        expectedDiagram.addRfs(expectedRfs);
        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(expectedRfs));

        //ACT
        CompletionStage<Rfs> completionStage = fillDiagramWithRfsService.addToDiagram(resultDiagram, relation.getChildEntity());
        Rfs resultRfs = complete(completionStage);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
        assertEquals(expectedRfs, resultRfs);
    }

    @Test
    public void navigateChild_aNewEntity_addedToDiagram()  {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsNoChild = rfsWithRelations();
        rfsNoChild.setChildRfs(new ArrayList<>());
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();
        expectedDiagram.addRfs(rfsNoChild);

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Rfs rfs = rfsWithRelations();
        rfs.setId(relation.getChildEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsNoChild));
        doNothing().when(fillDiagramWithResourceService).navigateChild(rfs.getChildResources(), resultDiagram);

        //ACT
        fillDiagramWithRfsService.navigateChild(rfs.getChildRfs(), resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }

    @Test
    public void fillWithChildren_validEntity_fillDiagram() {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsNoChild = rfsWithRelations();
        rfsNoChild.setChildRfs(new ArrayList<>());
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();
        expectedDiagram.addRfs(rfsNoChild);

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Rfs rfs = rfsWithRelations();
        rfs.setId(relation.getChildEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsNoChild));
        doNothing().when(fillDiagramWithResourceService).navigateChild(rfs.getChildResources(), resultDiagram);

        //ACT
        fillDiagramWithRfsService.fillWithChildren(rfs, resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }


    @Test
    public void fillWithChildren_invalidEntity_NoFillDiagram() {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsNoChild = rfsWithRelations();
        rfsNoChild.setChildRfs(new ArrayList<>());
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Resource notValid = resourceWithRelations();
        notValid.setId(relation.getChildEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsNoChild));
        doNothing().when(fillDiagramWithResourceService).navigateChild(notValid.getChildResources(), resultDiagram);

        //ACT
        fillDiagramWithRfsService.fillWithChildren(notValid, resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }


    @Test
    public void navigateParent_aNewEntity_addedToDiagram()  {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsParent = rfsWithRelations();
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();
        expectedDiagram.addRfs(rfsParent);

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Rfs rfs = rfsWithRelations();
        rfs.setId(relation.getParentEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsParent));

        //ACT
        fillDiagramWithRfsService.navigateParent(rfs.getParentCfs(), resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }


    @Test
    public void fillWithParents_validEntity_fillDiagram()  {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsParent = rfsWithRelations();
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();
        expectedDiagram.addRfs(rfsParent);

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        Rfs rfs = rfsWithRelations();
        rfs.setId(relation.getParentEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsParent));

        //ACT
        fillDiagramWithRfsService.fillWithParents(rfs, resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }


    @Test
    public void fillWithParents_invalidEntity_NoFillDiagram()  {
        //ARRANGE
        Relation relation = rfsRfsRelation();

        Rfs rfsParent = rfsWithRelations();
        EntityWithDiagram<Rfs> expectedDiagram = new EntityWithDiagram<>();

        EntityWithDiagram<Rfs> resultDiagram = new EntityWithDiagram<>();
        EntityWithAttributes notValid = componentType();
        notValid.setId(relation.getParentEntity().getId());

        when(backendClient.getEntityInstanceOfTypeById(Long.parseLong(relation.getChildEntity().getId()),
                setOdataBuilder().build(),EntityType.RFS))
                .thenReturn(createCompletionStage(rfsParent));

        //ACT
        fillDiagramWithRfsService.fillWithParents(notValid, resultDiagram);

        //ASSERT
        assertEquals(expectedDiagram, resultDiagram);
    }

    private ODataQueryParametersBuilder setOdataBuilder() {
        return ODataQueryParametersBuilder
                .get()
                .exclude(CatalogConstants.ResourceAttributes.TAGS)
                .exclude(CatalogConstants.ResourceAttributes.VERSION)
                .exclude(CatalogConstants.ResourceAttributes.ATTRIBUTES)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_CFS)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_RFS)
                .expand(CatalogConstants.ResourceAttributes.CHILD_RFS)
                .expand(CatalogConstants.ResourceAttributes.CHILD_RESOURCES);
    }

}
