package temp.services.impl;

import nossis.tsccatalog.models.EntityType;
import nossis.tsccatalog.models.entities.Resource;
import nossis.tsccatalog.settings.CatalogConstants;
import org.junit.Before;
import temp.client.BackendClient;
import temp.services.AbstractIEntityServiceTest;
import tsc.base.utils.ODataQueryParametersBuilder;

import java.util.Map;

import static org.mockito.Mockito.mock;
import static temp.model.EntityTestModels.simpleResource;

public class ResourceServiceTest extends AbstractIEntityServiceTest<Resource> {

    @Before
    public void setUp() {
        client = mock(BackendClient.class);
        super.setUp(new ResourceService(client), client);
    }

    @Override
    protected Map<String, String> getEntityQuery() {
        return ODataQueryParametersBuilder.get()
                .expand(CatalogConstants.ResourceAttributes.TAGS)
                .expand(CatalogConstants.ResourceAttributes.VERSION)
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTES)
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTES + "/atributeType")
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTES + "/atributeType/constraints")
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTE_CONSTRAINTS_VALUE_LIST)
                .expand(CatalogConstants.ResourceAttributes.ATTRIBUTES_COMPONENT_TYPE)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_CFS)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_RFS)
                .exclude(CatalogConstants.ResourceAttributes.PARENT_RESOURCES)
                .exclude(CatalogConstants.ResourceAttributes.CHILD_RESOURCES).build();
    }

    @Override
    protected Resource getEntity() {
        return simpleResource();
    }

    @Override
    protected EntityType<Resource> getEntityType() {
        return EntityType.RESOURCE;
    }
}