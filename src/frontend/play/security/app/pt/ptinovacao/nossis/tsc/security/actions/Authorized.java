package pt.ptinovacao.nossis.tsc.security.actions;

import play.mvc.With;
import pt.alticelabs.nossis.security.api.Extractor;
import pt.alticelabs.nossis.security.api.Handler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@With({AuthorizedAction.class})
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Authorized {
    String[] value();

    Class<? extends Handler> handler() default Handler.class;

    Class<? extends Extractor> extractor() default Extractor.class;
}
