
package tsc.base.views.html.skeletons.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object contentTabSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(tabs: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.14*/("""

"""),format.raw/*3.1*/("""<ul id="fx-section-tabs" class="nav nav-tabs fx-main-tabs">
   <!-- TABS -->
    """),_display_(/*5.6*/tabs),format.raw/*5.10*/("""
"""),format.raw/*6.1*/("""</ul>"""))
      }
    }
  }

  def render(tabs:Html): play.twirl.api.HtmlFormat.Appendable = apply(tabs)

  def f:((Html) => play.twirl.api.HtmlFormat.Appendable) = (tabs) => apply(tabs)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jun 03 15:39:55 WEST 2025
                  SOURCE: /mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/basemodule/target/TwirlSource/tsc/base/views/skeletons/tabs/contentTabSkel.scala.html
                  HASH: 0de7257050ea2a6b041b8ce56a27d49c6ed474a1
                  MATRIX: 979->1|1086->13|1114->15|1221->97|1245->101|1272->102
                  LINES: 28->1|33->1|35->3|37->5|37->5|38->6
                  -- GENERATED --
              */
          