
package tsc.base.views.html.responses

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import tsc.base.views.html.resumes.skeletons._
/*2.2*/import tsc.base.views.html.resumes.components._
/*3.2*/import scala.collection.mutable.Map

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*4.3*/values/*4.9*/ = {{
	Map(
		"tsc.information.title" -> "tsc.information.description",
		"tsc.information.customerbenefits.title" -> "tsc.information.customerbenefits.description",
		"tsc.information.mainfunctionalities.title" -> "tsc.information.mainfunctionalities.description"
	)
}};
Seq[Any](format.raw/*10.2*/("""
"""),_display_(/*11.2*/resumeSkel/*11.12*/.render(descriptionComp.render(values), null, null, null)),format.raw/*11.69*/("""
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jun 03 15:39:55 WEST 2025
                  SOURCE: /mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/basemodule/target/TwirlSource/tsc/base/views/responses/resume.scala.html
                  HASH: 36ab8a7154ca5ac20460ce38cc9772eecd8a6e86
                  MATRIX: 670->1|724->50|779->100|1186->139|1199->145|1504->420|1533->423|1552->433|1630->490
                  LINES: 24->1|25->2|26->3|35->4|35->4|42->10|43->11|43->11|43->11
                  -- GENERATED --
              */
          