// @GENERATOR:play-routes-compiler
// @SOURCE:/mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/basemodule/conf/base.routes
// @DATE:Tue Jun 03 15:23:48 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:5
package pt.alticelabs.nossis.security.controllers {

  // @LINE:5
  class ReverseNossisAuthenticator(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:7
    def logout(): Call = {
    
      () match {
      
        // @LINE:7
        case ()  =>
          
          Call("GET", _prefix + { _defaultPrefix } + "logout")
      
      }
    
    }
  
    // @LINE:5
    def login(): Call = {
    
      () match {
      
        // @LINE:5
        case ()  =>
          
          Call("GET", _prefix + { _defaultPrefix } + "login")
      
      }
    
    }
  
  }


}
