
package tsc.base.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/tsc/*1.18*/.base.controllers.routes.Application.jsMessages()),format.raw/*1.67*/(""""></script>
<script src=""""),_display_(/*2.15*/tsc/*2.18*/.base.controllers.routes.Application.jsDateFormats()),format.raw/*2.70*/(""""></script>
<script src=""""),_display_(/*3.15*/tsc/*3.18*/.base.controllers.routes.Application.javascriptRoutes()),format.raw/*3.73*/(""""></script>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jun 03 15:39:55 WEST 2025
                  SOURCE: /mnt/external-ssd/nossis-tsc/NOSSISTSC-6935/trunk/src/frontend/play/basemodule/target/TwirlSource/tsc/base/views/imports/scripts.scala.html
                  HASH: 6f7cc2e57448fd109153cfb7313b64b6455d50cb
                  MATRIX: 1049->0|1089->14|1100->17|1169->66|1221->92|1232->95|1304->147|1356->173|1367->176|1442->231
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3
                  -- GENERATED --
              */
          