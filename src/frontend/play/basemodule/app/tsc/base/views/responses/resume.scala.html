@import tsc.base.views.html.resumes.skeletons._
@import tsc.base.views.html.resumes.components._
@import scala.collection.mutable.Map
	@values = @{
	Map(
		"tsc.information.title" -> "tsc.information.description",
		"tsc.information.customerbenefits.title" -> "tsc.information.customerbenefits.description",
		"tsc.information.mainfunctionalities.title" -> "tsc.information.mainfunctionalities.description"
	)
}
@resumeSkel.render(descriptionComp.render(values), null, null, null)
