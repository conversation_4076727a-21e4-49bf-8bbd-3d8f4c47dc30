package tsc.base.models.naDatatables;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by hugo-f-barrigas on 12/9/14.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatatableDataSearch {

    String value;
    boolean regex;

    public DatatableDataSearch(String value, boolean regex) {
        this.value = value;
        this.regex = regex;
    }
    public DatatableDataSearch(){

    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isRegex() {
        return regex;
    }

    public void setRegex(boolean regex) {
        this.regex = regex;
    }

    @Override
    public String toString() {
        return "DatatableDataSearch{" +
                "value='" + value + '\'' +
                ", regex=" + regex +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DatatableDataSearch that = (DatatableDataSearch) o;

        if (regex != that.regex) return false;
        return !(value != null ? !value.equals(that.value) : that.value != null);

    }

    @Override
    public int hashCode() {
        int result = value != null ? value.hashCode() : 0;
        result = 31 * result + (regex ? 1 : 0);
        return result;
    }
}
