package tsc.base.models.tables.response;


import tsc.base.models.tables.query.TableQuery;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <o<PERSON>-<EMAIL>>, 27-11-2018.
 */
public class TableResponse<T> {
    private final int version;
    private final int count;
    private final List<T> data;

    public TableResponse(int version, List<T> data, int count) {
        this.version = version;
        this.data = data;
        this.count = count;
    }

    public static TableResponse<?> fromQuery(TableQuery<?> query){
        return new TableResponse<>(query.getVersion(), Collections.emptyList(), 0);
    }

    public static TableResponse<?> fromQueryAndResponse(TableQuery<?> query, List<?> data, int count){
        return fromQuery(query).withDataAndCount(data, count);
    }

    public static <T> TableResponse<T> typedFromQueryAndResponse(TableQuery<?> query, List<T> data, int count) {
        return new TableResponse<T>(query.getVersion(), data, count);
    }

    public static  TableResponse<?> fromTableResponse (TableResponse<?> tableResponse, List<?> data) {
        return new TableResponse<>(tableResponse.getVersion(), data, tableResponse.getCount());
    }

    public TableResponse<?> withDataAndCount(List<?> data, int count) {
        return new TableResponse<>(this.version, data, count);
    }



    public int getVersion() {
        return version;
    }

    public List<T> getData() {
        return data;
    }

    public int getCount() {
        return count;
    }
}
