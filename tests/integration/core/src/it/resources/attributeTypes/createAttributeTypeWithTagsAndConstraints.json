{
  "idCatEntity": 9,
  "name": "$attributeTypeName",
  "userCreate": "postman",
  "userUpdate": "postman",
  "description": "teste de attribute types",
  "primitiveTypes": "2002",
  "constraints": [
    {
      "idCatEntity": 11,
      "minRange": 5,
      "maxRange": 10,
      "name": "RangeConstraint",
      "type": "RANGE"
    }
  ],
  "tags": [
    {
      "idCatEntity": 6,
      "name": "$tagName0"
    }
  ],
  "version": {
    "id": $versionId,
    "idCatEntity": 7
  }
}