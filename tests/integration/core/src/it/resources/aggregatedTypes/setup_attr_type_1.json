{
  "idCatEntity": 9,
  "name": "simpleAttributeType1",
  "userCreate": "ci-nossis-tsc",
  "userUpdate": "ci-nossis-tsc",
  "description": "simple attribute type for aggregatedType Tests",
  "primitiveTypes": "2001",
  "tags" : [
    { "idCatEntity": 6, "name": "all-tag" },
    { "idCatEntity": 6, "name": "aggregated-attributeTypeOnly-tag" },
    { "idCatEntity": 6, "name": "aggregated-attributeTypeSpecific1-tag" }
  ],
  "version": {
    "idCatEntity": 7,
    "id": $versionId
  }
}