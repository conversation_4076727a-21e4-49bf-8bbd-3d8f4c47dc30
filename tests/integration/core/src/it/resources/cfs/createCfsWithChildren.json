{
  "idCatEntity": 17,
  "name": "{{ name }}",
  "userCreate": "ci-nossis-tsc",
  "userUpdate": "ci-nossis-tsc",
  "description": "{{ description }}",
  "atributes": [
    {
      "idCatEntity": 16,
      "name": "exampleAttribute",
      "optional": "true",
      "description": "example attribute",
      "atributeType": {
        "idCatEntity": 9,
        "id": {{ attribute_type_id }}
      },
      "tags": [
        {
          "idCatEntity": 6,
          "name": "ci",
          "description": "continuous integration tests"
        }
      ]
    }
  ],
  "tags": [
    {
      "idCatEntity": 6,
      "name": "ci",
      "description": "continuous integration tests"
    }
  ],
  "version": {
    "idCatEntity": 7,
    "id": {{ version_id }}
  },
  "childCfs": [
    {{ child_cfs }}
  ]
}