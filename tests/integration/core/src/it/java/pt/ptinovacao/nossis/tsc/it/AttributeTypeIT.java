package pt.ptinovacao.nossis.tsc.it;

import app.getxray.xray.junit.customjunitxml.annotations.XrayTest;
import com.google.common.collect.Sets;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.nossistsc.core.model.AttributeType;
import pt.ptinovacao.nossistsc.core.model.EnumConstraint;
import pt.ptinovacao.nossistsc.core.model.FloatRangeConstraint;
import pt.ptinovacao.nossistsc.core.model.RangeConstraint;
import pt.ptinovacao.nossistsc.core.model.RegExpConstraint;
import pt.ptinovacao.nossistsc.core.model.SizeConstraint;
import pt.ptinovacao.nossistsc.core.model.Value;
import pt.ptinovacao.nossistsc.core.model.Version;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static pt.ptinovacao.nossis.tsc.it.Commons.OBJECT_MAPPER;
import static pt.ptinovacao.nossis.tsc.it.Commons.URL_ATTRIBUTE_TYPES;
import static pt.ptinovacao.nossis.tsc.it.Commons.URL_SEARCH_ATTRIBUTE_TYPES;
import static pt.ptinovacao.nossis.tsc.it.Commons.URL_TAGS;
import static pt.ptinovacao.nossis.tsc.it.Commons.URL_VERSIONS;
import static pt.ptinovacao.nossis.tsc.it.CommonsCore.getEntityIdsFromIT;
import static pt.ptinovacao.nossis.tsc.it.CommonsCore.validateImmutable;

public class AttributeTypeIT {

    private static final Logger LOGGER = LoggerFactory.getLogger(AttributeTypeIT.class);
    private static final String ATTR_TYPE_JSON_FOLDER = Commons.RESOURCES_DIR + "attributeTypes/";
    private static final String VERSION_JSON_FOLDER = Commons.RESOURCES_DIR + "versions/";
    private static final String VERSION_JSON_FILE = Commons.RESOURCES_DIR + "versions/testVersion_success_Crud.json";
    private static final String TAG_JSON_FILE = Commons.RESOURCES_DIR + "tags/createTag.json";

    private static final String TAG_BASE_NAME = "AttributeTypeTagName-";
    private static final String VERSION_BASE_NAME = "AttributeTypeITVersion-";
    private static final String ATTR_TYPE_BASE_NAME = "AttributeTypeName-";
    private static Version VERSION;

    @BeforeClass
    public static void setUp() {
        tearDown();

        // create version
        VERSION = EntityUtils.createVersion(Utils.readFile(VERSION_JSON_FILE).replace("$name", VERSION_BASE_NAME + "1"));

        // create tags
        for (int i = 0; i < 3; i++) {
            EntityUtils.createTag(Utils.readFile(TAG_JSON_FILE).replace("$tagName", TAG_BASE_NAME + i));
        }
    }

    @AfterClass
    public static void tearDown() {
        // delete versions
        List<Long> versionIds = getEntityIdsFromIT(URL_VERSIONS + "?$filter=startswith(name,'" + VERSION_BASE_NAME + "')", "versions");
        versionIds.forEach(EntityUtils::deleteVersion);
        // delete tags
        List<Long> tagIds = getEntityIdsFromIT(URL_TAGS + "?$filter=startswith(name,'" + TAG_BASE_NAME + "')", "tags");
        tagIds.forEach(EntityUtils::deleteTag);
    }

    @Test
    @XrayTest(key = "NOSSISTSC-223")
    public void testCRUDAttributeType_success_Base() {
        Long attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json";
            final String updateJsonFile = ATTR_TYPE_JSON_FOLDER + "updateAttributeTypeBase.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile).replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString());

            AttributeType original = OBJECT_MAPPER.readValue(createAttributeTypePayload, AttributeType.class);
            AttributeType created = EntityUtils.createAttributeType(createAttributeTypePayload);

            // validate create
            assertNotNull(created.getId());
            attributeTypeId = created.getId();

            assertEquals(original.getName(), created.getName());
            assertEquals(original.getPrimitiveTypes(), created.getPrimitiveTypes());
            assertEquals(original.getDescription(), created.getDescription());
            assertEquals(original.getVersion().getId(), created.getVersion().getId());

            // constraints
            assertEquals(original.getConstraints().size(), created.getConstraints().size());
            assertEquals(0, created.getConstraints().size());

            // tags
            assertNotNull(created.getTags());
            assertEquals(0, created.getTags().size());

            // update attribute type
            String updateAttributeTypePayload = Utils.readFile(updateJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString());

            EntityUtils.updateAttributeType(updateAttributeTypePayload, created.getId());
            AttributeType updated = EntityUtils.getAttributeType(created.getId());

            // validate update
            assertEquals("teste de attribute types2", updated.getDescription());
            assertEquals("2001", updated.getPrimitiveTypes());

            assertNotNull(updated.getTags());
            assertNotNull(updated.getConstraints());
            assertEquals(0, updated.getConstraints().size());
            assertEquals(0, updated.getTags().size());

            // delete attribute type
            EntityUtils.deleteAttributeType(attributeTypeId);

            // try to get
            assertEquals(404, HttpUtils.sendRestGet(URL_ATTRIBUTE_TYPES + "/" + attributeTypeId).getStatus());

            // try to update
            assertEquals(410, HttpUtils.sendRestPut(URL_ATTRIBUTE_TYPES + "/" + attributeTypeId, Entity.entity("{}", MediaType.APPLICATION_JSON_TYPE)).getStatus());

            // try to update with empty payload
            Response response = HttpUtils.sendRestPut(URL_ATTRIBUTE_TYPES + "/" + attributeTypeId, null);
            List errors = (List) response.readEntity(Map.class).get("errors");
            Map error = (Map) errors.get(0);
            assertEquals(400, response.getStatus());
            assertEquals("Cannot update the attributeTypes resource with an empty payload", error.get("developerMessage"));

            attributeTypeId = null;

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-224")
    public void testCRUDAttributeType_success_WithTagsAndConstraints() {
        Long attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithTagsAndConstraints.json";
            final String updateJsonFile = ATTR_TYPE_JSON_FOLDER + "updateAttributeTypeWithTagsAndConstraints.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString())
                    .replace("$tagName", TAG_BASE_NAME);
            AttributeType original = OBJECT_MAPPER.readValue(createAttributeTypePayload, AttributeType.class);
            AttributeType created = EntityUtils.createAttributeType(createAttributeTypePayload);

            // validate create
            assertNotNull(created.getId());
            attributeTypeId = created.getId();

            assertEquals(original.getName(), created.getName());
            assertEquals(original.getPrimitiveTypes(), created.getPrimitiveTypes());
            assertEquals(original.getDescription(), created.getDescription());
            assertEquals(original.getVersion().getId(), created.getVersion().getId());

            // constraints
            assertNotNull(created.getConstraints());
            assertEquals(original.getConstraints().size(), created.getConstraints().size());
            assertEquals(1, created.getConstraints().size());
            assertNotNull(created.getConstraints().get(0).getName());
            assertTrue(created.getConstraints().get(0) instanceof RangeConstraint);
            assertEquals(Long.valueOf(5L), ((RangeConstraint) created.getConstraints().get(0)).getMinRange());
            assertEquals(Long.valueOf(10L), ((RangeConstraint) created.getConstraints().get(0)).getMaxRange());

            // tags
            assertNotNull(created.getTags());
            assertEquals(1, created.getTags().size());
            assertEquals(TAG_BASE_NAME + "0", created.getTags().get(0).getName());


            // update attribute type
            String updateAttributeTypePayload = Utils.readFile(updateJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString())
                    .replace("$tagName", TAG_BASE_NAME);

            EntityUtils.updateAttributeType(updateAttributeTypePayload, created.getId());
            AttributeType updated = EntityUtils.getAttributeType(created.getId());

            // validate update
            assertEquals("teste de attribute types2", updated.getDescription());
            assertEquals("2001", updated.getPrimitiveTypes());

            assertNotNull(updated.getTags());
            assertNotNull(updated.getConstraints());
            assertEquals(1, updated.getConstraints().size());
            assertEquals(2, updated.getTags().size());

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-225")
    public void testUpdateAttributeType_failure_DifferentVersion() {
        Long versionId = null, attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            versionId = EntityUtils.createVersion(Utils.readFile(VERSION_JSON_FOLDER + "testVersion_success_Crud.json")
                            .replace("$name", VERSION_BASE_NAME + UUID.randomUUID().toString()))
                    .getId();
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json";
            final String updateJsonFile = ATTR_TYPE_JSON_FOLDER + "updateAttributeTypeBase.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString());

            AttributeType attributeType = EntityUtils.createAttributeType(createAttributeTypePayload);
            attributeTypeId = attributeType.getId();

            // update attribute type with different version
            String updatePayload = Utils.readFile(updateJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", versionId.toString());

            new CommonsCore.Test().url(URL_ATTRIBUTE_TYPES + "/" + attributeTypeId + "?$expand=tags,constraints,version")
                    .expectedCode("EXCEPTION-NOSSISTSC-00004")
                    .expectedMessage("The AttributeType's version can not be changed from '" + VERSION.getId() + "' to '" + versionId + "'.")
                    .requestType("PUT")
                    .payload(updatePayload)
                    .customStatus(Response.Status.PRECONDITION_FAILED.getStatusCode())
                    .run();

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
            EntityUtils.deleteVersion(versionId);
            versionId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
            if (versionId != null) {
                EntityUtils.deleteVersion(versionId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-226")
    public void testCreateAttributeType_failure_DuplicateNameAndVersion() {
        Long attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString());

            AttributeType attributeType = EntityUtils.createAttributeType(createAttributeTypePayload);
            attributeTypeId = attributeType.getId();
            new CommonsCore.Test()
                    .url(URL_ATTRIBUTE_TYPES + "?$expand=tags,constraints,version")
                    .expectedCode("EXCEPTION-NOSSISTSC-00021")
                    .requestType("POST")
                    .expectedMessage("The Version '" + VERSION.getName() + "' already contains the Type '" + uniqueName + "'.")
                    .payload(createAttributeTypePayload)
                    .customStatus(Response.Status.PRECONDITION_FAILED.getStatusCode())
                    .run();

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-227")
    public void testCreateAttributeType_failure_InvalidPrimitiveType() {
        try {
            String uniqueName = getAttributeTypeName();
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString())
                    .replace("2002", "2010");

            new CommonsCore.Test().url(URL_ATTRIBUTE_TYPES + "?$expand=tags,constraints,version")
                    .expectedCode("FMWK-COMMON-00014")
                    .requestType("POST")
                    .expectedMessage("The attribute AttributeType.Type with value 2010 is not a valid value.")
                    .payload(createAttributeTypePayload)
                    .customStatus(Response.Status.PRECONDITION_FAILED.getStatusCode())
                    .run();

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-228")
    public void testSearchAttributeTypes_success_byAttribute() {
        Long attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            final String searchJsonFile = ATTR_TYPE_JSON_FOLDER + "searchAttributeTypeByAttribute.json";
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString());

            AttributeType attributeType = EntityUtils.createAttributeType(createAttributeTypePayload);
            attributeTypeId = attributeType.getId();

            // search attribute type
            String searchAttributeTypePayload = Utils.readFile(searchJsonFile)
                    .replace("$attributeName", "name")
                    .replace("$operator", "eq")
                    .replace("$attributeValue", uniqueName);

            AttributeType attr = searchAttributeTypes(searchAttributeTypePayload, 1).get(0);
            assertEquals(uniqueName, attr.getName());

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-229")
    public void testSearchAttributeTypes_success_byTag() {
        Long attributeTypeId = null;
        try {
            String uniqueName = getAttributeTypeName();
            final String searchJsonFile = ATTR_TYPE_JSON_FOLDER + "searchAttributeTypeByTag.json";
            final String createJsonFile = ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithTagsAndConstraints.json";

            // create attribute type
            String createAttributeTypePayload = Utils.readFile(createJsonFile)
                    .replace("$attributeTypeName", uniqueName)
                    .replace("$versionId", VERSION.getId().toString())
                    .replace("$tagName", TAG_BASE_NAME);

            AttributeType attributeType = EntityUtils.createAttributeType(createAttributeTypePayload);
            attributeTypeId = attributeType.getId();

            // search attribute type
            String searchAttributeTypePayload = Utils.readFile(searchJsonFile)
                    .replace("$tagOperator", "eq")
                    .replace("$tagValue", TAG_BASE_NAME + "0");

            AttributeType attr = searchAttributeTypes(searchAttributeTypePayload, 1).get(0);
            assertEquals(uniqueName, attr.getName());

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            // delete attribute type
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-371")
    public void testCreateAttributeType_failure_versionImmutableState() {

        Long versionID = null;

        try {
            final Long updatedState = 3L;
            final String versionName = VERSION_BASE_NAME + CommonsCore.createUUID();
            final String createVersion = Utils.readFile(Commons.RESOURCES_DIR + "versions/createVersion.json")
                    .replace("{{ name }}", versionName)
                    .replace("{{ description }}", "testCreateAttributeType_failure_versionImmutableState")
                    .replace("{{ state }}", "1");
            final String updateVersion = Utils.readFile(Commons.RESOURCES_DIR + "versions/createVersion.json")
                    .replace("{{ name }}", versionName)
                    .replace("{{ description }}", "testCreateAttributeType_failure_versionImmutableState")
                    .replace("{{ state }}", updatedState.toString());

            // create version
            Version version = EntityUtils.createVersion(createVersion);
            versionID = version.getId();

            // update version state to an immutable one
            Response response = EntityUtils.updateVersion(versionID, updateVersion);
            assertNotNull(response);
            assertEquals(204, response.getStatus());

            // get updated version
            version = EntityUtils.getVersion(versionID);
            assertNotNull(version);
            assertEquals(versionID, version.getId());
            assertEquals(updatedState, version.getState());

            // try to create attribute type and fail
            final String attrTypePayload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json")
                    .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "bornToFail")
                    .replace("$versionId", versionID.toString());

            // validate
            response = HttpUtils.sendRestPost(Commons.URL_ATTRIBUTE_TYPES, Entity.entity(attrTypePayload, MediaType.APPLICATION_JSON_TYPE));
            final Map businessException = response.readEntity(Map.class);
            final List exceptionErrors = (List) businessException.get("errors");
            final Map error = (Map) exceptionErrors.get(0);
            assertEquals(412, response.getStatus());
            assertEquals("EXCEPTION-NOSSISTSC-00011", error.get("errorCode"));
            assertEquals("The version '" + versionName + "' is in the immutable state 'Abandoned'. To allow for changes it must be in one of the following states '[Development]'.", error.get("developerMessage"));

            // delete version
            EntityUtils.deleteVersion(versionID);

            versionID = null;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            fail(e.getMessage());
        } finally {
            if (versionID != null) {
                EntityUtils.deleteVersion(versionID);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-379")
    public void testUpdateAndDeleteAttributeType_failure_versionImmutableState() {

        Long versionID = null;

        try {
            final Long updatedState = 3L;
            final String versionName = VERSION_BASE_NAME + CommonsCore.createUUID();
            final String createVersion = Utils.readFile(Commons.RESOURCES_DIR + "versions/createVersion.json")
                    .replace("{{ name }}", versionName)
                    .replace("{{ description }}", "testUpdateAndDeleteAttributeType_failure_versionImmutableState")
                    .replace("{{ state }}", "1");
            final String updateVersion = Utils.readFile(Commons.RESOURCES_DIR + "versions/createVersion.json")
                    .replace("{{ name }}", versionName)
                    .replace("{{ description }}", "testUpdateAndDeleteAttributeType_failure_versionImmutableState")
                    .replace("{{ state }}", updatedState.toString());

            // create version
            Version version = EntityUtils.createVersion(createVersion);
            versionID = version.getId();

            // create attribute type
            final String createAttributeType = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json")
                    .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "success")
                    .replace("$versionId", versionID.toString());
            AttributeType attributeType = EntityUtils.createAttributeType(createAttributeType);
            final Long attributeTypeID = attributeType.getId();

            // update version state to an immutable one
            Response response = EntityUtils.updateVersion(versionID, updateVersion);
            assertNotNull(response);
            assertEquals(204, response.getStatus());

            // get updated version
            version = EntityUtils.getVersion(versionID);
            assertNotNull(version);
            assertEquals(versionID, version.getId());
            assertEquals(updatedState, version.getState());

            // try to update attribute type and fail
            final String updateAttributeType = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeBase.json")
                    .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "fail")
                    .replace("$versionId", versionID.toString());

            response = HttpUtils.sendRestPost(Commons.URL_ATTRIBUTE_TYPES, Entity.entity(updateAttributeType, MediaType.APPLICATION_JSON_TYPE));

            // validate
            validateImmutable(response, versionName, updatedState);

            // try to delete attribute type and fail
            response = HttpUtils.sendRestDelete(Commons.URL_ATTRIBUTE_TYPES + "/" + attributeTypeID);

            // validate
            validateImmutable(response, versionName, updatedState);

            // delete version
            EntityUtils.deleteVersion(versionID);

            versionID = null;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            fail(e.getMessage());
        } finally {
            if (versionID != null) {
                EntityUtils.deleteVersion(versionID);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-495")
    public void testCreateAttributeType_success_regExpConstraint() {
        Long attributeTypeId = null;
        try {
            String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRegExpConstraint.json")
                    .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                    .replace("$versionId", VERSION.getId().toString());

            AttributeType attributeType = EntityUtils.createAttributeType(payload);

            assertNotNull(attributeType);
            assertNotNull(attributeType.getId());
            attributeTypeId = attributeType.getId();
            assertEquals(1, attributeType.getConstraints().size());
            assertTrue(attributeType.getConstraints().get(0) instanceof RegExpConstraint);
            assertEquals("[a-z]*", ((RegExpConstraint) attributeType.getConstraints().get(0)).getRegexp());

            EntityUtils.deleteAttributeType(attributeTypeId);
            attributeTypeId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (attributeTypeId != null) {
                EntityUtils.deleteAttributeType(attributeTypeId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-496")
    public void testCreateAttributeType_failure_invalidSizeConstraint() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithSizeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"min\": 5,", "");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00017",
                "Invalid SizeConstraint with values (min=null, max=10).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-497")
    public void testCreateAttributeType_failure_invalidRangeConstraintMinGreaterThanMax() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"minRange\" : 5,", "\"minRange\" : 11,");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00018",
                "Invalid RangeConstraint with values (minRange=11, maxRange=10).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-498")
    public void testCreateAttributeType_failure_invalidEnumConstraintPrimitiveType() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithEnumConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"primitiveTypes\": \"2004\",", "\"primitiveTypes\": \"2002\",");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00015",
                "Invalid constraint of type 'ENUMC'. In order to add an ENUMC constraint, AttributeType's primitiveTypes property should be [2004].");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-501")
    public void testCreateAttributeType_failure_invalidRegExpConstraintPrimitiveType() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRegExpConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"primitiveTypes\": \"2001\",", "\"primitiveTypes\": \"2004\",");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00015",
                "Invalid constraint of type 'REGEXP'. In order to add an REGEXP constraint, AttributeType's primitiveTypes property should be [2001, 2003, 2002].");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-502")
    public void testCreateAttributeType_failure_invalidSizeConstraintPrimitiveType() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithSizeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"primitiveTypes\": \"2001\",", "\"primitiveTypes\": \"2002\",");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00015",
                "Invalid constraint of type 'SIZE'. In order to add an SIZE constraint, AttributeType's primitiveTypes property should be [2001].");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-503")
    public void testCreateAttributeType_failure_invalidRangeConstraintPrimitiveType() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"primitiveTypes\": \"2002\",", "\"primitiveTypes\": \"2001\",");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00015",
                "Invalid constraint of type 'RANGE'. In order to add an RANGE constraint, AttributeType's primitiveTypes property should be [2002].");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-504")
    public void testCreateAttributeType_failure_invalidFloatRangeConstraintPrimitiveType() {
        // create attribute type
        final String createAttributeType = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithFloatRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"primitiveTypes\": \"2003\",", "\"primitiveTypes\": \"2002\",");

        createAttributeTypeWithInvalidConstraint(createAttributeType, "EXCEPTION-NOSSISTSC-00015",
                "Invalid constraint of type 'FLOATRANGE'. In order to add an FLOATRANGE constraint, AttributeType's primitiveTypes property should be [2003].");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-505")
    public void testCreateAttributeType_failure_invalidRegExpConstraint() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRegExpConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"type\" : \"REGEXP\",", "\"type\" : \"REGEXP\"")
                .replace("\"regexp\" : \"[a-z]*\"", "");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00006",
                "The field 'REGEXP' can not be null or empty.");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-506")
    public void testCreateAttributeType_failure_invalidRangeConstraintNullMinMax() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"type\" : \"RANGE\",", "\"type\" : \"RANGE\"")
                .replace("\"minRange\" : 5,", "")
                .replace("\"maxRange\": 10", "");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00018",
                "Invalid RangeConstraint with values (minRange=null, maxRange=null).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-507")
    public void testCreateAttributeType_failure_invalidSizeConstraintMinLessThanZero() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithSizeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"min\": 5,", "\"min\": -1,");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00017",
                "Invalid SizeConstraint with values (min=-1, max=10).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-508")
    public void testCreateAttributeType_failure_invalidEnumConstraintEmptyValueList() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithEnumConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"valueList\" : [\n" +
                        "        {\n" +
                        "          \"idCatEntity\": 15,\n" +
                        "          \"position\": 1,\n" +
                        "          \"value\": \"valor1\",\n" +
                        "          \"name\": \"valor1\"\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"idCatEntity\": 15,\n" +
                        "          \"position\": 2,\n" +
                        "          \"value\": \"valor2\",\n" +
                        "          \"name\": \"valor2\"\n" +
                        "        }\n" +
                        "      ]", "\"valueList\" : []");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00016",
                "Enum Constraint has no values.");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-509")
    public void testCreateAttributeType_failure_invalidFloatRangeConstraintNullMinMax() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithFloatRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"type\" : \"FLOATRANGE\",", "\"type\" : \"FLOATRANGE\"")
                .replace("\"minRange\" : 5.0,", "")
                .replace("\"maxRange\": 10.0", "");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00022",
                "Invalid FloatRangeConstraint with values (minRange=null, maxRange=null).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-510")
    public void testCreateAttributeType_failure_invalidFloatRangeConstraintMinGreaterThanMax() {
        final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithFloatRangeConstraint.json")
                .replace("$attributeTypeName", ATTR_TYPE_BASE_NAME + "1")
                .replace("$versionId", VERSION.getId().toString())
                .replace("\"minRange\" : 5.0,", "\"minRange\" : 11.0,");

        createAttributeTypeWithInvalidConstraint(payload, "EXCEPTION-NOSSISTSC-00022",
                "Invalid FloatRangeConstraint with values (minRange=11.0, maxRange=10.0).");
    }

    @Test
    @XrayTest(key = "NOSSISTSC-819")
    public void testCloneAttributeType_success_withEnumConstraint() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithEnumConstraint.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withEnumConstraint")
                    .replace("$versionId", VERSION.getId().toString());
            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(cloned.getConstraints().get(0) instanceof EnumConstraint);
            assertEquals(2, ((EnumConstraint) cloned.getConstraints().get(0)).getValueList().size());
            assertEquals(Sets.newHashSet("valor1", "valor2"), ((EnumConstraint) cloned.getConstraints().get(0)).getValueList().stream().map(Value::getValue).collect(Collectors.toSet()));

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-820")
    public void testCloneAttributeType_success_withFloatRangeConstraint() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithFloatRangeConstraint.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withFloatRangeConstraint")
                    .replace("$versionId", VERSION.getId().toString());
            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(cloned.getConstraints().get(0) instanceof FloatRangeConstraint);
            assertEquals(((FloatRangeConstraint) created.getConstraints().get(0)).getMinRange(), ((FloatRangeConstraint) cloned.getConstraints().get(0)).getMinRange());
            assertEquals(((FloatRangeConstraint) created.getConstraints().get(0)).getMaxRange(), ((FloatRangeConstraint) cloned.getConstraints().get(0)).getMaxRange());

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-821")
    public void testCloneAttributeType_success_withRangeConstraint() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRangeConstraint.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withRangeConstraint")
                    .replace("$versionId", VERSION.getId().toString());
            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(cloned.getConstraints().get(0) instanceof RangeConstraint);
            assertEquals(((RangeConstraint) created.getConstraints().get(0)).getMinRange(), ((RangeConstraint) cloned.getConstraints().get(0)).getMinRange());
            assertEquals(((RangeConstraint) created.getConstraints().get(0)).getMaxRange(), ((RangeConstraint) cloned.getConstraints().get(0)).getMaxRange());

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-822")
    public void testCloneAttributeType_success_withSizeConstraint() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithSizeConstraint.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withSizeConstraint")
                    .replace("$versionId", VERSION.getId().toString());
            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(cloned.getConstraints().get(0) instanceof SizeConstraint);
            assertEquals(((SizeConstraint) created.getConstraints().get(0)).getMin(), ((SizeConstraint) cloned.getConstraints().get(0)).getMin());
            assertEquals(((SizeConstraint) created.getConstraints().get(0)).getMax(), ((SizeConstraint) cloned.getConstraints().get(0)).getMax());

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-824")
    public void testCloneAttributeType_success_withRegExpConstraint() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithRegExpConstraint.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withRegExpConstraint")
                    .replace("$versionId", VERSION.getId().toString());
            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(cloned.getConstraints().get(0) instanceof RegExpConstraint);
            assertEquals(((RegExpConstraint) created.getConstraints().get(0)).getRegexp(), ((RegExpConstraint) cloned.getConstraints().get(0)).getRegexp());

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }

    @Test
    @XrayTest(key = "NOSSISTSC-823")
    public void testCloneAttributeType_success_withTags() {
        Long createdId = null, clonedId = null;
        try {
            final String payload = Utils.readFile(ATTR_TYPE_JSON_FOLDER + "createAttributeTypeWithTagsAndConstraints.json")
                    .replace("$attributeTypeName", "testCloneAttributeType_success_withTags")
                    .replace("$versionId", VERSION.getId().toString())
                    .replace("$tagName0", "ciTests");

            AttributeType created = EntityUtils.createAttributeType(payload);
            assertNotNull(created.getId());
            createdId = created.getId();
            AttributeType cloned = EntityUtils.cloneAttributeType(created.getId(), "");
            assertNotNull(cloned.getId());
            clonedId = cloned.getId();
            assertEquals("Clone_" + created.getName(), cloned.getName());
            assertEquals(created.getConstraints().size(), cloned.getConstraints().size());
            assertEquals(1, cloned.getConstraints().size());
            assertTrue(created.getConstraints().get(0) instanceof RangeConstraint);
            assertEquals(created.getTags().size(), cloned.getTags().size());
            assertEquals(1, cloned.getTags().size());
            assertEquals("ciTests", cloned.getTags().get(0).getName());

            EntityUtils.deleteAttributeType(createdId);
            createdId = null;
            EntityUtils.deleteAttributeType(clonedId);
            clonedId = null;
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
            fail(ex.getMessage());
        } finally {
            if (createdId != null) {
                EntityUtils.deleteAttributeType(createdId);
            }
            if (clonedId != null) {
                EntityUtils.deleteAttributeType(clonedId);
            }
        }
    }


    private List<AttributeType> searchAttributeTypes(String payload, int expectedCount) {
        Map<String, Object> response = new CommonsCore.Test()
                .url(URL_SEARCH_ATTRIBUTE_TYPES + "?$expand=tags,constraints,version,constraints/valueList")
                .requestType("POST")
                .payload(payload)
                .customStatus(201)
                .run();
        assertTrue(response.containsKey("count"));
        assertTrue(response.containsKey("attributeTypes"));
        assertEquals(expectedCount, response.get("count"));
        List<AttributeType> attrTypes = new ArrayList<>();
        for (Object o : (List<Object>) response.get("attributeTypes")) {
            attrTypes.add(OBJECT_MAPPER.convertValue(o, AttributeType.class));
        }
        return attrTypes;
    }

    private void createAttributeTypeWithInvalidConstraint(String payload, String expectedErrorCore, String expectedErrorMessage) {
        try {
            new CommonsCore.Test()
                    .url(URL_ATTRIBUTE_TYPES)
                    .expectedCode(expectedErrorCore)
                    .customStatus(Response.Status.PRECONDITION_FAILED.getStatusCode())
                    .expectedMessage(expectedErrorMessage)
                    .requestType("POST")
                    .payload(payload)
                    .run();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            fail(e.getMessage());
        }
    }

    private String getAttributeTypeName() {
        return (ATTR_TYPE_BASE_NAME + UUID.randomUUID().toString()).substring(0, 20);
    }
}
