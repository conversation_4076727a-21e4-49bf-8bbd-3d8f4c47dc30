package pt.ptinovacao.nossis.tsc.it;

import app.getxray.xray.junit.customjunitxml.annotations.XrayTest;
import org.junit.Assert;
import org.junit.Test;

import javax.ws.rs.core.Response;

public class HealthCheckTestsIT {

    @Test
    @XrayTest(key = "NOSSISTSC-4283")
    public void testHealthCheck() {
        Response response = HttpUtils.sendRestGet(Commons.NOSSIS_TSC_BASE_URL + "health/");
        Assert.assertNotNull(response);
        Assert.assertEquals(200, response.getStatus());
    }
}
