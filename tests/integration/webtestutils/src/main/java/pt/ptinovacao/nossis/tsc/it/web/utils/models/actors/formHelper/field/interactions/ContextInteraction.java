package pt.ptinovacao.nossis.tsc.it.web.utils.models.actors.formHelper.field.interactions;

import pt.ptinovacao.nossis.tsc.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.nossis.tsc.it.web.utils.driver.WebElementReference;

interface ContextInteraction {

    ItWebDriver driver();

    WebElementReference getElementReference();

    FormFieldInteraction interaction(ItWebDriver driver, String formElement);

    FormFieldInteraction interaction(ItWebDriver driver, WebElementReference elementReference);

    FormFieldInteraction interaction(ItWebDriver driver);
}
