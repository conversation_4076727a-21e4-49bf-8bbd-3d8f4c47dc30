package pt.ptinovacao.nossis.tsc.it.web.utils.helpers;

import org.openqa.selenium.remote.LocalFileDetector;
import pt.ptinovacao.nossis.tsc.it.web.utils.driver.ItWebDriver;

import java.io.File;
import java.util.List;

/**
 * Helper for upload of content to the browser.
 *
 * <AUTHOR> <o<PERSON>-<EMAIL>>, 04-11-2015.
 */
public class FileUpload {
    public static final String DEFAULT_ASSETS_RESOURCE_PATH = "src/it/resources/assets";
    public static final String DEFAULT_ASSETS_RESOURCE_FULL_PATH = System.getProperty("user.dir")+"/"+DEFAULT_ASSETS_RESOURCE_PATH;

    private final ItWebDriver driver;
    private final String resourceFullPath;


    private FileUpload(ItWebDriver driver, String resourceFullPath){
        this.driver = driver;
        this.resourceFullPath = resourceFullPath;
    }

    public static FileUpload using(ItWebDriver driver){
        return using(driver, DEFAULT_ASSETS_RESOURCE_FULL_PATH);
    }

    public static FileUpload using(ItWebDriver driver, String resourceFullPath){
        return new FileUpload(driver, resourceFullPath);
    }


    public String fullPathOfAsset(String assetFileName){
        return new File(resourceFullPath,assetFileName).getPath();
    }

    private void setLocalFileDetector(){
        if(!driver.isFileDetectorSet()){
            driver.setFileDetector(new LocalFileDetector());
        }
    }

    public void uploadFileFromResource(String file){
        uploadFileFrom(fullPathOfAsset(file));
    }

    public void uploadFileFrom(String file){
        setLocalFileDetector();
        driver.element("upload button").sendKeys(file);
    }

    public void uploadFilesFromResource(List<String> files){
        for (String file : files){
            uploadFileFrom(fullPathOfAsset(file));
        }
    }
}
