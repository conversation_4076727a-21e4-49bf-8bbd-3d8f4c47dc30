package pt.ptinovacao.nossis.tsc.it.web.utils.models.factory;

import org.slf4j.Logger;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.common.I18nDefinition;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.common.LabelDefinition;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.table_configs.ColumnType;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.table_configs.TableColumnDefinition;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.table_configs.TableDefinition;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>, 13-01-2016.
 */
public class TableDefinitionFactory {

    protected static final Logger LOGGER = LoggerFactory.getLogger(TableDefinitionFactory.class);


    private interface TableParameters {
        String ELEMENT_KEY = "elementKey";
        String FEATURES = "features";
        String COLUMNS = "columns";
    }

    private interface ColumnParameter {
        String TYPE = "type";
        String CSS_CLASS = "css_class";
        String LABEL = "label";
        String META_INFO = "meta";
    }

    private interface LabelParameter {
        String I18N = "i18n";
        String I18N_KEY = "key";
    }


    @SuppressWarnings("unchecked")
    public static TableDefinition fromConfig(String tableName, Map<String, Object> config) {

        final TableDefinition tableDefinition = new TableDefinition();
        tableDefinition.setTableName(tableName);
        tableDefinition.setElementKey(config.get(TableParameters.ELEMENT_KEY).toString());
        final List<String> features = (List<String>) config.get(TableParameters.FEATURES);
        tableDefinition.setFeatures(features);

        final Map<String, Map<String, Object>> rawColumns = (Map<String, Map<String, Object>>) config.get(TableParameters.COLUMNS);
        final Map<String, TableColumnDefinition> columns = new HashMap<>();
        rawColumns.forEach((key, rawColDef) -> {
            final TableColumnDefinition columnDefinition = new TableColumnDefinition();
            columnDefinition.setType(ColumnType.valueOf(rawColDef.get(ColumnParameter.TYPE).toString().toUpperCase()));
            columnDefinition.setCss_class(rawColDef.get(ColumnParameter.CSS_CLASS).toString());

            if(rawColDef.containsKey(ColumnParameter.LABEL)){
                final Map<String, Map<String, String>> labelDef = (Map<String, Map<String, String>>) rawColDef.get(ColumnParameter.LABEL);
                Map<String, String> i18nDef = labelDef.get(LabelParameter.I18N);
                final I18nDefinition i18nDefinition = new I18nDefinition();
                i18nDefinition.setKey(i18nDef.get(LabelParameter.I18N_KEY));
                i18nDef.forEach(i18nDefinition::addTextByLang);
                columnDefinition.setLabel(new LabelDefinition(i18nDefinition));
            }

            if(rawColDef.containsKey(ColumnParameter.META_INFO)){
                columnDefinition.setMeta(rawColDef.get(ColumnParameter.META_INFO));
            }
            columns.put(key, columnDefinition);
        });

        tableDefinition.setColumns(columns);
        return tableDefinition;

    }


}
