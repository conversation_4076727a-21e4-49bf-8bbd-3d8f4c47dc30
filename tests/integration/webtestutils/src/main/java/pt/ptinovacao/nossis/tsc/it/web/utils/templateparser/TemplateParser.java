package pt.ptinovacao.nossis.tsc.it.web.utils.templateparser;

import pt.ptinovacao.nossis.tsc.it.web.utils.templateparser.transformers.EpochMillisTransformer;
import pt.ptinovacao.nossis.tsc.it.web.utils.templateparser.transformers.FuzzyTimeTransformer;
import pt.ptinovacao.nossis.tsc.it.web.utils.templateparser.transformers.TransformationResult;
import pt.ptinovacao.nossis.tsc.it.web.utils.templateparser.transformers.Transformer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> <<EMAIL>>, 20-07-2018.
 */
public class TemplateParser {

    private final Pattern startTag;
    private final Pattern endTag;


    private static final Map<String, Transformer> transformerMap;
    static {
        Map<String, Transformer> transformers = new HashMap<>();
        transformers.put("fuzzyTime", new FuzzyTimeTransformer());
        transformers.put("fuzzyDate", new FuzzyTimeTransformer());
        transformers.put("epochMillis", new EpochMillisTransformer());
        transformerMap = Collections.unmodifiableMap(transformers);
    }

    private TemplateParser(Pattern startTag, Pattern endTag){
        this.startTag = startTag;
        this.endTag = endTag;
    }

    public static final TemplateParser DOUBLE_BRACKETS_TAGS =  new TemplateParser(Pattern.compile("\\{\\{"), Pattern.compile("}}"));
    public static final TemplateParser ARROW_PERCENT_TAGS =  new TemplateParser(Pattern.compile("<%"), Pattern.compile("%>"));

    public String parse(String text){

        ArrayList<Boolean> isExpr = new ArrayList<>();
        isExpr.add(false);
        return startTag.splitAsStream(text)
                .flatMap(endTag::splitAsStream)
                .map(string -> {
                    Boolean isExpression = isExpr.get(0);
                    isExpr.set(0, !isExpression);
            if(!isExpression){
                return string;
            } else {
                final List<String> strings = Stream.of(string.split("\\|"))
                        .map(String::trim)
                        .collect(Collectors.toList());
                final List<Transformer> transformers = strings.stream().skip(1)
                        .map(transformerMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                TransformationResult result = new TransformationResult(strings.get(0), strings.get(0));
                for (Transformer transformer: transformers) {
                    result = transformer.transform(result);
                }
                return result.getFinalResult();
            }
        }).collect(Collectors.joining(""));
    }

}
