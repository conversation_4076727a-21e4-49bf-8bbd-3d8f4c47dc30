package pt.ptinovacao.nossis.tsc.it.web.utils.models.actors.notification;

import com.google.common.base.Preconditions;
import pt.ptinovacao.nossis.tsc.it.web.utils.exception.UndefinedException;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>, 26/02/20.
 */
public class NotificationInteraction {
    private static Map<String, Map<String, NotificationInfo>> loadedNotifications;


    public static void loadNotifications(Map<String, Object> tablesToLoad){
        Preconditions.checkArgument( tablesToLoad != null , "trying to load a null Map<String, ?> notification");

        Map<String, Map<String, NotificationInfo>> result = new HashMap<>();

        tablesToLoad.forEach((key, val) -> {
            Map<String, NotificationInfo> info = new HashMap<>();
            if(val instanceof Map){
                ((Map<?,?>)val).forEach((key1, val1) -> {
                    if(val1 instanceof Map) {
                        info.put(String.valueOf(key1), new NotificationInfo(
                            String.valueOf(((Map<?,?>)val1).get("title")),
                            String.valueOf(((Map<?,?>)val1).get("content"))
                        ));
                    }

                });
            }


            result.put(key, info);
        });

        loadedNotifications = result;
    }

    public static NotificationInfo getNotification(String notificationName, Locale i18nLocale){
        if(!loadedNotifications.containsKey(notificationName)){
            throw new NoNotificationFoundException(notificationName);
        }

        Map<String, NotificationInfo> loadedNotification = loadedNotifications.get(notificationName);

        NotificationInfo node = loadedNotification.get(i18nLocale.toLanguageTag());
        if(node == null){
            node = loadedNotification.get(i18nLocale.getLanguage());
            if(node == null){
                throw new NoNotificationLanguageFoundException(notificationName, i18nLocale);
            }
        }
        return node;

    }



    public static class NotificationInfo {
        private final String title;
        private final String content;

        public NotificationInfo(String title, String content) {
            this.title = title;
            this.content = content;
        }

        public String getTitle() {
            return title;
        }

        public String getContent() {
            return content;
        }
    }

     /* ***********************************
                  Exceptions
    ************************************* */


    public static class NoNotificationFoundException extends UndefinedException {
        NoNotificationFoundException(String notificationName){
            super("Could not get notification '%s', valid notifications: %s",
                notificationName, loadedNotifications.keySet());
        }

    }

    public static class NoNotificationLanguageFoundException extends UndefinedException {
        NoNotificationLanguageFoundException(String notificationName, Locale i18nLang){
            super("Could not get notification '%s' using language '%s' or '%s', valid languages: %s",
                notificationName, i18nLang.getLanguage() ,i18nLang.toLanguageTag(), loadedNotifications.get(notificationName).keySet());
        }

    }


}
