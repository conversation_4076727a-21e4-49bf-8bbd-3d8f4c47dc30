package pt.ptinovacao.nossis.tsc.it.web.utils.models.actors.table.interaction;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.table_configs.TableColumnDefinition;
import pt.ptinovacao.nossis.tsc.it.web.utils.uuid.UUID_Manager;

/**
 * <AUTHOR> <<EMAIL>>, 29-02-2016.
 */
public class TextUUIDAlphanumericColumnInteraction extends TextColumnInteraction {


    @Override
    protected boolean verifyCellMatch(WebElement cellElement, String cellTextToMatch, TableColumnDefinition columnDefinition) {
        return super.verifyCellMatch(cellElement, UUID_Manager.getUniqueNameOrName(cellTextToMatch, UUID_Manager.UUIDConversion.ALPHANUMERIC), columnDefinition);
    }
}
