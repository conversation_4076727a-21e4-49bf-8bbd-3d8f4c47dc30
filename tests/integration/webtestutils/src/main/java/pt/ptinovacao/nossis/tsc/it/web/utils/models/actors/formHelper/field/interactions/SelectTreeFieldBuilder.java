package pt.ptinovacao.nossis.tsc.it.web.utils.models.actors.formHelper.field.interactions;

public class SelectTreeFieldBuilder extends BasicFieldBuilder<SelectTreeField, SelectTreeFieldBuilder> {

    private SelectTreeFieldBuilder(String fieldName, String elementName) {
        super(fieldName, elementName);
    }

    public static SelectTreeFieldBuilder builder(String fieldName, String elementName) {
        return new SelectTreeFieldBuilder(fieldName, elementName);
    }

    @Override
    public SelectTreeField build() {
        return new SelectTreeField(getFieldName(), getElementName(), getUuidInsert(), getUuidContext()) ;
    }

}
