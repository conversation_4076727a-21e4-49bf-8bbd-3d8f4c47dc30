package pt.ptinovacao.nossis.tsc.it.web.utils.driver.operations;

import org.slf4j.Logger;
import pt.ptinovacao.nossis.tsc.it.web.utils.driver.ItWebDriver;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> <<EMAIL>>, 30-07-2019.
 *
 * Interface used to make navigation maintaining browser frame context
 *
 * Because going back and forward resets the browser context.
 *
 */
public interface FrameContextNavigation extends DriverOperation {

    Logger LOGGER = LoggerFactory.getLogger(FrameContextNavigation.class);


    static String driverContext(final ItWebDriver driver) {
        final String context = String.valueOf(driver.execJS("return self.name"));
        LOGGER.info("current driver context = {}", context);
        return context;
    }

    void navigateBack();

    void navigateForward();

}
