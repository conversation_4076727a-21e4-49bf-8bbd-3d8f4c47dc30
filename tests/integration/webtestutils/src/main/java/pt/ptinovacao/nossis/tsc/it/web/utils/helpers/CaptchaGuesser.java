package pt.ptinovacao.nossis.tsc.it.web.utils.helpers;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

/**
 * Captcha guesser
 *
 * CaptchaG<PERSON><PERSON> calls an external application to guess the captcha
 * in the login in case it exists.
 *
 * in case this function is to be used it requires the following installed apps in the machine:
 *
 *  - python
 *  - opencv
 *  - the equivalent opencv bindings for python (e.g. opencv-python)
 *
 * Created by omar on 24-09-2015.
 */
public class CaptchaGuesser {

    private static final String EXEC_NAME = "python";
    private static final String SOURCE_FILENAME = "captcha_guesser.py";
    private static final String SOURCE_RELATIVE_DIR = "bin";
    private static final String SOURCE_FILE = SOURCE_RELATIVE_DIR + "/" + SOURCE_FILENAME ;

    private static final CaptchaGuesser builder = new CaptchaGuesser();

    public Process buildCaptchaGuesserProcess(){
        try{
            ProcessBuilder pb = new ProcessBuilder(EXEC_NAME, SOURCE_FILE);
            return pb.start();
        } catch (IOException ex){
            throw errorLoadingCaptchaGuesser(ex);
        }
    }

    public static String guessCaptchaText(BufferedImage eleScreenshot){
        Process process = CaptchaGuesser.builder.buildCaptchaGuesserProcess();
        try {
            OutputStream stdin = process.getOutputStream();
            InputStream stdout = process.getInputStream();
            BufferedReader reader = new BufferedReader (new InputStreamReader(stdout));

            ImageIO.write(eleScreenshot, "png", stdin);
            stdin.close();
            String captchaText = reader.readLine ();
            reader.close();
            return captchaText;

        } catch (IOException e) {
            process.destroy();
            throw errorSendingCaptchaToProcess(e);
        }
    }





    private static RuntimeException errorSendingCaptchaToProcess(IOException ex){
        return new RuntimeException("error sending captcha image to captcha guesser",ex);
    }

    private static RuntimeException errorLoadingCaptchaGuesser(IOException ex){
        return new RuntimeException("error loading captcha guesser",ex);
    }

}
