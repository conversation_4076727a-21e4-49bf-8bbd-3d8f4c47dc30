#
#
#                            TAGS MAPPING
#
#    This file configures what folders should be loaded for each scenario,
#  in short, it isolates each test scenaio to load the necessary content.
#  It allows to define object with the same type and ID (e.g. element and form names)
#  in different files without having any conflict. A secondary advantage of isolating
#  the scenario is to fail the test when trying to use resources of other files.
#
#
# USAGE ON THIS FILE:
#
#     < test kind id >
#       contains any of tags: [Array[string]]<list of tags that define the kind of test >
#       load config folders: [Array[string]]< list of folders to load on tests with the tags defined on the previous line >
#

catalog_test:
  contains any of tags: ["@CAT","@CATALOG"]
  load config folders:
    - base
    - catalog
