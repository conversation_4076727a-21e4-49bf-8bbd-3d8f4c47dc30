table versions list: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table catalog results
  columns:
    version:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT_WITH_UUID_ALPHANUMERIC
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    state:
      css_class: state
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            element: table versions list view button
          edit:
            element: table versions list edit button
          more options:
            element: table versions list more options button
          state transition:
            subActionOf: "more options"
            element: table versions list state transition button
          transition to development:
            subActionOf: "more options"
            element: table versions list transition to development button
          transition to testing:
            subActionOf: "more options"
            element: table versions list transition to testing button
          transition to abandoned:
            subActionOf: "more options"
            element: table versions list transition to abandoned button
          transition to production:
            subActionOf: "more options"
            element: table versions list transition to production button
          transition to blocked:
            subActionOf: "more options"
            element: table versions list transition to blocked button
          transition to deprecated:
            subActionOf: "more options"
            element: table versions list transition to deprecated button
          delete:
            subActionOf: "more options"
            element: table versions list delete button
          clone:
            subActionOf: "more options"
            element: table versions list clone button


table cfs list: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table catalog results
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT_WITH_UUID_ALPHANUMERIC
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    action:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            element: table cfs list view button
          edit:
            element: table cfs list edit button
          more options:
            element: table cfs list more options button
          clone:
            subActionOf: "more options"
            element: table cfs list clone button
          clone_without:
            subActionOf: "more options"
            element: table cfs list clone without button
          delete:
            subActionOf: "more options"
            element: table cfs list delete button

table rfs list: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table catalog results
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT_WITH_UUID_ALPHANUMERIC
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    action:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            element: table rfs list view button
          edit:
            element: table rfs list edit button
          more options:
            element: table rfs list more options button
          clone:
            subActionOf: "more options"
            element: table rfs list clone button
          clone_without:
            subActionOf: "more options"
            element: table rfs list clone without button
          delete:
            subActionOf: "more options"
            element: table rfs list delete button

table resource list: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table catalog results
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT_WITH_UUID_ALPHANUMERIC
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    action:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            element: table resource list view button
          edit:
            element: table resource list edit button
          more options:
            element: table resource list more options button
          clone:
            element: table resource list clone button
            subActionOf: "more options"
          clone_without:
            element: table resource list clone without button
            subActionOf: "more options"
          delete:
            element: table resource list delete button
            subActionOf: "more options"

table attributes list: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table catalog results
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT_WITH_UUID_ALPHANUMERIC
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    type:
      css_class: type
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            element: table attributes list view button
          edit:
            element: table attributes list edit button
          more options:
            element: table attributes list more options button
          clone:
            element: table attributes list clone button
            subActionOf: "more options"
          delete:
            element: table attributes list delete button
            subActionOf: "more options"

table attribute enum: !table
  elementKey: table attribute enum inputs
  columns:
    value:
      css_class: value
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          edit:
            css: .glyphicon.glyphicon-pencil
          remove:
            css: .glyphicon.glyphicon-remove
          ok:
            css: .glyphicon.glyphicon-ok

table entity attributes: !table
  elementKey: entity view field table attributes
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    attribute type:
      css_class: type
      label: {i18n: {key: ""}}
      type: TEXT
    restriction:
      css_class: restrictions
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: .fuxicons.fuxicons-eye

table modal entity attributes: !table
  elementKey: modal entity attributes table
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    attribute type:
      css_class: type
      label: {i18n: {key: ""}}
      type: TEXT
    restriction:
      css_class: restrictions
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: .fuxicons.fuxicons-eye


table version characteristics entities: !table
  elementKey: version view characteristics entities table
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: .fuxicons.fuxicons-eye

table version characteristics attributes: !table
  elementKey: version view characteristics attributes table
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    tags:
      css_class: tags
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.nossis.tsc.it.models.data.actors.table.TagsColumn"
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: .fuxicons.fuxicons-eye

table enumerator attribute values: !table
  elementKey: table enum
  columns:
    value:
      css_class: value-col
      label: {i18n: {key: ""}}
      type: TEXT

table compound attributes: !table
  elementKey: attributes editable table
  columns:
    name:
      css_class: name-col
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description-col
      label: {i18n: {key: ""}}
      type: TEXT
    type:
      css_class: type-col
      label: {i18n: {key: ""}}
      type: TEXT
    action:
      css_class: action-col
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: .fuxicons.fuxicons-eye
          edit:
            css: "a i.glyphicon-pencil"
          delete:
            css: "a i.glyphicon-remove"

table component attributes: !table
  elementKey: component attributes view table
  columns:
    name:
      css_class: name
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    attribute type:
      css_class: type
      label: {i18n: {key: ""}}
      type: TEXT
    restriction:
      css_class: restrictions
      label: {i18n: {key: ""}}
      type: TEXT