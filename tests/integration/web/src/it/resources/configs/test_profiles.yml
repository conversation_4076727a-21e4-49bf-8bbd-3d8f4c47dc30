##############################################
#      TEST PROFILES CONFIGURATION FILE      #
##############################################
# Test profiles
#
# USAGE:
#   mvn <goals> -Pintegration-tests -Dit.web.profile=<test profile> -Dit.web.profiles.config.file=<profiles conf> -Dit.web.auth.profile=IAM
#
#     <test profile> => Test profile to use during selenium tests execution
#     <profiles conf> => Configuration file to read for test execution
#
# FILE TEMPLATE:
#   default_profile: <default test profile>
#
#   <test profile>:
#     login:
#       <login id>:
#         username: <username>
#         password: <password>
#       ...
#     defaultLogin: <login key to be used by default>
#     startingURL: <URL where the tests starts to execute>
#     browserProfile: <browser profile to be used instead of the default (defined on browser_profiles.yml)>
#     record: <flag to indicate screen recording of the tests (NOTE: only programmed to record X11 virtual sessions) >
#     recordPath: < output of recorded tests >
#     authProfile: <*authentication profile>
#     logs:
#       <logName>:
#         on scenario: <*log condition>
#         filter: [Array[String]]<scenaio tag filter condition to save the log, optional>
#         type: <ssh or selenium>
#         timezone: <ssh server timezone to get the correct timestamp interval since the server can have a different timezone than the test machine>
#         host: <hostname or url, works only on ssh type>
#         path: <path, works only on ssh type>
#         date format: <date format, works only on ssh type>
#        ...
#
#
#   <*log condition>:
#     ╔═════════════╦════════════════════════════╗
#     ║     Key     ║        Description         ║
#     ╠═════════════╬════════════════════════════╣
#     ║ success     ║ Log when test passed       ║
#     ║ failure     ║ Log when test failed       ║
#     ║ any         ║ Log when test ran, default ║
#     ╚═════════════╩════════════════════════════╝
#   Note: key is optional
#
#   <*authentication profile>:
#     ╔═════════════╦═════════════════════════════════════════╗
#     ║     Key     ║                Description              ║
#     ╠═════════════╬═════════════════════════════════════════╣
#     ║ SCA         ║ Login using SCA authentication system   ║
#     ║ IAM         ║ Login using IAM authentication system   ║
#     ║ portal      ║ Login using portal login page, default  ║
#     ╚═════════════╩═════════════════════════════════════════╝
#   Note: key is optional
#
#

############# VARIABLES #############
vars:
  localurl: &localStartingURL
    startingURL: http://[YOUR LOCAL IP GOES HERE]/portal/tsc/login #DO NOT COMMIT!


# Default test profile used when -Dit.web.profile cmd argument is absent
default_profile: localhost

############## PROFILES ##############

# default profile (Local Firefox to development Play)
localhost:
  <<: [*localStartingURL]
  browserProfile: default_profile


# Local Docker browsers to development Play
local_docker_google_chrome:
  <<: [*localStartingURL]
  browserProfile: LOCAL_DOCKER_CHROME

local_docker_firefox:
  <<: [*localStartingURL]
  browserProfile: LOCAL_DOCKER_FF
