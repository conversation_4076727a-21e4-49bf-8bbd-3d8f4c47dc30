@REQ_NOSSISTSC-22
Feature: [VERSIONS][SEARCH][VIEW] Visualizar detalhes de uma versão
	#O utilizador deverá poder visualizar o detalhe de uma versão de um catalogo de entidades. 
	# Quando entra na página de contexto terá disponíveis todas as ações existentes em contexto de pesquisa, aplicando-se as mesmas restrições de segurança.
	#{panel}
	#!screenshot-1.png!
	#{panel}
	#{panel}
	#A pesquisa deve ser feita apenas pelo campo name.
	# Deve existir paginação tal como para as páginas de listagem das entidades e atributos.
	#  (!) Não serão implementadas contagens (Number of Operations, Number of Entities)
	#
	#(i) Nas listagem de atributos deverão ser adicionadas a coluna com listagem de tags.
	#{panel}
	#{panel}
	#!screenshot-3.png!
	#{panel}
	#{panel}
	#!EntitiesInVersion.png!
	#{panel}

	#Testar operações de search em view de características de versões
	@TEST_NOSSISTSC-811 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @CATALOG
	Scenario: [VERSIONS] Testar operações de search em view de características de versões
		Given the data related to "VERSION" is loaded
		And I am logged in as "administrator"
		When I go to page "catalog version search"
		And I submit form "version side search" with the following values:
		  | field | value   |
		  | name  | 1.0.0-SELFNET |
		Then the table columns of "table versions list" contains:
		  | column  | value   |
		  | version | 1.0.0-SELFNET |
		When I click first row action "view" of column "action"
		Then the first rows of "table version characteristics entities" matches table:
		  | name           |
		  | CFS.IM         |
		  | LR.FLOWT       |
		  | LR.VNF.SNORT   |
		  | RESOURCE.CHILD |
		  | RF.NS.SNORT    |
		  | RFS.FLOWT      |
		  | RFS.NS.SNORT   |
		And "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "11"
		And "version view characteristics attributes search" is hidden
		# ----------------
		# --- Entities ---
		# ----------------
		# CFS*
		When I submit form "version view entities search" with the following values:
		  | field | value |
		  | name  | CFS*  |
		Then "version view characteristics entities count" content is "1"
		And "version view characteristics attributes count" content is "11"
		And the table columns of "table version characteristics entities" contains:
		  | column | value  |
		  | name   | CFS.IM |
		# *FLOWT
		When I submit form "version view entities search" with the following values:
		  | field | value  |
		  | name  | *FLOWT |
		Then "version view characteristics entities count" content is "2"
		And "version view characteristics attributes count" content is "11"
		Then the first rows of "table version characteristics entities" matches table:
		  | name      |
		  | LR.FLOWT  |
		  | RFS.FLOWT |
		# *VNF*
		When I submit form "version view entities search" with the following values:
		  | field | value |
		  | name  | *VNF* |
		Then "version view characteristics entities count" content is "1"
		And "version view characteristics attributes count" content is "11"
		And the table columns of "table version characteristics entities" contains:
		  | column | value        |
		  | name   | LR.VNF.SNORT |
		# *
		When I submit form "version view entities search" with the following values:
		  | field | value |
		  | name  | *     |
		Then "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "11"
		# ------------------
		# --- Attributes ---
		# ------------------
		When I click "version view characteristics attributes count"
		Then "version view characteristics entities search" is hidden
		Then the first rows of "table version characteristics attributes" matches table:
		  | name          |
		  | alert         |
		  | appType       |
		  | creation date |
		# i*
		When I submit form "version view attributes search" with the following values:
		  | field | value |
		  | name  | i*    |
		Then "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "3"
		Then the first rows of "table version characteristics attributes" matches table:
			| name         |
		    | id           |
		    | instanceName |
		    | ipAddress    |
		# *e
		When I submit form "version view attributes search" with the following values:
		  | field | value |
		  | name  | *e    |
		Then "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "4"
		Then the first rows of "table version characteristics attributes" matches table:
		  | name          |
		  | appType       |
		  | creation date |
		  | instanceName  |
		  | size          |
		# *ion*
		When I submit form "version view attributes search" with the following values:
		  | field | value |
		  | name  | *ion* |
		Then "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "2"
		Then the first rows of "table version characteristics attributes" matches table:
			| name         |
			|creation date |
		  	| location     |
		# *
		When I submit form "version view attributes search" with the following values:
		  | field | value |
		  | name  | * |
		Then "version view characteristics entities count" content is "7"
		And "version view characteristics attributes count" content is "11"