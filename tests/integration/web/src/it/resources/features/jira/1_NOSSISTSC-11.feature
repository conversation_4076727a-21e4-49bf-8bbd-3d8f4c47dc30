@REQ_NOSSISTSC-11
Feature: [VERSIONS][SEARCH] Pesquisa de versões de catalogo de entidades
	#h5. O utilizador deverá poder efetuar pesquisa por versões de catalogo, sendo a pesquisa contextualizada pelos seguintes paramentos:
	#* Versão - Identificador único da versão;
	#* Estado - Estado da versão Enumerado 
	#
	#h5. O resultado deverá ser apresentado no formato tabular com os seguintes campos:
	#||Campo||Ordenação||Descrição||
	#| Versão | Sim | Identificador único da versão |
	#| Descrição | Sim | Texto descritivo da versão |
	#| Estado | Sim | Estado administrativo da versão | 
	#
	#h5. Ações disponíveis:
	#* Criar 
	#* Tabela
	#** Editar 
	#** Consultar (Hiperligação da versão)
	#** Clonar
	#** Remover
	#** Mudar <PERSON>stado
	#** Importar ( (flag) não é âmbito deste issue )
	#** Exportar ( (flag) não é âmbito deste issue )
	#
	#{panel}
	#(i) Neste requisito estão excluídas as seguintes funcionalidades da tabela:
	#
	#*     Agrupar por
	#*     Favorito
	#*     Exportação
	#
	#Deverá ser suportada paginação seguindo as regras fuxi.
	#{panel}
	#
	#h5. Ciclo de vida de uma versão
	#!http://wiki.ptin.corppt.com/download/attachments/770441658/Entity%20State.png?api=v2!

	
	@TEST_NOSSISTSC-91 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @CATALOG
	Scenario: [CATALOG-WEB] Pesquisa de versões
		Given the data related to "VERSION" is loaded
		And I am logged in as "administrator"
		When I go to page "catalog version search"
		And I submit form "version side search" with the following values:
		  | field         | value                           |
		  | name          | 1.0.0-SELF*                     |
		  | state         | Desenvolvimento, Abandonada     |
		Then the table columns of "table versions list" contains:
		  | column        | value                           |
		  | version       | 1.0.0-SELF.*                   |
		  | state         | (Desenvolvimento\|Abandonada)   |