@REQ_NOSSISTSC-28
Feature: [CFS][CREATE][EDIT] Criar/Editar Serviço de Cliente - Diagrama de Composição - Grafo
	#Um utilizador deverá possuir uma página de edição de diagramas de composição. 
	# Em modo de edição o utilizador deverá poder pesquisar numa página lateral sobre todos os cfs, rfs, resource, podendo associar elementos ao diagrama de associação ou agregação.
	#
	#!screenshot-1.png|width=1024,height=600!
	#
	#!screenshot-2.png|width=1024,height=600!
	#
	#!screenshot-3.png|width=1024,height=600!

	
	@TEST_NOSSISTSC-1072 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: [CATALOG-WEB] Edit an existing CFS, add a child CFS relation
		Given the data related to "edit a CFS relations" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I view edit page of cfs "TEST CFS EDIT"
		And I click "tab composition diagram cfs"
		Then The following graph is rendered:
		  | name          |  type       | connections        |
		  | TEST CFS EDIT |  CFS        |                    |
		And I drag node "Serviço de cliente" from sidebar to node "TEST CFS EDIT"
		Then The following graph is rendered:
		  | name          |  type       | connections        |
		  | TEST CFS EDIT |  CFS        | Serviço de cliente |
		And I set the "CFS" form with the following values:
		  | field       | value                   |
		  | name        | TEST CFS EDIT (EDITED)  |
		  | description | This is a edited CFS    |
		And I submit the form
		Then I should be on "catalog cfs search" page
		When I view cfs "TEST CFS EDIT (EDITED)"
		And I click "tab composition diagram cfs"
		Then The following graph is rendered:
		  | name                   |  type       | connections        |
		  | TEST CFS EDIT (EDITED) |  CFS        | Serviço de cliente |	

	
	@TEST_NOSSISTSC-1079 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [CATALOG-WEB] Create a CFS with aggregated child entities
		Given the data related to "creation of CFS with relations" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "diagram test version"
		When I  navigate to "CFS" create page
		And I set the "CFS" form with the following values:
		  | field       | value           |
		  | name        | TEST CFS CREATE |
		  | description | This is a test  |
		  | tags        | testTag, TagA   |
		And I click "tab composition diagram cfs"
		Then The following graph is rendered:
		  | name            |  type       | connections        |
		  | TEST CFS CREATE |  CFS        |                    |
		When I drag node "Serviço de cliente" from sidebar to node "TEST CFS CREATE"
		Then The following graph is rendered:
		  | name               |  type       | connections        |
		  | TEST CFS CREATE    |  CFS        | Serviço de cliente |
		  | Serviço de cliente |  CFS        |                    |
		When I drag node "TEST CFS WITH CHILD" from sidebar to node "TEST CFS CREATE"
		Then The following graph is rendered:
		  | name                  |  type       | connections                                                 |
		  | TEST CFS CREATE       |  CFS        | Serviço de cliente, TEST CFS WITH CHILD                     |
		  | TEST CFS WITH CHILD   |  CFS        | TEST CFS EDIT, Serviço de cliente, Serviço de rede, Recurso |
		  | Serviço de cliente    |  CFS        |                                                             |
		  | Serviço de rede       |  RFS        | Recurso                                                     |
		  | Recurso               |  Resource   |                                                             |
		When I submit the form
		Then I should be on "catalog cfs search" page
		When I view cfs "TEST CFS CREATE"
		And I click "tab composition diagram cfs"
		Then The following graph is rendered:
		  | name                  |  type       | connections                                                 |
		  | TEST CFS CREATE       |  CFS        | Serviço de cliente, TEST CFS WITH CHILD                     |
		  | TEST CFS WITH CHILD   |  CFS        | TEST CFS EDIT, Serviço de cliente, Serviço de rede, Recurso |
		  | Serviço de cliente    |  CFS        |                                                             |
		  | Serviço de rede       |  RFS        | Recurso                                                     |
		  | Recurso               |  Resource   |                                                             |
		When I go back
		And I view cfs "TEST CFS WITH CHILD"
		And I click "tab composition diagram cfs"
		And I toggle parents
		Then The following graph is rendered:
		  | name                  |  type       | connections                                                 |
		  | TEST CFS WITH CHILD   |  CFS        | TEST CFS EDIT, Serviço de cliente, Serviço de rede, Recurso |
		  | TEST CFS CREATE       |  CFS        | TEST CFS WITH CHILD                                         |
		  | Serviço de cliente    |  CFS        |                                                             |
		  | Serviço de rede       |  RFS        | Recurso                                                     |
		  | Recurso               |  Resource   |                                                             |	

	
	@TEST_NOSSISTSC-1088 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [CATALOG-WEB] Check CFS diagram sidebar entities and maximum depth policy
		Given the data related to "entity relations diagram" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "diagram test version"
		When I view edit page of cfs "Serviço de cliente"
		And I click "tab composition diagram cfs"
		Then The entities "Serviço de cliente 2, Serviço de cliente 3" exists in the sidebar
		When I click "sidebar navigation cfs"
		And I view edit page of cfs "Serviço de cliente 2"
		And I click "tab composition diagram cfs"
		And I drag node "Serviço de cliente" from sidebar to node "Serviço de cliente 2"
		And I toggle parents
		Then The following graph is rendered:
		  | name                  |  type     | connections           |
		  | Serviço de cliente 2  |  CFS      | Serviço de cliente    |
		  | Serviço de cliente 3  |  CFS      | Serviço de cliente 2  |
		  | Serviço de cliente    |  CFS      |                       |
		When I submit the form
		And I view edit page of cfs "Serviço de cliente"
		And I click "tab composition diagram cfs"
		And I toggle parents
		Then The following graph is rendered:
		  | name                  |  type     | connections         |
		  | Serviço de cliente    |  CFS      |                     |
		  | Serviço de cliente 2  |  CFS      | Serviço de cliente  |
		  | TEST CFS WITH CHILD   |  CFS      | Serviço de cliente  |
		Then The entities "Serviço de cliente 2, Serviço de cliente 3" does not exist in the sidebar
		When I drag node "CFS filho" from sidebar to node "Serviço de cliente"
		When I submit the form
		Then an "relation maximum depth policy violation" error notification should appear