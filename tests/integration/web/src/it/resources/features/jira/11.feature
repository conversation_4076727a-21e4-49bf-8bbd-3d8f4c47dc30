Feature: Default

	
	@TEST_NOSSISTSC-715 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [CATALOG-WEB] RFS view characteristics tab and check the attributes
		Given I am logged in
		Then I go to page "catalog rfs search"
		When I contextualize search to version "SELFNET"
		And I submit form "rfs side search" with the following values:
		| field        | value           |
		| name         | RFS.FLOWT       |
		Then the table columns of "table rfs list" contains:
		| column        | value                    |
		| name          | RFS.FLOWT                |
		| description   | Resource Facing Service.* |
		| tags          | selfnet                  |
		And I click first row action "view" of column "action"
		And I click "tab characteristics rfs"
		Then I am on "catalog rfs view" page
		And I'm on the view page of "RFS" with name "RFS.FLOWT" with description "Resource Facing Service SDN-APP FlowT" and tags "selfnet"
		And the entity attribute table has the following values:
		|   name                     | description                    | attribute type        | restriction  |
		|  appManagerAppInstanceUrl  | APP Manager APP instance URL   | uri.*string.*         | 	--       |
		|  cloneIpAddress            | 	Clone IP Address              | ipAddress.*string.*   | .*regular.*  |
		|  dstIpAddress              | SELFNET destination IP Address | ipAddress.*string.*   | .*regular.*  |	

	
	@TEST_NOSSISTSC-738 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [CATALOG-WEB] Check RFS diagram is correctly rendered
		Given I am logged in
		When I go to page "catalog rfs search"
		And I contextualize search to version "SELFNET"
		Then I submit form "rfs side search" with the following values:
			| field        | value           |
			| name         | RFS.FLOWT       |
		And the table columns of "table rfs list" contains:
			| column        | value          |
			| name          | RFS.FLOWT      |
		When I click first row action "view" of column "action"
		And I click "tab composition diagram rfs"
		Then The following graph is rendered:
			| name          |  type       | connections        |
			| RFS.FLOWT     |  RFS        | LR.FLOWT           |
			| LR.FLOWT      |  Resource   |                    |
		When I toggle parents
		Then The following graph is rendered:
			| name          |  type       | connections        |
			| RFS.FLOWT     |  RFS        | LR.FLOWT           |
			| LR.FLOWT      |  Resource   |                    |
			| CFS.IM        |  CFS        | RFS.FLOWT          |
		When I change to association graph
		Then The following graph is rendered:
			| name          |  type       | connections        |
			| RFS.FLOWT     |  RFS        |                    |	

	
	@TEST_NOSSISTSC-1154 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: Actions on Versions have to be conditioned based on its state (Search)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog version search" page
		And I submit form "version side search" with the following values:
		  | field | value          |
		  | name  | 1.0.0-SELFNET  |
		Then the table columns of "table versions list" contains:
		  | column  | value                                                             |
		  | version | 1.0.0-SELFNET.*                                                   |
		  | state   | Desenvolvimento                                                   |
		  | action  | edit (enabled), view (enabled), delete (enabled), clone (enabled) |
		Then I change the version state to "testing"
		Then the table columns of "table versions list" contains:
		  | column  | value                                                               |
		  | version | 1.0.0-SELFNET.*                                                     |
		  | state   | Testes                                                              |
		  | action  | edit (disabled), view (enabled), delete (disabled), clone (enabled) |
		Then I change the version state to "production"
		Then the table columns of "table versions list" contains:
		  | column  | value                                                               |
		  | version | 1.0.0-SELFNET.*                                                     |
		  | state   | Produção                                                            |
		  | action  | edit (disabled), view (enabled), delete (disabled), clone (enabled) |
		Then I change the version state to "blocked"
		Then the table columns of "table versions list" contains:
		  | column  | value                                                               |
		  | version | 1.0.0-SELFNET.*                                                     |
		  | state   | Bloqueada                                                           |
		  | action  | edit (disabled), view (enabled), delete (disabled), clone (enabled) |
		Then I change the version state to "production"
		Then I change the version state to "deprecated"
		Then the table columns of "table versions list" contains:
		  | column  | value                                                               |
		  | version | 1.0.0-SELFNET.*                                                     |
		  | state   | Obsoleta                                                            |
		  | action  | edit (disabled), view (enabled), delete (disabled), clone (enabled) |
		Then I change the version state to "abandoned"
		Then the table columns of "table versions list" contains:
		  | column  | value                                                              |
		  | version | 1.0.0-SELFNET.*                                                    |
		  | state   | Abandonada                                                         |
		  | action  | edit (disabled), view (enabled), delete (enabled), clone (enabled) |	

	
	@TEST_NOSSISTSC-1195 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @CATALOG
	Scenario: Canceling an untouched entity create form shouldn't activate "data loss" warning
		Given the data related to "create a CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I  navigate to "CFS" create page
		And I click "create cancel button"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1196 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: Canceling an untouched entity create (diagram tab) form shouldn't activate "data loss" warning
		Given the data related to "create a CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I  navigate to "CFS" create page
		And I click "tab composition diagram cfs"
		And I click "create cancel button"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1197 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: Canceling an untouched entity EDIT form shouldn't activate "data loss" warning
		Given the data related to "CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I view edit page of cfs "TEST CFS EDIT"
		And I click "create cancel button"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1198 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: Canceling an untouched entity EDIT (diagram tab) form shouldn't activate "data loss" warning
		Given the data related to "CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I view edit page of cfs "TEST CFS EDIT"
		And I click "tab composition diagram cfs"
		And I click "create cancel button"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1199 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: navigating back from an untouched atribute create form shouldn't activate "data loss" warning
		Given the data related to "ATTRIBUTE" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "AttrTypesScenario"
		When I navigate to create catalog attribute type page
		And I click "sidebar navigation attributes"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1200 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: Canceling from an untouched attribute create form shouldn't activate "data loss" warning
		Given the data related to "ATTRIBUTE" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "AttrTypesScenario"
		When I navigate to create catalog attribute type page
		And I click "create cancel button"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1210 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: Navigating back from an untouched entity create form shouldn't activate "data loss" warning
		Given the data related to "create a CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I  navigate to "CFS" create page
		And I go back
		And I click "sidebar navigation attributes"
		Then a modal shouldn't appear	

	
	@TEST_NOSSISTSC-1212 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: Cancel creation of an non-prestine CFS entity form should trigger a "data loss" 
		Given the data related to "create a CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		When I  navigate to "CFS" create page
		And I set the "CFS" form with the following values:
		  | field       | value           |
		  | name        | TEST CFS CREATE |
		  | description | This is a test  |
		And I click "create cancel button"
		Then a "change page possible data loss" confirmation modal appears	

	
	@TEST_NOSSISTSC-1213 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: Cancel EDIT COMPOUNDTYPE with attributes and a "data loss" warning should appear
		Given the data related to "edit a component" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "CompEditTest"
		And I submit form "attribute side search" with the following values:
		  | field        | value         |
		  | name         | UserInfo      |
		And the table columns of "table attributes list" contains:
		  | column        | value        |
		  | name          | UserInfo     |
		When I click 1st row action "edit"
		And the editable attribute table has the following values:
		  |   name            | description              | type                       |
		  | cellphone number  |                          | phone number \(numérico\)  |
		  | name              | nome do utilizador       | name \(string\)            |
		  | phone number      | numero de telefone       | phone number \(numérico\)  |
		  | surname           | sobrenome do utilizador  | surname \(string\)         |
		And I edit the row with name "name" with the following values:
		  | name        | description                   | type           |
		  | first name  | primeiro nome do utilizador   | name (string)  |
		And the editable attribute table has the following values:
		  |   name            | description                   | type                      |
		  | cellphone number  |                               | phone number \(numérico\) |
		  | first name        | primeiro nome do utilizador   | name \(string\)           |
		  | phone number      | numero de telefone            | phone number \(numérico\) |
		  | surname           | sobrenome do utilizador       | surname \(string\)        |
		Then I click "create cancel button"
		And a "change page possible data loss" confirmation modal appears	

	#Validar ao filtro de pesquisas na sidebar do diagram.
	@TEST_NOSSISTSC-1429 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-39 @CATALOG
	Scenario: [CATALOG-WEB] Check diagram sidebar filter
		Given the data related to "entity relations diagram" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "diagram test version"
		When I view edit page of cfs "CFS filho"
		Then I click "tab composition diagram cfs"
		And I filter entities by type "CFS" and by name "cliente" in sidebar
		Then The entities "Serviço de cliente, Serviço de cliente 2, Serviço de cliente 3" exists in the sidebar
		When I drag node "Serviço de cliente" from sidebar to node "CFS filho"
		Then The entities "Serviço de cliente 2, Serviço de cliente 3" exists in the sidebar
		And The following graph is rendered:
			| name                  |  type     | connections           |
			| CFS filho             |  CFS      | Serviço de cliente    |
			| Serviço de cliente    |  CFS      |                       |
		When I filter entities by type "RFS" and by name "rede" in sidebar
		Then I drag node "Serviço de rede" from sidebar to node "CFS filho"
		And The following graph is rendered:
			| name                  |  type     | connections           |
			| CFS filho             |  CFS      | Serviço de cliente    |
			| CFS filho             |  CFS      | Serviço de rede       |
			| Serviço de cliente    |  CFS      |                       |
			| Serviço de rede       |  RFS      | Recurso               |
		When I filter entities by type "all" in sidebar
		Then The entities "Serviço de cliente, Serviço de rede" does not exist in the sidebar	

	
	@TEST_NOSSISTSC-2358 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [WEB][CFS] The table page length, sidebar collapse and columns visibility should be saved on cfs search page even after the search is triggered
		Given the data related to "CFS search context" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs pagination test"
		When I collapse search sidebar
		And I set table "table cfs list" to show 30 results per page
		And I go to 3rd table page
		And I click 1st row action "view"
		Then I should be on "catalog cfs view" page
		When I go back
		Then I should be on "catalog cfs search" page
		And the search sidebar is collapsed
		And I should be on 3rd table page
		And the table is configured to show 30 results per page	

	
	@TEST_NOSSISTSC-2359 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [WEB][RFS] The table page length, sidebar collapse and columns visibility should be saved on rfs search page even after the search is triggered
		Given the data related to "RFS search context" is loaded
		And I am "administrator" on "catalog rfs search" page
		And I am contextualized to the version "rfs pagination test"
		When I collapse search sidebar
		And I set table "table rfs list" to show 60 results per page
		And I go to 2nd table page
		And I click 1st row action "view"
		Then I should be on "catalog rfs view" page
		When I go back
		Then I should be on "catalog rfs search" page
		And the search sidebar is collapsed
		And I should be on 2nd table page
		And the table is configured to show 60 results per page	

	
	@TEST_NOSSISTSC-2360 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [WEB][RESOURCE] The table page length, sidebar collapse and columns visibility should be saved on resource search page even after the search is triggered
		Given the data related to "resource search context" is loaded
		And I am "administrator" on "catalog resource search" page
		And I am contextualized to the version "resource pagination test"
		When I collapse search sidebar
		And I set table "table resource list" to show 15 results per page
		And I go to 5th table page
		And I click 1st row action "view"
		Then I should be on "catalog resource view" page
		When I go back
		Then I should be on "catalog resource search" page
		And the search sidebar is collapsed
		And I should be on 5th table page
		And the table is configured to show 15 results per page	

	
	@TEST_NOSSISTSC-2361 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [WEB][VERSION] The table page length, sidebar collapse and columns visibility should be saved on version search page even after the search is triggered
		Given the data related to "version search context" is loaded
		And I am "administrator" on "catalog version search" page
		When I collapse search sidebar
		And I set table "table versions list" to show 30 results per page
		And I go to 2nd table page
		And I click 1st row action "view"
		Then I should be on "catalog version view" page
		When I go back
		Then I should be on "catalog version search" page
		And the search sidebar is collapsed
		And I should be on 2nd table page
		And the table is configured to show 30 results per page	

	
	@TEST_NOSSISTSC-2362 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-2356 @TESTSET_NOSSISTSC-4284 @CATALOG
	Scenario: [WEB] The table page length, sidebar collapse and columns visibility should be reset when changing submodules
		Given the data related to "search context switch" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "search context switch"
		When I collapse search sidebar
		And I set table "table attributes list" to show 30 results per page
		And I go to 2nd table page
		And I navigate to "RESOURCE" search page
		And I collapse search sidebar
		And I set table "table resource list" to show 60 results per page
		And I navigate to "RFS" search page
		And I collapse search sidebar
		And I set table "table rfs list" to show 30 results per page
		And I go to 2nd table page
		And I navigate to "CFS" search page
		And I set table "table cfs list" to show 15 results per page
		And I go to 3rd table page
		And I navigate to "VERSION" search page
		And I set table "table versions list" to show 15 results per page
		And I collapse search sidebar
		And I go to 2nd table page
		And I navigate to "ATTRIBUTE" search page
		Then the search sidebar is not collapsed
		And I should be on 1st page of table "table attributes list"
		And the table is configured to show 15 results per page
		When I navigate to "RESOURCE" search page
		Then the search sidebar is not collapsed
		And table "table resource list" is configured to show 15 results per page
		And I should be on 1st table page
		When I navigate to "RFS" search page
		Then the search sidebar is not collapsed
		And table "table rfs list" is configured to show 15 results per page
		And I should be on 1st table page
		When I navigate to "CFS" search page
		Then the search sidebar is not collapsed
		And I should be on 1st page of table "table cfs list"
		And the table is configured to show 15 results per page
		When I navigate to "VERSION" search page
		Then the search sidebar is not collapsed
		And I should be on 1st page of table "table versions list"
		And the table is configured to show 15 results per page	

	
	@TEST_NOSSISTSC-2365 @TESTSET_NOSSISTSC-4852 @TESTSET_NOSSISTSC-4284 @TESTSET_NOSSISTSC-2356 @CATALOG
	Scenario: [WEB] The version context should remain the same when a version is cloned
		Given the data related to "SELFNET" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "SELFNET"
		When I clone catalog version "SELFNET" from search
		And I navigate to "CFS" search page
		Then I should be contextualized on version "SELFNET"
		When I navigate to "RFS" search page
		Then I should be contextualized on version "SELFNET"	

	
	@TEST_NOSSISTSC-4987 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [CATALOG-WEB] Reset form validation when leaving 'enumerator' form
		Given I am "administrator" on "catalog attributes search" page
		When I contextualize catalog attributes search to version "2.0.0-test"
		And I set the attribute form with the following values:
			| field        | value                                   |
			| name         | AttributeTypeEnum                       |
			| type         | enumerador                              |
			| tags         | TagB                                    |
			| description  | AttributeType with enum type            |
		Then the submit button is disabled
		When I edit the attribute create form to the following values:
			| field        | value                                   |
			| name         | AttributeTypeEnum                       |
			| type         | float                                   |
		Then the submit button is enabled
		When I submit the form
		Then the catalog attribute was created successfully	

	
	@TEST_NOSSISTSC-4988 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on CFS have to be conditioned based on its version state (Search)
		Given the data related to "CFS" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "cfs test version"
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                             |
		  | name   | Serviço de cliente                                                |
		  | action | edit (enabled), view (enabled), delete (enabled), clone (enabled) |
		And I navigate to "VERSION" search page
		Then I change version "cfs test version" state to "testing"
		And I navigate to "CFS" search page
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                    |
		  | name   | Serviço de cliente                                       |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "cfs test version" state to "production"
		And I navigate to "CFS" search page
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                    |
		  | name   | Serviço de cliente                                       |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "cfs test version" state to "blocked"
		And I navigate to "CFS" search page
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                    |
		  | name   | Serviço de cliente                                       |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "cfs test version" state to "production"
		Then I change version "cfs test version" state to "deprecated"
		And I navigate to "CFS" search page
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                    |
		  | name   | Serviço de cliente                                       |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "cfs test version" state to "abandoned"
		And I navigate to "CFS" search page
		And I submit form "cfs side search" with the following values:
		  | field | value              |
		  | name  | Serviço de cliente |
		Then the table columns of "table cfs list" contains:
		  | column | value                                                    |
		  | name   | Serviço de cliente                                       |
		  | action | edit (disabled), view (enabled), more options (disabled) |	

	
	@TEST_NOSSISTSC-4989 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on RFS have to be conditioned based on its version state (Search)
		Given the data related to "RFS" is loaded
		And I am "administrator" on "catalog rfs search" page
		And I am contextualized to the version "rfs test version"
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                             |
		  | name   | RFS DUMMY                                                         |
		  | action | edit (enabled), view (enabled), delete (enabled), clone (enabled) |
		And I navigate to "VERSION" search page
		Then I change version "rfs test version" state to "testing"
		And I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                    |
		  | name   | RFS DUMMY                                                |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "rfs test version" state to "production"
		And I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                    |
		  | name   | RFS DUMMY                                                |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "rfs test version" state to "blocked"
		And I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                    |
		  | name   | RFS DUMMY                                                |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "rfs test version" state to "production"
		Then I change version "rfs test version" state to "deprecated"
		And I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                    |
		  | name   | RFS DUMMY                                                |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "rfs test version" state to "abandoned"
		And I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | name  | RFS DUMMY |
		Then the table columns of "table rfs list" contains:
		  | column | value                                                    |
		  | name   | RFS DUMMY                                                |
		  | action | edit (disabled), view (enabled), more options (disabled) |	

	
	@TEST_NOSSISTSC-4990 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on RESOURCE have to be conditioned based on its version state (Search)
		Given the data related to "RESOURCE" is loaded
		And I am "administrator" on "catalog resource search" page
		And I am contextualized to the version "resource test version"
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value          |                                                   
		  | name   | RESOURCE DUMMY |                                                    
		  | action | edit (enabled), view (enabled), delete (enabled), clone (enabled) | 
		And I navigate to "VERSION" search page
		Then I change version "resource test version" state to "testing"
		And I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value                                                    |
		  | name   | RESOURCE DUMMY                                           |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "resource test version" state to "production"
		And I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value                                                    |
		  | name   | RESOURCE DUMMY                                           |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "resource test version" state to "blocked"
		And I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value                                                    |
		  | name   | RESOURCE DUMMY                                           |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "resource test version" state to "production"
		Then I change version "resource test version" state to "deprecated"
		And I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value                                                    |
		  | name   | RESOURCE DUMMY                                           |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		And I navigate to "VERSION" search page
		Then I change version "resource test version" state to "abandoned"
		And I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value          |
		  | name  | RESOURCE DUMMY |
		Then the table columns of "table resource list" contains:
		  | column | value                                                    |
		  | name   | RESOURCE DUMMY                                           |
		  | action | edit (disabled), view (enabled), more options (disabled) |	

	
	@TEST_NOSSISTSC-4991 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on ATTRIBUTE have to be conditioned based on its version state (Search)
		Given the data related to "ATTRIBUTE" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "AttrTypesScenario"
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                             |
		  | name   | phone number                                                      |
		  | action | edit (enabled), view (enabled), delete (enabled), clone (enabled) |
		# Change Version state
		And I navigate to "VERSION" search page
		Then I change version "AttrTypesScenario" state to "testing"
		And I navigate to "ATTRIBUTE" search page
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                    |
		  | name   | phone number                                             |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		# Change Version state
		And I navigate to "VERSION" search page
		Then I change version "AttrTypesScenario" state to "production"
		And I navigate to "ATTRIBUTE" search page
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                    |
		  | name   | phone number                                             |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		# Change Version state
		And I navigate to "VERSION" search page
		Then I change version "AttrTypesScenario" state to "blocked"
		And I navigate to "ATTRIBUTE" search page
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                    |
		  | name   | phone number                                             |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		 # Change Version state
		And I navigate to "VERSION" search page
		Then I change version "AttrTypesScenario" state to "production"
		Then I change version "AttrTypesScenario" state to "deprecated"
		And I navigate to "ATTRIBUTE" search page
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                    |
		  | name   | phone number                                             |
		  | action | edit (disabled), view (enabled), more options (disabled) |
		# Change Version state
		And I navigate to "VERSION" search page
		Then I change version "AttrTypesScenario" state to "abandoned"
		And I navigate to "ATTRIBUTE" search page
		When I submit form "attribute side search" with the following values:
		  | field | value        |
		  | name  | phone number |
		Then the table columns of "table attributes list" contains:
		  | column | value                                                    |
		  | name   | phone number                                             |
		  | action | edit (disabled), view (enabled), more options (disabled) |	

	
	@TEST_NOSSISTSC-4992 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [CATALOG-WEB] Tests permissions for user with operation's profile
		Given the data related to "VERSION" is loaded
		And I am "oper user" on "catalog version search" page
		# Version
		When I submit form "version side search" with the following values:
		  | field  | value                   |
		  | name   | 1.0.0-SELFNET           |
		Then the table columns of "table versions list" contains:
			| column  | value                                                                                       |
			| action  | edit (visible), view (visible), delete (hidden), clone (visible), state transition (hidden) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone" of the version view page are visible
		But the actions buttons "delete, state transition" of the version view page are hidden
		## CFS
		When I navigate to "CFS" search page
		And I contextualize search to version "1.0.0-SELFNET"
		Then the table columns of "table cfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# RFS
		When I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table rfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# Resource
		When I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table resource list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# Attribute
		When I navigate to "ATTRIBUTE" search page
		And I submit form "attribute side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table attributes list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible	

	
	@TEST_NOSSISTSC-4993 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [CATALOG-WEB] Tests permissions for user with consult's profile
		Given the data related to "VERSION" is loaded
		And I am "consult user" on "catalog version search" page
		# Version
		When I submit form "version side search" with the following values:
		  | field  | value                  |
		  | name   | 1.0.0-SELFNET          |
		Then the table columns of "table versions list" contains:
		  | column  | value                                                |
		  | action  | edit (hidden), view (visible), more options (hidden) |
		And "create page button" is absent
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete,state transition" of the view page are hidden
		# CFS
		When I navigate to "CFS" search page
		And I contextualize search to version "1.0.0-SELFNET"
		Then the table columns of "table cfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (hidden), view (visible), more options (hidden) |
		And "create page button" is absent
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are hidden
		# RFS
		When I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table rfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (hidden), view (visible), more options (hidden) |
		And "create page button" is absent
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are hidden
		# Resource
		When I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table resource list" contains:
		  | column  | value                                                             |
		  | action  | edit (hidden), view (visible), more options (hidden) |
		And "create page button" is absent
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are hidden
		# Attribute
		When I navigate to "ATTRIBUTE" search page
		And I submit form "attribute side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table attributes list" contains:
		  | column  | value                                                             |
		  | action  | edit (hidden), view (visible), more options (hidden) |
		And "create page button" is absent
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are hidden	

	
	@TEST_NOSSISTSC-4994 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [CATALOG-WEB] Tests permissions for user with administration's profile
		Given the data related to "VERSION" is loaded
		And I am "admin user" on "catalog version search" page
		# Version
		When I submit form "version side search" with the following values:
		  | field  | value                     |
		  | name  | 1.0.0-SELFNET           |
		Then the table columns of "table versions list" contains:
		  | column  | value                                                            |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible), state transition (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete,state transition" of the version view page are visible
		# CFS
		When I navigate to "CFS" search page
		And I contextualize search to version "1.0.0-SELFNET"
		Then the table columns of "table cfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# RFS
		When I navigate to "RFS" search page
		And I submit form "rfs side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table rfs list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# Resource
		When I navigate to "RESOURCE" search page
		And I submit form "resource side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table resource list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the view page are visible
		# Attribute
		When I navigate to "ATTRIBUTE" search page
		And I submit form "attribute side search" with the following values:
		  | field | value     |
		  | tags  | selfnet   |
		Then the table columns of "table attributes list" contains:
		  | column  | value                                                             |
		  | action  | edit (visible), view (visible), delete (visible), clone (visible) |
		And "create page button" is visible
		When I click 1st row action "view"
		Then the actions buttons "edit,clone,delete" of the attribute view page are visible	

	
	@TEST_NOSSISTSC-4995 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on Versions have to be conditioned based on its state (View)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog version search" page
		When I view version "1.0.0-SELFNET"
		Then I'm on the view page of "VERSION" with name "1.0.0-SELFNET" and state "development"
		And the version have the following next states "testing, abandoned"
		And the actions buttons "edit, clone, delete" of the version view page are enabled
		When I change the version state to "testing"
		Then an "version state change successfully" success notification should appear
		And I'm on the view page of "VERSION" with name "1.0.0-SELFNET" and state "testing"
		And the version have the following next states "abandoned, development, production"
		And the actions buttons "clone" of the version view page are enabled
		When I change the version state to "production"
		Then an "version state change successfully" success notification should appear
		And I'm on the view page of "VERSION" with name "1.0.0-SELFNET" and state "production"
		And the version have the following next states "blocked, deprecated"
		And the actions buttons "clone" of the version view page are enabled
		But the actions buttons "edit, delete" of the version view page are disabled
		When I change the version state to "deprecated"
		Then an "version state change successfully" success notification should appear
		And I'm on the view page of "VERSION" with name "1.0.0-SELFNET" and state "deprecated"
		And the version have the following next states "abandoned"
		And the actions buttons "clone" of the version view page are enabled
		But the actions buttons "edit, delete" of the version view page are disabled
		When I change the version state to "abandoned"
		Then an "version state change successfully" success notification should appear
		And I'm on the view page of "VERSION" with name "1.0.0-SELFNET" and state "abandoned"
		And the actions buttons "clone, delete" of the version view page are enabled
		But the actions buttons "edit" of the version view page are disabled	

	
	@TEST_NOSSISTSC-4996 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on CFS have to be conditioned based on its version state (View)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog cfs search" page
		And I am contextualized to the version "1.0.0-SELFNET"
		When I view cfs "CFS.IM"
		Then the actions buttons "edit, clone, delete" of the view page are enabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "testing"
		And I navigate to "CFS" search page
		And I view cfs "CFS.IM"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "production"
		And I navigate to "CFS" search page
		And I view cfs "CFS.IM"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "deprecated"
		And I navigate to "CFS" search page
		And I view cfs "CFS.IM"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "abandoned"
		And I navigate to "CFS" search page
		And I view cfs "CFS.IM"
		Then the actions buttons "edit, clone, delete" of the view page are disabled	

	
	@TEST_NOSSISTSC-4997 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on RFS have to be conditioned based on its version state (View)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog rfs search" page
		And I am contextualized to the version "1.0.0-SELFNET"
		When I view rfs "RFS.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are enabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "testing"
		And I navigate to "RFS" search page
		When I view rfs "RFS.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "production"
		And I navigate to "RFS" search page
		When I view rfs "RFS.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "deprecated"
		And I navigate to "RFS" search page
		When I view rfs "RFS.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "abandoned"
		And I navigate to "RFS" search page
		When I view rfs "RFS.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled	

	
	@TEST_NOSSISTSC-4998 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on RESOURCE have to be conditioned based on its version state (View)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog resource search" page
		And I am contextualized to the version "1.0.0-SELFNET"
		When I view resource "LR.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are enabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "testing"
		And I navigate to "RESOURCE" search page
		When I view resource "LR.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "production"
		And I navigate to "RESOURCE" search page
		When I view resource "LR.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "deprecated"
		And I navigate to "RESOURCE" search page
		When I view resource "LR.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "abandoned"
		And I navigate to "RESOURCE" search page
		When I view resource "LR.FLOWT"
		Then the actions buttons "edit, clone, delete" of the view page are disabled	

	
	@TEST_NOSSISTSC-4999 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Actions on ATTRIBUTE have to be conditioned based on its version state (View)
		Given the data related to "VERSION" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "1.0.0-SELFNET"
		When I view attribute type "id"
		Then the actions buttons "edit, clone, delete" of the attribute view page are enabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "testing"
		And I navigate to "ATTRIBUTE" search page
		When I view attribute type "id"
		Then the actions buttons "edit, clone, delete" of the attribute view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "production"
		And I navigate to "ATTRIBUTE" search page
		When I view attribute type "id"
		Then the actions buttons "edit, clone, delete" of the attribute view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "deprecated"
		And I navigate to "ATTRIBUTE" search page
		When I view attribute type "id"
		Then the actions buttons "edit, clone, delete" of the attribute view page are disabled
		When I navigate to "VERSION" search page
		And I change version "1.0.0-SELFNET" state to "abandoned"
		And I navigate to "ATTRIBUTE" search page
		When I view attribute type "id"
		Then the actions buttons "edit, clone, delete" of the attribute view page are disabled	

	
	@TEST_NOSSISTSC-5001 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: Cancel creation of an attribute with set restrictions
		Given I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "SELFNET"
		And I set the attribute form with the following values:
		  | field                   | value               |
		  | type                    | numérico            |
		  | selected characteristic | logical restriction |
		  | minRange                | 0                   |
		  | maxRange                | 9                   |
		And I cancel create attribute form
		Then a "change page possible data loss" confirmation modal appears	

	
	@TEST_NOSSISTSC-5002 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [CATALOG-WEB] When contextualized version is deleted, it should select the more recent version
		Given the data related to "VERSION" is loaded
			And I am "administrator" on "catalog cfs search" page
			When I contextualize search to version "1.0.0-SELFNET"
			Then I clone catalog version "1.0.0-SELFNET" from search
			And I'm on the view page of "VERSION" with name "Clone_1.0.0-SELFNET" with description "version SELFNET description" and no tags
			When I navigate to "VERSION" search page
			Then I click 1st row action "delete"
			When a "version delete confirmation" confirmation modal appears
			Then I confirm I want to delete
			And an "global notification success" success notification should appear
			When I submit form "version side search" with the following values:
				| field        | value              |
				| name         | 1.0.0-SELFNET |
			Then the table "table versions list" is empty
			When I navigate to "CFS" search page
			Then I am contextualized to the version "Clone_1.0.0-SELFNET"	

	
	@TEST_NOSSISTSC-5242 @TESTSET_NOSSISTSC-4852 @CATALOG
	Scenario: [WEB][ATTRIBUTE] The table page length, sidebar collapse and columns visibility should be saved on attribute search page even after the search is triggered
		Given the data related to "attribute search context" is loaded
		And I am "administrator" on "catalog attributes search" page
		And I am contextualized to the version "attribute pagination test"
		When I collapse search sidebar
		And I set table "table attributes list" to show 60 results per page
		And I click 1st row action "view"
		Then I should be on "catalog attributes view" page
		When I go back
		Then I should be on "catalog attributes search" page
		And the search sidebar is collapsed
		And I should be on 1st table page
		And the table is configured to show 60 results per page