package pt.ptinovacao.nossis.tsc.it.dataStore.snapshots.rfs;

import org.slf4j.Logger;
import pt.ptinovacao.nossis.tsc.it.dataStore.DataSnapshotUtils;
import pt.ptinovacao.nossis.tsc.it.dataStore.TscSnapshot;
import pt.ptinovacao.nossis.tsc.it.payload.AttributePayloadBuilder;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.nossis.tsc.it.web.utils.uuid.UUID_Manager;
import pt.ptinovacao.nossistsc.core.model.AttributeType;
import pt.ptinovacao.nossistsc.core.model.Component;
import pt.ptinovacao.nossistsc.core.model.Version;

/**
 * <AUTHOR>  18-12-2017.
 */
public class RFSDataSnapshot implements TscSnapshot {

    private static final Logger LOGGER = LoggerFactory.getLogger(RFSDataSnapshot.class);


    private static final String VERSION_NAME = "rfs test version";
    private final String versionName;

    public RFSDataSnapshot() {
        versionName = UUID_Manager.generateUniqueName(VERSION_NAME, UUID_Manager.UUIDConversion.ALPHANUMERIC);
    }

    @Override
    public void appendToDB() {

        LOGGER.info("***************** Pre-Push snapshot DB clean up *************************");

        DataSnapshotUtils.deleteVersionIfExists(versionName);

        LOGGER.info("***************** Push snapshot to DB *************************");

        final Version versionTestRfs = DataSnapshotUtils.createVersion(versionName);

        final AttributeType name = DataSnapshotUtils.createAttributeType("client name", "the name of the client", PrimitiveTypes.STRING, versionTestRfs);
        final AttributeType address = DataSnapshotUtils.createAttributeType("town name", "the location of the client", PrimitiveTypes.STRING, versionTestRfs);
        final AttributeType phone_number = DataSnapshotUtils.createAttributeType("phone number", "the phone number of the client", PrimitiveTypes.NUMBER, versionTestRfs);

        final AttributeType userName = DataSnapshotUtils.createAttributeType("username", "the username of the client", PrimitiveTypes.STRING, versionTestRfs);
        final AttributeType password = DataSnapshotUtils.createAttributeType("password", "the password", PrimitiveTypes.STRING, versionTestRfs);

        final Component user = DataSnapshotUtils.createComponent("Meo user", versionTestRfs,
                new AttributePayloadBuilder("Meo Username", userName).withDescription("Meo platform Username"),
                new AttributePayloadBuilder("Meo Password", password).withDescription("Meo platform Password")
        );

        DataSnapshotUtils.createRfs("TEST RFS EDIT", versionTestRfs,
                "This is a rfs",
                DataSnapshotUtils.createTag("testTag1", "testTag2"),
                new AttributePayloadBuilder("nome", name).withDescription("o nome do cliente"),
                new AttributePayloadBuilder("cidade", address).withDescription(" a cidade do cliente"),
                new AttributePayloadBuilder("numero", phone_number).withDescription("numero telefoníco"),
                new AttributePayloadBuilder("Utilizador", user).withDescription("Utilizador meo")
        );

        DataSnapshotUtils.createRfs("RFS DUMMY", versionTestRfs,
                "This is a rfs",
                DataSnapshotUtils.createTag("testTag1", "testTag2"),
                new AttributePayloadBuilder("nome", name).withDescription("o nome do cliente"),
                new AttributePayloadBuilder("cidade", address).withDescription(" a cidade do cliente"),
                new AttributePayloadBuilder("numero", phone_number).withDescription("numero telefoníco"),
                new AttributePayloadBuilder("Utilizador", user).withDescription("Utilizador meo")
        );

        LOGGER.info("***************** Finished snapshot push *************************");
    }

    @Override
    public void removeFromDB() {
        LOGGER.info("***************** Remove snapshot from DB *************************");
        DataSnapshotUtils.deleteVersionIfExists(versionName);
        LOGGER.info("**************** Finished snapshot removal ************************");
    }
}
