package pt.ptinovacao.nossis.tsc.it.models.data.profile;

import com.google.common.base.MoreObjects;
import cucumber.api.Scenario;
import cucumber.runtime.RuntimeOptions;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import pt.ptinovacao.nossis.tsc.it.config.loader.ExtendedConfigFile;
import pt.ptinovacao.nossis.tsc.it.models.data.profile.auth.AuthProfile;
import pt.ptinovacao.nossis.tsc.it.runner.TestRunner;
import pt.ptinovacao.nossis.tsc.it.web.utils.config.loader.ConfigFile;
import pt.ptinovacao.nossis.tsc.it.web.utils.config.loader.ConfigNode;
import pt.ptinovacao.nossis.tsc.it.web.utils.config.loader.ConfigTree;
import pt.ptinovacao.nossis.tsc.it.web.utils.driver.ItWebDriver;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.nossis.tsc.it.web.utils.logs.profile.LogProfile;
import pt.ptinovacao.nossis.tsc.it.web.utils.models.data.testprofile.auth.AuthI18nProcedure;

import java.io.File;
import java.nio.file.Paths;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 
 * Responsible to read tests profile
 *
 * <AUTHOR>
 */
public class TestProfile {
	private static final Logger LOGGER = LoggerFactory.getLogger(TestProfile.class);


	public static final TestProfile instance = new TestProfile();
	private static final ConfigFile nodeName = ExtendedConfigFile.TEST_PROFILES;
	private static final ConfigFile browserProfile = ExtendedConfigFile.BROWSER_PROFILES;
	private static final ConfigFile authProfile = ExtendedConfigFile.AUTH_PROFILES;
	private ConfigNode node;

	private TestProfile() {
	}

	/**
	 * loads configuration files
	 */
	public void loadConfig() {
		node = ConfigTree.instance.get(nodeName);
		assert node.isValid() : nodeName + " not loaded in the config";
		String testProfile = getItWebProfileEnv();
		node = node.get(TestProfile.getItWebProfileEnv());
		assert node.isValid() : testProfile + " not defined in config " + nodeName;
	}

	private static String getItWebProfileEnv() {
		String testProfile = SystemProperty.test_profile.getValue();
		if (testProfile == null || testProfile.isEmpty()) {
			return ConfigTree.instance.get(nodeName).getString(Keys.defaultProfile);
		} else {
			return testProfile;
		}
	}

	public void logEnvironmentForScenario(Scenario scenario) {
		final EnvironmentLogger logger = new EnvironmentLogger();
		{
			logger.addProperty(SystemProperty.auth_profile,                 getProfileName());
			logger.addProperty(SystemProperty.config_dir_path,              ExtendedConfigFile.CONFIG_PATH);
			logger.addProperty(SystemProperty.test_profile ,                TestProfile.getItWebProfileEnv());
			logger.addProperty(SystemProperty.profiles_config_file,         ExtendedConfigFile.TEST_PROFILES.fileName());
			logger.addProperty(SystemProperty.selenium_hub,                 getHub());
			logger.addProperty(SystemProperty.starting_url,                 getUrl());
			logger.addProperty(SystemProperty.access_management_endpoint,   getAccessManagementEndpoint());
			logger.addProperty(SystemProperty.report_path,   				getReportPath());
		}
		LOGGER.info("{}", logger);
	}

	public void logCucumberOptionsForScenario(Scenario scenario) {
		final RuntimeOptions currentRuntimeOptions = TestRunner.getCurrentRuntimeOptions();
		CucumberOptionsLogger logger = new CucumberOptionsLogger();

		if (currentRuntimeOptions != null) {
			logger.addProperty("feature paths", String.join(" ", currentRuntimeOptions.getFeaturePaths()));
			logger.addProperty("tags",
					currentRuntimeOptions.getFilters()
							.stream()
							.filter(obj -> obj instanceof String)
							.map(tagFilter -> "-t " + tagFilter.toString())
							.collect(Collectors.joining(" "))
			);
			logger.addProperty("glue", currentRuntimeOptions.getGlue().stream()
					.map(glue -> "-g " + glue)
					.collect(Collectors.joining()));
		}
		LOGGER.info("{}", logger);

	}

	private static String getCurrentTimezoneOffset() {
		final int offsetInSeconds = ZonedDateTime.now().getOffset().getTotalSeconds();
		String offset = String.format("%02d:%02d", Math.abs(offsetInSeconds / 3600), Math.abs((offsetInSeconds / 60) % 60));
		offset = (offsetInSeconds >= 0 ? "+" : "-") + offset;
		return offset;
	}


	public static void logTimeZones(ItWebDriver driver) {
		final StringBuilder builder = new StringBuilder();
		final Function<String, String> pad = (String text) -> String.format("%-" + 45 + "s", text);
		final String bar = pad.apply(" ").replace(' ', '═');

		builder.append("\n");
		builder.append("╔═").append(bar).append("═══").append(bar).append("═╗").append("\n");
		builder.append("║ ").append(pad.apply("Timezones")).append("   ").append(pad.apply(" ")).append(" ║").append("\n");
		builder.append("╠═").append(bar).append("═╦═").append(bar).append("═╝").append("\n");
		builder.append("║ ").append(pad.apply("Browser Timezone")).append(" ║ ").append(driver.getTimeZoneName()).append("\n");
		int browserOffset = driver.getTimezoneOffsetInMinutesToUtC();
		String browserTZOffset = (browserOffset >= 0 ? "+" : "-") + String.format("%02d:%02d", Math.abs(browserOffset / 60), Math.abs(browserOffset % 60));
		int diffOffset = driver.getTimeOffsetInMinutesToLocalTimeZone();
		String diffTZOffset = (diffOffset >= 0 ? "+" : "-") + String.format("%02d:%02d", Math.abs(diffOffset / 60), Math.abs(diffOffset % 60));
		builder.append("║ ").append(pad.apply("Browser Timezone offset")).append(" ║ ").append("UTC").append(browserTZOffset).append("\n");
		builder.append("║ ").append(pad.apply("IT Executor Timezone")).append(" ║ ").append(ZoneId.systemDefault().getId()).append("\n");
		builder.append("║ ").append(pad.apply("IT Executor Timezone offset")).append(" ║ ").append("UTC").append(getCurrentTimezoneOffset()).append("\n");
		builder.append("║ ").append(pad.apply("offset difference Browser -> IT Executor")).append(" ║ ").append(diffTZOffset).append("\n");
		builder.append("╚═").append(bar).append("═╝").append("\n");

		LOGGER.info("{}", builder);


	}





	/**
	 * Verifies if the "record" parameter is active
	 * <p>
	 * if the Record parameter is active, the tests should record and saved as video format.
	 *
	 * @return record status
	 */
	public boolean isRecordEnabled() {
		return node.getBoolean(Keys.recordVideo);
	}


	/**
	 * Gets the path name where to saved the recoded tests using video
	 *
	 * @return the recorded path name
	 */
	public String recordPathName() {
		ConfigNode recordPathVideoNode = node.get(Keys.recordPathVideo);
		if (recordPathVideoNode.isNull()) {
			return Paths.get("").toAbsolutePath().normalize().toString();
		}

		String recordPath = recordPathVideoNode.toString();

		if (recordPath.isEmpty()) {
			return Paths.get("").toAbsolutePath().normalize().toString();
		}
		if (recordPath.startsWith("~/")) {
			return FileUtils.getUserDirectoryPath() + recordPath.substring(1);
		}
		if (recordPath.equals("~")) {
			return FileUtils.getUserDirectoryPath();
		}
		return recordPath;
	}

	public File recordPath() {
		return new File(recordPathName());
	}

	public String getReportPath(){
		String reportPath = System.getProperty(SystemProperty.report_path.keyName);
		if(reportPath != null && !reportPath.isEmpty()){
			return reportPath;
		}
		return "target/public";
	}

	public String getUrl() {
		return MoreObjects.firstNonNull(SystemProperty.starting_url.getValue(), node.getString(Keys.startingURL));
	}

	private String getHub() {
		final String property = SystemProperty.selenium_hub.getValue();
		if (property == null || property.isEmpty()) {
			return ConfigTree.instance.getString(browserProfile, getBrowserProfileName(), "grid", "hub");
		}
		return property;
	}

	private String getBrowserProfileName() {
		if (node.get(Keys.browserProfile).isNull()) {
			return ConfigTree.instance.get(browserProfile, "profile").toString();
		}
		return node.getString(Keys.browserProfile);
	}


	public Map<String, Object> getBrowserProfile() {
		return browserProfileWithHub(SystemProperty.selenium_hub.getValue());
	}

	private Map<String, Object> browserProfileWithHub(final String overrideSeleniumHub) {
		final ConfigNode profileConfig = ConfigTree.instance.get(browserProfile, getBrowserProfileName());
		Map<String, Object> config = new HashMap<>();
		config.putAll(profileConfig.toMap());
		if (overrideSeleniumHub != null && !overrideSeleniumHub.isEmpty()) {
			Map<String, Object> grid = new HashMap<>();
			grid.putAll(profileConfig.get("grid").toMap());
			grid.put("hub", overrideSeleniumHub);
			config.put("grid", grid);
		}
		return config;
	}


	/**
	 * Gets the logs configuration for this test profile
	 *
	 * @return Map of log where the key is the name of the log, and the values is the log configuration
	 */
	public Map<String, LogProfile> getLogs() {
		Map<String, LogProfile> logs = new HashMap<>();
		if (node.get("logs").isValid()) {
			for (String key : node.get("logs").toMap().keySet()) {
				LogProfile profile = LogProfile.initLog(node.get("logs", key));
				if (profile != null) {
					logs.put(key, profile);
				}
			}
		}
		return logs;
	}


	private String getProfileName() {
		String profileName = SystemProperty.auth_profile.getValue();
		if (profileName != null && !profileName.isEmpty()) {
			return profileName;
		}
		profileName = node.getString(Keys.authProfile);
		if (profileName != null && !profileName.isEmpty()) {
			return profileName;
		}
		profileName = ConfigTree.instance.get(authProfile, "default").asString();
		if (profileName != null && !profileName.isEmpty()) {
			return profileName;
		}
		return "portal";
	}

	/**
	 * Gets the logs configuration for this test profile
	 *
	 * @return Map of log where the key is the name of the log, and the values is the log configuration
	 */
	public AuthProfile getAuthProfile() {
		final String profileName = getProfileName();
		final ConfigNode configNode = ConfigTree.instance.get(authProfile, profileName);
		final String authForm = configNode.getString(Keys.authForm);
		final String authUsernameInput = configNode.getString(Keys.authUsernameInput);
		final String authPasswordInput = configNode.getString(Keys.authPasswordInput);
		final String authSubmitButton = configNode.getString(Keys.authSubmitButton);
		final String i18nProcedure = configNode.getString(Keys.i18nProcedure);

		AuthI18nProcedure procedure = AuthI18nProcedure.PORTAL_LOGIN;
		if (i18nProcedure.equalsIgnoreCase(KeyValues.i18nProcedure_COOKIE_INJECTION)) {
			procedure = AuthI18nProcedure.COOKIE_INJECTION;
		}
		return new AuthProfile(
				profileName, authForm, authUsernameInput, authPasswordInput, authSubmitButton, procedure,
				getAccessManagementEndpoint()
		);
	}

	public String getAccessManagementEndpoint() {
		String accessManagementEndpoint = SystemProperty.access_management_endpoint.getValue();
		if (accessManagementEndpoint == null || accessManagementEndpoint.isEmpty()) {
			return node.getString(Keys.accessManagementEndpoint);
		}
		return accessManagementEndpoint;
	}

	public String getAccessManagementView() {
		return node.getString(Keys.accessManagementView);
	}

	private static class TableLogger {

		String title;
		String leftColumnLabel;
		String rightColumnLabel;
		List<PropertyEntry> entries = new ArrayList<>();


		public TableLogger(String title, String leftColumnLabel, String rightColumnLabel) {
			this.title = title;
			this.leftColumnLabel = leftColumnLabel;
			this.rightColumnLabel = rightColumnLabel;
		}

		public TableLogger addProperty(final SystemProperty key, String value) {
			entries.add(new PropertyEntry(key.keyName, value));
			return this;
		}

		public TableLogger addProperty(final String key, String value) {
			entries.add(new PropertyEntry(key, value));
			return this;
		}

		public String showLogs() {
			int padding = Stream.concat(
					Stream.of(leftColumnLabel),
					entries.stream().map(PropertyEntry::getProperty).map(String::trim)
			).max(Comparator.comparingInt(String::length))
					.map(String::length)
					.orElse(0);


            final Function<String, String> pad = (String text) -> String.format("%-" + padding + "s", text);
            final Function<String, String> padTitle = (String text) -> String.format("%-" + (padding * 2 + 3) + "s", text);
			final String bar = pad.apply(" ").replace(' ', '═');
			final StringBuilder builder = new StringBuilder();
			builder.append("\n");
			builder.append("╔═").append(bar).append("═══").append(bar).append("═╗").append("\n");
			builder.append("║ ").append(padTitle.apply(title)).append(" ║").append("\n");
			builder.append("╠═").append(bar).append("═╦═").append(bar).append("═╣").append("\n");
			builder.append("║ ").append(pad.apply(leftColumnLabel)).append(" ║ ").append(pad.apply(rightColumnLabel)).append(" ║").append("\n");
			builder.append("╠═").append(bar).append("═╬═").append(bar).append("═╝").append("\n");
			entries.forEach(propertyEntry -> {
				builder.append("║ ").append(pad.apply(propertyEntry.getProperty().trim())).append(" ║ ").append(propertyEntry.getValue()).append("\n");

			});
			builder.append("╚═").append(bar).append("═╝").append("\n");
			return builder.toString();
		}

		@Override
		public String toString() {
			return showLogs();
		}

		private class PropertyEntry {
			final String property;
			final String value;

			PropertyEntry(String property, String value) {
				this.property = property;
				this.value = value;
			}

			public String getProperty() {
				return property;
			}

			public String getValue() {
				return value;
			}
		}


	}

	private static class EnvironmentLogger extends TableLogger {
		public EnvironmentLogger() {
			super("Environment", "System Property", "Value");
		}
	}

	private static class CucumberOptionsLogger extends TableLogger {
		public CucumberOptionsLogger() {
			super("Cucumber options", "option", "Value");
		}
	}

	private enum SystemProperty {
		access_management_endpoint("it.web.access.management.endpoint"),
		auth_profile("it.web.auth.profile"),
		config_dir_path("it.web.config.dir.path"),
		profiles_config_file("it.web.profiles.config.file"),
		report_path("it.web.report.path"),
		selenium_hub("it.web.selenium.hub"),
		starting_url("it.web.starting.url"),
		test_profile("it.web.profile");

		final String keyName;

		SystemProperty(String keyName) {
			this.keyName = keyName;
		}

		String getValue() {
			return System.getProperty(this.keyName);
		}
	}

	private static class Keys {
		final static String startingURL = "startingURL";
		final static String recordVideo = "record";
		final static String recordPathVideo = "recordPath";
		final static String defaultProfile = "default_profile";
		final static String browserProfile = "browserProfile";
		final static String authProfile = "authProfile";

		final static String authForm = "form";
		final static String authUsernameInput = "username input";
		final static String authPasswordInput = "password input";
		final static String authSubmitButton = "submit button";
		final static String i18nProcedure = "i18nProcedure";
		final static String accessManagementEndpoint = "access management endpoint";
		final static String accessManagementView = "access management view";
	}

	private static class KeyValues {
		final static String i18nProcedure_COOKIE_INJECTION = "cookie injection";
	}


}

