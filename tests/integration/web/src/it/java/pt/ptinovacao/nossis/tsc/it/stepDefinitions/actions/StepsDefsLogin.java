package pt.ptinovacao.nossis.tsc.it.stepDefinitions.actions;

import cucumber.api.java.en.Given;
import cucumber.api.java.en.When;
import pt.ptinovacao.nossis.tsc.it.models.data.profile.TestProfile;
import pt.ptinovacao.nossis.tsc.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.nossis.tsc.it.web.utils.helpers.LoginHelper;
import pt.ptinovacao.nossis.tsc.it.web.utils.helpers.ViewHelper;

/**
 *
 * Step definitions related to user authentication in the application
 *
 */
public class StepsDefsLogin extends CommonStepDefinitions {

    private static final int numOfLoginRetries = 5;

    @Given("^I am logged in$")
    public void I_am_logged_in() {
        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile()).withLoginRetries(numOfLoginRetries).loginAsDefaultUser();
    }

    @Given("^I am logged in as \"(.+)\"")
    public void I_am_logged_as(String user) {
        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile()).withLoginRetries(numOfLoginRetries).loginAsUser(user);
    }

    @Given("^I am \"(.+)\" on \"(.+)\" page$")
    public void I_am_user_on_page(String user, String pageName) {
        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile()).withLoginRetries(numOfLoginRetries).loginAsUser(user);
        ViewHelper.using(driver()).gotoViewFromUserPage(pageName);
        driver().waitLoadingFades();
        ViewHelper.using(driver()).checkOnView(pageName);
    }

    @When("^(?:I )?logout$")
    public void I_logout(){
        driver().logout();
    }

    @When("^(?:I )?switch to user \"(.+)\"$")
    public void I_switch_to_user(String user) {
        driver().logout();
        driver().waitLoadingFades();

        if (!driver().originalDriver().getCurrentUrl().endsWith("login")) {
            driver().click("expired page init_session");
            driver().waitLoadingFades();
            driver().click("expired page change_user_account");
        }

        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile()).loginAsUser(user);
    }

    @When("^I switch to user \"(.+)\" on \"(.+)\" page$")
    public void I_switch_to_user_on_page(String user, String pageName) {
        driver().logout();
        driver().waitLoadingFades();

        if (!driver().originalDriver().getCurrentUrl().endsWith("login")) {
            driver().click("expired page init_session");
            driver().waitLoadingFades();
            driver().click("expired page change_user_account");
        }

        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile()).loginAsUser(user);
        ViewHelper.using(driver()).gotoViewFromUserPage(pageName);
        driver().waitLoadingFades();
        ViewHelper.using(driver()).checkOnView(pageName);
    }

}
