package pt.ptinovacao.nossis.tsc.it.stepDefinitions.components;

import cucumber.api.java.en.Then;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedCondition;
import pt.ptinovacao.nossis.tsc.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.nossis.tsc.it.web.utils.Retry;
import pt.ptinovacao.nossis.tsc.it.web.utils.driver.ItWebDriver;

/**
 * Created by omar on 29-10-2015.
 *
 *  Step definitions related to verification of help tooltip content
 *
 */
public class StepsDefsHelpTooltip extends CommonStepDefinitions {
    @Then("^help tooltip \"(.+)\" content is \"(.+)\"")
    public void help_tooltip_content_is(String tooltip, final String content) {
        WebElement topElement = driver().waitElementVisible(tooltip);
        Retry.call(() -> checkBalloon(5, topElement, content), (Retry.RetryError e) -> {
            if (!(e.getThrowable() instanceof TimeoutException)) {
                throw new RuntimeException(e.getThrowable().getMessage(),e.getThrowable());
            }
        });
    }

    @Then("^help tooltip button (?:of )?\"(.+)\" is disabled")
    public void help_tooltip_is_disabled(String tooltip) {

        WebElement topElement = driver().element(tooltip);
        driver().click(topElement, "help tooltip button");
        driver().click(topElement, "help tooltip balloon");
    }

    private void checkBalloon(int timeoutInSeconds, WebElement topElement,final String content){
        driver().click(topElement, "help tooltip button");

        driver().waitUntil(timeoutInSeconds,new ExpectedCondition<Boolean>() {
            public Boolean apply(WebDriver driver) {
                return ItWebDriver.grabText(driver().element(topElement,"help tooltip balloon")).equals(content);
            }
            public String toString() {
                return "element to have content: " + content + "\n content: " +  ItWebDriver.grabText(driver().element(topElement,"help tooltip balloon"));
            }
        });
    }

}
