package pt.ptinovacao.nossis.tsc.it.stepDefinitions.actions;

import cucumber.api.java.en.When;
import pt.ptinovacao.nossis.tsc.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.nossis.tsc.it.web.utils.helpers.FileUpload;

import java.util.List;

/**
 * Created by omar on 04-11-2015.
 *
 * Step definitions related to upload of files.
 *
 */
public class StepsDefsFileUpload extends CommonStepDefinitions {

    @When("^I upload file \"(.+)\"")
    public static void I_upload_file(String file) {
        FileUpload.using(driver()).uploadFileFromResource(file);
    }

    @When("^I upload files \"(.+)\"")
    public static void I_upload_files(List<String> files) {
        FileUpload.using(driver()).uploadFilesFromResource(files);
    }
}
