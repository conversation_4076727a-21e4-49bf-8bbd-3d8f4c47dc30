{
  "idCatEntity": 17,
  "name": "CFS.IM",
  "description": "CFS Infrastructure Monitoring",
  "tags": [
    {
      "idCatEntity": 6,
      "name": "selfnet",
      "description": "selfnet tag"
    }
  ],
  "atributes": [
    {
      "idCatEntity": 16,
      "name": "location",
      "description": "SELFNET deployment location",
      "atributeType": {
        "id": {{ attributeType_location }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "appType",
      "description": "SELFNET application type",
      "atributeType": {
        "id": {{ attributeType_appType }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "srcIpAddress",
      "description": "SELFNET source IP Address",
      "atributeType": {
        "id": {{ attributeType_ipAddress }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "dstIpAddress",
      "description": "SELFNET destination IP Address",
      "atributeType": {
        "id": {{ attributeType_ipAddress }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "dstPort",
      "description": "SELFNET destination Port",
      "atributeType": {
        "id": {{ attributeType_port }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "dstUri",
      "description": "SELFNET destination URI",
      "atributeType": {
        "id": {{ attributeType_uri }},
        "idCatEntity": 9
      }
    },
    {
      "idCatEntity": 16,
      "name": "alert",
      "description": "SELFNET alert",
      "atributeType": {
        "id": {{ attributeType_alert }},
        "idCatEntity": 9
      }
    }
  ],
  "childRfs": [
    {
      "childEntity": {
        "id": {{ rfs_RFS.NS.SNORT }},
        "idCatEntity": 18
      },
      "idCatEntity": 27,
      "relationType": "AGGREGATION"
    },
    {
      "childEntity": {
        "id": {{ rfs_RFS.FLOWT }},
        "idCatEntity": 18
      },
      "idCatEntity": 27,
      "relationType": "AGGREGATION"
    }
  ],
  "childResources": [
    {
      "childEntity": {
        "id": {{ resource_RF.NS.SNORT }},
        "idCatEntity": 19
      },
      "idCatEntity": 26,
      "relationType": "AGGREGATION"
    }
  ],
  "version": {
    "id": {{ versionId }},
    "idCatEntity": 7
  }
}