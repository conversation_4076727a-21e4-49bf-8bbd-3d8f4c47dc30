package pt.ptinovacao.nossis.tsc.it.payload;

import pt.ptinovacao.nossistsc.core.model.AttributeType;
import pt.ptinovacao.nossistsc.core.model.Component;

public class AttributePayloadBuilder extends PayloadBuilder {

    private final String name;
    private String description = "Continuous Integration - Attribute";
    private AttributeType attributeType = null;
    private Component componentType = null;

    public AttributePayloadBuilder(String name, AttributeType type) {
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("Invalid AttributeType name!");
        }

        this.name = name;
        this.attributeType = type;
    }

    public AttributePayloadBuilder(String name, Component type) {
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("Invalid AttributeType name!");
        }

        this.name = name;
        this.componentType = type;
    }

    public AttributePayloadBuilder withDescription(String description) {
        this.description = description;
        return this;
    }

    private String buildAttributeType() {
        if (attributeType == null) {
            return "null";
        }
        return "{\"id\": " + attributeType.getId() + ", \"idCatEntity\": " + attributeType.getIdCatEntity() + "}";
    }

    private String buildComponentType() {
        if (componentType == null) {
            return "null";
        }
        return "{\"id\": " + componentType.getId() + ", \"idCatEntity\": " + componentType.getIdCatEntity() + "}";
    }

    @Override
    public String build() {
        return EntityType.ATTRIBUTE.payload
                .replace("$name", name)
                .replace("$description", description != null ? description : "")
                .replace("$attributeType", buildAttributeType())
                .replace("$componentType", buildComponentType());
    }

}