package pt.ptinovacao.nossis.tsc.it;

import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractBusinessEntityWithAttributes;
import pt.ptinovacao.nossistsc.core.model.AttributeType;
import pt.ptinovacao.nossistsc.core.model.Cfs;
import pt.ptinovacao.nossistsc.core.model.Component;
import pt.ptinovacao.nossistsc.core.model.Resource;
import pt.ptinovacao.nossistsc.core.model.Rfs;
import pt.ptinovacao.nossistsc.core.model.Version;

import static pt.ptinovacao.nossis.tsc.it.Commons.URL_VERSIONS;
import static pt.ptinovacao.nossis.tsc.it.CommonsCore.componentAttribute;
import static pt.ptinovacao.nossis.tsc.it.CommonsCore.simpleAttribute;

public class FillEntitiesTree {


    public static void main(String[] args) {
        AbstractBusinessEntityWithAttributes abstractBusinessEntityWithAttributes = null;
        fillEntitiesTreeBase("tests/integration/core/", "EntitiesGenerator");
    }

    // caso se queira extender o cenário aqui descrito, deve-se implementar um novo método que chame este.
    // o cenário criado por este método serve de base aos testes implementados em AggregatedEntityTestsIT
    public static void fillEntitiesTreeBase(String basePath, String baseName) {
        final String CFS_BASE_NAME = baseName + "CfsName-";
        final String RFS_BASE_NAME = baseName + "RfsName-";
        final String RESOURCE_BASE_NAME = baseName + "ResourceName-";
        final String COMPONENT_BASE_NAME = baseName + "ComponentName-";
        final String ATTRIBUTE_TYPE_BASE_NAME = baseName + "AttributeTypeName-";
        final String VERSION_BASE_NAME = baseName + "VersionName-";

        final String VERSION_JSON_FILE = basePath + Commons.RESOURCES_DIR + "versions/createVersion.json";
        final String CFS_JSON_FOLDER = basePath + Commons.RESOURCES_DIR + "cfs/";
        final String RFS_JSON_FOLDER = basePath + Commons.RESOURCES_DIR + "rfs/";
        final String RESOURCE_JSON_FOLDER = basePath + Commons.RESOURCES_DIR + "resource/";
        final String COMPONENT_JSON_FOLDER = basePath + Commons.RESOURCES_DIR + "components/";
        final String ATTRIBUTE_TYPE_JSON_FOLDER = basePath + Commons.RESOURCES_DIR + "attributeTypes/";

        CommonsCore.getEntityIdsFromIT(URL_VERSIONS + "?$filter=startswith(name,'" + VERSION_BASE_NAME + "')", "versions").forEach(EntityUtils::deleteVersion);

        // Version
        Version version = EntityUtils.createVersion(Utils.readFile(VERSION_JSON_FILE)
                .replace("{{ name }}", VERSION_BASE_NAME + "-DUMMY")
                .replace("{{ description }}", VERSION_BASE_NAME + "1 version description")
                .replace("{{ state }}", "1"));

        // attribute types
        AttributeType typeNumber = EntityUtils.createAttributeType(Utils.readFile(ATTRIBUTE_TYPE_JSON_FOLDER + "createAttributeTypeWithRangeConstraint.json")
                .replace("$attributeTypeName", ATTRIBUTE_TYPE_BASE_NAME + "Number")
                .replace("teste de attribute types2", ATTRIBUTE_TYPE_BASE_NAME + "Number description")
                .replace("$versionId", version.getId().toString()));

        AttributeType typeString = EntityUtils.createAttributeType(Utils.readFile(ATTRIBUTE_TYPE_JSON_FOLDER + "createAttributeTypeWithRegExpConstraint.json")
                .replace("$attributeTypeName", ATTRIBUTE_TYPE_BASE_NAME + "String")
                .replace("teste de attribute type", ATTRIBUTE_TYPE_BASE_NAME + "String description")
                .replace("$versionId", version.getId().toString()));

        // components
        Component componentAssociation = EntityUtils.createComponent(Utils.readFile(COMPONENT_JSON_FOLDER + "createComponentBase.json")
                .replace("{{ componentName }}", COMPONENT_BASE_NAME + "Association")
                .replace("{{ versionId }}", version.getId().toString())
                .replace("\"version\": {", "\"atributes\": [" + simpleAttribute(typeNumber.getId(), "number attribute")
                        + ", " + simpleAttribute(typeString.getId(), "string attribute") + "]," +
                        "\n\"version\": {"));

        Component componentAggregation = EntityUtils.createComponent(Utils.readFile(COMPONENT_JSON_FOLDER + "createComponentBase.json")
                .replace("{{ componentName }}", COMPONENT_BASE_NAME + "Aggregation")
                .replace("{{ versionId }}", version.getId().toString())
                .replace("\"version\": {", "\"atributes\": [" + simpleAttribute(typeNumber.getId(), "component number attribute")
                        + ", " + simpleAttribute(typeString.getId(), "component string attribute") + "]," +
                        "\n\"version\": {"));

        // resources
        Resource resourceAssociation = EntityUtils.createResource(Utils.readFile(RESOURCE_JSON_FOLDER + "createResourceWithChild.json")
                .replace("{{ name }}", RESOURCE_BASE_NAME + "Association")
                .replace("{{ description }}", RESOURCE_BASE_NAME + "Association description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Components")
                .replace("{{ child_entities }}", "")
                .replace("\"version\": {", "\"atributes\": [" + componentAttribute(componentAssociation.getId(), "resource attribute compound association")
                        + ", " + simpleAttribute(typeNumber.getId(), "resource number attribute association") + "]," +
                        "\n\"version\": {"));

        Resource resourceAssociationP1 = EntityUtils.createResource(Utils.readFile(RESOURCE_JSON_FOLDER + "createResourceWithChild.json")
                .replace("{{ name }}", RESOURCE_BASE_NAME + "Association- P1")
                .replace("{{ description }}", RESOURCE_BASE_NAME + "Association description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Components")
                .replace("{{ child_entities }}", "")
                .replace("\"version\": {", "\"atributes\": [" + componentAttribute(componentAssociation.getId(), "resource attribute compound association")
                        + ", " + simpleAttribute(typeNumber.getId(), "resource number attribute association") + "]," +
                        "\n\"version\": {"));

        Resource resourceAggregation = EntityUtils.createResource(Utils.readFile(RESOURCE_JSON_FOLDER + "createResourceWithChild.json")
                .replace("{{ name }}", RESOURCE_BASE_NAME + "Aggregation")
                .replace("{{ description }}", RESOURCE_BASE_NAME + "Aggregation description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Components")
                .replace("{{ child_entities }}", "")
                .replace("\"version\": {", "\"atributes\": [" + componentAttribute(componentAggregation.getId(), "resource attribute compound aggregation")
                        + ", " + simpleAttribute(typeNumber.getId(), "resource number attribute aggregation") + "]," +
                        "\n\"version\": {"));
        // rfs
        Rfs rfsAssociation = EntityUtils.createRfs(Utils.readFile(RFS_JSON_FOLDER + "createRfsWithChild.json")
                .replace("{{ name }}", RFS_BASE_NAME + "Association")
                .replace("{{ description }}", RFS_BASE_NAME + "Association description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ entity_name }}", "Resources")
                .replace("{{ child_entities }}", CommonsCore.childResource(resourceAssociation.getId(), "ASSOCIATION", 28L))
                .replace("\"version\": {", "\"atributes\": [" + simpleAttribute(typeString.getId(), "rfs string attribute association") + "]," +
                        "\n\"version\": {"));

        Rfs rfsAggregation = EntityUtils.createRfs(Utils.readFile(RFS_JSON_FOLDER + "createRfsWithChild.json")
                .replace("{{ name }}", RFS_BASE_NAME + "Aggregation")
                .replace("{{ description }}", RFS_BASE_NAME + "Aggregation description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ entity_name }}", "Resources")
                .replace("{{ child_entities }}", CommonsCore.childResource(resourceAggregation.getId(), "AGGREGATION", 28L))
                .replace("\"version\": {", "\"atributes\": [" + simpleAttribute(typeString.getId(), "rfs string attribute aggregation") + "]," +
                        "\n\"version\": {"));

        // cfs
        Cfs cfsChildAssociation = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "ChildAssociation")
                .replace("{{ description }}", CFS_BASE_NAME + "ChildAssociation description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Rfs")
                .replace("{{ child_entities }}", CommonsCore.childRfs(rfsAssociation.getId(), "ASSOCIATION", 27L))
                .replace("\"version\":{", "\"atributes\": [" + simpleAttribute(typeString.getId(), "cfs child string attribute association") + "]," +
                        "\n\"version\": {"));

        Cfs cfsChildAggregation = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "ChildAggregation")
                .replace("{{ description }}", CFS_BASE_NAME + "ChildAggregation description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Rfs")
                .replace("{{ child_entities }}", CommonsCore.childRfs(rfsAggregation.getId(), "AGGREGATION", 27L))
                .replace("\"version\":{", "\"atributes\": [" + simpleAttribute(typeString.getId(), "cfs child string attribute aggregation") + "]," +
                        "\n\"version\": {"));

        Cfs cfsChildAggregation2 = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "ChildAggregation2")
                .replace("{{ description }}", CFS_BASE_NAME + "ChildAggregation description2")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Rfs")
                .replace("{{ child_entities }}", CommonsCore.childRfs(rfsAggregation.getId(), "AGGREGATION", 27L))
                .replace("\"version\":{", "\"atributes\": [" + simpleAttribute(typeString.getId(), "cfs child string attribute aggregation") + "]," +
                        "\n\"version\": {"));

        Cfs cfsChildAggregation3 = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "ChildAggregation3")
                .replace("{{ description }}", CFS_BASE_NAME + "ChildAggregation description3")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Rfs")
                .replace("{{ child_entities }}", CommonsCore.childRfs(rfsAggregation.getId(), "AGGREGATION", 27L))
                .replace("\"version\":{", "\"atributes\": [" + simpleAttribute(typeString.getId(), "cfs child string attribute aggregation") + "]," +
                        "\n\"version\": {"));

        Cfs CfsP1 = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "Parent1")
                .replace("{{ description }}", CFS_BASE_NAME + "Parent description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Cfs")
                .replace("{{ child_entities }}", CommonsCore.childCfs(cfsChildAggregation.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAggregation2.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAggregation3.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAssociation.getId(), "ASSOCIATION")));

        Cfs CfsP2 = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "Parent2")
                .replace("{{ description }}", CFS_BASE_NAME + "Parent description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Cfs")
                .replace("{{ child_entities }}", CommonsCore.childCfs(cfsChildAggregation.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAggregation2.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAggregation3.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAssociation.getId(), "ASSOCIATION")));

        Cfs CfsPPlus = EntityUtils.createCfs(Utils.readFile(CFS_JSON_FOLDER + "createCfsWithChild.json")
                .replace("{{ name }}", CFS_BASE_NAME + "ParentPLUS")
                .replace("{{ description }}", CFS_BASE_NAME + "Parent description")
                .replace("{{ version_id }}", version.getId().toString())
                .replace("{{ child_entity }}", "Cfs")
                .replace("{{ child_entities }}", CommonsCore.childCfs(cfsChildAggregation.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(CfsP2.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(CfsP1.getId(), "AGGREGATION") +
                        "\n,\n" +
                        CommonsCore.childCfs(cfsChildAssociation.getId(), "ASSOCIATION")));
//                        "\n,\n"+
//                        CommonsCore.childResource(resourceAssociationP1.getId(), "AGGREGATION", resourceAssociationP1.getIdCatEntity())
    }

}
