nossis_tsc_install_entities_catalog_module: {{ nossis_tsc_install_entities_catalog_module }}
nossis_tsc_use_remote_auth_strategy: {{ nossis_tsc_use_remote_auth_strategy }}

PORTAL_LANGS: '{{ PORTAL_LANGS }}'

nossis_tsc_iam_host: "{{ nossis_tsc_iam_host }}"
nossis_tsc_access_management_endpoint: "{{ nossis_tsc_access_management_endpoint }}"
nossis_tsc_iam_oauth_client: "{{ nossis_tsc_iam_oauth_client }}"
nossis_tsc_iam_oauth_secret: "{{ nossis_tsc_iam_oauth_secret }}"
FUXI_HTTP_SERVER: "{{ FUXI_HTTP_SERVER }}"

# optionals
{% if nossis_tsc_frontend_portal_optional_variables is defined %}
{% for optional_variable in nossis_tsc_frontend_portal_optional_variables %}
{{ optional_variable.param }}: {{ optional_variable.value }}
{% endfor %}
{% endif %}
